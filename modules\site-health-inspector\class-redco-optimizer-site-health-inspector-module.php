<?php
/**
 * Site Health Inspector Module
 *
 * @link       https://redco.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/site-health-inspector
 */

// Include the checks trait
require_once plugin_dir_path( __FILE__ ) . 'class-redco-optimizer-site-health-checks.php';

/**
 * Site Health Inspector Module
 *
 * This class defines all code necessary to run the Site Health Inspector module.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/site-health-inspector
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Site_Health_Inspector_Module {

    // Include the checks trait
    use Redco_Optimizer_Site_Health_Checks;

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Scan results reference.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $scan_results    Reference to scan results.
     */
    private $scan_results = [];

    /**
     * Issues array.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $issues    Array of issues.
     */
    private $issues = [];

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Register hooks
        $this->register_hooks();
    }

    /**
     * Register all hooks for this module.
     *
     * @since    1.0.0
     */
    private function register_hooks() {
        // Add admin page
        add_action('redco_optimizer_admin_tabs', array($this, 'add_admin_tab'), 10, 1);

        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add AJAX handlers
        add_action('wp_ajax_redco_run_site_scan', array($this, 'ajax_run_site_scan'));
        add_action('wp_ajax_redco_export_site_health_report', array($this, 'ajax_export_report'));
        add_action('wp_ajax_redco_plugin_action', array($this, 'ajax_plugin_action'));
        add_action('wp_ajax_redco_delete_content', array($this, 'ajax_delete_content'));
        add_action('wp_ajax_redco_enable_module', array($this, 'ajax_enable_module'));

        // Schedule daily scan
        if (!wp_next_scheduled('redco_daily_site_scan')) {
            wp_schedule_event(time(), 'daily', 'redco_daily_site_scan');
        }

        // Add hook for scheduled scan
        add_action('redco_daily_site_scan', array($this, 'run_scheduled_scan'));

        // Add admin notification for issues
        add_action('admin_notices', array($this, 'display_admin_notice'));
    }

    /**
     * Add admin tab for the module.
     *
     * @since    1.0.0
     * @param    array    $tabs    The admin tabs.
     * @return   array    The updated admin tabs.
     */
    public function add_admin_tab($tabs) {
        $tabs['site-health-inspector'] = array(
            'tab' => 'redco-site-health-inspector-tab',
            'icon' => 'dashicons-shield',
            'text' => __('Site Health', 'redco-optimizer'),
            'description' => __('Scan your site for potential issues and get recommendations to fix them', 'redco-optimizer')
        );

        return $tabs;
    }

    /**
     * Register the JavaScript and CSS for the admin area.
     *
     * @since    1.0.0
     * @param    string    $hook_suffix    The current admin page.
     */
    public function enqueue_scripts($hook_suffix) {
        if (strpos($hook_suffix, 'redco-optimizer') === false) {
            return;
        }

        // Enqueue CSS
        wp_enqueue_style(
            'redco-site-health-inspector-admin',
            plugin_dir_url(__FILE__) . 'admin/css/site-health-inspector-admin.css',
            array(),
            $this->version,
            'all'
        );

        // Enqueue JavaScript
        wp_enqueue_script(
            'redco-site-health-inspector-admin',
            plugin_dir_url(__FILE__) . 'admin/js/site-health-inspector-admin.js',
            array('jquery'),
            $this->version,
            false
        );

        // Localize script
        wp_localize_script(
            'redco-site-health-inspector-admin',
            'redcoSiteHealth',
            array(
                'nonce' => wp_create_nonce('redco_optimizer_nonce'),
                'scanning_text' => __('Scanning...', 'redco-optimizer'),
                'scanning_title' => __('Site Scan in Progress', 'redco-optimizer'),
                'scanning_message' => __('Scanning your site for issues. This may take a moment...', 'redco-optimizer'),
                'scan_complete_title' => __('Scan Complete', 'redco-optimizer'),
                'scan_error_title' => __('Scan Error', 'redco-optimizer'),
                'scan_error_message' => __('There was an error running the site scan. Please try again.', 'redco-optimizer'),
                'show_details_text' => __('Show Details', 'redco-optimizer'),
                'hide_details_text' => __('Hide Details', 'redco-optimizer'),
                'export_success_title' => __('Export Successful', 'redco-optimizer'),
                'export_success_message' => __('Site health report has been exported successfully.', 'redco-optimizer'),
                'export_error_title' => __('Export Error', 'redco-optimizer'),
                'export_error_message' => __('There was an error exporting the site health report. Please try again.', 'redco-optimizer'),
                // Plugin action text
                'update_text' => __('Updating...', 'redco-optimizer'),
                'deactivate_text' => __('Deactivating...', 'redco-optimizer'),
                'delete_text' => __('Deleting...', 'redco-optimizer'),
                'deleting_text' => __('Deleting...', 'redco-optimizer'),
                // Confirmation messages
                'confirm_delete_plugin' => __('Are you sure you want to delete this plugin?', 'redco-optimizer'),
                'confirm_deactivate_plugin' => __('Are you sure you want to deactivate this plugin?', 'redco-optimizer'),
                'confirm_delete_content' => __('Are you sure you want to delete this content?', 'redco-optimizer'),
                // Module enabling
                'enabling_text' => __('Enabling...', 'redco-optimizer')
            )
        );
    }

    /**
     * AJAX handler for running a site scan.
     *
     * @since    1.0.0
     */
    public function ajax_run_site_scan() {
        // Check nonce
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        try {
            // Set a reasonable time limit for the scan
            set_time_limit(120); // 2 minutes

            // Run the scan
            $results = $this->run_scan();

            // Return the results
            wp_send_json_success(array(
                'message' => __('Scan completed successfully.', 'redco-optimizer'),
                'results' => $results
            ));
        } catch (Exception $e) {
            // Log the error
            error_log('Redco Optimizer - Error in site scan: ' . $e->getMessage());

            // Return an error response
            wp_send_json_error(array(
                'message' => __('An error occurred during the site scan. Please try again.', 'redco-optimizer')
            ));
        }
    }

    /**
     * AJAX handler for exporting the site health report.
     *
     * @since    1.0.0
     */
    public function ajax_export_report() {
        // Check nonce
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Get the scan results
        $scan_results = get_option('redco_site_scan_results', array());

        if (empty($scan_results)) {
            wp_send_json_error(array(
                'message' => __('No scan results found.', 'redco-optimizer')
            ));
        }

        // Generate the report
        $report = $this->generate_report($scan_results);

        // Generate filename
        $filename = 'site-health-report-' . date('Y-m-d') . '.txt';

        // Return the report
        wp_send_json_success(array(
            'report' => $report,
            'filename' => $filename
        ));
    }

    /**
     * Generate a text report from the scan results.
     *
     * @since    1.0.0
     * @param    array    $results    The scan results.
     * @return   string   The generated report.
     */
    private function generate_report($results) {
        $site_name = get_bloginfo('name');
        $site_url = get_bloginfo('url');
        $report = '';

        // Add header
        $report .= "=== SITE HEALTH REPORT ===\n";
        $report .= "Site: $site_name ($site_url)\n";
        $report .= "Date: " . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), current_time('timestamp')) . "\n";
        $report .= "WordPress Version: " . get_bloginfo('version') . "\n";
        $report .= "PHP Version: " . phpversion() . "\n\n";

        // Add summary
        $report .= "=== SUMMARY ===\n";
        $report .= "Total Issues: " . $results['total_issues'] . "\n";
        $report .= "Critical Issues: " . $results['critical_issues'] . "\n";
        $report .= "Warning Issues: " . $results['warning_issues'] . "\n";
        $report .= "Info Issues: " . $results['info_issues'] . "\n\n";

        // Add issues by category
        foreach ($results['categories'] as $category => $issues) {
            if (empty($issues)) {
                continue;
            }

            $report .= "=== " . strtoupper($category) . " ===\n";

            foreach ($issues as $issue) {
                $report .= "[" . strtoupper($issue['severity']) . "] " . $issue['title'] . "\n";
                $report .= $issue['description'] . "\n";

                if (!empty($issue['data'])) {
                    $report .= "Details:\n";

                    foreach ($issue['data'] as $key => $value) {
                        if (is_array($value)) {
                            $report .= "- " . ucfirst(str_replace('_', ' ', $key)) . ":\n";

                            foreach ($value as $subkey => $subvalue) {
                                if (is_array($subvalue)) {
                                    $report .= "  - " . (is_numeric($subkey) ? '' : ucfirst(str_replace('_', ' ', $subkey)) . ": ") . "\n";

                                    foreach ($subvalue as $subsubkey => $subsubvalue) {
                                        $report .= "    - " . ucfirst(str_replace('_', ' ', $subsubkey)) . ": " . $subsubvalue . "\n";
                                    }
                                } else {
                                    $report .= "  - " . (is_numeric($subkey) ? $subvalue : ucfirst(str_replace('_', ' ', $subkey)) . ": " . $subvalue) . "\n";
                                }
                            }
                        } else {
                            $report .= "- " . ucfirst(str_replace('_', ' ', $key)) . ": " . $value . "\n";
                        }
                    }
                }

                $report .= "\n";
            }

            $report .= "\n";
        }

        return $report;
    }

    /**
     * Run a scheduled scan.
     *
     * @since    1.0.0
     */
    public function run_scheduled_scan() {
        $results = $this->run_scan();

        // Send email notification if there are critical issues
        $this->maybe_send_notification($results);
    }

    /**
     * Run a full site scan.
     *
     * @since    1.0.0
     * @return   array    The scan results.
     */
    public function run_scan() {
        // Initialize scan results
        $scan_results = array(
            'last_scan' => current_time('mysql'),
            'total_issues' => 0,
            'critical_issues' => 0,
            'warning_issues' => 0,
            'info_issues' => 0,
            'categories' => array(
                'plugins' => array(),
                'themes' => array(),
                'wordpress' => array(),
                'server' => array(),
                'database' => array(),
                'security' => array(),
                'performance' => array(),
                'javascript' => array()
            )
        );

        // Set up the scan results object for the trait
        $this->scan_results = &$scan_results;
        $this->issues = array();

        try {
            // Check for outdated plugins
            try {
                $outdated_plugins = $this->check_outdated_plugins();
                if (!empty($outdated_plugins)) {
                    $scan_results['categories']['plugins'][] = array(
                        'severity' => 'critical',
                        'title' => 'Outdated plugins detected',
                        'description' => sprintf(
                            __('You have %d outdated plugins. Outdated plugins can contain security vulnerabilities.', 'redco-optimizer'),
                            count($outdated_plugins)
                        ),
                        'data' => array(
                            'count' => count($outdated_plugins),
                            'plugins' => $outdated_plugins
                        )
                    );
                    $scan_results['critical_issues']++;
                    $scan_results['total_issues']++;
                }
            } catch (Exception $e) {
                error_log('Redco Optimizer - Error checking outdated plugins: ' . $e->getMessage());
            }

            // Check for default WordPress content
            try {
                $default_content = $this->check_default_wp_content();
                if (!empty($default_content)) {
                    $scan_results['categories']['wordpress'][] = array(
                        'severity' => 'warning',
                        'title' => 'Default WordPress content detected',
                        'description' => __('Your site still contains default WordPress content. Consider removing these for a more professional site.', 'redco-optimizer'),
                        'data' => $default_content
                    );
                    $scan_results['warning_issues']++;
                    $scan_results['total_issues']++;
                }
            } catch (Exception $e) {
                error_log('Redco Optimizer - Error checking default content: ' . $e->getMessage());
            }

            // Check database size
            try {
                $db_size = $this->check_database_size();
                if ($db_size > 100) {
                    $scan_results['categories']['database'][] = array(
                        'severity' => 'info',
                        'title' => 'Database is large',
                        'description' => sprintf(
                            __('Your WordPress database is %d MB. Large databases can slow down your site.', 'redco-optimizer'),
                            $db_size
                        ),
                        'data' => array(
                            'size_mb' => $db_size
                        )
                    );
                    $scan_results['info_issues']++;
                    $scan_results['total_issues']++;
                }
            } catch (Exception $e) {
                error_log('Redco Optimizer - Error checking database size: ' . $e->getMessage());
            }

            // Check for JavaScript issues
            try {
                $this->check_javascript();
            } catch (Exception $e) {
                error_log('Redco Optimizer - Error in JavaScript check: ' . $e->getMessage());
            }

            // Count JavaScript issues
            if (!empty($scan_results['categories']['javascript'])) {
                foreach ($scan_results['categories']['javascript'] as $issue) {
                    $scan_results['total_issues']++;
                    if ($issue['severity'] === 'critical') {
                        $scan_results['critical_issues']++;
                    } elseif ($issue['severity'] === 'warning') {
                        $scan_results['warning_issues']++;
                    } elseif ($issue['severity'] === 'info') {
                        $scan_results['info_issues']++;
                    }
                }
            }
        } catch (Exception $e) {
            error_log('Redco Optimizer - Error in run_scan: ' . $e->getMessage());
        }

        // Save the results
        update_option('redco_site_scan_results', $scan_results);

        return $scan_results;
    }

    /**
     * Check for outdated plugins.
     *
     * @since    1.0.0
     * @return   array    Array of outdated plugins with details.
     */
    private function check_outdated_plugins() {
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $outdated_plugins = array();
        $plugins = get_plugins();
        $plugin_updates = get_site_transient('update_plugins');

        if (empty($plugin_updates) || empty($plugin_updates->response)) {
            return $outdated_plugins;
        }

        foreach ($plugin_updates->response as $plugin_path => $plugin_data) {
            if (isset($plugins[$plugin_path])) {
                $plugin_info = $plugins[$plugin_path];
                $outdated_plugins[] = array(
                    'name' => $plugin_info['Name'],
                    'slug' => dirname($plugin_path),
                    'current_version' => $plugin_info['Version'],
                    'new_version' => $plugin_data->new_version,
                    'plugin_path' => $plugin_path,
                    'update_url' => wp_nonce_url(
                        self_admin_url('update.php?action=upgrade-plugin&plugin=' . $plugin_path),
                        'upgrade-plugin_' . $plugin_path
                    ),
                    'deactivate_url' => wp_nonce_url(
                        self_admin_url('plugins.php?action=deactivate&plugin=' . $plugin_path),
                        'deactivate-plugin_' . $plugin_path
                    ),
                    'delete_url' => wp_nonce_url(
                        self_admin_url('plugins.php?action=delete-selected&checked[]=' . $plugin_path),
                        'bulk-plugins'
                    )
                );
            }
        }

        return $outdated_plugins;
    }

    /**
     * Check for default WordPress content.
     *
     * @since    1.0.0
     * @return   array    Array of default content found.
     */
    private function check_default_wp_content() {
        $default_content = array();

        // Check for sample page
        $sample_page = get_page_by_path('sample-page');
        if ($sample_page) {
            $default_content['sample_page'] = array(
                'id' => $sample_page->ID,
                'title' => $sample_page->post_title,
                'delete_url' => get_delete_post_link($sample_page->ID, '', true)
            );
        }

        // Check for hello world post
        $hello_world = get_posts(array(
            'name' => 'hello-world',
            'post_type' => 'post',
            'post_status' => 'publish',
            'numberposts' => 1
        ));

        if (!empty($hello_world)) {
            $default_content['hello_world_post'] = array(
                'id' => $hello_world[0]->ID,
                'title' => $hello_world[0]->post_title,
                'delete_url' => get_delete_post_link($hello_world[0]->ID, '', true)
            );
        }

        return $default_content;
    }

    /**
     * Check database size.
     *
     * @since    1.0.0
     * @return   int    Database size in MB.
     */
    private function check_database_size() {
        global $wpdb;

        $db_size = 0;
        $tables = $wpdb->get_results("SHOW TABLE STATUS", ARRAY_A);

        if ($tables) {
            foreach ($tables as $table) {
                $db_size += $table['Data_length'] + $table['Index_length'];
            }

            $db_size = round($db_size / (1024 * 1024), 2);
        }

        return $db_size;
    }

    /**
     * Display admin notice for critical issues.
     *
     * @since    1.0.0
     */
    public function display_admin_notice() {
        // Get the scan results
        $results = get_option('redco_site_scan_results', array());

        // Check if there are critical issues
        if (isset($results['critical_issues']) && $results['critical_issues'] > 0) {
            ?>
            <div class="notice notice-error is-dismissible">
                <p>
                    <strong><?php echo sprintf(__('Site Health Inspector found %d critical issues on your site!', 'redco-optimizer'), $results['critical_issues']); ?></strong>
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=site-health-inspector'); ?>" class="button button-primary" style="margin-left: 10px;">
                        <?php _e('View Issues', 'redco-optimizer'); ?>
                    </a>
                </p>
            </div>
            <?php
        }
    }

    /**
     * Send email notification for critical issues.
     *
     * @since    1.0.0
     * @param    array    $results    The scan results.
     */
    private function maybe_send_notification($results) {
        // Check if there are critical issues
        if (isset($results['critical_issues']) && $results['critical_issues'] > 0) {
            // Get admin email
            $admin_email = get_option('admin_email');

            // Set up email content
            $subject = sprintf(__('[%s] Critical Site Health Issues Detected', 'redco-optimizer'), get_bloginfo('name'));

            $message = sprintf(__('Site Health Inspector has detected %d critical issues on your site.', 'redco-optimizer'), $results['critical_issues']) . "\n\n";
            $message .= __('Issues:', 'redco-optimizer') . "\n";

            // Add critical issues to the message
            foreach ($results['categories'] as $category => $issues) {
                foreach ($issues as $issue) {
                    if ($issue['severity'] === 'critical') {
                        $message .= '- ' . $issue['title'] . ': ' . $issue['description'] . "\n";
                    }
                }
            }

            $message .= "\n" . __('Please visit your site admin to view and fix these issues:', 'redco-optimizer') . "\n";
            $message .= admin_url('admin.php?page=redco-optimizer&tab=site-health-inspector') . "\n\n";
            $message .= __('This email was sent by the Redco Optimizer plugin.', 'redco-optimizer');

            // Send the email
            wp_mail($admin_email, $subject, $message);
        }
    }

    /**
     * AJAX handler for plugin actions (update, deactivate, delete).
     *
     * @since    1.0.0
     */
    public function ajax_plugin_action() {
        // Check nonce
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has capability
        if (!current_user_can('activate_plugins')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get parameters
        $plugin = isset($_POST['plugin']) ? sanitize_text_field($_POST['plugin']) : '';
        $action = isset($_POST['plugin_action']) ? sanitize_text_field($_POST['plugin_action']) : '';

        if (empty($plugin) || empty($action)) {
            wp_send_json_error(array(
                'message' => __('Missing required parameters.', 'redco-optimizer')
            ));
        }

        // Make sure plugin exists
        if (!file_exists(WP_PLUGIN_DIR . '/' . $plugin)) {
            wp_send_json_error(array(
                'message' => __('Plugin does not exist.', 'redco-optimizer')
            ));
        }

        // Perform the action
        $result = array(
            'success' => false,
            'message' => '',
            'new_version' => ''
        );

        switch ($action) {
            case 'update':
                // Include required files
                require_once(ABSPATH . 'wp-admin/includes/class-wp-upgrader.php');
                require_once(ABSPATH . 'wp-admin/includes/class-plugin-upgrader.php');
                require_once(ABSPATH . 'wp-admin/includes/plugin-install.php');
                require_once(ABSPATH . 'wp-admin/includes/file.php');
                require_once(ABSPATH . 'wp-admin/includes/misc.php');

                // Initialize the upgrader
                $upgrader = new Plugin_Upgrader(new WP_Ajax_Upgrader_Skin());

                // Run the upgrade
                $upgrade_result = $upgrader->upgrade($plugin);

                if ($upgrade_result) {
                    // Get the new version
                    $plugins = get_plugins();
                    $new_version = isset($plugins[$plugin]) ? $plugins[$plugin]['Version'] : '';

                    $result = array(
                        'success' => true,
                        'message' => __('Plugin updated successfully.', 'redco-optimizer'),
                        'new_version' => $new_version
                    );
                } else {
                    $result = array(
                        'success' => false,
                        'message' => __('Failed to update plugin.', 'redco-optimizer')
                    );
                }
                break;

            case 'deactivate':
                // Deactivate the plugin
                $deactivate_result = deactivate_plugins($plugin, false, is_network_admin());

                if ($deactivate_result === null) {
                    $result = array(
                        'success' => true,
                        'message' => __('Plugin deactivated successfully.', 'redco-optimizer')
                    );
                } else {
                    $result = array(
                        'success' => false,
                        'message' => __('Failed to deactivate plugin.', 'redco-optimizer')
                    );
                }
                break;

            case 'delete':
                // Include required files
                require_once(ABSPATH . 'wp-admin/includes/plugin.php');
                require_once(ABSPATH . 'wp-admin/includes/file.php');

                // Make sure plugin is deactivated first
                if (is_plugin_active($plugin)) {
                    deactivate_plugins($plugin, false, is_network_admin());
                }

                // Delete the plugin
                $delete_result = delete_plugins(array($plugin));

                if (!is_wp_error($delete_result)) {
                    $result = array(
                        'success' => true,
                        'message' => __('Plugin deleted successfully.', 'redco-optimizer')
                    );
                } else {
                    $result = array(
                        'success' => false,
                        'message' => $delete_result->get_error_message()
                    );
                }
                break;

            default:
                $result = array(
                    'success' => false,
                    'message' => __('Invalid action.', 'redco-optimizer')
                );
                break;
        }

        // Run a new scan to update the results
        $this->run_scan();

        // Return the result
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * AJAX handler for deleting default WordPress content.
     *
     * @since    1.0.0
     */
    public function ajax_delete_content() {
        // Check nonce
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has capability
        if (!current_user_can('delete_posts')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get parameters
        $content_id = isset($_POST['content_id']) ? intval($_POST['content_id']) : 0;
        $content_type = isset($_POST['content_type']) ? sanitize_text_field($_POST['content_type']) : '';

        if (empty($content_id) || empty($content_type)) {
            wp_send_json_error(array(
                'message' => __('Missing required parameters.', 'redco-optimizer')
            ));
        }

        // Determine post type based on content type
        $post_type = ($content_type === 'sample_page') ? 'page' : 'post';

        // Delete the content
        $delete_result = wp_delete_post($content_id, true);

        if ($delete_result) {
            // Run a new scan to update the results
            $this->run_scan();

            wp_send_json_success(array(
                'message' => __('Content deleted successfully.', 'redco-optimizer')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to delete content.', 'redco-optimizer')
            ));
        }
    }

    /**
     * Check if the user has premium access.
     *
     * @since    1.0.0
     * @return   bool    True if the user has premium access, false otherwise.
     */
    private function has_premium_access() {
        // Check if license is valid and active
        $license_status = get_option('redco_optimizer_license_status', '');
        $license_key = get_option('redco_optimizer_license_key', '');

        // Only return true if license is valid and active
        if ($license_status === 'valid' && !empty($license_key)) {
            return true;
        }

        // Default to false for free version
        return false;
    }

    /**
     * AJAX handler for enabling Redco Optimizer modules.
     *
     * @since    1.0.0
     */
    public function ajax_enable_module() {
        // Check nonce
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get module name
        $module = isset($_POST['module']) ? sanitize_text_field($_POST['module']) : '';

        if (empty($module)) {
            wp_send_json_error(array(
                'message' => __('Missing required parameters.', 'redco-optimizer')
            ));
        }

        // Get current modules
        $modules = get_option('redco_optimizer_modules', array());

        // Check if this is a premium module
        if ($module === 'cdn' || (isset($modules[$module]) && isset($modules[$module]['premium']) && $modules[$module]['premium'])) {
            // Check if user has premium access
            if (!$this->has_premium_access()) {
                wp_send_json_error(array(
                    'message' => __('This is a premium feature. Please upgrade to Redco Optimizer Premium to enable this module.', 'redco-optimizer'),
                    'premium' => true
                ));
            }
        }

        // Enable the module
        if (isset($modules[$module])) {
            $modules[$module]['enabled'] = true;
            update_option('redco_optimizer_modules', $modules);

            // Run a new scan to update the results
            $this->run_scan();

            wp_send_json_success(array(
                'message' => sprintf(__('%s module has been enabled successfully.', 'redco-optimizer'), ucfirst(str_replace('_', ' ', $module)))
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Module not found.', 'redco-optimizer')
            ));
        }
    }
}
