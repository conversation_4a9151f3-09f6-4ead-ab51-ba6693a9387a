<?php
/**
 * Dashboard Help Documentation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials/help
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="redco-help-section" id="dashboard-help">
    <div class="redco-help-header">
        <h2><?php esc_html_e('Dashboard', 'redco-optimizer'); ?></h2>
        <p class="redco-help-description"><?php esc_html_e('The Dashboard provides an overview of your site\'s performance and quick access to key optimization features.', 'redco-optimizer'); ?></p>
    </div>

    <div class="redco-help-content">
        <h3><?php esc_html_e('Dashboard Overview', 'redco-optimizer'); ?></h3>
        <p><?php esc_html_e('The Dashboard is your central hub for monitoring and improving your website\'s performance. It displays your current optimization status, performance score, and provides quick access to common optimization tasks.', 'redco-optimizer'); ?></p>
        
        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Performance Score', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('The circular gauge displays your site\'s current performance score as a percentage. This score is calculated based on various performance metrics and indicates how well your site is optimized.', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('90-100%: Excellent - Your site is well-optimized', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('70-89%: Good - Your site performs well but has room for improvement', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('50-69%: Average - Your site needs optimization', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Below 50%: Poor - Your site requires significant optimization', 'redco-optimizer'); ?></li>
            </ul>
            <p><?php esc_html_e('Click "View detailed report" to see a breakdown of your performance metrics and specific recommendations for improvement.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Quick Actions', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('This section provides one-click access to common optimization tasks:', 'redco-optimizer'); ?></p>
            <ul>
                <li><strong><?php esc_html_e('Clear Cache Files', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Removes all cached files generated by Redco Optimizer. Use this after making significant changes to your site to ensure visitors see the latest version.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Optimize Images', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Compresses all unoptimized image files in your media library to reduce their file size while maintaining visual quality.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Database Cleanup', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Removes unnecessary data from your database to improve performance.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Bulk Optimization', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Optimizes all images in your media library at once.', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Optimization Status', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('This section displays the current status of all optimization features:', 'redco-optimizer'); ?></p>
            <ul>
                <li><strong><?php esc_html_e('Green Circle', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Feature is enabled and working properly', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Gray Circle', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Feature is disabled or not configured', 'redco-optimizer'); ?></li>
            </ul>
            <p><?php esc_html_e('Click on any feature to navigate directly to its settings page for configuration.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Optimization Tips', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('This section provides actionable recommendations to improve your site\'s performance:', 'redco-optimizer'); ?></p>
            <ul>
                <li><strong><?php esc_html_e('Enable Browser Caching', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Browser caching stores website resources locally in visitors\' browsers, reducing load times for returning visitors.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Optimize Images', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Reduce image file sizes without sacrificing quality to improve page load times.', 'redco-optimizer'); ?></li>
            </ul>
            <p><?php esc_html_e('Follow these recommendations to significantly improve your site\'s performance.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Frequently Asked Questions', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Common questions about website optimization and how to use Redco Optimizer effectively:', 'redco-optimizer'); ?></p>
            <ul>
                <li><strong><?php esc_html_e('What is page caching and how does it work?', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Page caching creates static HTML versions of your dynamic WordPress pages, significantly reducing server processing time and improving load speed.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Will optimization break my site?', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Redco Optimizer is designed to be safe and compatible with most WordPress setups. However, it\'s always recommended to test changes on a staging site first or have a backup available.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('How do I know if the optimizations are working?', 'redco-optimizer'); ?></strong>: <?php esc_html_e('The performance score on the dashboard will increase as you implement optimizations. You can also use tools like Google PageSpeed Insights to measure improvements.', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Best Practices', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Follow these recommendations for optimal performance:', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Enable page caching for significant performance improvements', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Optimize all images before uploading them to your site', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Run database cleanup regularly (once a month is recommended)', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Use a CDN for global performance improvements', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Minify and combine CSS and JavaScript files', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable lazy loading for images and videos', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Clear cache after making significant changes to your site', 'redco-optimizer'); ?></li>
            </ol>
        </div>
    </div>
</div>
