<?php

/**
 * The Async CSS functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The Async CSS functionality of the plugin.
 *
 * Defines the functionality for loading CSS asynchronously.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_Async_CSS {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for async CSS.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for async CSS.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
    }

    /**
     * Get async CSS settings.
     *
     * @since    1.0.0
     * @return   array    The async CSS settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_file_optimization_settings', array() );
        
        // Default settings
        $defaults = array(
            'async_css' => 0,
            'async_css_exclusions' => '',
            'critical_css' => '',
        );
        
        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize async CSS.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if async CSS is enabled
        if ( ! $this->settings['async_css'] ) {
            return;
        }

        // Add filter to process HTML
        add_filter( 'redco_buffer', array( $this, 'process_html' ), 25 );
    }

    /**
     * Process HTML to load CSS asynchronously.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    public function process_html( $html ) {
        // Don't process if user is logged in
        if ( is_user_logged_in() ) {
            return $html;
        }
        
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }
        
        // Get exclusions
        $exclusions = $this->get_exclusions();
        
        // Extract all CSS files
        preg_match_all( '/<link\s+([^>]*rel=["\']stylesheet["\'][^>]*)>/isU', $html, $matches );
        
        if ( empty( $matches[0] ) ) {
            return $html;
        }
        
        $critical_css = $this->settings['critical_css'];
        
        // Add critical CSS
        if ( ! empty( $critical_css ) ) {
            $html = preg_replace( '/<\/title>/i', '</title><style id="redco-critical-css">' . $critical_css . '</style>', $html, 1 );
        }
        
        // Process each CSS file
        foreach ( $matches[0] as $i => $tag ) {
            $attributes = $matches[1][$i];
            
            // Skip if excluded
            foreach ( $exclusions as $exclusion ) {
                if ( strpos( $attributes, $exclusion ) !== false ) {
                    continue 2;
                }
            }
            
            // Extract href
            if ( preg_match( '/href=["\']([^"\']+)["\']/i', $attributes, $href_match ) ) {
                $href = $href_match[1];
                
                // Create preload tag
                $preload_tag = '<link rel="preload" href="' . esc_url( $href ) . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
                $noscript_tag = '<noscript><link rel="stylesheet" href="' . esc_url( $href ) . '"></noscript>';
                
                // Replace original tag with preload tag
                $html = str_replace( $tag, $preload_tag . $noscript_tag, $html );
            }
        }
        
        // Add loadCSS polyfill
        $html = $this->add_loadcss_script( $html );
        
        return $html;
    }

    /**
     * Get exclusions for async CSS.
     *
     * @since    1.0.0
     * @return   array    The exclusions for async CSS.
     */
    private function get_exclusions() {
        $exclusions = array();
        
        // Add default exclusions
        $exclusions[] = 'admin-bar.min.css';
        
        // Add user exclusions
        if ( ! empty( $this->settings['async_css_exclusions'] ) ) {
            $user_exclusions = explode( "\n", $this->settings['async_css_exclusions'] );
            $exclusions = array_merge( $exclusions, array_map( 'trim', $user_exclusions ) );
        }
        
        return array_unique( $exclusions );
    }

    /**
     * Add loadCSS script to HTML.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The HTML content with loadCSS script.
     */
    private function add_loadcss_script( $html ) {
        $loadcss_script = $this->get_loadcss_script();
        
        // Add loadCSS script before </head>
        $html = str_replace( '</head>', $loadcss_script . '</head>', $html );
        
        return $html;
    }

    /**
     * Get loadCSS script.
     *
     * @since    1.0.0
     * @return   string    The loadCSS script.
     */
    private function get_loadcss_script() {
        $script = <<<EOT
<script>
/* loadCSS. [c]2017 Filament Group, Inc. MIT License */
(function(w){"use strict";if(!w.loadCSS){w.loadCSS=function(){}}
var rp=loadCSS.relpreload={};rp.support=(function(){var ret;try{ret=w.document.createElement("link").relList.supports("preload")}catch(e){ret=!1}
return function(){return ret}})();rp.bindMediaToggle=function(link){var finalMedia=link.media||"all";function enableStylesheet(){link.media=finalMedia}
if(link.addEventListener){link.addEventListener("load",enableStylesheet)}else if(link.attachEvent){link.attachEvent("onload",enableStylesheet)}
setTimeout(function(){link.rel="stylesheet";link.media="only x"});setTimeout(enableStylesheet,3000)};rp.poly=function(){if(rp.support()){return}
var links=w.document.getElementsByTagName("link");for(var i=0;i<links.length;i++){var link=links[i];if(link.rel==="preload"&&link.getAttribute("as")==="style"&&!link.getAttribute("data-loadcss")){link.setAttribute("data-loadcss",!0);rp.bindMediaToggle(link)}}};if(!rp.support()){rp.poly();var run=w.setInterval(rp.poly,500);if(w.addEventListener){w.addEventListener("load",function(){rp.poly();w.clearInterval(run)})}else if(w.attachEvent){w.attachEvent("onload",function(){rp.poly();w.clearInterval(run)})}}
if(typeof exports!=="undefined"){exports.loadCSS=loadCSS}
else{w.loadCSS=loadCSS}}(typeof global!=="undefined"?global:this))
</script>
EOT;

        return $script;
    }

    /**
     * Generate critical CSS for a URL.
     *
     * @since    1.0.0
     * @param    string    $url    The URL to generate critical CSS for.
     * @return   string    The critical CSS.
     */
    public function generate_critical_css( $url ) {
        // In a real implementation, this would use a service to analyze the page and extract critical CSS
        // For now, we'll just return a placeholder
        
        $critical_css = <<<EOT
/* Critical CSS placeholder */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    margin: 0;
    padding: 0;
    color: #333;
    background-color: #fff;
}
header, footer, main, nav {
    display: block;
}
a {
    background-color: transparent;
    color: #0073aa;
    text-decoration: none;
}
h1, h2, h3, h4, h5, h6 {
    clear: both;
    margin: 0.75em 0;
}
p {
    margin: 0 0 1.5em;
}
img {
    max-width: 100%;
    height: auto;
}
EOT;
        
        return $critical_css;
    }
}
