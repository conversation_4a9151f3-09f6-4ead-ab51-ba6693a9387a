<?php

/**
 * The LazyLoad for CSS background images functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The LazyLoad for CSS background images functionality of the plugin.
 *
 * Defines the functionality for lazy loading CSS background images.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_LazyLoad_CSS_BG {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for LazyLoad CSS background images.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for LazyLoad CSS background images.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
    }

    /**
     * Get LazyLoad CSS background images settings.
     *
     * @since    1.0.0
     * @return   array    The LazyLoad CSS background images settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_lazyload_settings', array() );
        
        // Default settings
        $defaults = array(
            'lazyload_css_bg' => 0,
            'lazyload_css_bg_exclusions' => '',
        );
        
        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize LazyLoad CSS background images.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if LazyLoad CSS background images is enabled
        if ( ! $this->settings['lazyload_css_bg'] ) {
            return;
        }

        // Add filter to process HTML
        add_filter( 'redco_buffer', array( $this, 'process_html' ), 35 );
        
        // Add script to footer
        add_action( 'wp_footer', array( $this, 'add_lazyload_script' ) );
    }

    /**
     * Process HTML to lazy load CSS background images.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    public function process_html( $html ) {
        // Don't process if user is logged in
        if ( is_user_logged_in() ) {
            return $html;
        }
        
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }
        
        // Get exclusions
        $exclusions = $this->get_exclusions();
        
        // Process inline background images
        $html = preg_replace_callback(
            '/<(div|section|span|li|a|figure)[^>]*style=["\'][^"\']*background-image\s*:\s*url\(["\']?([^"\'()]+)["\']?\)[^>]*>/i',
            function( $matches ) use ( $exclusions ) {
                $tag = $matches[0];
                $element = $matches[1];
                $bg_url = $matches[2];
                
                // Skip if excluded
                foreach ( $exclusions as $exclusion ) {
                    if ( strpos( $tag, $exclusion ) !== false || strpos( $bg_url, $exclusion ) !== false ) {
                        return $tag;
                    }
                }
                
                // Skip if already processed
                if ( strpos( $tag, 'data-bg' ) !== false ) {
                    return $tag;
                }
                
                // Extract style attribute
                preg_match( '/style=["\']([^"\']*)["\']/', $tag, $style_matches );
                
                if ( empty( $style_matches ) ) {
                    return $tag;
                }
                
                $style = $style_matches[1];
                
                // Remove background-image from style
                $new_style = preg_replace( '/background-image\s*:\s*url\(["\']?([^"\'()]+)["\']?\)\s*;?/i', '', $style );
                
                // Create new tag with data-bg attribute
                $new_tag = str_replace(
                    'style="' . $style . '"',
                    'data-bg="url(' . $bg_url . ')" class="redco-lazyload" style="' . $new_style . '"',
                    $tag
                );
                
                return $new_tag;
            },
            $html
        );
        
        // Process CSS files for background images
        $html = $this->process_css_files( $html );
        
        return $html;
    }

    /**
     * Process CSS files for background images.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    private function process_css_files( $html ) {
        // Extract all CSS files
        preg_match_all( '/<link\s+([^>]*rel=["\']stylesheet["\'][^>]*)>/isU', $html, $matches );
        
        if ( empty( $matches[0] ) ) {
            return $html;
        }
        
        // Get exclusions
        $exclusions = $this->get_exclusions();
        
        // Process each CSS file
        foreach ( $matches[0] as $i => $tag ) {
            $attributes = $matches[1][$i];
            
            // Skip if excluded
            foreach ( $exclusions as $exclusion ) {
                if ( strpos( $attributes, $exclusion ) !== false ) {
                    continue 2;
                }
            }
            
            // Extract href
            if ( preg_match( '/href=["\']([^"\']+)["\']/i', $attributes, $href_match ) ) {
                $href = $href_match[1];
                
                // Get CSS content
                $response = wp_remote_get( $href );
                
                if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
                    continue;
                }
                
                $css = wp_remote_retrieve_body( $response );
                
                // Extract background images
                preg_match_all( '/background(-image)?\s*:\s*url\(["\']?([^"\'()]+)["\']?\)/i', $css, $bg_matches );
                
                if ( empty( $bg_matches[2] ) ) {
                    continue;
                }
                
                // Add background images to lazyload script
                foreach ( $bg_matches[2] as $bg_url ) {
                    // Skip if excluded
                    foreach ( $exclusions as $exclusion ) {
                        if ( strpos( $bg_url, $exclusion ) !== false ) {
                            continue 2;
                        }
                    }
                    
                    // Add to lazyload script
                    $this->add_bg_to_lazyload( $bg_url );
                }
            }
        }
        
        return $html;
    }

    /**
     * Add background image to lazyload script.
     *
     * @since    1.0.0
     * @param    string    $bg_url    The background image URL.
     */
    private function add_bg_to_lazyload( $bg_url ) {
        // This would be implemented in a real plugin to add the background image to a list
        // that would be processed by the lazyload script
    }

    /**
     * Get exclusions for LazyLoad CSS background images.
     *
     * @since    1.0.0
     * @return   array    The exclusions for LazyLoad CSS background images.
     */
    private function get_exclusions() {
        $exclusions = array();
        
        // Add default exclusions
        $exclusions[] = 'no-lazyload';
        $exclusions[] = 'skip-lazy';
        
        // Add user exclusions
        if ( ! empty( $this->settings['lazyload_css_bg_exclusions'] ) ) {
            $user_exclusions = explode( "\n", $this->settings['lazyload_css_bg_exclusions'] );
            $exclusions = array_merge( $exclusions, array_map( 'trim', $user_exclusions ) );
        }
        
        return array_unique( $exclusions );
    }

    /**
     * Add LazyLoad script to footer.
     *
     * @since    1.0.0
     */
    public function add_lazyload_script() {
        // Don't add script if user is logged in
        if ( is_user_logged_in() ) {
            return;
        }
        
        // Don't add script on admin pages
        if ( is_admin() ) {
            return;
        }
        
        // Add script
        $script = $this->get_lazyload_script();
        echo $script;
    }

    /**
     * Get LazyLoad script.
     *
     * @since    1.0.0
     * @return   string    The LazyLoad script.
     */
    private function get_lazyload_script() {
        $script = <<<EOT
<script>
(function() {
    'use strict';
    
    // Load CSS background images
    function loadCSSBgImages() {
        var elements = document.querySelectorAll('.redco-lazyload');
        
        if (!elements.length) {
            return;
        }
        
        var observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var element = entry.target;
                    var bg = element.getAttribute('data-bg');
                    
                    if (bg) {
                        element.style.backgroundImage = bg;
                        element.classList.remove('redco-lazyload');
                        element.removeAttribute('data-bg');
                        observer.unobserve(element);
                    }
                }
            });
        }, {
            rootMargin: '200px 0px'
        });
        
        elements.forEach(function(element) {
            observer.observe(element);
        });
    }
    
    // Check if IntersectionObserver is supported
    if ('IntersectionObserver' in window) {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadCSSBgImages);
        } else {
            loadCSSBgImages();
        }
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        var elements = document.querySelectorAll('.redco-lazyload');
        
        elements.forEach(function(element) {
            var bg = element.getAttribute('data-bg');
            
            if (bg) {
                element.style.backgroundImage = bg;
                element.classList.remove('redco-lazyload');
                element.removeAttribute('data-bg');
            }
        });
    }
})();
</script>
EOT;

        return $script;
    }
}
