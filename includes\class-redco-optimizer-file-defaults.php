<?php
/**
 * File Optimization Default Settings
 *
 * This file contains the default settings for the File Optimization module.
 *
 * @link       https://redco.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * Get default settings for file optimization.
 *
 * @since    1.0.0
 * @return   array    The default settings.
 */
function redco_get_file_optimization_defaults() {
    // Define default settings
    $defaults = array(
        // HTML settings
        'minify_html' => 0,
        'minify_html_comments' => 1,
        'minify_html_inline_css' => 1,
        'minify_html_inline_js' => 1,
        'minify_html_exclusions' => '',

        // CSS settings
        'minify_css' => 0,
        'combine_css' => 0,

        // JavaScript settings
        'minify_js' => 0,
        'combine_js' => 0,
        'defer_js' => 0,
        'defer_js_exclusions' => 'jquery.js',

        // Other settings
        'cache_lifetime' => 86400,
        'remove_unused_css' => 0,
        'css_safelist' => '',
        'delay_js' => 0,
        'delay_js_exclusions' => '',
        'delay_js_safe_mode' => 0,

        // Advanced settings
        'async_css' => 0,
        'async_css_exclusions' => '',
        'optimize_google_fonts' => 0,
        'self_host_google_fonts' => 0,
        'optimize_css_delivery' => 0,
        'critical_css' => '',
        'file_exclusions' => '',

        // Delay JS Exclusion Checkboxes
        // jQuery & Related
        'delay_js_exclude_jquery' => 1,
        'delay_js_exclude_jquery_migrate' => 1,
        'delay_js_exclude_jquery_core' => 1,

        // WP Core Scripts
        'delay_js_exclude_wp_embed' => 1,
        'delay_js_exclude_wp_core' => 1,
        'delay_js_exclude_comment_reply' => 0,

        // Analytics & Trackers
        'delay_js_exclude_analytics' => 0,
        'delay_js_exclude_gtag' => 0,
        'delay_js_exclude_gtm' => 0,
        'delay_js_exclude_fbevents' => 0,
        'delay_js_exclude_hotjar' => 0,
        'delay_js_exclude_clarity' => 0,

        // Ad Networks
        'delay_js_exclude_adsense' => 0,
        'delay_js_exclude_admanager' => 0,
        'delay_js_exclude_amazon' => 0,

        // Payment Processors
        'delay_js_exclude_stripe' => 0,
        'delay_js_exclude_paypal' => 0,
        'delay_js_exclude_square' => 0,
        'delay_js_exclude_authorize' => 0,
        'delay_js_exclude_recaptcha' => 0,

        // Other Services
        'delay_js_exclude_livechat' => 0,
        'delay_js_exclude_intercom' => 0,
        'delay_js_exclude_zopim' => 0,
        'delay_js_exclude_zendesk' => 0,

        // Form Handlers
        'delay_js_exclude_cf7' => 0,
        'delay_js_exclude_wpcf7' => 0,
        'delay_js_exclude_formidable' => 0,

        // Media Players
        'delay_js_exclude_mediaelement' => 0,
        'delay_js_exclude_vimeo' => 0,
        'delay_js_exclude_youtube' => 0,

        // Sliders & Carousels
        'delay_js_exclude_swiper' => 0,
        'delay_js_exclude_slider' => 0,
        'delay_js_exclude_carousel' => 0,
        'delay_js_exclude_revolution' => 0,

        // Theme frameworks
        'delay_js_exclude_elementor' => 0,
        'delay_js_exclude_avada' => 0,
        'delay_js_exclude_divi' => 0,
        'delay_js_exclude_astra' => 0,
        'delay_js_exclude_generatepress' => 0,
        'delay_js_exclude_oceanwp' => 0,
    );

    // Allow developers to filter the default settings
    return apply_filters('redco_file_optimization_defaults', $defaults);
}
