<?php
/**
 * The performance monitor module functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/performance-monitor
 */

/**
 * The performance monitor module functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for the performance monitor module.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/performance-monitor
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Performance_Monitor extends Redco_Optimizer_Module {

    /**
     * The start time for performance monitoring.
     *
     * @since    1.0.0
     * @access   private
     * @var      float    $start_time    The start time for performance monitoring.
     */
    private $start_time;

    /**
     * The database queries count at the start of the request.
     *
     * @since    1.0.0
     * @access   private
     * @var      int    $start_queries    The database queries count at the start of the request.
     */
    private $start_queries;

    /**
     * Register the hooks for this module.
     *
     * @since    1.0.0
     */
    protected function register_hooks() {
        // Add performance monitoring hooks
        $this->loader->add_action( 'init', $this, 'start_monitoring', 0 );
        $this->loader->add_action( 'wp_footer', $this, 'end_monitoring', 999 );
        $this->loader->add_action( 'admin_footer', $this, 'end_monitoring', 999 );

        // Add AJAX hooks for performance data
        $this->loader->add_action( 'wp_ajax_redco_optimizer_get_performance_data', $this, 'ajax_get_performance_data' );

        // Add scheduled performance check hook
        $this->loader->add_action( 'redco_optimizer_scheduled_performance_check', $this, 'scheduled_performance_check' );

        // Schedule the performance check event if it's not already scheduled
        if ( ! wp_next_scheduled( 'redco_optimizer_scheduled_performance_check' ) ) {
            wp_schedule_event( time(), 'hourly', 'redco_optimizer_scheduled_performance_check' );
        }
    }

    /**
     * Start monitoring performance.
     *
     * @since    1.0.0
     */
    public function start_monitoring() {
        // Record the start time
        $this->start_time = microtime( true );

        // Record the number of database queries at the start
        global $wpdb;
        $this->start_queries = $wpdb->num_queries;
    }

    /**
     * End monitoring performance and record the data.
     *
     * @since    1.0.0
     */
    public function end_monitoring() {
        // Skip if we didn't start monitoring
        if ( ! isset( $this->start_time ) ) {
            return;
        }

        // Calculate the page load time
        $end_time = microtime( true );
        $load_time = $end_time - $this->start_time;

        // Calculate the number of database queries
        global $wpdb;
        $queries = $wpdb->num_queries - $this->start_queries;

        // Get the memory usage
        $memory_usage = memory_get_peak_usage( true );

        // Record the performance data
        $this->record_performance_data( array(
            'load_time' => $load_time,
            'queries' => $queries,
            'memory_usage' => $memory_usage,
            'url' => isset( $_SERVER['REQUEST_URI'] ) ? $_SERVER['REQUEST_URI'] : '',
            'timestamp' => time(),
        ) );
    }

    /**
     * Record performance data.
     *
     * @since    1.0.0
     * @param    array    $data    The performance data to record.
     */
    private function record_performance_data( $data ) {
        // Get the existing performance data
        $performance_data = get_option( 'redco_optimizer_performance_data', array() );

        // Add the new data
        $performance_data[] = $data;

        // Limit the number of records to keep
        $max_records = 1000;
        if ( count( $performance_data ) > $max_records ) {
            $performance_data = array_slice( $performance_data, -$max_records );
        }

        // Update the option
        update_option( 'redco_optimizer_performance_data', $performance_data );
    }

    /**
     * Handle AJAX request to get performance data.
     *
     * @since    1.0.0
     */
    public function ajax_get_performance_data() {
        // Check nonce for security
        check_ajax_referer( 'redco_optimizer_nonce', 'nonce' );

        // Check if user has permission
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to access performance data.', 'redco-optimizer' ) ) );
        }

        // Get the performance data
        $performance_data = get_option( 'redco_optimizer_performance_data', array() );

        wp_send_json_success( array(
            'data' => $performance_data
        ) );
    }

    /**
     * Perform scheduled performance check.
     *
     * @since    1.0.0
     */
    public function scheduled_performance_check() {
        // Get the advanced settings
        $settings = get_option( 'redco_optimizer_advanced_settings', array() );

        // Check if scheduled performance check is enabled
        if ( ! isset( $settings['scheduled_performance_check'] ) || ! $settings['scheduled_performance_check'] ) {
            return;
        }

        // Get the performance data
        $performance_data = get_option( 'redco_optimizer_performance_data', array() );

        // If we have no data, return
        if ( empty( $performance_data ) ) {
            return;
        }

        // Calculate the average load time for the last 24 hours
        $last_24_hours = time() - ( 24 * 60 * 60 );
        $recent_data = array_filter( $performance_data, function( $item ) use ( $last_24_hours ) {
            return $item['timestamp'] >= $last_24_hours;
        } );

        if ( empty( $recent_data ) ) {
            return;
        }

        $load_times = array_column( $recent_data, 'load_time' );
        $avg_load_time = array_sum( $load_times ) / count( $load_times );

        // Check if the average load time exceeds the threshold
        $threshold = isset( $settings['performance_threshold'] ) ? $settings['performance_threshold'] : 2.0;

        if ( $avg_load_time > $threshold ) {
            // Send notification email
            $this->send_performance_notification( $avg_load_time, $threshold );
        }
    }

    /**
     * Send performance notification email.
     *
     * @since    1.0.0
     * @param    float    $avg_load_time    The average load time.
     * @param    float    $threshold        The threshold that was exceeded.
     */
    private function send_performance_notification( $avg_load_time, $threshold ) {
        // Get the admin email
        $admin_email = get_option( 'admin_email' );

        // Set up the email
        $subject = sprintf( __( '[%s] Performance Alert: Slow Page Load Times', 'redco-optimizer' ), get_bloginfo( 'name' ) );

        $message = sprintf(
            __( 'Hello,

This is an automated alert from Redco Optimizer on your website %1$s.

The average page load time over the last 24 hours was %2$.2f seconds, which exceeds your threshold of %3$.2f seconds.

You may want to check your website performance and make optimizations to improve load times.

Visit your WordPress dashboard to view detailed performance data and optimization recommendations:
%4$s

Regards,
Redco Optimizer', 'redco-optimizer' ),
            get_bloginfo( 'name' ),
            $avg_load_time,
            $threshold,
            admin_url( 'admin.php?page=redco-optimizer-modules' )
        );

        // Send the email
        wp_mail( $admin_email, $subject, $message );
    }

    /**
     * Get the module settings HTML.
     *
     * @since    1.0.0
     * @return   string    The module settings HTML.
     */
    public function get_settings_html() {
        $settings = get_option( 'redco_optimizer_advanced_settings', array() );

        // Performance settings
        $scheduled_performance_check = isset( $settings['scheduled_performance_check'] ) ? $settings['scheduled_performance_check'] : false;
        $performance_threshold = isset( $settings['performance_threshold'] ) ? $settings['performance_threshold'] : 2.0;

        ob_start();
        ?>
        <div class="redco-module-settings-section">
            <h3><?php esc_html_e( 'Performance Monitoring Settings', 'redco-optimizer' ); ?></h3>

            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="scheduled_performance_check" <?php checked( $scheduled_performance_check, true ); ?>>
                    <?php esc_html_e( 'Enable Performance Alerts', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Send email alerts when page load times exceed the threshold.', 'redco-optimizer' ); ?></p>
            </div>

            <div class="redco-settings-field">
                <label for="performance_threshold"><?php esc_html_e( 'Performance Threshold (seconds)', 'redco-optimizer' ); ?></label>
                <input type="number" id="performance_threshold" name="performance_threshold" value="<?php echo esc_attr( $performance_threshold ); ?>" min="0.1" step="0.1">
                <p class="description"><?php esc_html_e( 'Send alerts when the average page load time exceeds this threshold.', 'redco-optimizer' ); ?></p>
            </div>

            <div class="redco-settings-actions">
                <button type="button" class="redco-button" id="redco-view-performance"><?php esc_html_e( 'View Performance Data', 'redco-optimizer' ); ?></button>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
