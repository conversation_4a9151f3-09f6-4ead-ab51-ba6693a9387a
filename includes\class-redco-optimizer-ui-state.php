<?php
/**
 * UI State Handler
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * UI State Handler
 *
 * This class handles saving and retrieving UI state preferences to user meta.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Team
 */
class Redco_Optimizer_UI_State {

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_redco_save_ui_state', array($this, 'save_ui_state'));

        // Add UI state data to admin pages
        add_action('admin_enqueue_scripts', array($this, 'add_ui_state_data'), 20);
    }

    /**
     * Save UI state to user meta.
     *
     * @since    1.0.0
     */
    public function save_ui_state() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'redco-optimizer')));
        }

        // Check user capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get state type and data
        $state_type = isset($_POST['state_type']) ? sanitize_text_field($_POST['state_type']) : '';
        $state_data = isset($_POST['state_data']) ? sanitize_text_field($_POST['state_data']) : '';

        if (empty($state_type) || empty($state_data)) {
            wp_send_json_error(array('message' => __('Invalid state data.', 'redco-optimizer')));
        }

        // Get current user ID
        $user_id = get_current_user_id();

        // Save state to user meta
        $meta_key = 'redco_' . $state_type;
        update_user_meta($user_id, $meta_key, $state_data);

        wp_send_json_success(array('message' => __('UI state saved successfully.', 'redco-optimizer')));
    }

    /**
     * Add UI state data to admin pages.
     *
     * @since    1.0.0
     * @param    string    $hook_suffix    The current admin page.
     */
    public function add_ui_state_data($hook_suffix) {
        // Only add on Redco Optimizer admin pages
        if (strpos($hook_suffix, 'redco-optimizer') === false) {
            return;
        }

        // Get current user ID
        $user_id = get_current_user_id();

        // Get UI state data
        $expandable_states = get_user_meta($user_id, 'redco_expandable_sections', true);
        $active_tab = get_user_meta($user_id, 'redco_active_tab', true);
        $congratulations_closed = get_user_meta($user_id, 'redco_congratulations_closed', true);

        // Add data to redco_data object for admin script
        wp_localize_script('redco-optimizer-admin', 'redco_data', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_optimizer_nonce'),
            'expandable_states' => $expandable_states,
            'active_tab' => $active_tab,
            'congratulations_closed' => $congratulations_closed
        ));

        // Also add data to expandable sections script
        wp_localize_script('redco-optimizer-expandable-sections', 'redco_data', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_optimizer_nonce'),
            'expandable_states' => $expandable_states
        ));
    }

    /**
     * Get UI state for a specific type.
     *
     * @since    1.0.0
     * @param    string    $state_type    The state type.
     * @param    mixed     $default       The default value if state is not found.
     * @return   mixed                    The state value.
     */
    public static function get_ui_state($state_type, $default = '') {
        // Get current user ID
        $user_id = get_current_user_id();

        // Get state from user meta
        $meta_key = 'redco_' . $state_type;
        $state = get_user_meta($user_id, $meta_key, true);

        return !empty($state) ? $state : $default;
    }
}
