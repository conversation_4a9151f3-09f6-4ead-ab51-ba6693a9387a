/**
 * Redco Optimizer - Add-ons Tab Fix
 * Removes help documentation from the add-ons tab
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/js
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Redco Add-ons Tab Fix loaded');

        // Function to clean up help documentation from add-ons tab
        function cleanupHelpDocumentation() {
            // Check if we're on the add-ons tab
            if ($('#redco-addons-tab').is(':visible')) {
                console.log('Cleaning up help documentation from add-ons tab');
                
                // Remove any help documentation elements from the add-ons tab
                $('#redco-addons-tab .redco-help-container, #redco-addons-tab .redco-help-content-container, #redco-addons-tab .redco-help-content').remove();
                
                // Also remove any help documentation that might be outside the add-ons tab but visible
                if (!$('#redco-help-tab').is(':visible')) {
                    $('.redco-help-container, .redco-help-content-container, .redco-help-content').not('#redco-help-tab .redco-help-container, #redco-help-tab .redco-help-content-container, #redco-help-tab .redco-help-content').remove();
                }
            }
        }

        // Run cleanup on page load
        cleanupHelpDocumentation();

        // Also run cleanup when clicking on the add-ons tab
        $('.redco-nav-item[data-tab="redco-addons-tab"]').on('click', function() {
            // Run cleanup after a short delay to ensure the tab has loaded
            setTimeout(cleanupHelpDocumentation, 100);
            setTimeout(cleanupHelpDocumentation, 500);
        });

        // Run cleanup when switching tabs via JavaScript
        $(document).on('redco-tab-switched', function(e, tabId) {
            if (tabId === 'redco-addons-tab') {
                setTimeout(cleanupHelpDocumentation, 100);
                setTimeout(cleanupHelpDocumentation, 500);
            }
        });
    });

})(jQuery);
