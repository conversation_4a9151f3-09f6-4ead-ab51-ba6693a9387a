/**
 * Redco Optimizer - Compact Module Cards
 * Small, simple module cards that act as toggle buttons
 */

/* Module Grid */
.redco-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    grid-gap: 20px 16px; /* row-gap column-gap - using grid-gap for better compatibility */
    margin-top: 20px;
    padding: 0;
}

/* Module Card - Compact Design */
.redco-module-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.15s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    min-height: 50px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* Card States - Enhanced Visual Distinction */
.redco-module-card:hover {
    border-color: #00A66B;
    box-shadow: 0 2px 6px rgba(0, 166, 107, 0.15);
    transform: translateY(-1px);
}

/* Enabled State - Clear Green Styling */
.redco-module-card.redco-module-enabled {
    border-color: #00A66B !important;
    background: rgba(0, 166, 107, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 166, 107, 0.1) !important;
}

.redco-module-card.redco-module-enabled:hover {
    background: rgba(0, 166, 107, 0.08) !important;
    box-shadow: 0 3px 8px rgba(0, 166, 107, 0.2) !important;
}

/* Disabled State - Clear Gray Styling */
.redco-module-card.redco-module-disabled {
    border-color: #ddd !important;
    background: #f5f5f5 !important;
    opacity: 0.8 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.redco-module-card.redco-module-disabled:hover {
    opacity: 1 !important;
    background: #f9f9f9 !important;
    border-color: #bbb !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Premium Cards */
.redco-module-card.redco-module-premium {
    border-color: #ffa500;
    background: rgba(255, 165, 0, 0.02);
}

.redco-module-card.redco-module-premium:hover {
    border-color: #ff8c00;
    box-shadow: 0 2px 4px rgba(255, 165, 0, 0.1);
}

/* Module Icon - Enhanced Visibility */
.redco-module-icon {
    flex-shrink: 0;
    width: 36px;
    height: 36px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    background: rgba(0, 166, 107, 0.15);
    border-radius: 8px;
    transition: all 0.15s ease;
    border: 1px solid rgba(0, 166, 107, 0.2);
}

.redco-module-icon .dashicons {
    font-size: 18px !important;
    color: #00A66B !important;
    width: 18px !important;
    height: 18px !important;
    display: block !important;
    line-height: 1 !important;
}

/* Disabled State - Clear Visual Difference */
.redco-module-card.redco-module-disabled .redco-module-icon {
    background: rgba(0, 0, 0, 0.08) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.redco-module-card.redco-module-disabled .redco-module-icon .dashicons {
    color: #999 !important;
}

/* Premium State */
.redco-module-card.redco-module-premium .redco-module-icon {
    background: rgba(255, 165, 0, 0.15) !important;
    border: 1px solid rgba(255, 165, 0, 0.2) !important;
}

.redco-module-card.redco-module-premium .redco-module-icon .dashicons {
    color: #ffa500 !important;
}

/* Module Content */
.redco-module-content {
    flex: 1;
    min-width: 0;
}

.redco-module-title {
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 2px 0;
    color: #23282d;
    line-height: 1.2;
}

.redco-module-description {
    font-size: 11px;
    color: #666;
    margin: 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.redco-module-card.redco-module-disabled .redco-module-title {
    color: #999;
}

.redco-module-card.redco-module-disabled .redco-module-description {
    color: #aaa;
}

/* Premium Badge */
.redco-premium-badge {
    display: inline-block;
    background: #ffa500;
    color: #fff;
    font-size: 9px;
    font-weight: 600;
    padding: 1px 4px;
    border-radius: 8px;
    margin-top: 2px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Module Status Indicator - Enhanced Visibility */
.redco-module-status {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.redco-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.15s ease;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.redco-status-indicator.redco-status-enabled {
    background: #00A66B !important;
    border-color: #fff !important;
    box-shadow: 0 1px 3px rgba(0, 166, 107, 0.3) !important;
}

.redco-status-indicator.redco-status-disabled {
    background: #999 !important;
    border-color: #fff !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* Loading State */
.redco-module-card.redco-loading {
    pointer-events: none;
    opacity: 0.6;
}

.redco-module-card.redco-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #00A66B;
    border-radius: 50%;
    animation: redco-spin 1s linear infinite;
}

@keyframes redco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Animation */
.redco-module-card.redco-settings-saved {
    animation: redco-success-pulse 0.6s ease-out;
}

@keyframes redco-success-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); box-shadow: 0 4px 20px rgba(0, 166, 107, 0.3); }
    100% { transform: scale(1); }
}

/* Force proper grid spacing with important declarations */
.redco-modules-grid {
    grid-gap: 20px 16px !important; /* Force row and column spacing */
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-modules-grid {
        grid-template-columns: 1fr !important;
        grid-gap: 16px 14px !important; /* Force mobile spacing */
        padding: 0;
    }

    .redco-module-card {
        padding: 14px;
        min-height: 60px;
    }

    .redco-module-icon {
        width: 36px;
        height: 36px;
    }

    .redco-module-icon .dashicons {
        font-size: 18px;
        width: 18px;
        height: 18px;
    }

    .redco-module-title {
        font-size: 13px;
    }

    .redco-module-description {
        font-size: 11px;
    }
}

/* Override any existing module card styles */
.redco-modules-grid .redco-module-card {
    /* Force our compact design */
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    padding: 12px !important;
    min-height: 50px !important;
    border: 1px solid #ddd !important;
    border-radius: 6px !important;
    background: #fff !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04) !important;
    margin: 0 0 4px 0 !important; /* Add small bottom margin as fallback for row spacing */
    position: relative !important;
}

/* Remove any pseudo-elements that might interfere */
.redco-module-card::before,
.redco-module-card:before,
.redco-module-card::after,
.redco-module-card:after {
    display: none !important;
    content: none !important;
}

/* Ensure proper spacing and clean layout */
.redco-modules-grid .redco-module-card * {
    box-sizing: border-box;
}

.redco-modules-grid {
    box-sizing: border-box;
    width: 100%;
}

/* Remove old card styles that might conflict */
.redco-module-card .redco-module-click-overlay,
.redco-module-card .redco-module-footer,
.redco-module-card .redco-module-features,
.redco-module-card .redco-button-toggle,
.redco-module-card .redco-configure-module,
.redco-module-card .redco-module-header,
.redco-module-card .redco-module-title-wrapper,
.redco-module-card .redco-module-status-wrapper,
.redco-module-card .redco-module-footer-buttons {
    display: none !important;
}

/* Force our layout structure with maximum specificity */
.redco-modules-grid .redco-module-card .redco-module-icon {
    width: 36px !important;
    height: 36px !important;
    border-radius: 8px !important;
    background: rgba(0, 166, 107, 0.15) !important;
    border: 1px solid rgba(0, 166, 107, 0.2) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
}

.redco-modules-grid .redco-module-card .redco-module-icon .dashicons {
    font-size: 18px !important;
    color: #00A66B !important;
    width: 18px !important;
    height: 18px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-modules-grid .redco-module-card .redco-module-content {
    flex: 1 !important;
    min-width: 0 !important;
}

.redco-modules-grid .redco-module-card .redco-module-status {
    flex-shrink: 0 !important;
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}
