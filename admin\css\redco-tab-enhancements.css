/**
 * Redco Optimizer - Tab Enhancements
 * Improves the styling of tabs and tab content
 */

/* Tab Navigation Enhancements */
.redco-nav-item {
    position: relative;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.redco-nav-item:hover {
    background: linear-gradient(to right, rgba(0, 166, 107, 0.08), transparent);
    border-left: 4px solid rgba(0, 166, 107, 0.5);
}

.redco-nav-item.active {
    background: linear-gradient(to right, rgba(0, 166, 107, 0.12), transparent);
    border-left: 4px solid #00A66B;
}

.redco-nav-item::before {
    display: none; /* Remove the existing before pseudo-element */
}

/* Tab Icon Enhancements */
.redco-nav-icon {
    font-size: 24px !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.redco-nav-item:hover .redco-nav-icon {
    transform: scale(1.1);
    background-color: rgba(0, 166, 107, 0.12);
}

.redco-nav-item.active .redco-nav-icon {
    background-color: rgba(0, 166, 107, 0.15);
    transform: scale(1.1);
}

/* Tab Text Enhancements */
.redco-nav-text {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.redco-nav-description {
    font-size: 13px;
    color: #646970;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
    transition: all 0.3s ease;
    opacity: 0.85;
}

.redco-nav-item:hover .redco-nav-text {
    color: #00A66B;
}

.redco-nav-item:hover .redco-nav-description {
    opacity: 1;
}

.redco-nav-item.active .redco-nav-text {
    color: #00A66B;
}

.redco-nav-item.active .redco-nav-description {
    opacity: 1;
}

/* Tab Content Enhancements */
.redco-tab-content {
    padding: 30px 25px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Card Enhancements */
.redco-card {
    border: 1px solid #e2e4e7;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.redco-card:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    border-color: #d0d5dd;
}

/* Card Header Enhancements */
.redco-card-header {
    background: linear-gradient(to right, #f8f9fa, #ffffff);
    padding: 16px 20px;
    border-bottom: 1px solid #e2e4e7;
    position: relative;
}

.redco-card-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0; /* Changed from 50px to 0 to remove the green line */
    height: 0; /* Changed from 2px to 0 */
    background: transparent; /* Changed from gradient to transparent */
    opacity: 0;
}

.redco-card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
    display: flex;
    align-items: center;
}

/* Card Content Enhancements */
.redco-card-content {
    padding: 20px;
    background-color: #fff;
}

/* Form Field Enhancements */
.redco-form-row {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
}

.redco-form-label {
    flex-basis: 200px;
    padding-right: 20px;
    margin-bottom: 8px;
}

.redco-form-label label {
    font-weight: 600;
    font-size: 14px;
    color: #23282d;
}

.redco-form-field {
    flex: 1;
    min-width: 250px;
}

.redco-form-help {
    font-size: 12px;
    color: #646970;
    margin-top: 6px;
    line-height: 1.4;
    opacity: 0.9;
    padding-left: 0 !important;
}

/* Toggle Row Enhancements */
.redco-toggle-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.redco-toggle-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.redco-toggle-info {
    flex: 1;
    padding-right: 20px;
}

.redco-toggle-info h4 {
    margin: 0 0 8px 0;
    font-size: 15px;
    font-weight: 600;
    color: #23282d;
    display: flex;
    align-items: center;
}

.redco-toggle-info p {
    margin: 0;
    font-size: 13px;
    color: #646970;
    line-height: 1.5;
}

/* Button Enhancements */
.redco-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border: 1px solid transparent;
}

.redco-button-primary {
    background-color: #00A66B;
    color: #fff;
    border-color: #00A66B;
}

.redco-button-primary:hover {
    background-color: #008F5B;
    border-color: #008F5B;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.redco-button-secondary {
    background-color: #f8f9fa;
    color: #23282d;
    border-color: #d0d5dd;
}

.redco-button-secondary:hover {
    background-color: #f0f0f1;
    border-color: #c0c5d0;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Tab Header Enhancements */
.redco-tab-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.redco-tab-header h2 {
    margin: 0 0 10px 0;
    font-size: 20px;
    font-weight: 600;
    color: #23282d;
}

.redco-tab-header p {
    margin: 0;
    font-size: 14px;
    color: #646970;
    line-height: 1.5;
    max-width: 800px;
}
