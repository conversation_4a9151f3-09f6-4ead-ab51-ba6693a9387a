/**
 * Top Navigation JavaScript
 * Handles dropdown functionality and navigation interactions
 */

jQuery(document).ready(function($) {
    'use strict';

    // Define tab information for header updates
    const tabInfo = {
        'redco-dashboard-tab': {
            icon: 'dashicons-dashboard',
            title: 'Dashboard',
            description: 'Performance overview'
        },
        'redco-caching-tab': {
            icon: 'dashicons-superhero',
            title: 'Caching',
            description: 'Speed up page loading'
        },
        'redco-file-optimization-tab': {
            icon: 'dashicons-media-code',
            title: 'File Optimization',
            description: 'Minify & combine assets'
        },
        'redco-media-tab': {
            icon: 'dashicons-format-image',
            title: 'Media',
            description: 'Optimize images & lazy load'
        },
        'redco-preload-tab': {
            icon: 'dashicons-performance',
            title: 'Preload',
            description: 'Boost initial page speed'
        },
        'redco-database-tab': {
            icon: 'dashicons-database',
            title: 'Database',
            description: 'Clean & optimize DB'
        },
        'redco-cdn-tab': {
            icon: 'dashicons-admin-site',
            title: 'CDN',
            description: 'Global content delivery'
        },
        'redco-heartbeat-tab': {
            icon: 'dashicons-heart',
            title: 'Heartbeat',
            description: 'Reduce admin CPU usage'
        },
        'redco-site-health-inspector-tab': {
            icon: 'dashicons-shield',
            title: 'Site Health',
            description: 'Fix performance issues'
        },
        'redco-modules-tab': {
            icon: 'dashicons-admin-plugins',
            title: 'Modules',
            description: 'Enable/disable features'
        },
        'redco-tools-tab': {
            icon: 'dashicons-admin-tools',
            title: 'Tools',
            description: 'Backup & restore settings'
        },
        'redco-addons-tab': {
            icon: 'dashicons-admin-plugins',
            title: 'Add-Ons',
            description: 'Enhance optimization'
        },
        'redco-coming-soon-tab': {
            icon: 'dashicons-clock',
            title: 'Coming Soon',
            description: 'Exciting new features'
        },
        'redco-help-tab': {
            icon: 'dashicons-editor-help',
            title: 'Help',
            description: 'Documentation & support'
        }
    };

    // Function to update header based on active tab
    function updateHeader(tabId) {
        const info = tabInfo[tabId];
        if (info) {
            // Update icon
            $('#redco-header-icon-inner').removeClass().addClass('dashicons ' + info.icon);

            // Update title
            $('#redco-header-title').text(info.title);

            // Update description
            $('#redco-header-description').text(info.description);

            console.log('✅ Header updated for tab:', tabId, info);
        }
    }

    // Expose updateHeader function globally for use by main admin script
    window.updateHeaderFromTopNav = updateHeader;

    // Handle dropdown hover functionality
    $('.redco-nav-dropdown').hover(
        function() {
            // Mouse enter
            $(this).find('.redco-nav-dropdown-menu').stop(true, true).fadeIn(200);
        },
        function() {
            // Mouse leave
            $(this).find('.redco-nav-dropdown-menu').stop(true, true).fadeOut(200);
        }
    );

    // Handle navigation item clicks
    $('.redco-nav-item').on('click', function(e) {
        e.preventDefault();

        var tabId = $(this).data('tab');
        var url = $(this).data('url');

        // If it has a URL, navigate to it
        if (url) {
            window.open(url, '_blank');
            return;
        }

        // If it has a tab ID, switch to that tab
        if (tabId) {
            // Remove active class from all nav items
            $('.redco-nav-item').removeClass('active');

            // Add active class to clicked item
            $(this).addClass('active');

            // Update header
            updateHeader(tabId);

            // Use the existing switchTab function if available
            if (typeof window.switchTab === 'function') {
                window.switchTab(tabId);
            } else {
                // Fallback tab switching
                $('.redco-tab-content').hide();
                $('#' + tabId).show();
            }

            // Close any open dropdowns
            $('.redco-nav-dropdown-menu').fadeOut(200);
        }
    });

    // Handle dropdown toggle clicks (for mobile)
    $('.redco-nav-dropdown-toggle').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var $dropdown = $(this).closest('.redco-nav-dropdown');
        var $menu = $dropdown.find('.redco-nav-dropdown-menu');

        // Close other dropdowns
        $('.redco-nav-dropdown').not($dropdown).find('.redco-nav-dropdown-menu').fadeOut(200);

        // Toggle this dropdown
        $menu.fadeToggle(200);
    });

    // Close dropdowns when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.redco-nav-dropdown').length) {
            $('.redco-nav-dropdown-menu').fadeOut(200);
        }
    });

    // Handle form submission from save button
    $('.redco-nav-save .redco-button').on('click', function(e) {
        e.preventDefault();

        // Find the form and submit it
        var $form = $('#redco-optimizer-form');
        if ($form.length) {
            $form.submit();
        } else {
            // Fallback: trigger save via AJAX if form not found
            if (typeof window.saveSettings === 'function') {
                window.saveSettings();
            }
        }
    });

    // Update active navigation item based on current tab
    function updateActiveNavItem() {
        var $activeTab = $('.redco-tab-content:visible').first();
        if ($activeTab.length) {
            var activeTabId = $activeTab.attr('id');

            // Remove active class from all nav items
            $('.redco-nav-item').removeClass('active');

            // Add active class to corresponding nav item
            $('.redco-nav-item[data-tab="' + activeTabId + '"]').addClass('active');

            // Update header
            updateHeader(activeTabId);
        }
    }

    // Monitor for tab changes and update navigation
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                updateActiveNavItem();
            }
        });
    });

    // Observe all tab content elements for style changes
    $('.redco-tab-content').each(function() {
        observer.observe(this, { attributes: true, attributeFilter: ['style'] });
    });

    // Initial update
    updateActiveNavItem();

    // Handle responsive behavior
    function handleResponsive() {
        var windowWidth = $(window).width();

        if (windowWidth <= 768) {
            // Mobile behavior
            $('.redco-nav-dropdown').off('mouseenter mouseleave');
        } else {
            // Desktop behavior - re-enable hover
            $('.redco-nav-dropdown').hover(
                function() {
                    $(this).find('.redco-nav-dropdown-menu').stop(true, true).fadeIn(200);
                },
                function() {
                    $(this).find('.redco-nav-dropdown-menu').stop(true, true).fadeOut(200);
                }
            );
        }
    }

    // Handle window resize
    $(window).on('resize', handleResponsive);

    // Initial responsive setup
    handleResponsive();

    // Smooth scrolling for better UX
    $('.redco-nav-item').on('click', function() {
        $('html, body').animate({
            scrollTop: 0
        }, 300);
    });

    console.log('✅ Top Navigation initialized successfully');
});
