<?php
/**
 * Generate placeholder images for the help documentation
 * 
 * This is a standalone script that can be run from the command line
 * to generate placeholder images for the help documentation.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 */

// Check if GD library is available
if (!extension_loaded('gd')) {
    die("Error: GD library is not available. Please install or enable the GD extension.\n");
}

// Include the generator script
require_once 'generate-placeholder-images.php';

echo "Image generation complete!\n";
