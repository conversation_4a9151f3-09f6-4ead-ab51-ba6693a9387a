/**
 * Redco Optimizer Add-on Fixes
 *
 * This file contains specific fixes for add-on modals and settings
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Override the old modal system with our new one
        $(document).off('click', '.redco-addon-settings');

        // Use the new modal system for all add-on settings
        $(document).on('click', '.redco-addon-settings', function(e) {
            e.preventDefault();

            const $button = $(this);
            const addonId = $button.data('addon');
            const addonName = $button.closest('.redco-addon-card').find('.redco-addon-title').text().trim();

            // Open modal using the new system
            const modalTitle = addonName + ' Settings';

            // Create a new button with the redco-open-modal class
            const $newButton = $('<button>')
                .addClass('redco-open-modal')
                .attr('data-addon', addonId)
                .attr('data-modal-title', modalTitle);

            // Trigger click on the new button
            $newButton.appendTo('body').trigger('click').remove();
        });

        // Fix for Advanced Cache Preloader modal
        $(document).on('click', '.redco-open-modal[data-addon="advanced-cache-preloader"]', function() {
            console.log('Advanced Cache Preloader settings button clicked');

            // Wait for the modal to load
            setTimeout(function() {
                console.log('Applying Advanced Cache Preloader modal fixes');

                // Make sure all form elements are visible
                $('.redco-modal-content .redco-form-row, .redco-modal-content .redco-toggle-row').css({
                    'display': 'flex',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure all section headings are visible
                $('.redco-modal-content .redco-modal-section h3').css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure all toggle controls are visible
                $('.redco-modal-content .redco-toggle-control').css({
                    'display': 'flex',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure all form fields are visible
                $('.redco-modal-content .redco-form-field').css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure the modal footer is visible
                $('.redco-modal-footer').css({
                    'display': 'flex',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Initialize toggle functionality
                initAdvancedCachePreloaderToggles();

                // Log the visibility status
                console.log('Modal sections visible:', $('.redco-modal-content .redco-modal-section:visible').length);
                console.log('Form rows visible:', $('.redco-modal-content .redco-form-row:visible').length);
                console.log('Toggle rows visible:', $('.redco-modal-content .redco-toggle-row:visible').length);
            }, 500);
        });

        // Initialize Advanced Cache Preloader toggles
        function initAdvancedCachePreloaderToggles() {
            // Toggle sitemap URL field
            $('input[name="sitemap_based"]').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#sitemap-url-row').show();
                } else {
                    $('#sitemap-url-row').hide();
                }
            });

            // Toggle custom post types
            $('input[name="preload_custom_post_types"]').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#custom-post-types-row').show();
                } else {
                    $('#custom-post-types-row').hide();
                }
            });
        }
    });
})(jQuery);
