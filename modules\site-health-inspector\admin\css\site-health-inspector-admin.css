/**
 * Site Health Inspector Admin CSS
 *
 * @link       https://redco.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/site-health-inspector/admin/css
 */

/* Site Health Container */
.redco-site-health-container {
    position: relative;
    z-index: 1;
    margin-top: 0px;
}

/* Tab Header */
.redco-tab-header {
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

/* Fix for notification overlap */
#redco-site-health-inspector-tab .redco-tab-header {
    position: relative;
    z-index: 10;
}

/* Notification positioning */
.redco-site-health-notification {
    position: absolute;
    top: -30px;
    left: 0;
    right: 0;
    z-index: 5;
}

/* Site Health Summary */
.redco-site-health-summary {
    margin-bottom: 30px;
}

.redco-site-health-stats {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.redco-stat-item {
    flex: 1;
    min-width: 120px;
    padding: 15px;
    margin: 0 10px 10px 0;
    background-color: #f8f9fa;
    border-radius: 5px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.redco-stat-number {
    display: block;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.redco-stat-label {
    display: block;
    font-size: 14px;
    color: #666;
}

.redco-stat-total .redco-stat-number {
    color: #007bff;
}

.redco-stat-critical .redco-stat-number {
    color: #dc3545;
}

.redco-stat-warning .redco-stat-number {
    color: #ffc107;
}

.redco-stat-info .redco-stat-number {
    color: #17a2b8;
}

.redco-site-health-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.redco-last-scan {
    font-size: 12px;
    color: #666;
    margin-left: 10px;
}

/* Site Health Issues */
.redco-site-health-issues {
    margin-top: 20px;
}

.redco-category-card {
    margin-bottom: 20px;
}

.redco-category-card .redco-card-header h3 {
    display: flex;
    align-items: center;
}

.redco-category-card .redco-card-header .dashicons {
    margin-right: 8px;
    color: #00C67F;
}

.redco-issue-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #f8f9fa;
    border-radius: 50%;
    font-size: 12px;
    margin-left: 10px;
    color: #666;
}

.redco-issue-item {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 5px;
    background-color: #f8f9fa;
    border-left: 4px solid #ccc;
}

.redco-issue-item:last-child {
    margin-bottom: 0;
}

.redco-severity-critical {
    border-left-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.05);
}

.redco-severity-warning {
    border-left-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.05);
}

.redco-severity-info {
    border-left-color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.05);
}

.redco-issue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.redco-issue-header h4 {
    margin: 0;
    display: flex;
    align-items: center;
}

.redco-issue-header h4 .dashicons {
    margin-right: 8px;
}

.redco-severity-critical h4 .dashicons {
    color: #dc3545;
}

.redco-severity-warning h4 .dashicons {
    color: #ffc107;
}

.redco-severity-info h4 .dashicons {
    color: #17a2b8;
}

.redco-issue-severity {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    color: #fff;
    text-transform: uppercase;
}

.redco-issue-description {
    margin-top: 10px;
}

.redco-issue-description p {
    margin-top: 0;
}

.redco-issue-details {
    margin-top: 15px;
}

.redco-toggle-details {
    background: none;
    border: none;
    color: #00C67F;
    cursor: pointer;
    padding: 0;
    font-size: 13px;
    text-decoration: underline;
}

.redco-toggle-details:hover {
    color: #00A66B;
}

.redco-issue-details-content {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff;
    border-radius: 3px;
    border: 1px solid #eee;
}

.redco-detail-item {
    margin-bottom: 8px;
}

.redco-detail-item:last-child {
    margin-bottom: 0;
}

.redco-detail-item strong {
    margin-right: 5px;
}

.redco-detail-item ul {
    margin: 5px 0 5px 20px;
    padding: 0;
}

.redco-no-issues-message {
    padding: 30px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 5px;
    margin-top: 20px;
}

.redco-no-issues-message p {
    font-size: 16px;
    color: #666;
}

/* Spinning animation */
.redco-spin {
    animation: redco-spin 2s linear infinite;
}

@keyframes redco-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Severity filters */
.redco-severity-filters {
    display: flex;
    margin-bottom: 20px;
    gap: 10px;
}

.redco-severity-filter {
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
}

.redco-severity-filter.active {
    background-color: #00C67F;
    color: #fff;
    border-color: #00A66B;
}

.redco-severity-filter:hover {
    background-color: #eee;
}

.redco-severity-filter.active:hover {
    background-color: #00A66B;
}

/* Plugin Table Styles */
.redco-plugins-table-container {
    margin-top: 15px;
}

.redco-plugins-table-container h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.redco-plugins-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
}

.redco-plugins-table th,
.redco-plugins-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.redco-plugins-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.redco-plugins-table tr:last-child td {
    border-bottom: none;
}

.redco-plugins-table tr:hover td {
    background-color: #f9f9f9;
}

.redco-plugin-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.redco-plugin-actions .button {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    font-size: 12px;
}

.redco-plugin-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 3px;
}

.redco-update-plugin {
    background-color: #00C67F !important;
    color: #fff !important;
    border-color: #00A66B !important;
}

.redco-update-plugin:hover {
    background-color: #00A66B !important;
}

.redco-deactivate-plugin {
    background-color: #f8f9fa !important;
    color: #666 !important;
}

.redco-deactivate-plugin:hover {
    background-color: #eee !important;
}

.redco-delete-plugin {
    background-color: #dc3545 !important;
    color: #fff !important;
    border-color: #c82333 !important;
}

.redco-delete-plugin:hover {
    background-color: #c82333 !important;
}

.redco-delete-content {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    padding: 2px 8px;
    font-size: 12px;
    background-color: #dc3545 !important;
    color: #fff !important;
    border-color: #c82333 !important;
}

.redco-delete-content:hover {
    background-color: #c82333 !important;
}

.redco-delete-content .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 3px;
}

/* Spinning animation for loading indicators */
.redco-spin {
    animation: redco-spin 2s linear infinite;
}

@keyframes redco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* AJAX Notification Area */
#redco-ajax-notification {
    position: relative;
    z-index: 100;
    margin-bottom: 15px;
}

#redco-ajax-notification .notice {
    margin: 0;
    padding: 8px 12px;
}

/* Module Recommendation Styles */
.redco-module-recommendation {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background-color: #f0f6fc;
    border-left: 4px solid #2271b1;
    padding: 12px;
    margin-top: 15px;
}

.redco-module-recommendation strong {
    margin-right: 8px;
    color: #1d2327;
}

.redco-module-recommendation .button {
    margin-left: auto;
    display: inline-flex;
    align-items: center;
}

.redco-module-recommendation .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 5px;
}

@media screen and (max-width: 782px) {
    .redco-module-recommendation {
        flex-direction: column;
        align-items: flex-start;
    }

    .redco-module-recommendation .button {
        margin-left: 0;
        margin-top: 10px;
    }
}

/* JavaScript Files and Errors Tables */
.redco-js-files-container,
.redco-js-errors-container {
    margin-top: 15px;
}

.redco-js-files-container h4,
.redco-js-errors-container h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.redco-js-files-table,
.redco-js-errors-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
}

.redco-js-files-table th,
.redco-js-files-table td,
.redco-js-errors-table th,
.redco-js-errors-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.redco-js-files-table th,
.redco-js-errors-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.redco-js-files-table tr:last-child td,
.redco-js-errors-table tr:last-child td {
    border-bottom: none;
}

.redco-js-files-table tr:hover td,
.redco-js-errors-table tr:hover td {
    background-color: #f9f9f9;
}

.redco-file-path {
    position: relative;
    padding-right: 40px;
    word-break: break-all;
}

.redco-file-path .button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    padding: 0;
    width: 24px;
    height: 24px;
    line-height: 22px;
    text-align: center;
}

.redco-file-path .button .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    line-height: 22px;
    margin: 0;
}

.redco-js-errors-table td:first-child {
    color: #dc3545;
    font-family: monospace;
    font-size: 12px;
}
