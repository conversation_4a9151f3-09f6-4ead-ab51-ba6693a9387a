/**
 * JavaScript for Redco Optimizer Error Logs
 *
 * Handles the error logs functionality in the Tools tab.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/js
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Refresh logs
        $('.redco-refresh-logs').on('click', function() {
            const $button = $(this);
            const originalText = $button.html();

            // Show loading state
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> ' + redco_optimizer.refreshing_text);
            $button.prop('disabled', true);

            // Make AJAX request
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_refresh_error_logs',
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update log content
                        $('#redco-log-content').html(response.data.log_content);

                        // Update original log content for filtering
                        originalLogContent = response.data.log_content;

                        // Update log info
                        if (response.data.log_info) {
                            $('.redco-log-info p').html(response.data.log_info);
                        }

                        // Show success message
                        showNotification(response.data.message, 'success');

                        // Apply current filters
                        const level = $('#redco-log-level-filter').val();
                        const search = $('#redco-log-search').val();
                        if (level !== 'all' || search) {
                            filterLogs(level, search);
                        }
                    } else {
                        // Show error message
                        showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    // Show error message
                    showNotification(redco_optimizer.ajax_error, 'error');
                },
                complete: function() {
                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });

        // Clear logs
        $('.redco-clear-logs').on('click', function() {
            if (!confirm(redco_optimizer.confirm_clear_logs)) {
                return;
            }

            const $button = $(this);
            const originalText = $button.html();

            // Show loading state
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> ' + redco_optimizer.clearing_text);
            $button.prop('disabled', true);

            // Make AJAX request
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_clear_error_logs',
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update log content
                        $('#redco-log-content').html('');

                        // Reset original log content
                        originalLogContent = '';

                        // Show empty logs message
                        $('.redco-error-log-container').html(
                            '<div class="redco-empty-logs-message">' +
                            '<p>' + redco_optimizer.no_logs_message + '</p>' +
                            '</div>'
                        );

                        // Show success message
                        showNotification(response.data.message, 'success');
                    } else {
                        // Show error message
                        showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    // Show error message
                    showNotification(redco_optimizer.ajax_error, 'error');
                },
                complete: function() {
                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });

        // Export logs
        $('.redco-export-logs').on('click', function() {
            const $button = $(this);
            const originalText = $button.html();

            // Show loading state
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> ' + redco_optimizer.exporting_text);
            $button.prop('disabled', true);

            // Create the download URL with nonce
            const downloadUrl = redco_optimizer.admin_url + '?action=redco_download_error_logs&nonce=' + redco_optimizer.nonce;

            // Create a form to submit for download
            const $form = $('<form>', {
                method: 'GET',
                action: redco_optimizer.admin_url,
                target: '_blank'
            });

            // Add action and nonce as hidden fields
            $form.append($('<input>', {
                type: 'hidden',
                name: 'action',
                value: 'redco_download_error_logs'
            }));

            $form.append($('<input>', {
                type: 'hidden',
                name: 'nonce',
                value: redco_optimizer.nonce
            }));

            // Append form to body, submit it, and remove it
            $form.appendTo('body').submit().remove();

            // Show success message and restore button state
            setTimeout(function() {
                showNotification(redco_optimizer.export_success, 'success');

                // Restore button state
                $button.html(originalText);
                $button.prop('disabled', false);
            }, 1000);
        });

        // Email logs modal
        $('.redco-email-logs').on('click', function() {
            $('#redco-email-logs-modal').css({
                'display': 'flex',
                'visibility': 'visible',
                'opacity': '1'
            });
        });

        // Close email logs modal
        $('.redco-modal-close, .redco-modal-cancel').on('click', function() {
            $('#redco-email-logs-modal').css({
                'display': 'none',
                'visibility': 'hidden',
                'opacity': '0'
            });
        });

        // Send logs email
        $('.redco-send-logs-email').on('click', function() {
            const $button = $(this);
            const originalText = $button.html();

            // Get form data
            const recipient = $('#redco-email-recipient').val();
            const subject = $('#redco-email-subject').val();
            const message = $('#redco-email-message').val();

            // Validate email
            if (!isValidEmail(recipient)) {
                showNotification(redco_optimizer.invalid_email, 'error');
                return;
            }

            // Show loading state
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> ' + redco_optimizer.sending_text);
            $button.prop('disabled', true);

            // Make AJAX request
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_email_error_logs',
                    nonce: redco_optimizer.nonce,
                    recipient: recipient,
                    subject: subject,
                    message: message
                },
                success: function(response) {
                    if (response.success) {
                        // Close modal
                        $('#redco-email-logs-modal').css({
                            'display': 'none',
                            'visibility': 'hidden',
                            'opacity': '0'
                        });

                        // Show success message
                        showNotification(response.data.message, 'success');
                    } else {
                        // Show error message
                        showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    // Show error message
                    showNotification(redco_optimizer.ajax_error, 'error');
                },
                complete: function() {
                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });

        // Store original log content for filtering
        let originalLogContent = '';

        // Initialize original log content
        function initializeOriginalContent() {
            const $logContent = $('#redco-log-content');
            if ($logContent.length && !originalLogContent) {
                originalLogContent = $logContent.text();
            }
        }

        // Initialize on page load
        initializeOriginalContent();

        // Filter logs by level
        $('#redco-log-level-filter').on('change', function() {
            const level = $(this).val();
            const search = $('#redco-log-search').val();
            filterLogs(level, search);
        });

        // Search logs
        $('#redco-log-search').on('input', function() {
            const search = $(this).val();
            const level = $('#redco-log-level-filter').val();
            filterLogs(level, search);
        });

        // Reset filters
        $('#redco-reset-filters').on('click', function() {
            // Reset dropdown to "All Levels"
            $('#redco-log-level-filter').val('all');

            // Clear search input
            $('#redco-log-search').val('');

            // Reset to original content
            if (originalLogContent) {
                $('#redco-log-content').html(originalLogContent);
                highlightLogLevels();
            }
        });

        // Helper function to filter logs
        function filterLogs(level, search) {
            // Make sure we have the original content
            initializeOriginalContent();

            // If no original content, nothing to filter
            if (!originalLogContent) {
                return;
            }

            const $logContent = $('#redco-log-content');
            const logLines = originalLogContent.split('\n');
            let filteredLines = [];

            for (let i = 0; i < logLines.length; i++) {
                const line = logLines[i];

                // Skip empty lines
                if (!line.trim()) {
                    filteredLines.push(line);
                    continue;
                }

                // Filter by level - check for ERROR, WARNING, or INFO
                if (level !== 'all') {
                    // Extract the level from the log line
                    const levelMatch = line.match(/\|\s+(ERROR|WARNING|INFO)\s+\|/);
                    if (!levelMatch || levelMatch[1] !== level) {
                        continue;
                    }
                }

                // Filter by search
                if (search && !line.toLowerCase().includes(search.toLowerCase())) {
                    continue;
                }

                filteredLines.push(line);
            }

            // Update log content
            $logContent.html(filteredLines.join('\n'));

            // Highlight log levels
            highlightLogLevels();
        }

        // Helper function to highlight log levels
        function highlightLogLevels() {
            const $logContent = $('#redco-log-content');
            let html = $logContent.html();

            // Highlight ERROR - handle various spacing
            html = html.replace(/\|\s*ERROR\s*\|/g, '| <span class="error">ERROR</span> |');

            // Highlight WARNING - handle various spacing
            html = html.replace(/\|\s*WARNING\s*\|/g, '| <span class="warning">WARNING</span> |');

            // Highlight INFO - handle various spacing
            html = html.replace(/\|\s*INFO\s*\|/g, '| <span class="info">INFO</span> |');

            $logContent.html(html);
        }

        // Helper function to validate email
        function isValidEmail(email) {
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        }

        // Helper function to show notification
        function showNotification(message, type) {
            const $notification = $('<div class="redco-notification redco-notification-' + type + '">' +
                '<span class="redco-notification-icon dashicons dashicons-' + (type === 'success' ? 'yes' : 'warning') + '"></span>' +
                '<div class="redco-notification-content">' + message + '</div>' +
                '<button class="redco-notification-close dashicons dashicons-no-alt"></button>' +
                '</div>');

            // Add notification to the global notification area
            $('#redco-global-notification-area').append($notification);

            // Show notification
            setTimeout(function() {
                $notification.addClass('redco-notification-visible');
            }, 10);

            // Auto-hide notification after 5 seconds
            setTimeout(function() {
                $notification.removeClass('redco-notification-visible');

                // Remove notification after animation
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 5000);

            // Close notification on click
            $notification.find('.redco-notification-close').on('click', function() {
                $notification.removeClass('redco-notification-visible');

                // Remove notification after animation
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            });
        }

        // Initialize log highlighting
        highlightLogLevels();

        // Store original log content on page load
        initializeOriginalContent();
    });
})(jQuery);
