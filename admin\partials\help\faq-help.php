<?php
/**
 * Frequently Asked Questions Help Documentation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials/help
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="redco-help-section" id="faq-help">
    <div class="redco-help-header">
        <h2><?php esc_html_e('Frequently Asked Questions', 'redco-optimizer'); ?></h2>
        <p class="redco-help-description"><?php esc_html_e('Answers to common questions about Redco Optimizer and website performance optimization.', 'redco-optimizer'); ?></p>
    </div>

    <div class="redco-help-content">
        <div class="redco-help-subsection">
            <h4><?php esc_html_e('What is page caching and how does it work?', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Page caching creates static HTML versions of your dynamic WordPress pages. Here\'s how it works:', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('When a visitor first accesses a page, WordPress processes PHP, runs database queries, and generates HTML', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Redco Optimizer captures this HTML output and saves it as a static file', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('For subsequent visitors, this static HTML file is served directly, bypassing PHP processing and database queries', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('This significantly reduces server processing time and improves page load speed by 2-5x', 'redco-optimizer'); ?></li>
            </ol>
            <p><?php esc_html_e('Page caching is particularly effective for sites with high traffic or limited server resources. The cache is automatically refreshed when you update content or according to your cache lifespan settings.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Will optimization break my site?', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Redco Optimizer is designed to be safe and compatible with most WordPress setups. However, due to the wide variety of themes, plugins, and server configurations, there\'s always a small possibility of conflicts. Here are some best practices to avoid issues:', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('Test changes on a staging site first when possible', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable one optimization feature at a time and test your site thoroughly before enabling more', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Always have a recent backup available before making significant changes', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Use the exclusion options for minification and combination if you notice issues with specific files', 'redco-optimizer'); ?></li>
            </ul>
            <p><?php esc_html_e('If you do experience issues, simply disabling the problematic feature or clearing the cache will usually resolve them immediately.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('How do I know if the optimizations are working?', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('There are several ways to verify that Redco Optimizer is improving your site\'s performance:', 'redco-optimizer'); ?></p>
            <ol>
                <li><strong><?php esc_html_e('Performance Score', 'redco-optimizer'); ?></strong>: <?php esc_html_e('The performance score on the dashboard will increase as you implement optimizations.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('External Testing Tools', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Use tools like Google PageSpeed Insights, GTmetrix, or WebPageTest to measure improvements. Compare results before and after enabling optimizations.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Cache Files', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Check if cache files are being created in the wp-content/cache/redco-optimizer directory.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Load Time', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Measure your site\'s load time using browser developer tools before and after optimization.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Visual Comparison', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Your site should visibly load faster, especially on repeat visits.', 'redco-optimizer'); ?></li>
            </ol>
            <p><?php esc_html_e('For the most accurate results, test your site from different locations and devices, as performance can vary based on geographic location and connection speed.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('What\'s the difference between the free and premium versions?', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('The free version of Redco Optimizer includes essential optimization features that will significantly improve your site\'s performance:', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('Basic page caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Browser caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Basic image optimization', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('CSS and JavaScript minification', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Database cleanup', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Heartbeat control', 'redco-optimizer'); ?></li>
            </ul>
            <p><?php esc_html_e('The premium version includes additional advanced features:', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('Advanced caching options (separate mobile cache, user-specific cache)', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Scheduled cache preloading', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Advanced image optimization with WebP conversion', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Removal of unused CSS', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Delayed JavaScript execution', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Advanced database optimization tools', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Priority support', 'redco-optimizer'); ?></li>
            </ul>
            <p><?php esc_html_e('For most small to medium-sized websites, the free version provides sufficient optimization. The premium version is recommended for larger sites, e-commerce stores, or sites requiring maximum performance optimization.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('How do I clear the cache?', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('There are several ways to clear the Redco Optimizer cache:', 'redco-optimizer'); ?></p>
            <ol>
                <li><strong><?php esc_html_e('Dashboard Quick Action', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Click the "CLEAR" button next to "Cache files" in the Quick Actions section of the Dashboard.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Admin Bar', 'redco-optimizer'); ?></strong>: <?php esc_html_e('If enabled, you can click on the Redco Optimizer icon in the WordPress admin bar and select "Clear Cache".', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Caching Tab', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Go to the Caching tab and click the "Clear Cache" button.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Automatic Clearing', 'redco-optimizer'); ?></strong>: <?php esc_html_e('The cache is automatically cleared when you publish or update content, depending on your settings.', 'redco-optimizer'); ?></li>
            </ol>
            <p><?php esc_html_e('You should clear the cache after making significant changes to your site\'s design, structure, or content to ensure visitors see the latest version.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Is Redco Optimizer compatible with WooCommerce?', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Yes, Redco Optimizer is fully compatible with WooCommerce. The plugin automatically excludes dynamic WooCommerce pages like cart, checkout, and my account from caching to ensure proper functionality.', 'redco-optimizer'); ?></p>
            <p><?php esc_html_e('For optimal WooCommerce performance, we recommend:', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('Enabling page caching for product and category pages', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Optimizing product images', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enabling browser caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Using a CDN for global stores', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Regular database cleanup to remove old orders and sessions', 'redco-optimizer'); ?></li>
            </ul>
            <p><?php esc_html_e('These optimizations can significantly improve the shopping experience for your customers.', 'redco-optimizer'); ?></p>
        </div>
    </div>
</div>
