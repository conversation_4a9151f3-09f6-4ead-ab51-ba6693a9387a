<?php
/**
 * The base class for all Redco Optimizer add-ons.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * The base class for all Redco Optimizer add-ons.
 *
 * This class provides common functionality for all add-ons, including:
 * - Settings management
 * - Initialization
 * - Admin page rendering
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Addon {

    /**
     * The settings for this add-on.
     *
     * @since    1.0.0
     * @access   protected
     * @var      array    $settings    The settings for this add-on.
     */
    protected $settings;

    /**
     * The add-on slug.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $addon_slug    The add-on slug.
     */
    protected $addon_slug;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The add-on slug.
     */
    public function __construct($addon_slug) {
        $this->addon_slug = $addon_slug;
        $this->settings = $this->get_settings();
    }

    /**
     * Get add-on settings.
     *
     * @since    1.0.0
     * @return   array    The add-on settings.
     */
    public function get_settings() {
        $option_name = 'redco_' . str_replace('-', '_', $this->addon_slug) . '_settings';
        $settings = get_option($option_name, array());

        // Debug log
        error_log($this->addon_slug . ' - Retrieved settings: ' . print_r($settings, true));

        return $settings;
    }

    /**
     * Save add-on settings.
     *
     * @since    1.0.0
     * @param    array    $settings    The add-on settings.
     * @return   bool     True on success, false on failure.
     */
    public function save_settings($settings) {
        // Process checkbox fields
        foreach ($settings as $key => $value) {
            // Convert string '0' and '1' to integers for checkboxes
            if ($value === '0' || $value === '1') {
                $settings[$key] = (int) $value;
            }
        }

        // Save settings
        $option_name = 'redco_' . str_replace('-', '_', $this->addon_slug) . '_settings';
        $result = update_option($option_name, $settings);

        // Debug log
        error_log($this->addon_slug . ' - Settings saved: ' . print_r($settings, true));
        error_log($this->addon_slug . ' - Update result: ' . ($result ? 'true' : 'false'));

        // Update local settings
        if ($result) {
            $this->settings = $settings;
        }

        return $result;
    }

    /**
     * Save add-on settings via AJAX.
     *
     * @since    1.0.0
     * @param    array    $settings    The add-on settings.
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    public function save_settings_ajax($settings) {
        try {
            // Enhanced debugging
            error_log($this->addon_slug . ' - AJAX save settings called');
            error_log($this->addon_slug . ' - Raw settings before processing: ' . print_r($settings, true));

            // Process checkbox fields
            foreach ($settings as $key => $value) {
                // Convert string '0' and '1' to integers for checkboxes
                if ($value === '0' || $value === '1') {
                    $settings[$key] = (int) $value;
                    error_log($this->addon_slug . ' - Converted ' . $key . ' to integer: ' . $settings[$key]);
                }
            }

            error_log($this->addon_slug . ' - Processed settings: ' . print_r($settings, true));

            // Save settings
            $result = $this->save_settings($settings);

            // Verify the settings were saved correctly
            $saved_settings = $this->get_settings();

            // Compare original and saved settings to check for discrepancies
            foreach ($settings as $key => $value) {
                if (!isset($saved_settings[$key]) || $saved_settings[$key] !== $value) {
                    error_log($this->addon_slug . ' - WARNING: Setting mismatch for ' . $key .
                             '. Original: ' . (is_array($value) ? json_encode($value) : $value) .
                             ', Saved: ' . (isset($saved_settings[$key]) ?
                                           (is_array($saved_settings[$key]) ?
                                            json_encode($saved_settings[$key]) :
                                            $saved_settings[$key]) :
                                           'not set'));
                }
            }

            if ($result) {
                return true;
            } else {
                error_log($this->addon_slug . ' - Failed to save settings');
                return new WP_Error('save_failed', __('Failed to save settings.', 'redco-optimizer'));
            }
        } catch (Exception $e) {
            error_log($this->addon_slug . ' - Error saving settings: ' . $e->getMessage());
            return new WP_Error('save_failed', __('Failed to save settings: ', 'redco-optimizer') . $e->getMessage());
        }
    }

    /**
     * Check if add-on is enabled.
     *
     * @since    1.0.0
     * @return   bool    True if add-on is enabled, false otherwise.
     */
    public function is_enabled() {
        return isset($this->settings['enabled']) && $this->settings['enabled'];
    }

    /**
     * Check if user has premium access.
     *
     * @since    1.0.0
     * @return   bool    True if user has premium access, false otherwise.
     */
    protected function has_premium_access() {
        // Check if the user has premium access
        if (function_exists('redco_optimizer_has_premium_access')) {
            return redco_optimizer_has_premium_access();
        }

        return false;
    }
}
