<?php
/**
 * Provide a admin area view for the CDN tab
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get the CDN module
global $redco_optimizer;
$cdn_module = null;

if (isset($redco_optimizer->modules['cdn'])) {
    $cdn_module = $redco_optimizer->modules['cdn'];
}

// Check if the module is enabled
$is_enabled = false;
if ($cdn_module) {
    $modules = get_option('redco_optimizer_modules', array());
    $is_enabled = isset($modules['cdn']['enabled']) && $modules['cdn']['enabled'];
}

// Get premium status
$has_premium_access = false;
$license_status = get_option('redco_optimizer_license_status', '');
$license_key = get_option('redco_optimizer_license_key', '');
if ($license_status === 'valid' && !empty($license_key)) {
    $has_premium_access = true;
}
?>

<div id="redco-cdn-tab" class="redco-tab-content">
    <div class="redco-page-header">
        <div class="redco-page-header-content">
            <div class="redco-page-header-icon">
                <span class="dashicons dashicons-admin-site"></span>
            </div>
            <div class="redco-page-header-text">
                <h2><?php esc_html_e('CDN Integration', 'redco-optimizer'); ?></h2>
                <p><?php esc_html_e('Integrate with content delivery networks to speed up your website globally.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-tab-container">
        <?php if (!$is_enabled): ?>
            <div class="redco-notice redco-notice-warning">
                <p>
                    <span class="dashicons dashicons-warning"></span>
                    <?php esc_html_e('The CDN module is currently disabled. Enable it from the Modules tab to configure CDN settings.', 'redco-optimizer'); ?>
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=redco-modules-tab'); ?>" class="redco-button redco-button-small"><?php esc_html_e('Go to Modules', 'redco-optimizer'); ?></a>
                </p>
            </div>
        <?php endif; ?>

        <div class="redco-card redco-expandable-card redco-expandable-open">
            <div class="redco-card-header redco-expandable-header">
                <h3>
                    <span class="dashicons dashicons-admin-site"></span>
                    <?php esc_html_e('CDN Configuration', 'redco-optimizer'); ?>
                </h3>
                <span class="redco-expandable-toggle">
                    <span class="dashicons dashicons-arrow-up-alt2"></span>
                </span>
            </div>
            <div class="redco-card-content redco-expandable-content">
                <div class="redco-card-description">
                    <p><?php esc_html_e('Configure your CDN integration to serve static files from a global content delivery network.', 'redco-optimizer'); ?></p>
                </div>

                <?php if ($cdn_module): ?>
                    <?php echo $cdn_module->get_settings_html(); ?>
                <?php else: ?>
                    <div class="redco-notice redco-notice-error">
                        <p><?php esc_html_e('CDN module not found. Please reinstall the plugin or contact support.', 'redco-optimizer'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="redco-card redco-expandable-card">
            <div class="redco-card-header redco-expandable-header">
                <h3>
                    <span class="dashicons dashicons-info-outline"></span>
                    <?php esc_html_e('CDN Information', 'redco-optimizer'); ?>
                </h3>
                <span class="redco-expandable-toggle">
                    <span class="dashicons dashicons-arrow-down-alt2"></span>
                </span>
            </div>
            <div class="redco-card-content redco-expandable-content" style="display: none;">
                <div class="redco-card-description">
                    <p><?php esc_html_e('Learn more about CDN integration and how it can benefit your website.', 'redco-optimizer'); ?></p>
                </div>

                <div class="redco-info-section">
                    <h4><?php esc_html_e('What is a CDN?', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('A Content Delivery Network (CDN) is a network of servers distributed globally that delivers web content to users based on their geographic location. By serving content from servers closer to your visitors, a CDN reduces latency and improves page load times.', 'redco-optimizer'); ?></p>
                </div>

                <div class="redco-info-section">
                    <h4><?php esc_html_e('Benefits of Using a CDN', 'redco-optimizer'); ?></h4>
                    <ul class="redco-info-list">
                        <li><?php esc_html_e('Faster page load times for global visitors', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Reduced bandwidth costs for your origin server', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Improved website availability and redundancy', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Protection against traffic spikes and DDoS attacks', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Better SEO rankings due to improved site performance', 'redco-optimizer'); ?></li>
                    </ul>
                </div>

                <div class="redco-info-section">
                    <h4><?php esc_html_e('Supported CDN Providers', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Redco Optimizer supports integration with the following CDN providers:', 'redco-optimizer'); ?></p>
                    <ul class="redco-info-list">
                        <li><?php esc_html_e('Cloudflare', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Amazon CloudFront', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('StackPath', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Bunny CDN', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('KeyCDN', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Any CDN provider with custom domain support', 'redco-optimizer'); ?></li>
                    </ul>
                </div>
            </div>
        </div>

        <?php if (!$has_premium_access): ?>
        <div class="redco-card redco-premium-card">
            <div class="redco-card-header">
                <h3>
                    <span class="dashicons dashicons-star-filled"></span>
                    <?php esc_html_e('Premium CDN Features', 'redco-optimizer'); ?>
                </h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-card-description">
                    <p><?php esc_html_e('Upgrade to premium for advanced CDN features and integrations.', 'redco-optimizer'); ?></p>
                </div>

                <div class="redco-premium-features">
                    <div class="redco-premium-feature">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <div class="redco-premium-feature-text">
                            <h4><?php esc_html_e('Advanced CDN API Integration', 'redco-optimizer'); ?></h4>
                            <p><?php esc_html_e('Direct API integration with popular CDN providers for automated setup and management.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                    <div class="redco-premium-feature">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <div class="redco-premium-feature-text">
                            <h4><?php esc_html_e('CDN Analytics & Reporting', 'redco-optimizer'); ?></h4>
                            <p><?php esc_html_e('View detailed analytics on CDN usage, bandwidth, and performance improvements.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                    <div class="redco-premium-feature">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <div class="redco-premium-feature-text">
                            <h4><?php esc_html_e('Multi-CDN Support', 'redco-optimizer'); ?></h4>
                            <p><?php esc_html_e('Use multiple CDN providers simultaneously for optimal global performance.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                    <div class="redco-premium-feature">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <div class="redco-premium-feature-text">
                            <h4><?php esc_html_e('Automatic Cache Purging', 'redco-optimizer'); ?></h4>
                            <p><?php esc_html_e('Automatically purge CDN cache when content is updated on your site.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="redco-premium-cta">
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=redco-premium-tab'); ?>" class="redco-button redco-button-primary"><?php esc_html_e('Upgrade to Premium', 'redco-optimizer'); ?></a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
