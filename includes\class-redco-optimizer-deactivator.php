<?php
/**
 * Fired during plugin deactivation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Deactivator {

    /**
     * Deactivate the plugin.
     *
     * Clean up temporary files and perform necessary actions on deactivation.
     *
     * @since    1.0.0
     */
    public static function deactivate() {
        // Clean up cache files if they exist
        $cache_dir = WP_CONTENT_DIR . '/cache/redco-optimizer/';
        if ( file_exists( $cache_dir ) ) {
            self::delete_directory( $cache_dir );
        }
    }

    /**
     * Helper function to recursively delete a directory.
     *
     * @param string $dir Directory path to delete.
     * @return bool True on success, false on failure.
     */
    private static function delete_directory( $dir ) {
        if ( ! is_dir( $dir ) ) {
            return false;
        }

        $files = array_diff( scandir( $dir ), array( '.', '..' ) );
        foreach ( $files as $file ) {
            $path = $dir . '/' . $file;
            is_dir( $path ) ? self::delete_directory( $path ) : unlink( $path );
        }

        return rmdir( $dir );
    }
}
