<?php
/**
 * Generate placeholder images for the help documentation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    // Define ABSPATH for standalone execution
    define('ABSPATH', dirname(dirname(__FILE__)) . '/');
}

// List of image names to generate
$image_names = [
    'dashboard-overview',
    'caching-settings',
    'media-optimization',
    'file-optimization',
    'database-optimization',
    'dashboard-explained',
    'caching-overview',
    'page-caching',
    'browser-caching',
    'gzip-compression',
    'cache-exclusions',
    'performance-score',
    'database-schedule',
    'cdn-integration',
    'export-settings',
    'diagnostic-tools',
    'optimization-overview',
    'file-optimization-settings',
    'media-optimization-settings',
    'preload-settings',
    'heartbeat-settings'
];

// Directory to save images
$image_dir = dirname(__FILE__) . '/images/help/';

// Create directory if it doesn't exist
if (!file_exists($image_dir)) {
    mkdir($image_dir, 0755, true);
}

// Function to generate a placeholder image
function generate_placeholder_image($name, $dir) {
    // Image dimensions
    $width = 800;
    $height = 400;
    
    // Create image
    $image = imagecreatetruecolor($width, $height);
    
    // Colors
    $bg_color = imagecolorallocate($image, 240, 240, 241); // Light gray background
    $text_color = imagecolorallocate($image, 100, 100, 100); // Dark gray text
    $border_color = imagecolorallocate($image, 195, 196, 199); // Border color
    $accent_color = imagecolorallocate($image, 0, 166, 107); // Green accent color
    
    // Fill background
    imagefill($image, 0, 0, $bg_color);
    
    // Draw border
    imagerectangle($image, 0, 0, $width - 1, $height - 1, $border_color);
    
    // Draw accent line at top
    imagefilledrectangle($image, 0, 0, $width, 5, $accent_color);
    
    // Format the name for display
    $display_name = str_replace('-', ' ', $name);
    $display_name = ucwords($display_name);
    
    // Add text
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($display_name);
    $text_height = imagefontheight($font_size);
    $text_x = ($width - $text_width) / 2;
    $text_y = ($height - $text_height) / 2;
    
    // Draw text
    imagestring($image, $font_size, $text_x, $text_y - 20, $display_name, $text_color);
    imagestring($image, 3, $text_x, $text_y + 20, 'Redco Optimizer Help Documentation', $text_color);
    
    // Save image
    $filename = $dir . $name . '.jpg';
    imagejpeg($image, $filename, 90);
    
    // Free memory
    imagedestroy($image);
    
    return $filename;
}

// Generate images
$generated = [];
foreach ($image_names as $name) {
    $filename = generate_placeholder_image($name, $image_dir);
    $generated[] = $name;
}

// Output results
echo "Generated " . count($generated) . " placeholder images:\n";
foreach ($generated as $name) {
    echo "- {$name}.jpg\n";
}

echo "\nPlaceholder images have been generated in: {$image_dir}\n";
