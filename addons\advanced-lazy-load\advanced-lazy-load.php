<?php
/**
 * Advanced Lazy Load
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/advanced-lazy-load
 *
 * Addon Name: Advanced Lazy Load
 * Description: Enhanced lazy loading for images, iframes, and videos with advanced options and exclusions.
 * Version: 1.0.0
 * Author: Redco
 * Author URI: https://redco-optimizer.com
 * Premium: false
 * Has Settings: true
 * Icon: dashicons-images-alt2
 * Coming Soon: true
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Currently plugin version.
 */
define('REDCO_ADVANCED_LAZY_LOAD_VERSION', '1.0.0');

/**
 * The class responsible for defining all actions specific to this add-on.
 */
class Redco_Optimizer_Advanced_Lazy_Load {

    /**
     * The settings for this add-on.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for this add-on.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Load settings
        $this->settings = $this->get_settings();

        // Initialize the add-on
        add_action('init', array($this, 'init'));
    }

    /**
     * Initialize the add-on.
     *
     * @since    1.0.0
     */
    public function init() {
        // Register admin scripts
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));

        // Only proceed with frontend features if the add-on is enabled
        if (!isset($this->settings['enabled']) || !$this->settings['enabled']) {
            return;
        }

        // Register scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add lazy loading to content if enabled
        if (isset($this->settings['apply_to_content']) && $this->settings['apply_to_content']) {
            add_filter('the_content', array($this, 'add_lazy_loading_to_content'));
        }

        // Add lazy loading to post thumbnails if enabled
        if (isset($this->settings['apply_to_thumbnails']) && $this->settings['apply_to_thumbnails']) {
            add_filter('post_thumbnail_html', array($this, 'add_lazy_loading_to_thumbnail'));
        }

        // Add lazy loading to widgets if enabled
        if (isset($this->settings['apply_to_widgets']) && $this->settings['apply_to_widgets']) {
            add_filter('widget_text', array($this, 'add_lazy_loading_to_content'));
        }

        // Add lazy loading to avatars if enabled
        if (isset($this->settings['apply_to_avatars']) && $this->settings['apply_to_avatars']) {
            add_filter('get_avatar', array($this, 'add_lazy_loading_to_thumbnail'));
        }
    }

    /**
     * Enqueue admin scripts and styles.
     *
     * @since    1.0.0
     */
    public function admin_enqueue_scripts() {
        // Enqueue the modal fix script on all admin pages
        if (is_admin()) {
            wp_enqueue_script('redco-advanced-lazy-load-modal-fix', plugin_dir_url(__FILE__) . 'js/modal-fix.js', array('jquery'), REDCO_ADVANCED_LAZY_LOAD_VERSION, true);
        }
    }

    /**
     * Enqueue scripts and styles.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        // Only proceed if the add-on is enabled
        if (!isset($this->settings['enabled']) || !$this->settings['enabled']) {
            return;
        }

        // Enqueue the lazy load script
        wp_enqueue_script(
            'redco-advanced-lazy-load',
            plugin_dir_url(__FILE__) . 'js/advanced-lazy-load.js',
            array('jquery'),
            REDCO_ADVANCED_LAZY_LOAD_VERSION,
            true
        );

        // Parse exclusions
        $exclusions = array();
        if (isset($this->settings['exclusions']) && !empty($this->settings['exclusions'])) {
            $exclusions_raw = explode("\n", $this->settings['exclusions']);
            foreach ($exclusions_raw as $exclusion) {
                $exclusion = trim($exclusion);
                if (!empty($exclusion)) {
                    $exclusions[] = $exclusion;
                }
            }
        }

        // Pass settings to script
        wp_localize_script(
            'redco-advanced-lazy-load',
            'redcoAdvancedLazyLoad',
            array(
                // General settings
                'threshold' => isset($this->settings['threshold']) ? intval($this->settings['threshold']) : 200,
                'fadeIn' => isset($this->settings['fade_in']) ? (bool) $this->settings['fade_in'] : true,
                'fadeInDuration' => isset($this->settings['fade_in_duration']) ? intval($this->settings['fade_in_duration']) : 400,

                // Media types
                'lazyLoadImages' => isset($this->settings['lazy_load_images']) ? (bool) $this->settings['lazy_load_images'] : true,
                'lazyLoadIframes' => isset($this->settings['lazy_load_iframes']) ? (bool) $this->settings['lazy_load_iframes'] : true,
                'lazyLoadVideos' => isset($this->settings['lazy_load_videos']) ? (bool) $this->settings['lazy_load_videos'] : true,
                'lazyLoadBackgrounds' => isset($this->settings['lazy_load_backgrounds']) ? (bool) $this->settings['lazy_load_backgrounds'] : false,

                // Advanced options
                'exclusions' => $exclusions,
                'useNativeLazyLoading' => isset($this->settings['use_native_lazy_loading']) ? (bool) $this->settings['use_native_lazy_loading'] : true,
                'skipFirstImages' => isset($this->settings['skip_first_images']) ? (bool) $this->settings['skip_first_images'] : true,
                'skipFirstImagesCount' => isset($this->settings['skip_first_images_count']) ? intval($this->settings['skip_first_images_count']) : 3,
                'useLQIP' => isset($this->settings['use_lqip']) ? (bool) $this->settings['use_lqip'] : false,
            )
        );
    }

    /**
     * Add lazy loading to content.
     *
     * @since    1.0.0
     * @param    string    $content    The content.
     * @return   string    The modified content.
     */
    public function add_lazy_loading_to_content($content) {
        // Only proceed if the add-on is enabled
        if (!isset($this->settings['enabled']) || !$this->settings['enabled']) {
            return $content;
        }

        // Don't lazy load in admin or feeds
        if (is_admin() || is_feed()) {
            return $content;
        }

        // Don't lazy load AMP pages
        if (function_exists('is_amp_endpoint') && is_amp_endpoint()) {
            return $content;
        }

        // Replace img tags if enabled
        if (isset($this->settings['lazy_load_images']) && $this->settings['lazy_load_images']) {
            $content = preg_replace_callback('/<img([^>]+)>/i', array($this, 'replace_image'), $content);
        }

        // Replace iframe tags if enabled
        if (isset($this->settings['lazy_load_iframes']) && $this->settings['lazy_load_iframes']) {
            $content = preg_replace_callback('/<iframe([^>]+)>/i', array($this, 'replace_iframe'), $content);
        }

        // Replace video tags if enabled
        if (isset($this->settings['lazy_load_videos']) && $this->settings['lazy_load_videos']) {
            $content = preg_replace_callback('/<video([^>]+)>/i', array($this, 'replace_video'), $content);
        }

        // Replace background images if enabled
        if (isset($this->settings['lazy_load_backgrounds']) && $this->settings['lazy_load_backgrounds']) {
            $content = $this->add_lazy_loading_to_backgrounds($content);
        }

        return $content;
    }

    /**
     * Add lazy loading to post thumbnail.
     *
     * @since    1.0.0
     * @param    string    $html    The thumbnail HTML.
     * @return   string    The modified HTML.
     */
    public function add_lazy_loading_to_thumbnail($html) {
        // Only proceed if the add-on is enabled
        if (!isset($this->settings['enabled']) || !$this->settings['enabled']) {
            return $html;
        }

        // Don't lazy load in admin or feeds
        if (is_admin() || is_feed()) {
            return $html;
        }

        // Don't lazy load AMP pages
        if (function_exists('is_amp_endpoint') && is_amp_endpoint()) {
            return $html;
        }

        // Replace img tags if enabled
        if (isset($this->settings['lazy_load_images']) && $this->settings['lazy_load_images']) {
            $html = preg_replace_callback('/<img([^>]+)>/i', array($this, 'replace_image'), $html);
        }

        return $html;
    }

    /**
     * Add lazy loading to background images.
     *
     * @since    1.0.0
     * @param    string    $content    The content.
     * @return   string    The modified content.
     */
    public function add_lazy_loading_to_backgrounds($content) {
        // Find elements with inline style containing background-image
        $pattern = '/<([a-z]+)([^>]*?style=["\'][^"\']*?background-image\s*:\s*url\(["\']?(.*?)["\']?\)[^>]*?)>/i';

        return preg_replace_callback($pattern, array($this, 'replace_background'), $content);
    }

    /**
     * Replace image tag with lazy loading version.
     *
     * @since    1.0.0
     * @param    array    $matches    The regex matches.
     * @return   string    The modified HTML.
     */
    private function replace_image($matches) {
        $attr_string = $matches[1];
        static $image_counter = 0;

        // Skip if already has loading attribute
        if (strpos($attr_string, 'loading=') !== false) {
            return $matches[0];
        }

        // Skip if has data-no-lazy attribute
        if (strpos($attr_string, 'data-no-lazy') !== false) {
            return $matches[0];
        }

        // Check for exclusions
        if (isset($this->settings['exclusions']) && !empty($this->settings['exclusions'])) {
            $exclusions = explode("\n", $this->settings['exclusions']);
            foreach ($exclusions as $exclusion) {
                $exclusion = trim($exclusion);
                if (!empty($exclusion) && strpos($attr_string, $exclusion) !== false) {
                    return $matches[0];
                }
            }
        }

        // Skip first n images if enabled
        if (isset($this->settings['skip_first_images']) && $this->settings['skip_first_images']) {
            $skip_count = isset($this->settings['skip_first_images_count']) ? intval($this->settings['skip_first_images_count']) : 3;
            if ($image_counter < $skip_count) {
                $image_counter++;

                // Add native lazy loading if enabled
                if (isset($this->settings['use_native_lazy_loading']) && $this->settings['use_native_lazy_loading']) {
                    $attr_string .= ' loading="lazy"';
                }

                return '<img' . $attr_string . '>';
            }
        }

        // Extract src attribute
        if (preg_match('/src=[\'"](.*?)[\'"]/i', $attr_string, $src_matches)) {
            $src = $src_matches[1];

            // Replace src with data-src
            $attr_string = str_replace($src_matches[0], 'src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1 1\'%3E%3C/svg%3E" data-src="' . $src . '"', $attr_string);

            // Add lazy loading class
            if (preg_match('/class=[\'"](.*?)[\'"]/i', $attr_string, $class_matches)) {
                $classes = $class_matches[1];
                $attr_string = str_replace($class_matches[0], 'class="' . $classes . ' redco-lazy"', $attr_string);
            } else {
                $attr_string .= ' class="redco-lazy"';
            }

            // Add native lazy loading if enabled
            if (isset($this->settings['use_native_lazy_loading']) && $this->settings['use_native_lazy_loading']) {
                $attr_string .= ' loading="lazy"';
            }

            return '<img' . $attr_string . '>';
        }

        return $matches[0];
    }

    /**
     * Replace iframe tag with lazy loading version.
     *
     * @since    1.0.0
     * @param    array    $matches    The regex matches.
     * @return   string    The modified HTML.
     */
    private function replace_iframe($matches) {
        $attr_string = $matches[1];

        // Skip if already has loading attribute
        if (strpos($attr_string, 'loading=') !== false) {
            return $matches[0];
        }

        // Skip if has data-no-lazy attribute
        if (strpos($attr_string, 'data-no-lazy') !== false) {
            return $matches[0];
        }

        // Check for exclusions
        if (isset($this->settings['exclusions']) && !empty($this->settings['exclusions'])) {
            $exclusions = explode("\n", $this->settings['exclusions']);
            foreach ($exclusions as $exclusion) {
                $exclusion = trim($exclusion);
                if (!empty($exclusion) && strpos($attr_string, $exclusion) !== false) {
                    return $matches[0];
                }
            }
        }

        // Extract src attribute
        if (preg_match('/src=[\'"](.*?)[\'"]/i', $attr_string, $src_matches)) {
            $src = $src_matches[1];

            // Replace src with data-src
            $attr_string = str_replace($src_matches[0], 'src="about:blank" data-src="' . $src . '"', $attr_string);

            // Add lazy loading class
            if (preg_match('/class=[\'"](.*?)[\'"]/i', $attr_string, $class_matches)) {
                $classes = $class_matches[1];
                $attr_string = str_replace($class_matches[0], 'class="' . $classes . ' redco-lazy"', $attr_string);
            } else {
                $attr_string .= ' class="redco-lazy"';
            }

            // Add native lazy loading if enabled
            if (isset($this->settings['use_native_lazy_loading']) && $this->settings['use_native_lazy_loading']) {
                $attr_string .= ' loading="lazy"';
            }

            return '<iframe' . $attr_string . '></iframe>';
        }

        return $matches[0];
    }

    /**
     * Replace video tag with lazy loading version.
     *
     * @since    1.0.0
     * @param    array    $matches    The regex matches.
     * @return   string    The modified HTML.
     */
    private function replace_video($matches) {
        $attr_string = $matches[1];

        // Skip if already has loading attribute
        if (strpos($attr_string, 'loading=') !== false) {
            return $matches[0];
        }

        // Skip if has data-no-lazy attribute
        if (strpos($attr_string, 'data-no-lazy') !== false) {
            return $matches[0];
        }

        // Check for exclusions
        if (isset($this->settings['exclusions']) && !empty($this->settings['exclusions'])) {
            $exclusions = explode("\n", $this->settings['exclusions']);
            foreach ($exclusions as $exclusion) {
                $exclusion = trim($exclusion);
                if (!empty($exclusion) && strpos($attr_string, $exclusion) !== false) {
                    return $matches[0];
                }
            }
        }

        // Extract src attribute
        if (preg_match('/src=[\'"](.*?)[\'"]/i', $attr_string, $src_matches)) {
            $src = $src_matches[1];

            // Replace src with data-src
            $attr_string = str_replace($src_matches[0], 'data-src="' . $src . '"', $attr_string);

            // Add lazy loading class
            if (preg_match('/class=[\'"](.*?)[\'"]/i', $attr_string, $class_matches)) {
                $classes = $class_matches[1];
                $attr_string = str_replace($class_matches[0], 'class="' . $classes . ' redco-lazy"', $attr_string);
            } else {
                $attr_string .= ' class="redco-lazy"';
            }

            // Add preload="none" attribute to prevent automatic loading
            if (strpos($attr_string, 'preload=') === false) {
                $attr_string .= ' preload="none"';
            }

            return '<video' . $attr_string . '></video>';
        }

        return $matches[0];
    }

    /**
     * Replace background image with lazy loading version.
     *
     * @since    1.0.0
     * @param    array    $matches    The regex matches.
     * @return   string    The modified HTML.
     */
    private function replace_background($matches) {
        $tag = $matches[1];
        $attr_string = $matches[2];

        // Skip if has data-no-lazy attribute
        if (strpos($attr_string, 'data-no-lazy') !== false) {
            return $matches[0];
        }

        // Extract style attribute
        if (preg_match('/style=(["\'])(.*?)\1/i', $attr_string, $style_matches)) {
            $style = $style_matches[2];
            $quote = $style_matches[1];

            // Extract background-image URL
            if (preg_match('/background-image\s*:\s*url\(["\']?(.*?)["\']?\)/i', $style, $bg_matches)) {
                $bg_url = $bg_matches[1];

                // Replace background-image with data-bg
                $new_style = str_replace($bg_matches[0], '', $style);
                $new_attr_string = str_replace($style_matches[0], 'style=' . $quote . $new_style . $quote . ' data-bg="' . $bg_url . '"', $attr_string);

                // Add lazy loading class
                if (preg_match('/class=(["\'])(.*?)\1/i', $new_attr_string, $class_matches)) {
                    $classes = $class_matches[2];
                    $new_attr_string = str_replace($class_matches[0], 'class=' . $class_matches[1] . $classes . ' redco-lazy-bg' . $class_matches[1], $new_attr_string);
                } else {
                    $new_attr_string .= ' class="redco-lazy-bg"';
                }

                return '<' . $tag . $new_attr_string . '>';
            }
        }

        return $matches[0];
    }

    /**
     * Get add-on settings.
     *
     * @since    1.0.0
     * @return   array    The add-on settings.
     */
    public function get_settings() {
        $default_settings = array(
            'enabled' => false,
            'threshold' => 200,
            'fade_in' => true,
            'fade_in_duration' => 400,
            'lazy_load_images' => true,
            'lazy_load_iframes' => true,
            'lazy_load_videos' => true,
            'lazy_load_backgrounds' => false,
            'apply_to_content' => true,
            'apply_to_widgets' => true,
            'apply_to_thumbnails' => true,
            'apply_to_avatars' => true,
            'exclusions' => '',
            'use_native_lazy_loading' => true,
            'skip_first_images' => true,
            'skip_first_images_count' => 3,
            'use_lqip' => false
        );

        $settings = get_option('redco_advanced_lazy_load_settings', array());

        return wp_parse_args($settings, $default_settings);
    }

    /**
     * Save add-on settings.
     *
     * @since    1.0.0
     * @param    array    $settings    The add-on settings.
     * @return   bool     True on success, false on failure.
     */
    public function save_settings($settings) {
        // Sanitize settings
        $sanitized_settings = array();

        // Boolean settings
        $boolean_settings = array(
            'enabled', 'fade_in', 'lazy_load_images', 'lazy_load_iframes',
            'lazy_load_videos', 'lazy_load_backgrounds', 'apply_to_content',
            'apply_to_widgets', 'apply_to_thumbnails', 'apply_to_avatars',
            'use_native_lazy_loading', 'skip_first_images', 'use_lqip'
        );

        foreach ($boolean_settings as $setting) {
            $sanitized_settings[$setting] = isset($settings[$setting]) ? (bool) $settings[$setting] : false;
        }

        // Integer settings
        $sanitized_settings['threshold'] = isset($settings['threshold']) ? intval($settings['threshold']) : 200;
        $sanitized_settings['fade_in_duration'] = isset($settings['fade_in_duration']) ? intval($settings['fade_in_duration']) : 400;
        $sanitized_settings['skip_first_images_count'] = isset($settings['skip_first_images_count']) ? intval($settings['skip_first_images_count']) : 3;

        // Text settings
        $sanitized_settings['exclusions'] = isset($settings['exclusions']) ? sanitize_textarea_field($settings['exclusions']) : '';

        return update_option('redco_advanced_lazy_load_settings', $sanitized_settings);
    }
}

// Initialize the add-on
$redco_advanced_lazy_load = new Redco_Optimizer_Advanced_Lazy_Load();
