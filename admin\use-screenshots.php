<?php
/**
 * Use screenshots in the help documentation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    // Define ABSPATH for standalone execution
    define('ABSPATH', dirname(dirname(__FILE__)) . '/');
}

/**
 * This script updates the help documentation to use actual screenshots
 * instead of placeholders.
 */

// Define the path to the help documentation
$help_page_path = __DIR__ . '/redco-help-page.php';

// Check if the help page exists
if (!file_exists($help_page_path)) {
    echo "Error: Help page not found at $help_page_path\n";
    exit(1);
}

// Read the help page content
$help_page_content = file_get_contents($help_page_path);

// Define the screenshots to use
$screenshots = [
    'dashboard-overview' => [
        'src' => 'extracted/dashboard-overview.jpg',
        'alt' => 'Redco Optimizer Dashboard Overview',
        'caption' => 'The main dashboard of Redco Optimizer showing performance statistics and quick actions.'
    ],
    // Add more screenshots as needed
];

// Update the help page to use the actual screenshots
foreach ($screenshots as $name => $screenshot) {
    // Define the placeholder pattern
    $placeholder_pattern = '/<div class="redco-help-image-container">\s*<img src="[^"]*\/help\/' . $name . '\.jpg" alt="[^"]*"[^>]*>\s*(?:<p class="redco-help-image-caption">[^<]*<\/p>)?\s*<\/div>/s';
    
    // Define the replacement with the actual screenshot
    $replacement = '<div class="redco-help-image-container">
        <img src="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . \'admin/images/help/' . $screenshot['src'] . '\'); ?>" alt="' . $screenshot['alt'] . '" class="redco-help-image">
        <p class="redco-help-image-caption">' . $screenshot['caption'] . '</p>
    </div>';
    
    // Replace the placeholder with the actual screenshot
    $help_page_content = preg_replace($placeholder_pattern, $replacement, $help_page_content);
}

// Write the updated help page content
file_put_contents($help_page_path, $help_page_content);

echo "Help page updated to use actual screenshots.\n";

/**
 * Function to update the JavaScript to use actual screenshots
 */
function update_js_to_use_screenshots() {
    // Define the path to the help tab fix script
    $js_path = __DIR__ . '/js/redco-help-tab-fix.js';
    
    // Check if the script exists
    if (!file_exists($js_path)) {
        echo "Error: Help tab fix script not found at $js_path\n";
        return;
    }
    
    // Read the script content
    $js_content = file_get_contents($js_path);
    
    // Define the pattern to match the createPlaceholderImages function
    $pattern = '/function createPlaceholderImages\(\) \{.*?\}/s';
    
    // Define the replacement function that checks for actual images first
    $replacement = 'function createPlaceholderImages() {
        console.log("Creating placeholder images only if needed");
        
        // Process each image container
        $(\'.redco-help-image-container\').each(function() {
            var $container = $(this);
            var $img = $container.find(\'img\');
            
            if ($img.length > 0) {
                var imgSrc = $img.attr(\'src\');
                var imgAlt = $img.attr(\'alt\') || \'Redco Optimizer\';
                
                // Check if the image exists
                var img = new Image();
                img.onload = function() {
                    // Image exists, do nothing
                    console.log("Image exists: " + imgSrc);
                };
                img.onerror = function() {
                    // Image doesn\'t exist, create a placeholder
                    console.log("Creating placeholder for: " + imgSrc);
                    
                    // Extract image name from src
                    var imageName = imgSrc.split(\'/\').pop().replace(\'.jpg\', \'\');
                    
                    // Get appropriate dashicon
                    var dashicon = getDashiconForImage(imageName);
                    
                    // Create a placeholder div with better structure
                    var $placeholder = $(\'<div class="redco-help-placeholder"></div>\');
                    var $content = $(\'<div class="redco-help-placeholder-content"></div>\');
                    
                    // Add icon
                    $content.append(\'<span class="dashicons \' + dashicon + \'"></span>\');
                    
                    // Add title and subtitle
                    $content.append(\'<div class="redco-help-placeholder-title">\' + imgAlt + \'</div>\');
                    $content.append(\'<div class="redco-help-placeholder-subtitle">Placeholder for \' + imageName + \'</div>\');
                    
                    // Add content to placeholder
                    $placeholder.append($content);
                    
                    // Replace the image with the placeholder
                    $img.replaceWith($placeholder);
                    
                    // Add caption if it doesn\'t exist
                    if ($container.find(\'.redco-help-image-caption\').length === 0) {
                        $container.append(\'<p class="redco-help-image-caption">\' + imgAlt + \'</p>\');
                    }
                };
                img.src = imgSrc;
            }
        });
    }';
    
    // Replace the function
    $js_content = preg_replace($pattern, $replacement, $js_content);
    
    // Write the updated script content
    file_put_contents($js_path, $js_content);
    
    echo "Help tab fix script updated to use actual screenshots when available.\n";
}

// Update the JavaScript
update_js_to_use_screenshots();
