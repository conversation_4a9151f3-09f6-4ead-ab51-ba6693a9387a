<?php
/**
 * The public-facing functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/public
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the public-facing stylesheet and JavaScript.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/public
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Public {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        wp_enqueue_style( $this->plugin_name, plugin_dir_url( dirname( __FILE__ ) ) . 'public/css/redco-optimizer-public.css', array(), $this->version, 'all' );
    }

    /**
     * Register the JavaScript for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        wp_enqueue_script( $this->plugin_name, plugin_dir_url( dirname( __FILE__ ) ) . 'public/js/redco-optimizer-public.js', array( 'jquery' ), $this->version, false );

        // Get performance settings
        $performance_settings = get_option('redco_optimizer_advanced_settings', array());
        $good_threshold = isset($performance_settings['good_threshold']) ? $performance_settings['good_threshold'] : 1.0;
        $warn_threshold = isset($performance_settings['warn_threshold']) ? $performance_settings['warn_threshold'] : 2.0;

        // Localize the script with performance data
        wp_localize_script( $this->plugin_name, 'redco_optimizer_public', array(
            'show_performance_indicator' => isset($performance_settings['show_performance_indicator']) ? (bool)$performance_settings['show_performance_indicator'] : false,
            'enhanced_lazy_loading' => isset($performance_settings['enhanced_lazy_loading']) ? (bool)$performance_settings['enhanced_lazy_loading'] : false,
            'good_threshold' => $good_threshold,
            'warn_threshold' => $warn_threshold,
            'site_url' => get_site_url(),
            'home_url' => home_url(),
        ));

        // Check if heartbeat control is enabled
        $heartbeat_settings = $this->get_heartbeat_settings();
        if ($heartbeat_settings['control_heartbeat']) {
            // Enqueue the heartbeat script for frontend
            wp_enqueue_script( $this->plugin_name . '-heartbeat', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/redco-heartbeat.js', array( 'jquery', 'heartbeat' ), $this->version, true );

            // Localize the heartbeat settings
            wp_localize_script( $this->plugin_name . '-heartbeat', 'redco_heartbeat_settings', array(
                'backend' => 'default', // Not used in frontend
                'editor' => 'default',  // Not used in frontend
                'frontend' => $heartbeat_settings['heartbeat_behavior_frontend'],
            ) );
        }
    }

    /**
     * Get heartbeat settings.
     *
     * @since    1.0.0
     * @return   array    The heartbeat settings.
     */
    private function get_heartbeat_settings() {
        $settings = get_option( 'redco_optimizer_heartbeat_settings', array() );

        // Default settings
        $defaults = array(
            'control_heartbeat' => 0,
            'heartbeat_behavior_backend' => 'default',
            'heartbeat_behavior_editor' => 'default',
            'heartbeat_behavior_frontend' => 'disable',
        );

        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }
}
