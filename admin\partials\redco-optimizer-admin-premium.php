<?php
/**
 * Provide a admin area view for the plugin premium features
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="wrap redco-optimizer-wrap">
    <div class="redco-optimizer-header">
        <h1><?php esc_html_e( 'Premium Features', 'redco-optimizer' ); ?></h1>
        <span class="redco-version"><?php echo esc_html( 'v' . REDCO_OPTIMIZER_VERSION ); ?></span>
    </div>

    <div class="redco-optimizer-content">
        <div class="redco-premium-hero">
            <div class="redco-premium-hero-content">
                <h2><?php esc_html_e( 'Supercharge Your WordPress Site', 'redco-optimizer' ); ?></h2>
                <p><?php esc_html_e( 'Upgrade to Redco Optimizer Premium to unlock all features and get premium support. Take your website performance to the next level with our advanced optimization tools.', 'redco-optimizer' ); ?></p>
                <div class="redco-premium-hero-actions">
                    <a href="#pricing" class="redco-button redco-button-premium"><?php esc_html_e( 'View Pricing Plans', 'redco-optimizer' ); ?></a>
                    <a href="#features" class="redco-button redco-button-secondary"><?php esc_html_e( 'Explore Features', 'redco-optimizer' ); ?></a>
                </div>
            </div>
            <div class="redco-premium-hero-image">
                <div class="redco-premium-hero-badge">
                    <div class="redco-premium-hero-badge-inner">
                        <span class="redco-premium-hero-badge-text"><?php esc_html_e( 'PREMIUM', 'redco-optimizer' ); ?></span>
                        <span class="redco-premium-hero-badge-discount">50% OFF</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-premium-comparison">
            <h3><?php esc_html_e( 'Free vs Premium', 'redco-optimizer' ); ?></h3>
            <div class="redco-premium-comparison-table">
                <div class="redco-premium-comparison-header">
                    <div class="redco-premium-comparison-cell"></div>
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'Free', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><?php esc_html_e( 'Premium', 'redco-optimizer' ); ?></div>
                </div>
                <div class="redco-premium-comparison-row">
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'Basic Caching', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell"><span class="redco-premium-check"></span></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><span class="redco-premium-check"></span></div>
                </div>
                <div class="redco-premium-comparison-row">
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'Basic Image Optimization', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell"><span class="redco-premium-check"></span></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><span class="redco-premium-check"></span></div>
                </div>
                <div class="redco-premium-comparison-row">
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'Basic Database Cleanup', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell"><span class="redco-premium-check"></span></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><span class="redco-premium-check"></span></div>
                </div>
                <div class="redco-premium-comparison-row">
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'Performance Monitoring', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell"><span class="redco-premium-cross"></span></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><span class="redco-premium-check"></span></div>
                </div>
                <div class="redco-premium-comparison-row">
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'Advanced Caching', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell"><span class="redco-premium-cross"></span></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><span class="redco-premium-check"></span></div>
                </div>
                <div class="redco-premium-comparison-row">
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'WebP Conversion', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell"><span class="redco-premium-cross"></span></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><span class="redco-premium-check"></span></div>
                </div>
                <div class="redco-premium-comparison-row">
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'Scheduled Optimization', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell"><span class="redco-premium-cross"></span></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><span class="redco-premium-check"></span></div>
                </div>
                <div class="redco-premium-comparison-row">
                    <div class="redco-premium-comparison-cell"><?php esc_html_e( 'Priority Support', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-comparison-cell"><span class="redco-premium-cross"></span></div>
                    <div class="redco-premium-comparison-cell redco-premium-cell"><span class="redco-premium-check"></span></div>
                </div>
            </div>
        </div>

        <div id="features" class="redco-premium-features">
            <h3><?php esc_html_e( 'Premium Features', 'redco-optimizer' ); ?></h3>

            <div class="redco-premium-feature">
                <div class="redco-premium-feature-header">
                    <?php esc_html_e( 'Performance Monitoring', 'redco-optimizer' ); ?>
                </div>
                <div class="redco-premium-feature-content">
                    <p><?php esc_html_e( 'Monitor your site performance and get detailed insights to improve it.', 'redco-optimizer' ); ?></p>
                    <ul>
                        <li><?php esc_html_e( 'Real-time performance monitoring', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Detailed performance reports', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Performance history tracking', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Email notifications for performance issues', 'redco-optimizer' ); ?></li>
                    </ul>
                </div>
            </div>

            <div class="redco-premium-feature">
                <div class="redco-premium-feature-header">
                    <?php esc_html_e( 'Advanced Caching', 'redco-optimizer' ); ?>
                </div>
                <div class="redco-premium-feature-content">
                    <p><?php esc_html_e( 'Take your caching to the next level with advanced caching features.', 'redco-optimizer' ); ?></p>
                    <ul>
                        <li><?php esc_html_e( 'Browser caching', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Object caching', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Database caching', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'CDN integration', 'redco-optimizer' ); ?></li>
                    </ul>
                </div>
            </div>

            <div class="redco-premium-feature">
                <div class="redco-premium-feature-header">
                    <?php esc_html_e( 'Image Optimization Pro', 'redco-optimizer' ); ?>
                </div>
                <div class="redco-premium-feature-content">
                    <p><?php esc_html_e( 'Advanced image optimization features to reduce image size without losing quality.', 'redco-optimizer' ); ?></p>
                    <ul>
                        <li><?php esc_html_e( 'Lossless image compression', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'WebP conversion', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Automatic image resizing', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Bulk optimization', 'redco-optimizer' ); ?></li>
                    </ul>
                </div>
            </div>

            <div class="redco-premium-feature">
                <div class="redco-premium-feature-header">
                    <?php esc_html_e( 'Database Optimizer Pro', 'redco-optimizer' ); ?>
                </div>
                <div class="redco-premium-feature-content">
                    <p><?php esc_html_e( 'Advanced database optimization features to keep your database clean and fast.', 'redco-optimizer' ); ?></p>
                    <ul>
                        <li><?php esc_html_e( 'Scheduled database cleanup', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Database optimization', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Table optimization', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Database backup', 'redco-optimizer' ); ?></li>
                    </ul>
                </div>
            </div>

            <div class="redco-premium-feature">
                <div class="redco-premium-feature-header">
                    <?php esc_html_e( 'Premium Support', 'redco-optimizer' ); ?>
                </div>
                <div class="redco-premium-feature-content">
                    <p><?php esc_html_e( 'Get priority support from our team of experts.', 'redco-optimizer' ); ?></p>
                    <ul>
                        <li><?php esc_html_e( 'Priority support', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Dedicated support team', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Faster response times', 'redco-optimizer' ); ?></li>
                        <li><?php esc_html_e( 'Advanced troubleshooting', 'redco-optimizer' ); ?></li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="pricing" class="redco-premium-pricing">
            <h3><?php esc_html_e( 'Pricing Plans', 'redco-optimizer' ); ?></h3>

            <div class="redco-premium-pricing-plans">
                <div class="redco-premium-pricing-plan">
                    <div class="redco-premium-pricing-header">
                        <h4><?php esc_html_e( 'Personal', 'redco-optimizer' ); ?></h4>
                        <div class="redco-premium-pricing-price">
                            <span class="redco-premium-pricing-amount">$49</span>
                            <span class="redco-premium-pricing-period">/year</span>
                        </div>
                        <div class="redco-premium-pricing-sites">
                            <?php esc_html_e( '1 Website', 'redco-optimizer' ); ?>
                        </div>
                    </div>
                    <div class="redco-premium-pricing-features">
                        <ul>
                            <li><?php esc_html_e( 'All Premium Features', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Performance Monitoring', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Advanced Caching', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Image Optimization Pro', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Database Optimizer Pro', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Premium Support', 'redco-optimizer' ); ?></li>
                        </ul>
                    </div>
                    <div class="redco-premium-pricing-action">
                        <a href="#" class="redco-button redco-button-premium"><?php esc_html_e( 'Get Started', 'redco-optimizer' ); ?></a>
                    </div>
                </div>

                <div class="redco-premium-pricing-plan redco-premium-pricing-plan-featured">
                    <div class="redco-premium-pricing-badge"><?php esc_html_e( 'Most Popular', 'redco-optimizer' ); ?></div>
                    <div class="redco-premium-pricing-header">
                        <h4><?php esc_html_e( 'Business', 'redco-optimizer' ); ?></h4>
                        <div class="redco-premium-pricing-price">
                            <span class="redco-premium-pricing-amount">$99</span>
                            <span class="redco-premium-pricing-period">/year</span>
                        </div>
                        <div class="redco-premium-pricing-sites">
                            <?php esc_html_e( '5 Websites', 'redco-optimizer' ); ?>
                        </div>
                    </div>
                    <div class="redco-premium-pricing-features">
                        <ul>
                            <li><?php esc_html_e( 'All Premium Features', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Performance Monitoring', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Advanced Caching', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Image Optimization Pro', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Database Optimizer Pro', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Priority Support', 'redco-optimizer' ); ?></li>
                        </ul>
                    </div>
                    <div class="redco-premium-pricing-action">
                        <a href="#" class="redco-button redco-button-premium"><?php esc_html_e( 'Get Started', 'redco-optimizer' ); ?></a>
                    </div>
                </div>

                <div class="redco-premium-pricing-plan">
                    <div class="redco-premium-pricing-header">
                        <h4><?php esc_html_e( 'Agency', 'redco-optimizer' ); ?></h4>
                        <div class="redco-premium-pricing-price">
                            <span class="redco-premium-pricing-amount">$199</span>
                            <span class="redco-premium-pricing-period">/year</span>
                        </div>
                        <div class="redco-premium-pricing-sites">
                            <?php esc_html_e( 'Unlimited Websites', 'redco-optimizer' ); ?>
                        </div>
                    </div>
                    <div class="redco-premium-pricing-features">
                        <ul>
                            <li><?php esc_html_e( 'All Premium Features', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Performance Monitoring', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Advanced Caching', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Image Optimization Pro', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'Database Optimizer Pro', 'redco-optimizer' ); ?></li>
                            <li><?php esc_html_e( 'VIP Support', 'redco-optimizer' ); ?></li>
                        </ul>
                    </div>
                    <div class="redco-premium-pricing-action">
                        <a href="#" class="redco-button redco-button-premium"><?php esc_html_e( 'Get Started', 'redco-optimizer' ); ?></a>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-premium-cta">
            <h2><?php esc_html_e( 'Ready to Supercharge Your WordPress Site?', 'redco-optimizer' ); ?></h2>
            <p><?php esc_html_e( 'Get access to all premium features and take your site optimization to the next level. Join thousands of happy customers who have improved their website performance with Redco Optimizer Premium.', 'redco-optimizer' ); ?></p>
            <a href="#" class="redco-button redco-button-premium"><?php esc_html_e( 'Upgrade to Premium', 'redco-optimizer' ); ?></a>
            <div class="redco-premium-guarantee">
                <span class="redco-premium-guarantee-icon"></span>
                <span class="redco-premium-guarantee-text"><?php esc_html_e( '30-Day Money-Back Guarantee', 'redco-optimizer' ); ?></span>
            </div>
        </div>
    </div>
</div>
