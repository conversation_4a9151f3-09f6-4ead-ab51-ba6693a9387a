/**
 * All of the code for your admin-facing JavaScript source
 * should reside in this file.
 *
 * Version: 1.0.1 - Updated to fix caching issues
 */

// Global notification functions
var showNotification = function(type, message, options = {}) {
    // Use RedcoNotification if available
    if (typeof RedcoNotification !== 'undefined') {
        RedcoNotification.show(type, message, options.duration || 4000);
        return;
    }

    // Create notification container if it doesn't exist
    if (jQuery('.redco-notifications-container').length === 0) {
        jQuery('body').append('<div class="redco-notifications-container"></div>');
    }

    // Get notification title
    const title = options.title || (type === 'success' ? 'Success' : 'Notice');

    // Create notification HTML
    const icon = type === 'success'
        ? '<span class="dashicons dashicons-yes-alt"></span>'
        : (type === 'error' ? '<span class="dashicons dashicons-warning"></span>' : '<span class="dashicons dashicons-info"></span>');

    const notificationId = 'redco-notification-' + Date.now();
    const notificationHtml = `
        <div id="${notificationId}" class="redco-notification redco-notification-${type}">
            <div class="redco-notification-header">
                ${icon}
                <h3>${title}</h3>
                <button type="button" class="redco-notification-close">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
            <div class="redco-notification-content">
                <p>${message}</p>
            </div>
        </div>
    `;

    // Add notification to container
    jQuery('.redco-notifications-container').append(notificationHtml);

    // Show notification with animation
    setTimeout(function() {
        jQuery('#' + notificationId).addClass('redco-notification-show');
    }, 10);

    // Auto-hide notification after duration
    const duration = options.duration || 5000;
    if (duration > 0) {
        setTimeout(function() {
            jQuery('#' + notificationId).removeClass('redco-notification-show');
            setTimeout(function() {
                jQuery('#' + notificationId).remove();
            }, 300);
        }, duration);
    }

    // Close notification on click
    jQuery('#' + notificationId + ' .redco-notification-close').on('click', function() {
        jQuery('#' + notificationId).removeClass('redco-notification-show');
        setTimeout(function() {
            jQuery('#' + notificationId).remove();
        }, 300);
    });
};

var getNotificationTitle = function(type) {
    return type === 'success' ? 'Success' : (type === 'error' ? 'Error' : 'Notice');
};

var closeNotification = function(notificationId) {
    jQuery('#' + notificationId).removeClass('redco-notification-show');
    setTimeout(function() {
        jQuery('#' + notificationId).remove();
    }, 300);
};

(function( $ ) {
    'use strict';

    // Add keyboard event listener to close modals with Escape key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });

    $(document).ready(function() {
        // Hide all modals on page load - use both CSS and inline styles to ensure they're hidden
        $('.redco-modal-overlay').css({
            'display': 'none',
            'visibility': 'hidden',
            'opacity': '0'
        }).removeClass('active');

        // Check for notification cookie
        checkForNotificationCookie();

        // Make sure the Tools and Add-Ons tabs are visible (non-module tabs)
        $('.redco-nav-item[data-tab="redco-tools-tab"]').removeClass('redco-module-tab-disabled').show();
        $('.redco-nav-item[data-tab="redco-addons-tab"]').removeClass('redco-module-tab-disabled').show();
        // Heartbeat and Site Health Inspector tab visibility is now controlled by module status

        // Debug: Log which tabs exist
        console.log('Tab debugging:');
        console.log('Tools tab exists:', $('.redco-nav-item[data-tab="redco-tools-tab"]').length);
        console.log('Add-ons tab exists:', $('.redco-nav-item[data-tab="redco-addons-tab"]').length);
        console.log('Heartbeat tab exists:', $('.redco-nav-item[data-tab="redco-heartbeat-tab"]').length);
        console.log('Site Health tab exists:', $('.redco-nav-item[data-tab="redco-site-health-inspector-tab"]').length);
        console.log('Tools tab content exists:', $('#redco-tools-tab').length);
        console.log('Add-ons tab content exists:', $('#redco-addons-tab').length);
        console.log('Heartbeat tab content exists:', $('#redco-heartbeat-tab').length);
        console.log('Site Health tab content exists:', $('#redco-site-health-inspector-tab').length);

        // Initialize tab visibility on page load
        initializeTabVisibility();

        // Ensure premium badges are visible
        ensurePremiumBadgesVisible();

        // Ensure add-on dashicons are visible
        ensureAddOnDashiconsVisible();

        // Check if congratulations message should be hidden
        checkCongratulationsMessage();

        // Initialize Scheduled Cleanup Frequency dropdown state
        initScheduledCleanupFrequency();

        // Standard checkboxes don't need color management

        // Make sure checkboxes are not disabled on page load
        $('input[type="checkbox"]:not(.redco-premium-feature)').prop('disabled', false);

        // Reset all toggles to ensure they're properly enabled
        resetAllToggles();

        // Set up a periodic check to ensure toggles remain enabled
        setInterval(function() {
            resetAllToggles();
        }, 1000);

        // Toggle switch handlers removed - using standard checkboxes now

        // Ensure Custom Exclusions is hidden when Delay JavaScript is off
        ensureCustomExclusionsVisibility();

        // Ensure premium sections are properly collapsed
        ensurePremiumSectionsCollapsed();

        // Sidebar navigation - use the unified switchTab function
        $('.redco-nav-item').on('click', function() {
            const tabId = $(this).data('tab');
            const tabUrl = $(this).data('url');

            console.log('=== TAB CLICK DEBUG ===');
            console.log('Tab clicked:', tabId);
            console.log('Tab URL:', tabUrl);
            console.log('Tab element:', this);
            console.log('Tab data:', $(this).data());
            console.log('switchTab function exists:', typeof switchTab);

            // If this tab has a URL, navigate to it instead of showing the tab
            if (tabUrl) {
                console.log('Navigating to URL:', tabUrl);
                window.location.href = tabUrl;
                return;
            }

            // Use the unified switchTab function
            console.log('Calling switchTab with:', tabId);
            switchTab(tabId);
            console.log('=== END TAB CLICK DEBUG ===');
        });

        // Handle Premium tab links and Upgrade buttons
        $('.redco-premium-tab-link, .redco-upgrade-button[data-tab], .redco-header-action-button[data-tab], .redco-help-button').on('click', function(e) {
            e.preventDefault();

            const tabId = $(this).data('tab');
            switchTab(tabId);

            // Add a subtle animation to the button
            if ($(this).hasClass('redco-header-action-button') || $(this).hasClass('redco-help-button')) {
                $(this).css('transform', 'scale(0.95)');
                setTimeout(function() {
                    $('.redco-header-action-button, .redco-help-button').css('transform', '');
                }, 200);
            }
        });

        // Handle Configure buttons in modules tab
        $(document).on('click', '.redco-configure-module', function(e) {
            e.preventDefault();

            const tabId = $(this).data('tab');
            if (tabId) {
                switchTab(tabId);
            }
        });

        // Close success message
        $('.redco-close-message').on('click', function() {
            $(this).closest('.redco-success-message').slideUp(300, function() {
                $(this).remove();

                // Save in user meta that the message has been closed
                $.ajax({
                    url: redco_optimizer.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'redco_save_ui_state',
                        nonce: redco_optimizer.nonce,
                        state_type: 'congratulations_closed',
                        state_data: 'true'
                    }
                });
            });
        });

        // Handle Exclude Files button click
        $('.redco-exclude-files-btn').on('click', function() {
            // Get current exclusions from hidden input
            const currentExclusions = $('#file_exclusions').val();

            // Set the value in the textarea
            $('#redco-exclusions-textarea').val(currentExclusions);

            // Show the modal
            $('#redco-exclude-files-modal').css({
                'display': 'flex',
                'visibility': 'visible',
                'opacity': '1'
            }).addClass('active');
        });

        // Handle JavaScript Defer Exclusions button click
        $('.redco-js-defer-exclusions-btn').on('click', function() {
            // Get current exclusions from hidden input
            const currentExclusions = $('#defer_js_exclusions_field').val();

            // Set the value in the textarea
            $('#redco-js-defer-exclusions-textarea').val(currentExclusions);

            console.log('JS Defer Exclusions button clicked');
            console.log('Modal element exists:', $('#redco-js-defer-exclusions-modal').length > 0);
            console.log('Current exclusions:', currentExclusions);

            // Show the modal
            $('#redco-js-defer-exclusions-modal').css({
                'display': 'flex',
                'visibility': 'visible',
                'opacity': '1'
            }).addClass('active');
        });

        // Handle modal close button - use event delegation for all modals
        $(document).on('click', '.redco-modal-close, .redco-modal-cancel', function() {
            // Close all modals
            closeAllModals();
        });

        // Close modal when clicking outside the modal content
        $(document).on('click', '.redco-modal-overlay', function(e) {
            // Only close if the click is directly on the overlay, not on its children
            if (e.target === this) {
                closeAllModals();
            }
        });

        // Ensure toggle switches in modals are green when checked
        $(document).on('click', '.redco-modal-overlay, .redco-open-modal', function() {
            // Apply green color to all checked toggle switches in modals after a short delay
            setTimeout(function() {
                $('.redco-modal-content input:checked + .redco-slider').css('background-color', '#00A66B');
            }, 100);
        });

        // Handle save exclusions button for file exclusions
        $('#redco-exclude-files-modal .redco-modal-save').on('click', function() {
            // Get the exclusions from the textarea
            const exclusions = $('#redco-exclusions-textarea').val();

            // Set the value in the hidden input
            $('#file_exclusions').val(exclusions);

            // Close the modal
            closeAllModals();

            // Show a notification
            showNotification('success', 'File exclusions saved. Remember to save your settings to apply changes.', {
                title: 'Exclusions Updated'
            });
        });

        // Handle save exclusions button for JavaScript defer exclusions
        $('#redco-js-defer-exclusions-modal .redco-modal-save').on('click', function() {
            // Get the exclusions from the textarea
            const exclusions = $('#redco-js-defer-exclusions-textarea').val();

            // Set the value in the hidden input
            $('#defer_js_exclusions_field').val(exclusions);

            // Log the update for debugging
            console.log('Saving defer_js_exclusions:', exclusions);
            console.log('Updated field value:', $('#defer_js_exclusions_field').val());

            // Close the modal
            closeAllModals();

            // Show a notification
            showNotification('success', 'JavaScript defer exclusions saved. Remember to save your settings to apply changes.', {
                title: 'Exclusions Updated'
            });
        });

        // Refresh account info
        $('.redco-refresh-info').on('click', function() {
            const $this = $(this);
            const $icon = $this.find('.dashicons');

            // Add spinning animation
            $icon.addClass('redco-spin');

            // Simulate refreshing data
            setTimeout(function() {
                $icon.removeClass('redco-spin');
                showNotification('success', 'Account information refreshed successfully.');
            }, 1000);
        });

        // Initialize dashboard charts
        initDashboardCharts();

        // Tools tab functionality

        // Import file handling
        $('#redco-import-file').on('change', function() {
            const file = this.files[0];
            if (file) {
                $('#redco-import-file-name').text(file.name);
                $('.redco-import-settings').prop('disabled', false);
            } else {
                $('#redco-import-file-name').text('');
                $('.redco-import-settings').prop('disabled', true);
            }
        });

        // Export settings
        $('.redco-export-settings').on('click', function() {
            // Show loading state
            const $button = $(this);
            const originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> Exporting...');
            $button.prop('disabled', true);

            // Send AJAX request to get settings
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_export_settings',
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        try {
                            // Create a download link
                            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(response.data.settings));
                            const downloadAnchorNode = document.createElement('a');
                            downloadAnchorNode.setAttribute("href", dataStr);
                            downloadAnchorNode.setAttribute("download", "redco-optimizer-settings.json");
                            document.body.appendChild(downloadAnchorNode);
                            downloadAnchorNode.click();
                            downloadAnchorNode.remove();

                            // Show success notification
                            showNotification('success', 'Settings exported successfully.', {
                                title: 'Export Complete'
                            });
                        } catch (error) {
                            console.error('Export error:', error);
                            // Show error notification with more details
                            showNotification('error', 'Error processing export data: ' + error.message, {
                                title: 'Export Failed'
                            });
                        }
                    } else {
                        // Show error notification
                        showNotification('error', response.data ? (response.data.message || 'Failed to export settings.') : 'Unknown error occurred.', {
                            title: 'Export Failed'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', status, error);
                    // Show error notification with more details
                    let errorMessage = 'Connection error. Please try again.';
                    if (xhr.responseText) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.data && response.data.message) {
                                errorMessage = response.data.message;
                            }
                        } catch (e) {
                            // If we can't parse the JSON, use the raw response text
                            if (xhr.responseText.length < 100) {
                                errorMessage += ' Server response: ' + xhr.responseText;
                            }
                        }
                    }
                    showNotification('error', errorMessage, {
                        title: 'Export Failed'
                    });
                },
                complete: function() {
                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });

        // Import settings
        $('.redco-import-settings').on('click', function() {
            const file = $('#redco-import-file')[0].files[0];
            if (!file) {
                showNotification('error', 'Please select a file to import.', {
                    title: 'Import Failed'
                });
                return;
            }

            // Validate file type
            if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
                showNotification('error', 'Please select a valid JSON file.', {
                    title: 'Import Failed'
                });
                return;
            }

            // Show loading state
            const $button = $(this);
            const originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> Importing...');
            $button.prop('disabled', true);

            // Read the file
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const settings = JSON.parse(e.target.result);

                    // Send AJAX request to import settings
                    $.ajax({
                        url: redco_optimizer.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'redco_optimizer_import_settings',
                            nonce: redco_optimizer.nonce,
                            settings: JSON.stringify(settings)
                        },
                        success: function(response) {
                            if (response.success) {
                                // Show success notification
                                showNotification('success', 'Settings imported successfully. Reloading page...', {
                                    title: 'Import Complete'
                                });

                                // Reload the page after a short delay
                                setTimeout(function() {
                                    window.location.reload();
                                }, 2000);
                            } else {
                                // Show error notification
                                showNotification('error', response.data ? (response.data.message || 'Failed to import settings.') : 'Unknown error occurred.', {
                                    title: 'Import Failed'
                                });

                                // Restore button state
                                $button.html(originalText);
                                $button.prop('disabled', false);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX import error:', status, error);
                            // Show error notification with more details
                            let errorMessage = 'Connection error. Please try again.';
                            if (xhr.responseText) {
                                try {
                                    const response = JSON.parse(xhr.responseText);
                                    if (response.data && response.data.message) {
                                        errorMessage = response.data.message;
                                    }
                                } catch (e) {
                                    // If we can't parse the JSON, use the raw response text
                                    if (xhr.responseText.length < 100) {
                                        errorMessage += ' Server response: ' + xhr.responseText;
                                    }
                                }
                            }
                            showNotification('error', errorMessage, {
                                title: 'Import Failed'
                            });

                            // Restore button state
                            $button.html(originalText);
                            $button.prop('disabled', false);
                        }
                    });
                } catch (error) {
                    // Show error notification
                    showNotification('error', 'Invalid settings file. Please select a valid JSON file.', {
                        title: 'Import Failed'
                    });

                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            };
            reader.readAsText(file);
        });

        // Rollback version selection
        $('#redco-rollback-version').on('change', function() {
            const version = $(this).val();
            if (version) {
                $('.redco-rollback-button').prop('disabled', false);
            } else {
                $('.redco-rollback-button').prop('disabled', true);
            }
        });

        // Rollback to previous version
        $('.redco-rollback-button').on('click', function() {
            const version = $('#redco-rollback-version').val();
            if (!version) {
                return;
            }

            // Confirm rollback
            if (!confirm('Are you sure you want to rollback to version ' + version + '? This action cannot be undone.')) {
                return;
            }

            // Show loading state
            const $button = $(this);
            const originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> Rolling back...');
            $button.prop('disabled', true);

            // Send AJAX request to rollback
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_rollback',
                    nonce: redco_optimizer.nonce,
                    version: version
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        showNotification('success', 'Rollback to version ' + version + ' initiated. The page will reload shortly.', {
                            title: 'Rollback Initiated'
                        });

                        // Reload the page after a short delay
                        setTimeout(function() {
                            window.location.reload();
                        }, 3000);
                    } else {
                        // Show error notification
                        showNotification('error', response.data.message || 'Failed to rollback to version ' + version + '.', {
                            title: 'Rollback Failed'
                        });

                        // Restore button state
                        $button.html(originalText);
                        $button.prop('disabled', false);
                    }
                },
                error: function() {
                    // Show error notification
                    showNotification('error', 'Connection error. Please try again.', {
                        title: 'Rollback Failed'
                    });

                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });

        // Reset settings
        $('.redco-reset-settings').on('click', function() {
            // Confirm reset
            if (!confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
                return;
            }

            // Show loading state
            const $button = $(this);
            const originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> Resetting...');
            $button.prop('disabled', true);

            // Send AJAX request to reset settings
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_reset_settings',
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        showNotification('success', 'All settings have been reset to their default values. Reloading page...', {
                            title: 'Reset Complete'
                        });

                        // Reload the page after a short delay
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        // Show error notification
                        showNotification('error', response.data ? (response.data.message || 'Failed to reset settings.') : 'Unknown error occurred.', {
                            title: 'Reset Failed'
                        });

                        // Restore button state
                        $button.html(originalText);
                        $button.prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX reset error:', status, error);
                    // Show error notification with more details
                    let errorMessage = 'Connection error. Please try again.';
                    if (xhr.responseText) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.data && response.data.message) {
                                errorMessage = response.data.message;
                            }
                        } catch (e) {
                            // If we can't parse the JSON, use the raw response text
                            if (xhr.responseText.length < 100) {
                                errorMessage += ' Server response: ' + xhr.responseText;
                            }
                        }
                    }
                    showNotification('error', errorMessage, {
                        title: 'Reset Failed'
                    });

                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });

        // Function to update Enable/Disable All button text based on current state
        function updateToggleAllButtonText() {
            // Always show "Disable All" regardless of the current state
            $('.redco-modules-toggle-all').text('Disable All');
        }

        // Initialize the button text on page load
        updateToggleAllButtonText();

        // Check if we're coming back after an Enable/Disable All action
        const storedModulesState = sessionStorage.getItem('redco_all_modules_state');
        if (storedModulesState) {
            // Clear the stored state
            sessionStorage.removeItem('redco_all_modules_state');

            // Show a notification about the completed action
            showNotification('success',
                storedModulesState === 'enabled'
                    ? 'All modules have been enabled successfully.'
                    : 'All modules have been disabled successfully.',
                { duration: 5000 }
            );
        }

        // Initialize tab visibility based on module status
        initializeTabVisibility();

        // Enable All modules button
        $('.redco-modules-toggle-all').on('click', function(e) {
            e.preventDefault();

            const $button = $(this);
            const originalText = $button.text();

            // Always disable all modules
            const isEnableAll = false;

            // Update button text and state
            $button.text('Disabling...');
            $button.prop('disabled', true);

            // Removed duplicate notification - the green success notification is sufficient

            // Add loading state to all module cards
            $('.redco-module-card').addClass('redco-loading');

            // Send a single AJAX request to toggle all modules at once
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_toggle_all_modules',
                    enable: isEnableAll ? 1 : 0,
                    nonce: redco_optimizer.nonce,
                    no_redirect: 1 // Add parameter to prevent redirect
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        showNotification('success', response.data.message, {
                            title: 'Modules Disabled',
                            duration: 3000
                        });

                        // Check if we need to redirect
                        if (response.data.force_redirect && response.data.redirect) {
                            // Show a brief notification before redirecting
                            showNotification('info', 'Reloading page...', {
                                duration: 1000
                            });

                            // Redirect to the specified URL after a short delay
                            setTimeout(function() {
                                window.location.href = response.data.redirect;
                            }, 1000);
                        } else {
                            // Update UI without reloading
                            $('.redco-module-toggle').each(function() {
                                const $toggle = $(this);
                                const $moduleCard = $toggle.closest('.redco-module-card');

                                // Update toggle state
                                $toggle.prop('checked', false);

                                // Update module card class
                                $moduleCard.removeClass('redco-module-active redco-module-enabled');

                                // Remove loading state
                                $moduleCard.removeClass('redco-loading');

                                // Add a subtle highlight effect
                                $moduleCard.addClass('redco-settings-saved');
                                setTimeout(function() {
                                    $moduleCard.removeClass('redco-settings-saved');
                                }, 1500);

                                // Update related UI elements
                                const moduleId = $toggle.data('module');
                                if (moduleId) {
                                    updateModuleRelatedUI(moduleId, false);
                                }
                            });

                            // Re-enable the button
                            $button.prop('disabled', false).text('Disable All');

                            // Refresh dashboard stats if we're on the dashboard
                            if ($('#redco-dashboard-tab').is(':visible')) {
                                refreshDashboardStats();
                            }

                            // Update tab visibility
                            initializeTabVisibility();

                            // Reset all toggles to ensure they're properly enabled
                            resetAllToggles();
                        }
                    } else {
                        // Show error notification
                        showNotification('error', response.data.message || 'Failed to update modules.', {
                            title: 'Error',
                            duration: 5000
                        });

                        // Restore button state
                        $button.text('Disable All');
                        $button.prop('disabled', false);

                        // Remove loading state from all module cards
                        $('.redco-module-card').removeClass('redco-loading');
                    }
                },
                error: function() {
                    // Show error notification
                    showNotification('error', 'Connection error. Please try again.', {
                        title: 'Error',
                        duration: 5000
                    });

                    // Restore button state
                    $button.text('Disable All');
                    $button.prop('disabled', false);

                    // Remove loading state from all module cards
                    $('.redco-module-card').removeClass('redco-loading');
                }
            });
        });

        // Simplified card-based module toggle (entire card is clickable)
        $('.redco-module-card').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const $moduleCard = $(this);
            const moduleId = $moduleCard.data('module');
            const currentStatus = $moduleCard.data('enabled') === 'true';
            const newStatus = !currentStatus;

            // Skip if this is a premium module and user doesn't have access
            if ($moduleCard.hasClass('redco-module-premium') && !redco_optimizer.has_premium_access) {
                // Show premium modal/tab instead
                showPremiumModal();
                return;
            }

            toggleModule(moduleId, newStatus, $moduleCard);
        });

        // Legacy toggle switch support (for backward compatibility)
        $('.redco-module-toggle').on('change', function() {
            const $toggle = $(this);
            const moduleId = $toggle.data('module');
            const status = $toggle.prop('checked') || $toggle.val() === '1';
            const $moduleCard = $toggle.closest('.redco-module-card');

            toggleModule(moduleId, status, $moduleCard);
        });

        // Function to handle module toggling
        function toggleModule(moduleId, status, $moduleCard) {
            const moduleName = $moduleCard.find('.redco-module-title').text().trim();

            // Disable interactions while processing
            $moduleCard.addClass('redco-loading');
            $moduleCard.css('pointer-events', 'none');

            // Send AJAX request to toggle module
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_toggle_module',
                    module_id: moduleId,
                    status: status ? 1 : 0,
                    nonce: redco_optimizer.nonce,
                    no_redirect: 1 // Add parameter to prevent redirect
                },
                success: function(response) {
                    if (response.success) {
                        // Only redirect if absolutely necessary (e.g., for critical modules)
                        if (response.data.force_redirect && response.data.redirect) {
                            // Show a brief notification before redirecting
                            showNotification('success', 'Module status updated. Reloading page...', {
                                title: status ? 'Module Enabled' : 'Module Disabled',
                                duration: 1000
                            });

                            // Redirect to the specified URL after a short delay
                            setTimeout(function() {
                                window.location.href = response.data.redirect;
                            }, 500);
                        } else {
                            // Show success notification without reloading
                            showNotification('success', response.data.message, {
                                title: status ? 'Module Enabled' : 'Module Disabled',
                                duration: 4000
                            });

                            // Update card state
                            $moduleCard.data('enabled', status ? 'true' : 'false');

                            if (status) {
                                $moduleCard.removeClass('redco-module-disabled').addClass('redco-module-enabled');
                            } else {
                                $moduleCard.removeClass('redco-module-enabled').addClass('redco-module-disabled');
                            }

                            // Status badge is handled by CSS ::after content based on card class
                            // No need to update badge text manually

                            // Update card title
                            $moduleCard.attr('title', status ? 'Click to disable this module' : 'Click to enable this module');

                            // Update hidden input
                            const $hiddenInput = $moduleCard.find('.redco-module-toggle');
                            $hiddenInput.val(status ? '1' : '0');

                            // Update any related UI elements
                            updateModuleRelatedUI(moduleId, status);
                        }
                    } else {
                        // Show error notification
                        showNotification('error', response.data.message, {
                            title: 'Module Toggle Failed',
                            duration: 8000
                        });

                        // If premium feature, show upgrade modal and notification
                        if (response.data.premium) {
                            showPremiumModal();

                            // Show premium upgrade notification
                            showNotification('info', 'Upgrade to Redco Optimizer Premium to unlock this feature and more.', {
                                title: 'Premium Feature',
                                duration: 10000,
                                position: 'toast'
                            });
                        }

                        // Revert the toggle
                        $toggle.prop('checked', !status);
                    }
                },
                error: function() {
                    // Show error notification
                    showNotification('error', redco_optimizer.error_text, {
                        title: 'Connection Error',
                        duration: 8000
                    });

                    // Revert the toggle
                    $toggle.prop('checked', !status);
                },
                complete: function() {
                    // Re-enable interactions and remove loading state
                    $moduleCard.removeClass('redco-loading');
                    $moduleCard.css('pointer-events', 'auto');

                    // Add a subtle highlight effect to indicate the module was updated
                    $moduleCard.addClass('redco-settings-saved');
                    setTimeout(function() {
                        $moduleCard.removeClass('redco-settings-saved');
                    }, 1500);

                    // Refresh dashboard stats if we're on the dashboard
                    if ($('#redco-dashboard-tab').is(':visible')) {
                        refreshDashboardStats();
                    }

                    // Update the Enable/Disable All button text
                    updateToggleAllButtonText();
                }
            });
        }

        /**
         * Update UI elements related to a module's state
         *
         * @param {string} moduleId The module ID
         * @param {boolean} enabled Whether the module is enabled
         */
        function updateModuleRelatedUI(moduleId, enabled) {
            // Update related UI elements based on module state
            // For example, enable/disable related quick action buttons

            // Find related quick action buttons
            $('.redco-quick-action[data-module="' + moduleId + '"]').each(function() {
                if (enabled) {
                    $(this).removeClass('disabled').prop('disabled', false);
                } else {
                    $(this).addClass('disabled').prop('disabled', true);
                }
            });

            // Update module tab visibility if needed
            initializeTabVisibility();

            // Ensure toggle switches have the correct color
            ensureToggleSwitchesColor();
        }

        /**
         * Ensure toggle switches have the correct color based on their checked state
         */
        function ensureToggleSwitchesColor() {
            // Find all toggle switches
            $('input[type="checkbox"]').each(function() {
                const $checkbox = $(this);
                const $slider = $checkbox.next('.redco-slider');

                if ($checkbox.prop('checked')) {
                    $slider.css('background-color', '#00A66B');
                } else {
                    $slider.css('background-color', '');
                }
            });
        }

        /**
         * Update UI elements after settings are saved
         *
         * @param {string} tab The tab that was saved
         * @param {object} data The response data from the server
         */
        function updateUIAfterSettingsSaved(tab, data) {
            // Update UI based on the tab that was saved
            switch (tab) {
                case 'caching':
                    // Update caching-related UI elements
                    updateCachingUI(data);
                    break;

                case 'file-optimization':
                    // Update file optimization UI elements
                    updateFileOptimizationUI(data);
                    break;

                case 'media':
                    // Update media-related UI elements
                    updateMediaUI(data);
                    break;

                case 'database':
                    // Update database-related UI elements
                    updateDatabaseUI(data);
                    break;

                case 'heartbeat':
                    // Update heartbeat-related UI elements
                    updateHeartbeatUI(data);
                    break;

                case 'cdn':
                    // Update CDN-related UI elements
                    updateCdnUI(data);
                    break;
            }

            // Update any global UI elements
            updateGlobalUI();
        }

        /**
         * Update caching-related UI elements
         *
         * @param {object} data The response data from the server
         */
        function updateCachingUI(data) {
            // Update cache status indicators if they exist
            if ($('.redco-cache-status').length) {
                if (data.debug && data.debug.settings) {
                    const settings = data.debug.settings;

                    // Update page caching status
                    if (settings.enable_page_caching) {
                        $('.redco-cache-status-page').addClass('active').text('Active');
                    } else {
                        $('.redco-cache-status-page').removeClass('active').text('Inactive');
                    }

                    // Update browser caching status
                    if (settings.enable_browser_caching) {
                        $('.redco-cache-status-browser').addClass('active').text('Active');
                    } else {
                        $('.redco-cache-status-browser').removeClass('active').text('Inactive');
                    }
                }
            }

            // Show/hide dependent fields based on toggle states
            const $pageCachingToggle = $('#enable_page_caching');
            if ($pageCachingToggle.length) {
                const isEnabled = $pageCachingToggle.prop('checked');
                $('.redco-depends-on-page-caching').toggle(isEnabled);
            }

            const $mobileCachingToggle = $('#enable_mobile_caching');
            if ($mobileCachingToggle.length) {
                const isEnabled = $mobileCachingToggle.prop('checked');
                $('.redco-depends-on-mobile-caching').toggle(isEnabled);
            }
        }

        /**
         * Update file optimization UI elements
         *
         * @param {object} data The response data from the server
         */
        function updateFileOptimizationUI(data) {
            // Show/hide dependent fields based on toggle states

            // Delay JavaScript
            const $delayJsToggle = $('#delay_js');
            if ($delayJsToggle.length) {
                const isEnabled = $delayJsToggle.prop('checked');
                $('#delay-js-exclusions-row, #delay-js-safe-mode-row').toggle(isEnabled);
            }

            // Remove Unused CSS
            const $removeUnusedCssToggle = $('#remove_unused_css');
            if ($removeUnusedCssToggle.length) {
                const isEnabled = $removeUnusedCssToggle.prop('checked');
                $('#css-safelist-row').toggle(isEnabled);
            }

            // Defer JavaScript
            const $deferJsToggle = $('#defer_js');
            if ($deferJsToggle.length) {
                const isEnabled = $deferJsToggle.prop('checked');
                $('#defer-js-exclusions-row').toggle(isEnabled);
            }

            // Minify HTML
            const $minifyHtmlToggle = $('#minify_html');
            if ($minifyHtmlToggle.length) {
                const isEnabled = $minifyHtmlToggle.prop('checked');
                $('#minify-html-options-row').toggle(isEnabled);
            }
        }

        /**
         * Update media-related UI elements
         *
         * @param {object} data The response data from the server
         */
        function updateMediaUI(data) {
            // Show/hide dependent fields based on toggle states

            // Lazy Load Images
            const $lazyLoadToggle = $('#lazy_load_images');
            if ($lazyLoadToggle.length) {
                const isEnabled = $lazyLoadToggle.prop('checked');
                $('#lazy-load-threshold-row').toggle(isEnabled);
            }

            // Image Optimization
            const $imageOptToggle = $('#enable_image_optimization');
            if ($imageOptToggle.length) {
                const isEnabled = $imageOptToggle.prop('checked');
                $('#image-quality-row').toggle(isEnabled);
            }
        }

        /**
         * Update database-related UI elements
         *
         * @param {object} data The response data from the server
         */
        function updateDatabaseUI(data) {
            // Show/hide dependent fields based on toggle states

            // Scheduled Cleanup
            const $scheduleCleanupToggle = $('#schedule_cleanup');
            if ($scheduleCleanupToggle.length) {
                const isEnabled = $scheduleCleanupToggle.prop('checked');
                $('#schedule-frequency-row').toggle(isEnabled);
            }
        }

        /**
         * Update heartbeat-related UI elements
         *
         * @param {object} data The response data from the server
         */
        function updateHeartbeatUI(data) {
            // Show/hide dependent fields based on toggle states

            // Control Heartbeat
            const $controlHeartbeatToggle = $('#control_heartbeat');
            if ($controlHeartbeatToggle.length) {
                const isEnabled = $controlHeartbeatToggle.prop('checked');
                $('.redco-depends-on-heartbeat').toggle(isEnabled);
            }
        }

        /**
         * Update CDN-related UI elements
         *
         * @param {object} data The response data from the server
         */
        function updateCdnUI(data) {
            // Show/hide dependent fields based on toggle states

            // CDN Integration
            const $cdnToggle = $('#enable_cdn');
            if ($cdnToggle.length) {
                const isEnabled = $cdnToggle.prop('checked');
                $('.redco-depends-on-cdn').toggle(isEnabled);
            }
        }

        /**
         * Update global UI elements
         */
        function updateGlobalUI() {
            // Update dashboard stats if we're on the dashboard
            if ($('#redco-dashboard-tab').is(':visible')) {
                // Refresh dashboard stats
                refreshDashboardStats();
            }

            // Update module cards if we're on the modules tab
            if ($('#redco-modules-tab').is(':visible')) {
                // Refresh module cards
                refreshModuleCards();
            }
        }

        /**
         * Refresh dashboard statistics via AJAX
         */
        function refreshDashboardStats() {
            // Only proceed if dashboard stats container exists
            if ($('.redco-dashboard-stats').length === 0) {
                return;
            }

            // Show loading state
            $('.redco-dashboard-stats').addClass('redco-loading');

            // Send AJAX request to get updated stats
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_get_dashboard_stats',
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        // Update stats in the UI
                        updateDashboardStatsUI(response.data);
                    }

                    // Remove loading state
                    $('.redco-dashboard-stats').removeClass('redco-loading');
                },
                error: function() {
                    // Remove loading state on error
                    $('.redco-dashboard-stats').removeClass('redco-loading');

                    // Log error
                    console.error('Failed to refresh dashboard stats');
                }
            });
        }

        /**
         * Update dashboard stats UI with new data
         *
         * @param {object} data The stats data from the server
         */
        function updateDashboardStatsUI(data) {
            // Update cache hits
            if (data.cache_hits !== undefined && $('.redco-stat-cache-hits').length) {
                $('.redco-stat-cache-hits').text(data.cache_hits);
            }

            // Update images optimized
            if (data.images_optimized !== undefined && $('.redco-stat-images-optimized').length) {
                $('.redco-stat-images-optimized').text(data.images_optimized);
            }

            // Update database size reduced
            if (data.db_size_reduced !== undefined && $('.redco-stat-db-size-reduced').length) {
                $('.redco-stat-db-size-reduced').text(data.db_size_reduced);
            }

            // Update total saved
            if (data.total_saved !== undefined && $('.redco-stat-total-saved').length) {
                $('.redco-stat-total-saved').text(data.total_saved);
            }

            // Update performance score if available
            if (data.performance_score !== undefined && $('.redco-performance-score').length) {
                $('.redco-performance-score-value').text(data.performance_score + '%');
                $('.redco-performance-score-circle').css('--percentage', data.performance_score);
            }
        }

        /**
         * Refresh module cards via AJAX
         */
        function refreshModuleCards() {
            // Only proceed if modules container exists
            if ($('.redco-modules-grid').length === 0) {
                return;
            }

            // Show loading state
            $('.redco-modules-grid').addClass('redco-loading');

            // Send AJAX request to get updated module status
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_get_modules',
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success && response.data && response.data.modules) {
                        // Update module cards in the UI
                        updateModuleCardsUI(response.data.modules);
                    }

                    // Remove loading state
                    $('.redco-modules-grid').removeClass('redco-loading');
                },
                error: function() {
                    // Remove loading state on error
                    $('.redco-modules-grid').removeClass('redco-loading');

                    // Log error
                    console.error('Failed to refresh module cards');
                }
            });
        }

        /**
         * Update module cards UI with new data
         *
         * @param {object} modules The modules data from the server
         */
        function updateModuleCardsUI(modules) {
            // Loop through each module
            Object.keys(modules).forEach(function(moduleId) {
                const module = modules[moduleId];
                const $moduleCard = $('.redco-module-card[data-module="' + moduleId + '"]');

                if ($moduleCard.length) {
                    // Update module enabled/disabled state
                    const $toggle = $moduleCard.find('.redco-module-toggle');

                    if ($toggle.length) {
                        $toggle.prop('checked', module.enabled);

                        // Update module card class
                        if (module.enabled) {
                            $moduleCard.addClass('redco-module-active');
                        } else {
                            $moduleCard.removeClass('redco-module-active');
                        }
                    }

                    // Update related UI elements
                    updateModuleRelatedUI(moduleId, module.enabled);
                }
            });

            // Update the toggle all button text
            updateToggleAllButtonText();
        }

        // Listen for checkbox changes in forms
        $('.redco-form').on('redco-checkbox-changed', function(e, $checkbox) {
            // Update any related UI elements if needed
            if ($checkbox.attr('id') === 'delay_js') {
                // Special handling for delay_js toggle
                ensureCustomExclusionsVisibility();
            }
        });

        // Form toggle switch handlers removed - using standard checkboxes now

        // Settings form submission
        $('.redco-form').on('submit', function(e) {
            e.preventDefault();

            const $form = $(this);
            const $submitButton = $form.find('button[type="submit"]');
            const tabId = $form.closest('.redco-tab-content').attr('id');
            const tab = tabId.replace('redco-', '').replace('-tab', '');

            // Save the current state of all checkboxes before submission
            const checkboxStates = {};
            $form.find('input[type="checkbox"]').each(function() {
                const id = $(this).attr('id');
                if (id) {
                    checkboxStates[id] = $(this).prop('checked');
                }
            });

            // Log form submission for debugging
            console.log('Form submitted for tab:', tab);
            console.log('Form elements:', $form.find('input, select, textarea').length);

            // Disable the submit button while processing
            $submitButton.prop('disabled', true);
            const originalText = $submitButton.text();
            $submitButton.html('<span class="dashicons dashicons-update redco-spin"></span> ' + redco_optimizer.saving_text);

            // Create FormData object to properly handle checkboxes
            let formData = new FormData($form[0]);

            // Add action and nonce
            formData.append('action', 'redco_optimizer_save_settings');
            formData.append('nonce', redco_optimizer.nonce);
            formData.append('tab', tab);
            formData.append('no_redirect', true); // Always use AJAX without redirect

            // Add loading state to the form
            $form.addClass('redco-loading');

            // Set a timeout to re-enable all checkboxes after a short delay
            setTimeout(function() {
                $form.find('input[type="checkbox"]:not(.redco-premium-feature)').prop('disabled', false);
                $form.find('input[type="checkbox"]:not(.redco-premium-feature)').each(function() {
                    this.disabled = false;
                    this.removeAttribute('disabled');
                });
                console.log('Re-enabled checkboxes after form submission');
            }, 500);

            // Special handling for file optimization tab
            if (tab === 'file-optimization') {
                // Critical checkbox fields that must be explicitly set
                const criticalCheckboxFields = [
                    'minify_js', 'combine_js', 'defer_js',
                    'remove_unused_css', 'delay_js', 'delay_js_safe_mode'
                ];

                // Process each critical checkbox field
                for (let field of criticalCheckboxFields) {
                    const $field = $form.find('#' + field);

                    if ($field.length > 0) {
                        // If the checkbox exists, set its value based on checked state
                        const isChecked = $field.prop('checked');

                        // Force the value to be a string '1' or '0'
                        const fieldValue = isChecked ? '1' : '0';

                        // Remove any existing value and set the new one
                        formData.delete(field);
                        formData.append(field, fieldValue);

                        // Add a debug field to track this value
                        formData.append(field + '_debug', fieldValue);
                    } else {
                        // If the field doesn't exist, set a default value of 0
                        formData.delete(field);
                        formData.append(field, '0');

                        // Add a debug field to track this value
                        formData.append(field + '_debug', '0');
                    }
                }

                // Critical textarea fields that must be included
                const criticalTextareaFields = [
                    'defer_js_exclusions', 'css_safelist', 'delay_js_exclusions'
                ];

                // Process each critical textarea field
                for (let field of criticalTextareaFields) {
                    // Special handling for defer_js_exclusions which might be a hidden field
                    if (field === 'defer_js_exclusions') {
                        const $hiddenField = $form.find('#defer_js_exclusions_field');
                        const $textareaField = $form.find('#defer_js_exclusions');

                        if ($hiddenField.length > 0) {
                            // If hidden field exists, use its value
                            formData.set('defer_js_exclusions', $hiddenField.val());
                        } else if ($textareaField.length > 0) {
                            // If textarea exists, use its value
                            formData.set('defer_js_exclusions', $textareaField.val());
                        } else {
                            // Default value if neither exists
                            formData.append('defer_js_exclusions', 'jquery.js');
                        }
                    } else {
                        // For other textarea fields
                        const $field = $form.find('#' + field);

                        if ($field.length > 0) {
                            // If the textarea exists, use its value
                            formData.set(field, $field.val());
                        } else {
                            // Default empty value if it doesn't exist
                            formData.append(field, '');
                        }
                    }
                }


            }

            // Handle unchecked checkboxes - they need to be explicitly set to 0
            // Get all checkboxes in the form
            $form.find('input[type="checkbox"]').each(function() {
                const name = $(this).attr('name');
                if (!$(this).prop('checked') && !$(this).prop('disabled')) {
                    // If the checkbox is not checked, set it to 0
                    formData.append(name, '0');
                } else if ($(this).prop('checked')) {
                    // If the checkbox is checked, ensure it's set to 1
                    formData.set(name, '1');
                }
            });

            // Special handling for file optimization tab
            if (tab === 'file-optimization') {
                // Ensure critical fields are included
                const criticalFields = [
                    'minify_html', 'minify_css', 'minify_js', 'combine_css', 'combine_js', 'defer_js',
                    'remove_unused_css', 'delay_js', 'delay_js_safe_mode'
                ];

                for (let field of criticalFields) {
                    if (!formData.has(field)) {
                        formData.append(field, '0');
                    }
                }

                // Ensure all delay_js_exclude_* fields are included
                const delayJsExcludeFields = [
                    'delay_js_exclude_jquery', 'delay_js_exclude_jquery_migrate', 'delay_js_exclude_jquery_core',
                    'delay_js_exclude_wp_embed', 'delay_js_exclude_wp_core', 'delay_js_exclude_comment_reply',
                    'delay_js_exclude_analytics', 'delay_js_exclude_gtag', 'delay_js_exclude_gtm',
                    'delay_js_exclude_fbevents', 'delay_js_exclude_hotjar', 'delay_js_exclude_clarity',
                    'delay_js_exclude_adsense', 'delay_js_exclude_admanager', 'delay_js_exclude_amazon',
                    'delay_js_exclude_stripe', 'delay_js_exclude_paypal', 'delay_js_exclude_square',
                    'delay_js_exclude_authorize', 'delay_js_exclude_recaptcha', 'delay_js_exclude_livechat',
                    'delay_js_exclude_intercom', 'delay_js_exclude_zopim', 'delay_js_exclude_zendesk',
                    'delay_js_exclude_cf7', 'delay_js_exclude_wpcf7', 'delay_js_exclude_formidable',
                    'delay_js_exclude_mediaelement', 'delay_js_exclude_vimeo', 'delay_js_exclude_youtube',
                    'delay_js_exclude_swiper', 'delay_js_exclude_slider', 'delay_js_exclude_carousel',
                    'delay_js_exclude_revolution', 'delay_js_exclude_elementor', 'delay_js_exclude_avada',
                    'delay_js_exclude_divi', 'delay_js_exclude_astra', 'delay_js_exclude_generatepress',
                    'delay_js_exclude_oceanwp'
                ];

                for (let field of delayJsExcludeFields) {
                    if (!formData.has(field)) {
                        formData.append(field, '0');
                    }
                }

                // Ensure textarea fields are included
                const textareaFields = [
                    'defer_js_exclusions', 'css_safelist', 'delay_js_exclusions'
                ];

                for (let field of textareaFields) {
                    if (!formData.has(field)) {
                        formData.append(field, '');
                    }
                }
            }

            // Ensure numeric values are properly handled
            // Look for specific numeric fields and ensure they're numbers
            const numericFields = ['lazy_load_threshold', 'lazyload_threshold', 'image_quality', 'cache_lifetime', 'cache_lifespan'];

            // Look for specific boolean fields to ensure they're properly handled
            const booleanFields = [
                'webp_conversion', 'enable_page_caching', 'enable_browser_caching', 'enable_mobile_caching',
                'separate_mobile_cache', 'clear_on_post_edit', 'clear_on_comment', 'cache_logged_in_users',
                'cache_ssl', 'cache_404', 'cache_query_strings', 'minify_html', 'minify_html_comments',
                'minify_html_inline_css', 'minify_html_inline_js', 'minify_css', 'combine_css',
                'optimize_css_delivery', 'minify_js', 'combine_js', 'defer_js', 'remove_unused_css',
                'delay_js', 'delay_js_safe_mode', 'enable_image_optimization', 'lazy_load_images',
                'image_dimensions', 'disable_emojis', 'disable_embeds', 'preload_cache', 'preload_sitemap',
                'prefetch_dns', 'preload_fonts', 'preload_links', 'clean_post_revisions', 'clean_auto_drafts',
                'clean_trashed_posts', 'clean_spam_comments', 'clean_trashed_comments', 'clean_expired_transients',
                'clean_all_transients', 'clean_optimize_tables', 'schedule_cleanup', 'cleanup_post_revisions',
                'cleanup_auto_drafts', 'cleanup_trashed_posts', 'cleanup_spam_comments', 'cleanup_trashed_comments',
                'cleanup_expired_transients', 'cleanup_all_transients', 'cleanup_optimize_tables', 'cleanup_postmeta',
                'cleanup_commentmeta', 'cleanup_orphaned_term_relationships', 'cleanup_wp_options', 'lazyload_images',
                'lazyload_iframes', 'lazyload_videos', 'lazyload_css_bg', 'control_heartbeat'
            ];

            // Add all delay_js_exclude_* fields to boolean fields
            $form.find('input[type="checkbox"]').each(function() {
                const name = $(this).attr('name');
                if (name && name.startsWith('delay_js_exclude_')) {
                    if (!booleanFields.includes(name)) {
                        booleanFields.push(name);
                    }
                }
            });

            for (let numericField of numericFields) {
                if (formData.has(numericField)) {
                    let value = formData.get(numericField);
                    // Parse as integer and ensure it's a valid number
                    let numValue = parseInt(value);
                    if (!isNaN(numValue)) {
                        // Replace the value with the integer
                        formData.set(numericField, numValue);
                    }
                }
            }

            // Handle boolean fields
            for (let booleanField of booleanFields) {
                if (formData.has(booleanField)) {
                    let value = formData.get(booleanField);
                    // Ensure it's a proper boolean value (0 or 1)
                    let boolValue = (value === '1' || value === 'true' || value === 'on') ? 1 : 0;
                    formData.set(booleanField, boolValue);
                }
            }





            // Special handling for file optimization tab
            if (tab === 'file-optimization') {
                // Ensure critical fields are included
                const criticalFields = [
                    'minify_html', 'minify_css', 'minify_js', 'combine_css', 'combine_js', 'defer_js',
                    'remove_unused_css', 'delay_js', 'delay_js_safe_mode'
                ];

                for (let field of criticalFields) {
                    if (!formData.has(field)) {
                        formData.append(field, '0');
                    }
                }

                // Ensure defer_js_exclusions is included from the hidden field
                if (!formData.has('defer_js_exclusions') && $('#defer_js_exclusions_field').length > 0) {
                    const deferJsExclusions = $('#defer_js_exclusions_field').val();
                    formData.append('defer_js_exclusions', deferJsExclusions);
                }

                // Ensure delay_js_exclusions is included from the textarea
                if (!formData.has('delay_js_exclusions') && $('#delay_js_exclusions').length > 0) {
                    const delayJsExclusions = $('#delay_js_exclusions').val();
                    formData.append('delay_js_exclusions', delayJsExclusions);
                }

                // Ensure css_safelist is included from the textarea
                if (!formData.has('css_safelist') && $('#css_safelist').length > 0) {
                    const cssSafelist = $('#css_safelist').val();
                    formData.append('css_safelist', cssSafelist);
                }
            }

            // Convert FormData to URL-encoded string for jQuery AJAX
            let serializedData = '';
            for (let pair of formData.entries()) {
                serializedData += '&' + encodeURIComponent(pair[0]) + '=' + encodeURIComponent(pair[1]);
            }
            serializedData = serializedData.substring(1); // Remove the leading &

            // Send AJAX request to save settings
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: serializedData,
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        showNotification('success', response.data.message, {
                            title: 'Settings Saved',
                            duration: 3000
                        });

                        // Re-enable the submit button
                        $submitButton.prop('disabled', false).html(originalText);

                        // Remove loading state from the form
                        $form.removeClass('redco-loading');

                        // Re-enable all checkboxes and restore their state
                        $form.find('input[type="checkbox"]').each(function() {
                            const id = $(this).attr('id');
                            if (id && checkboxStates[id] !== undefined) {
                                $(this).prop('disabled', false);

                                // Update the debug field if it exists
                                const $debugField = $('#' + id + '_debug');
                                if ($debugField.length > 0) {
                                    $debugField.val(checkboxStates[id] ? '1' : '0');
                                }
                            }
                        });

                        // Update the color of toggle switches
                        ensureToggleSwitchesColor();

                        // Add a subtle highlight effect to indicate the form was saved
                        const $tabContent = $form.closest('.redco-tab-content');
                        $tabContent.addClass('redco-settings-saved');
                        setTimeout(function() {
                            $tabContent.removeClass('redco-settings-saved');
                        }, 1500);

                        // Trigger a custom event that other code can listen for
                        $(document).trigger('redco-settings-saved', [tab, response.data]);

                        // Update any UI elements that depend on these settings
                        updateUIAfterSettingsSaved(tab, response.data);

                        // Reset all toggles to ensure they're properly enabled
                        resetAllToggles();
                    } else {
                        // Show error notification
                        showNotification('error', response.data.message, {
                            title: 'Settings Error'
                        });

                        // Enable the submit button
                        $submitButton.prop('disabled', false).html(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    // Show error notification
                    showNotification('error', redco_optimizer.error_text, {
                        title: 'Connection Error'
                    });

                    // Enable the submit button
                    $submitButton.prop('disabled', false).html(originalText);

                    // Remove loading state from the form
                    $form.removeClass('redco-loading');

                    // Re-enable all checkboxes and restore their state
                    $form.find('input[type="checkbox"]').each(function() {
                        const id = $(this).attr('id');
                        if (id && checkboxStates[id] !== undefined) {
                            $(this).prop('disabled', false);

                            // Update the debug field if it exists
                            const $debugField = $('#' + id + '_debug');
                            if ($debugField.length > 0) {
                                $debugField.val(checkboxStates[id] ? '1' : '0');
                            }
                        }
                    });

                    // Ensure checkboxes are enabled
                    ensureCheckboxesEnabled();

                    // Log the error for debugging
                    console.error('AJAX error:', xhr, status, error);

                    // Reset all toggles to ensure they're properly enabled
                    resetAllToggles();
                }
            });
        });

        // Define the notification functions and make them globally accessible
        showNotification = function(type, message, options = {}) {
            // Default options
            const defaults = {
                title: getNotificationTitle(type),
                duration: 5000,
                position: 'toast', // 'toast' or 'inline'
                autoClose: true,
                showProgress: true,
                showCloseButton: true,
                onClose: null
            };

            // Merge defaults with provided options
            const settings = $.extend({}, defaults, options);

            // Create notification container if it doesn't exist
            if ($('.redco-notification-container').length === 0 && settings.position === 'toast') {
                $('body').append('<div class="redco-notification-container"></div>');
            }

            // Create the notification HTML
            const notificationId = 'redco-notification-' + Date.now();
            let notificationHtml = '<div id="' + notificationId + '" class="redco-notification redco-notification-' + type + '">';
            notificationHtml += '<div class="redco-notification-icon"></div>';
            notificationHtml += '<div class="redco-notification-content">';

            if (settings.title) {
                notificationHtml += '<div class="redco-notification-title">' + settings.title + '</div>';
            }

            notificationHtml += '<p class="redco-notification-message">' + message + '</p>';
            notificationHtml += '</div>';

            if (settings.showCloseButton) {
                notificationHtml += '<div class="redco-notification-close"></div>';
            }

            if (settings.showProgress && settings.autoClose) {
                notificationHtml += '<div class="redco-notification-progress">';
                notificationHtml += '<div class="redco-notification-progress-bar"></div>';
                notificationHtml += '</div>';
            }

            notificationHtml += '</div>';

            // Create jQuery object from HTML
            const $notification = $(notificationHtml);

            // Add the notification to the page
            if (settings.position === 'toast') {
                $('.redco-notification-container').append($notification);
            } else {
                // Add inline notification
                $notification.addClass('redco-notification-inline');
                $('.redco-optimizer-content').prepend($notification);
            }

            // Set progress bar animation duration
            if (settings.showProgress && settings.autoClose) {
                $notification.find('.redco-notification-progress-bar').css('animation-duration', (settings.duration / 1000) + 's');
            }

            // Handle close button click
            $notification.find('.redco-notification-close').on('click', function() {
                closeNotification($notification, settings.onClose);
            });

            // Auto-close the notification after specified duration
            if (settings.autoClose) {
                setTimeout(function() {
                    closeNotification($notification, settings.onClose);
                }, settings.duration);
            }

            // Return the notification element for further manipulation
            return $notification;
        };

        // Function to close a notification - make it globally accessible
        closeNotification = function($notification, callback) {
            $notification.css('animation', 'fadeOut 0.3s forwards');

            setTimeout(function() {
                $notification.remove();

                // Call the callback if provided
                if (typeof callback === 'function') {
                    callback();
                }
            }, 300);
        };

        // Function to get default title based on notification type - make it globally accessible
        getNotificationTitle = function(type) {
            switch (type) {
                case 'success':
                    return 'Success';
                case 'error':
                    return 'Error';
                case 'warning':
                    return 'Warning';
                case 'info':
                    return 'Information';
                default:
                    return '';
            }
        };

        // Function to show premium modal (now redirects to Premium tab)
        function showPremiumModal() {
            // Instead of showing a modal, switch to the Premium tab
            const tabId = 'redco-premium-tab';

            // Update active tab in sidebar
            $('.redco-nav-item').removeClass('active');
            $('.redco-nav-item[data-tab="' + tabId + '"]').addClass('active');

            // Update page header title, description and icon
            $('.redco-page-header h1').text($('.redco-nav-item[data-tab="' + tabId + '"]').find('.redco-nav-text').text());
            $('.redco-page-header-description').text($('.redco-nav-item[data-tab="' + tabId + '"]').find('.redco-nav-description').text());

            // Fix for header icon disappearing - ensure it's properly set and visible
            var $navIcon = $('.redco-nav-item[data-tab="' + tabId + '"]').find('.redco-nav-icon');
            var iconClass = 'dashicons-dashboard'; // Default fallback

            if ($navIcon.length) {
                var classes = $navIcon.attr('class');
                if (classes) {
                    var classArray = classes.split(' ');
                    // Find the dashicons-* class
                    for (var i = 0; i < classArray.length; i++) {
                        if (classArray[i].startsWith('dashicons-')) {
                            iconClass = classArray[i];
                            break;
                        }
                    }
                }
            }

            $('.redco-page-header-icon .dashicons').attr('class', 'dashicons ' + iconClass);

            // Also ensure the dashicon is visible with explicit CSS
            $('.redco-page-header-icon .dashicons').css({
                'display': 'inline-block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'font-family': 'dashicons !important',
                'font-size': '20px !important',
                'width': '20px !important',
                'height': '20px !important',
                'line-height': '1 !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: inline-block !important; visibility: visible !important; opacity: 1 !important; font-family: dashicons !important;';
            });

            $('.redco-page-header-icon').css({
                'display': 'flex',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Add animation effect when changing tabs
            $('.redco-page-header-icon').css('transform', 'scale(1.1)');
            setTimeout(function() {
                $('.redco-page-header-icon').css('transform', 'scale(1)');
            }, 200);

            // Show the selected tab content with fade effect
            $('.redco-tab-content').fadeOut(200);
            setTimeout(function() {
                $('#' + tabId).fadeIn(300);
            }, 200);

            // Save the active tab in user meta
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_save_ui_state',
                    nonce: redco_optimizer.nonce,
                    state_type: 'active_tab',
                    state_data: tabId
                }
            });
        }

        // Check if there's a tab parameter in the URL
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');
        let activeTab;

        if (tabParam) {
            // If tab parameter exists, try to find the corresponding tab
            const tabId = 'redco-' + tabParam + '-tab';
            if ($('.redco-nav-item[data-tab="' + tabId + '"]').length > 0) {
                activeTab = tabId;
            } else {
                // If tab doesn't exist, check for remembered tab from localStorage
                const localStorageTab = localStorage.getItem('redco_active_tab');
                if (localStorageTab && $('.redco-nav-item[data-tab="' + localStorageTab + '"]').length > 0 &&
                    !$('.redco-nav-item[data-tab="' + localStorageTab + '"]').hasClass('redco-module-tab-disabled') &&
                    $('.redco-nav-item[data-tab="' + localStorageTab + '"]').css('display') !== 'none') {
                    activeTab = localStorageTab;
                } else {
                    // If no localStorage tab or it's not available, check for remembered tab from server data
                    const rememberedTab = typeof redco_data !== 'undefined' && redco_data.active_tab ? redco_data.active_tab : '';
                    if (rememberedTab && $('.redco-nav-item[data-tab="' + rememberedTab + '"]').length > 0 &&
                        !$('.redco-nav-item[data-tab="' + rememberedTab + '"]').hasClass('redco-module-tab-disabled') &&
                        $('.redco-nav-item[data-tab="' + rememberedTab + '"]').css('display') !== 'none') {
                        activeTab = rememberedTab;
                    } else {
                        // If no remembered tab or it's not available, use dashboard
                        activeTab = 'redco-dashboard-tab';
                    }
                }
            }
        } else {
            // If no tab parameter, check for remembered tab from localStorage
            const localStorageTab = localStorage.getItem('redco_active_tab');
            if (localStorageTab && $('.redco-nav-item[data-tab="' + localStorageTab + '"]').length > 0 &&
                !$('.redco-nav-item[data-tab="' + localStorageTab + '"]').hasClass('redco-module-tab-disabled') &&
                $('.redco-nav-item[data-tab="' + localStorageTab + '"]').css('display') !== 'none') {
                activeTab = localStorageTab;
            } else {
                // If no localStorage tab or it's not available, check for remembered tab from server data
                const rememberedTab = typeof redco_data !== 'undefined' && redco_data.active_tab ? redco_data.active_tab : '';
                if (rememberedTab && $('.redco-nav-item[data-tab="' + rememberedTab + '"]').length > 0 &&
                    !$('.redco-nav-item[data-tab="' + rememberedTab + '"]').hasClass('redco-module-tab-disabled') &&
                    $('.redco-nav-item[data-tab="' + rememberedTab + '"]').css('display') !== 'none') {
                    activeTab = rememberedTab;
                } else {
                    // If no remembered tab or it's not available, use dashboard
                    activeTab = 'redco-dashboard-tab';
                }
            }
        }

        // Activate the tab
        $('.redco-nav-item[data-tab="' + activeTab + '"]').click();

        // Add special click handler for help tab
        $('.redco-nav-item[data-tab="redco-help-tab"]').on('click', function() {
            console.log('Help tab clicked directly');

            // Force the help tab to be visible after a short delay
            setTimeout(function() {
                $('#redco-help-tab').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-help-tab .redco-section').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-help-tab .redco-section-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-help-tab .redco-help-direct-container').attr('style', 'display: flex !important; visibility: visible !important; opacity: 1 !important;');

                // Add redco-help-direct-link class to all addon settings buttons in the help tab
                $('#redco-help-tab .redco-addon-settings').addClass('redco-help-direct-link');
            }, 300);
        });

        // Robust fix to ensure header icon is visible after page load
        function ensureHeaderIconVisibility() {
            var $activeTab = $('.redco-nav-item.active');
            if ($activeTab.length) {
                var $navIcon = $activeTab.find('.redco-nav-icon');
                var iconClass = 'dashicons-dashboard'; // Default fallback

                if ($navIcon.length) {
                    var classes = $navIcon.attr('class');
                    if (classes) {
                        var classArray = classes.split(' ');
                        // Find the dashicons-* class
                        for (var i = 0; i < classArray.length; i++) {
                            if (classArray[i].startsWith('dashicons-')) {
                                iconClass = classArray[i];
                                break;
                            }
                        }
                    }
                }

                // Update the icon class
                $('#redco-header-icon-inner').attr('class', 'dashicons ' + iconClass);

                // Also ensure the dashicon is visible with explicit CSS
                $('#redco-header-icon-inner').css({
                    'display': 'inline-block !important',
                    'visibility': 'visible !important',
                    'opacity': '1 !important',
                    'font-family': 'dashicons !important',
                    'font-size': '20px !important',
                    'width': '20px !important',
                    'height': '20px !important',
                    'line-height': '1 !important'
                }).attr('style', function(i, s) {
                    return (s || '') + 'display: inline-block !important; visibility: visible !important; opacity: 1 !important; font-family: dashicons !important;';
                });

                // Ensure the icon container is visible with important flags
                $('#redco-header-icon').css({
                    'display': 'flex !important',
                    'visibility': 'visible !important',
                    'opacity': '1 !important'
                }).attr('style', function(i, s) {
                    return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important;';
                });

                // Add a subtle entrance animation
                $('#redco-header-icon').css('transform', 'scale(0.9)');
                setTimeout(function() {
                    $('#redco-header-icon').css('transform', 'scale(1)');
                }, 100);
            }
        }

        // Call immediately
        ensureHeaderIconVisibility();

        // And also with a delay to catch any late DOM changes
        setTimeout(ensureHeaderIconVisibility, 300);
        setTimeout(ensureHeaderIconVisibility, 1000);

        // Set up a periodic check to ensure the icon stays visible
        setInterval(ensureHeaderIconVisibility, 2000);

        // Set up a periodic check to ensure premium badges stay visible
        setInterval(ensurePremiumBadgesVisible, 3000);

        // Handle quick action buttons
        $('.redco-clear-cache, .redco-optimize-images, .redco-clean-database, .redco-clear-used-css, .redco-clear-fonts-cache').on('click', function(e) {
            e.preventDefault();

            // Skip if button is disabled
            if ($(this).prop('disabled') || $(this).hasClass('disabled')) {
                return;
            }

            let actionType;
            if ($(this).hasClass('redco-clear-cache')) {
                actionType = 'cache';
            } else if ($(this).hasClass('redco-optimize-images')) {
                actionType = 'image';
            } else if ($(this).hasClass('redco-clean-database')) {
                actionType = 'database';
            } else if ($(this).hasClass('redco-clear-used-css')) {
                actionType = 'used_css';
            } else if ($(this).hasClass('redco-clear-fonts-cache')) {
                actionType = 'fonts_cache';
            }

            const $button = $(this);
            const originalText = $button.html();

            // Disable the button and show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-update redco-spin"></span> Processing...');

            // Prepare notification title based on action type
            let actionTitle = '';
            switch (actionType) {
                case 'cache':
                    actionTitle = 'Cache Cleared';
                    break;
                case 'image':
                    actionTitle = 'Images Optimized';
                    break;
                case 'database':
                    actionTitle = 'Database Cleaned';
                    break;
                case 'used_css':
                    actionTitle = 'Used CSS Cache Cleared';
                    break;
                case 'fonts_cache':
                    actionTitle = 'Google Fonts Cache Cleared';
                    break;
                default:
                    actionTitle = 'Action Completed';
            }

            // Perform the action based on type
            performQuickAction(actionType, function(success, message) {
                // Remove loading state
                $button.prop('disabled', false).html(originalText);

                // Show notification
                if (success) {
                    showNotification('success', message, {
                        title: actionTitle,
                        duration: 7000 // Show success notifications a bit longer
                    });
                } else {
                    showNotification('error', message || 'An error occurred. Please try again.', {
                        title: 'Action Failed',
                        duration: 10000 // Show error notifications even longer
                    });
                }
            });
        });
    });

    /**
     * Initialize dashboard charts and visualizations
     */
    function initDashboardCharts() {
        // Animate the score circle on load
        animateScoreCircle();

        // Animate progress bars
        animateProgressBars();
    }

    /**
     * Animate the performance score circle
     */
    function animateScoreCircle() {
        const $circle = $('.redco-score-circle-fill');
        if ($circle.length) {
            const score = parseInt($circle.attr('stroke-dasharray').split(',')[0]);

            // Start from 0 and animate to the actual score
            $circle.attr('stroke-dasharray', '0, 100');

            setTimeout(function() {
                $circle.css('transition', 'stroke-dasharray 1.5s ease-in-out');
                $circle.attr('stroke-dasharray', score + ', 100');
            }, 300);
        }
    }

    /**
     * Animate progress bars
     */
    function animateProgressBars() {
        $('.redco-score-item-progress, .redco-resource-progress').each(function() {
            const $this = $(this);
            const width = $this.css('width');

            // Start from 0 and animate to the actual width
            $this.css('width', '0');

            setTimeout(function() {
                $this.css('transition', 'width 1s ease-in-out');
                $this.css('width', width);
            }, 300);
        });
    }

    /**
     * Switch to a specific tab
     *
     * @param {string} tabId The ID of the tab to switch to
     */
    function switchTab(tabId) {
        console.log('Switching to tab:', tabId);

        // Check if the tab has a URL
        const tabUrl = $('.redco-nav-item[data-tab="' + tabId + '"]').data('url');
        if (tabUrl) {
            // Navigate to the URL instead of showing the tab
            window.location.href = tabUrl;
            return;
        }

        // Clean up help documentation if switching to a different tab
        if (tabId !== 'redco-help-tab') {
            // Remove any help documentation that might be showing in other tabs
            if ($('#redco-help-tab').length && !$('#redco-help-tab').is(':visible')) {
                console.log('Cleaning up help documentation from other tabs');
                $('.redco-help-container, .redco-help-content-container, .redco-help-content').not('#redco-help-tab .redco-help-container, #redco-help-tab .redco-help-content-container, #redco-help-tab .redco-help-content').remove();
            }
        }

        // Update active tab in sidebar
        $('.redco-nav-item').removeClass('active');
        $('.redco-nav-item[data-tab="' + tabId + '"]').addClass('active');

        // Update page header title, description and icon
        $('.redco-page-header h1').text($('.redco-nav-item[data-tab="' + tabId + '"]').find('.redco-nav-text').text());
        $('.redco-page-header-description').text($('.redco-nav-item[data-tab="' + tabId + '"]').find('.redco-nav-description').text());

        // Robust fix for header icon - ensure it's properly set and visible
        var $navIcon = $('.redco-nav-item[data-tab="' + tabId + '"]').find('.redco-nav-icon');
        var iconClass = 'dashicons-dashboard'; // Default fallback

        if ($navIcon.length) {
            var classes = $navIcon.attr('class');
            if (classes) {
                var classArray = classes.split(' ');
                // Find the dashicons-* class
                for (var i = 0; i < classArray.length; i++) {
                    if (classArray[i].startsWith('dashicons-')) {
                        iconClass = classArray[i];
                        break;
                    }
                }
            }
        }

        // Update the icon class
        $('#redco-header-icon-inner').attr('class', 'dashicons ' + iconClass);

        // Also ensure the dashicon is visible with explicit CSS
        $('#redco-header-icon-inner').css({
            'display': 'inline-block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'font-family': 'dashicons !important',
            'font-size': '20px !important',
            'width': '20px !important',
            'height': '20px !important',
            'line-height': '1 !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: inline-block !important; visibility: visible !important; opacity: 1 !important; font-family: dashicons !important;';
        });

        // Ensure the icon container is visible with important flags
        $('#redco-header-icon').css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important;';
        });

        // Add animation effect when changing tabs
        $('#redco-header-icon').css('transform', 'scale(1.1)');
        setTimeout(function() {
            $('#redco-header-icon').css('transform', 'scale(1)');
        }, 200);

        // Show the selected tab content with fade effect
        $('.redco-tab-content').fadeOut(200);
        setTimeout(function() {
            // Force display for all tabs with !important flags
            $('#' + tabId).attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');

            // Also use jQuery fadeIn as backup
            $('#' + tabId).fadeIn(300);

            // Special handling for specific tabs
            if (tabId === 'redco-help-tab') {
                console.log('Help tab selected, applying special handling');

                // Force display with !important flags
                $('#redco-help-tab .redco-section').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-help-tab .redco-section-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-help-tab .redco-help-direct-container').attr('style', 'display: flex !important; visibility: visible !important; opacity: 1 !important;');

                // Add redco-help-direct-link class to all addon settings buttons in the help tab
                $('#redco-help-tab .redco-addon-settings').addClass('redco-help-direct-link');

                // Log the visibility state
                console.log('Help tab display after forcing:', $('#redco-help-tab').css('display'));
                console.log('Help section display after forcing:', $('#redco-help-tab .redco-section').css('display'));
            }

            // Special handling for heartbeat tab
            if (tabId === 'redco-heartbeat-tab') {
                console.log('Heartbeat tab selected, ensuring content is visible');
                $('#redco-heartbeat-tab .redco-card').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-heartbeat-tab .redco-card-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-heartbeat-tab .redco-form').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-heartbeat-tab .redco-form-row').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-heartbeat-tab .redco-toggle-row').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
            }

            // Special handling for site health inspector tab
            if (tabId === 'redco-site-health-inspector-tab') {
                console.log('Site Health Inspector tab selected, ensuring content is visible');
                $('#redco-site-health-inspector-tab .redco-card').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-site-health-inspector-tab .redco-card-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-site-health-inspector-tab .redco-section').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-site-health-inspector-tab .redco-section-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
            }

            // Special handling for tools tab
            if (tabId === 'redco-tools-tab') {
                console.log('Tools tab selected, ensuring content is visible');
                $('#redco-tools-tab .redco-card').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-tools-tab .redco-card-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-tools-tab .redco-section').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-tools-tab .redco-section-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
            }

            // Special handling for addons tab
            if (tabId === 'redco-addons-tab') {
                console.log('Add-ons tab selected, ensuring content is visible');
                $('#redco-addons-tab .redco-card').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-addons-tab .redco-card-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-addons-tab .redco-section').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-addons-tab .redco-section-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
            }

            // General fallback: Force all child elements to be visible
            $('#' + tabId + ' *').each(function() {
                const $element = $(this);
                // Skip elements that should be hidden (like modals, overlays, etc.)
                if (!$element.hasClass('redco-modal-overlay') &&
                    !$element.hasClass('redco-modal') &&
                    !$element.hasClass('redco-hidden') &&
                    !$element.attr('style') || !$element.attr('style').includes('display: none')) {

                    // Force visibility for content elements
                    if ($element.hasClass('redco-card') ||
                        $element.hasClass('redco-card-content') ||
                        $element.hasClass('redco-section') ||
                        $element.hasClass('redco-section-content') ||
                        $element.hasClass('redco-form') ||
                        $element.hasClass('redco-form-row') ||
                        $element.hasClass('redco-toggle-row')) {

                        $element.css({
                            'display': 'block',
                            'visibility': 'visible',
                            'opacity': '1'
                        });
                    }
                }
            });

            // Log tab switch completion with detailed debugging
            console.log('Tab switched to:', tabId);
            console.log('Tab display:', $('#' + tabId).css('display'));
            console.log('Tab visibility:', $('#' + tabId).css('visibility'));
            console.log('Tab opacity:', $('#' + tabId).css('opacity'));
            console.log('Tab content elements found:', $('#' + tabId + ' .redco-card, #' + tabId + ' .redco-section').length);

            // Log specific content visibility
            $('#' + tabId + ' .redco-card, #' + tabId + ' .redco-section').each(function(index) {
                console.log('Content element ' + index + ':', {
                    display: $(this).css('display'),
                    visibility: $(this).css('visibility'),
                    opacity: $(this).css('opacity'),
                    class: $(this).attr('class')
                });
            });
        }, 200);

        // Save the current tab to localStorage
        if (tabId !== 'redco-dashboard-tab') {
            localStorage.setItem('redco_active_tab', tabId);
        } else {
            // If switching to dashboard, remove the stored tab
            localStorage.removeItem('redco_active_tab');
        }

        // Only save non-dashboard tabs in user meta
        if (tabId !== 'redco-dashboard-tab') {
            // Save to WordPress user meta via AJAX
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_save_ui_state',
                    nonce: redco_optimizer.nonce,
                    state_type: 'active_tab',
                    state_data: tabId
                }
            });
        } else {
            // If switching to dashboard, remove the stored tab
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_save_ui_state',
                    nonce: redco_optimizer.nonce,
                    state_type: 'active_tab',
                    state_data: ''
                }
            });
        }
    }

    /**
     * Perform quick action based on type
     *
     * @param {string} actionType The type of action to perform
     * @param {function} callback Callback function after action completes
     */
    function performQuickAction(actionType, callback) {
        // Send AJAX request to perform the action
        jQuery.ajax({
            url: redco_optimizer.ajax_url,
            type: 'POST',
            data: {
                action: 'redco_optimizer_quick_action',
                action_type: actionType,
                nonce: redco_optimizer.nonce
            },
            success: function(response) {
                if (response.success) {
                    callback(true, response.data.message);
                } else {
                    callback(false, response.data.message);
                }
            },
            error: function() {
                callback(false);
            }
        });
    }

    /**
     * Initialize tab visibility based on module status
     */
    function initializeTabVisibility() {
        console.log('Initializing tab visibility');

        // First, hide all module tabs
        $('.redco-nav-item').each(function() {
            const $tab = $(this);
            const tabId = $tab.data('tab');

            // Skip non-module tabs like dashboard, modules, tools, help, premium, addons
            // Heartbeat and Site Health Inspector are now module tabs and should be controlled by module status
            if (tabId === 'redco-dashboard-tab' ||
                tabId === 'redco-modules-tab' ||
                tabId === 'redco-tools-tab' ||
                tabId === 'redco-premium-tab' ||
                tabId === 'redco-addons-tab' ||
                tabId === 'redco-help-tab') {
                return;
            }

            // Hide all module tabs by default
            $tab.addClass('redco-module-tab-disabled').hide();
        });

        // Then, show tabs for enabled modules
        $('.redco-module-card').each(function() {
            const $moduleCard = $(this);
            const moduleId = $moduleCard.data('module');
            const isEnabled = $moduleCard.hasClass('redco-module-enabled');
            const tabId = $moduleCard.data('tab');

            console.log('Module: ' + moduleId + ' - Enabled: ' + isEnabled + ' - Tab: ' + tabId);

            // Special logging for Site Health Inspector and Heartbeat
            if (moduleId === 'site-health-inspector') {
                console.log('Site Health Inspector module found:');
                console.log('- Module ID:', moduleId);
                console.log('- Is Enabled:', isEnabled);
                console.log('- Tab ID:', tabId);
                console.log('- Module card classes:', $moduleCard.attr('class'));
            }

            if (moduleId === 'heartbeat') {
                console.log('Heartbeat module found:');
                console.log('- Module ID:', moduleId);
                console.log('- Is Enabled:', isEnabled);
                console.log('- Tab ID:', tabId);
                console.log('- Module card classes:', $moduleCard.attr('class'));
            }

            // If module is enabled, show its tab
            if (isEnabled && moduleId && tabId) {
                const tabSelector = '.redco-nav-item[data-tab="' + tabId + '"]';
                console.log('Tab selector: ' + tabSelector + ' - Exists: ' + ($(tabSelector).length > 0));

                $(tabSelector).removeClass('redco-module-tab-disabled').show();
                console.log('Showing tab for enabled module: ' + tabId);

                // Special logging for Site Health Inspector and Heartbeat
                if (moduleId === 'site-health-inspector') {
                    console.log('Site Health Inspector tab should now be visible');
                }
                if (moduleId === 'heartbeat') {
                    console.log('Heartbeat tab should now be visible');
                }
            } else {
                // Log when modules are disabled
                if (moduleId === 'site-health-inspector') {
                    console.log('Site Health Inspector module is DISABLED - tab will be hidden');
                }
                if (moduleId === 'heartbeat') {
                    console.log('Heartbeat module is DISABLED - tab will be hidden');
                }
            }
        });

        // Make sure the Tools, Add-Ons, and Help tabs are always visible (non-module tabs)
        $('.redco-nav-item[data-tab="redco-tools-tab"]').removeClass('redco-module-tab-disabled').show();
        $('.redco-nav-item[data-tab="redco-addons-tab"]').removeClass('redco-module-tab-disabled').show();
        $('.redco-nav-item[data-tab="redco-help-tab"]').removeClass('redco-module-tab-disabled').show();
        // CDN, Heartbeat, and Site Health Inspector tabs are now controlled by module status

        // If the current tab is hidden, switch to dashboard
        const currentTabId = $('.redco-tab-content:visible').attr('id');
        if (currentTabId && currentTabId !== 'redco-dashboard-tab' &&
            currentTabId !== 'redco-modules-tab' &&
            currentTabId !== 'redco-tools-tab' &&
            currentTabId !== 'redco-premium-tab' &&
            currentTabId !== 'redco-addons-tab' &&
            currentTabId !== 'redco-heartbeat-tab' &&
            currentTabId !== 'redco-site-health-inspector-tab' &&
            currentTabId !== 'redco-help-tab') {

            const $currentTab = $('.redco-nav-item[data-tab="' + currentTabId + '"]');
            if ($currentTab.hasClass('redco-module-tab-disabled') || $currentTab.css('display') === 'none') {
                console.log('Current tab is hidden, switching to dashboard');
                switchTab('redco-dashboard-tab');
            }
        }
    }

    /**
     * Close all modals
     */
    function closeAllModals() {
        $('.redco-modal-overlay').css({
            'display': 'none',
            'visibility': 'hidden',
            'opacity': '0'
        }).removeClass('active');
        console.log('All modals closed');
    }

    /**
     * Get module ID from tab ID
     *
     * @param {string} tabId The tab ID
     * @return {string|null} The module ID or null if not found
     */
    function getModuleIdFromTabId(tabId) {
        // Map tab IDs to module IDs
        const tabToModuleMap = {
            'redco-caching-tab': 'caching',
            'redco-file-optimization-tab': 'file-optimization',
            'redco-media-tab': 'media',
            'redco-preload-tab': 'preload',
            'redco-database-tab': 'database',
            'redco-heartbeat-tab': 'heartbeat',
            'redco-lazyload-tab': 'lazyload',
            'redco-cdn-tab': 'cdn',
            'redco-site-health-inspector-tab': 'site-health-inspector',
            'redco-tools-tab': 'tools',
            'redco-addons-tab': 'addons'
        };

        return tabToModuleMap[tabId] || null;
    }



    /**
     * Check for notification cookie and display notification if found
     */
    function checkForNotificationCookie() {
        // Function to get cookie by name
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
        }

        // Get the notification cookie
        const notificationCookie = getCookie('redco_notification');

        if (notificationCookie) {
            try {
                // First decode the URL encoding, then parse the JSON
                const decodedCookie = decodeURIComponent(notificationCookie);
                const notification = JSON.parse(decodedCookie);

                // Log the notification for debugging
                console.log('Notification from cookie:', notification);

                // Display the notification
                showNotification(
                    notification.type || 'success',
                    notification.message || 'Action completed successfully',
                    { title: notification.title || 'Notification' }
                );

                // Clear the cookie
                document.cookie = 'redco_notification=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
            } catch (e) {
                console.error('Error parsing notification cookie:', e, 'Cookie value:', notificationCookie);

                // Show a generic notification if parsing fails
                showNotification(
                    'success',
                    'Action completed successfully',
                    { title: 'Notification' }
                );
            }
        }
    }

    /**
     * Ensure premium badges are visible
     */
    function ensurePremiumBadgesVisible() {
        // List of free features that should not have premium badges
        const freeFeatures = [
            // File Optimization tab
            'combine_js',
            'defer_js',
            'remove_unused_css',
            'delay_js',
            // Media tab
            'webp_conversion',
            // Preload tab
            'preload_fonts',
            // Heartbeat tab
            'disable_heartbeat_customizer',
            // Database tab
            'schedule_cleanup'
        ];

        // Force premium badges to be visible with inline styles, except for free features
        $('.redco-premium-badge').each(function() {
            // Check if this badge is for a free feature
            let isFreeFeature = false;
            const $parent = $(this).closest('.redco-toggle-info');

            if ($parent.length) {
                const $input = $parent.closest('.redco-toggle-row').find('input[type="checkbox"]');
                if ($input.length) {
                    const inputId = $input.attr('id');
                    if (freeFeatures.includes(inputId)) {
                        // This is a free feature, hide the badge
                        $(this).css({
                            'display': 'none !important',
                            'visibility': 'hidden !important',
                            'opacity': '0 !important'
                        }).attr('style', function(i, s) {
                            return (s || '') + 'display: none !important; visibility: hidden !important; opacity: 0 !important;';
                        });

                        // Also enable the toggle
                        $input.prop('disabled', false);
                        $input.closest('.redco-switch').removeClass('redco-switch-disabled');

                        isFreeFeature = true;
                    }
                }
            }

            // If not a free feature, make sure the badge is visible
            if (!isFreeFeature) {
                $(this).css({
                    'display': 'inline-flex !important',
                    'visibility': 'visible !important',
                    'opacity': '1 !important',
                    'z-index': '100 !important',
                    'margin-left': '10px !important',
                    'margin-right': '0 !important',
                    'vertical-align': 'middle !important',
                    'position': 'relative !important',
                    'top': '-1px !important'
                }).attr('style', function(i, s) {
                    return (s || '') + 'display: inline-flex !important; visibility: visible !important; opacity: 1 !important; z-index: 100 !important; margin-left: 10px !important; margin-right: 0 !important; vertical-align: middle !important; position: relative !important; top: -1px !important;';
                });
            }
        });

        // Add a class to the body to ensure premium badges are visible via CSS
        $('body').addClass('redco-premium-badges-enabled');

        // Log to console for debugging
        console.log('Premium badges initialized: ' + $('.redco-premium-badge').length + ' badges found');
    }

    /**
     * Get module tab information based on module ID
     *
     * @param {string} moduleId The module ID
     * @param {string} moduleName The module name (fallback)
     * @return {object|null} Tab information object or null if not found
     */
    function getModuleTabInfo(moduleId, moduleName) {
        // Define module to tab mapping with descriptions and icons
        const moduleTabsInfo = {
            'caching': {
                tab: 'redco-caching-tab',
                icon: 'dashicons-superhero',
                text: 'Caching',
                description: 'Speed up your website by storing static copies of your pages'
            },
            'file-optimization': {
                tab: 'redco-file-optimization-tab',
                icon: 'dashicons-media-code',
                text: 'File Optimization',
                description: 'Optimize CSS & JS files'
            },
            'media': {
                tab: 'redco-media-tab',
                icon: 'dashicons-format-image',
                text: 'Media',
                description: 'LazyLoad, image dimensions, font optimization'
            },
            'preload': {
                tab: 'redco-preload-tab',
                icon: 'dashicons-performance',
                text: 'Preload',
                description: 'Generate cache files, preload resources'
            },
            'database': {
                tab: 'redco-database-tab',
                icon: 'dashicons-database',
                text: 'Database',
                description: 'Optimize, reduce bloat'
            },
            'heartbeat': {
                tab: 'redco-heartbeat-tab',
                icon: 'dashicons-heart',
                text: 'Heartbeat',
                description: 'Control WordPress Heartbeat API'
            },
            'site-health-inspector': {
                tab: 'redco-site-health-inspector-tab',
                icon: 'dashicons-shield',
                text: 'Site Health',
                description: 'Scan your site for potential issues'
            },
            'cdn': {
                tab: 'redco-cdn-tab',
                icon: 'dashicons-admin-site',
                text: 'CDN',
                description: 'Integrate with Content Delivery Networks'
            },
            'tools': {
                tab: 'redco-tools-tab',
                icon: 'dashicons-admin-tools',
                text: 'Tools',
                description: 'Import, export & rollback'
            },
            'addons': {
                tab: 'redco-addons-tab',
                icon: 'dashicons-admin-plugins',
                text: 'Add-Ons',
                description: 'Extend plugin functionality'
            },
            'site-health-inspector': {
                tab: 'redco-site-health-inspector-tab',
                icon: 'dashicons-shield',
                text: 'Site Health',
                description: 'Scan your site for potential issues and get recommendations to fix them'
            },
            'heartbeat': {
                tab: 'redco-heartbeat-tab',
                icon: 'dashicons-heart',
                text: 'Heartbeat',
                description: 'Control WordPress Heartbeat API to reduce server load'
            }
        };

        // Return the module tab info if found, otherwise create a generic one
        if (moduleTabsInfo[moduleId]) {
            return moduleTabsInfo[moduleId];
        } else {
            return {
                tab: 'redco-' + moduleId + '-tab',
                icon: 'dashicons-admin-generic',
                text: moduleName || moduleId.charAt(0).toUpperCase() + moduleId.slice(1).replace(/-/g, ' '),
                description: 'Configure module settings'
            };
        }
    }

    /**
     * Check if the congratulations message should be hidden
     * If the user has previously closed it, hide it
     */
    function checkCongratulationsMessage() {
        // Check if the message has been closed before using server-provided data
        if (typeof redco_data !== 'undefined' && redco_data.congratulations_closed === 'true') {
            // Hide the message if it exists
            $('.redco-success-message').hide();
        }
    }

    /**
     * Initialize and handle the Scheduled Cleanup Frequency dropdown state
     * Disables the dropdown when the Enable Scheduled Cleanup toggle is off
     * Enables the dropdown when the toggle is on
     */
    function initScheduledCleanupFrequency() {
        // Get the toggle and dropdown elements
        const $scheduleCleanupToggle = $('#schedule_cleanup');
        const $scheduleFrequencyDropdown = $('#schedule_frequency');

        // Set initial state of the dropdown based on toggle state
        if ($scheduleCleanupToggle.length && $scheduleFrequencyDropdown.length) {
            // Check if toggle is checked and not disabled
            const isEnabled = $scheduleCleanupToggle.prop('checked') && !$scheduleCleanupToggle.prop('disabled');

            // Set dropdown disabled state based on toggle state
            $scheduleFrequencyDropdown.prop('disabled', !isEnabled);

            // Add visual indication of disabled state
            if (!isEnabled) {
                $scheduleFrequencyDropdown.addClass('redco-select-disabled');
            } else {
                $scheduleFrequencyDropdown.removeClass('redco-select-disabled');
            }

            // Add event listener to toggle to update dropdown state when changed
            $scheduleCleanupToggle.on('change', function() {
                // Only enable the dropdown if the toggle is checked and not disabled
                const isNowEnabled = $(this).prop('checked') && !$(this).prop('disabled');

                // Update dropdown disabled state
                $scheduleFrequencyDropdown.prop('disabled', !isNowEnabled);

                // Update visual indication
                if (!isNowEnabled) {
                    $scheduleFrequencyDropdown.addClass('redco-select-disabled');
                } else {
                    $scheduleFrequencyDropdown.removeClass('redco-select-disabled');
                }
            });
        }
    }

    /**
     * Ensure dashicons in add-on cards are visible
     * This function forces dashicons in add-on cards to be visible
     */
    function ensureAddOnDashiconsVisible() {
        // Force dashicons in add-on cards to be visible
        $('.redco-addon-icon .dashicons').css({
            'display': 'inline-block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'z-index': '100 !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: inline-block !important; visibility: visible !important; opacity: 1 !important; z-index: 100 !important;';
        });

        // Call this function again after a short delay to ensure it works
        setTimeout(function() {
            $('.redco-addon-icon .dashicons').css({
                'display': 'inline-block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'z-index': '100 !important'
            });
        }, 500);
    }

    /**
     * Standard checkbox handling - no special color management needed
     */
    function ensureCheckboxesEnabled() {
        // Ensure checkboxes are enabled
        $('input[type="checkbox"]:not(.redco-premium-feature)').prop('disabled', false);
    }

    /**
     * Reset all checkboxes to ensure they're properly enabled
     * This function should be called after AJAX operations
     */
    function resetAllToggles() {
        // Enable all checkboxes that aren't premium features
        $('input[type="checkbox"]:not(.redco-premium-feature)').prop('disabled', false);

        // Force remove disabled attribute using DOM API
        document.querySelectorAll('input[type="checkbox"]:not(.redco-premium-feature)').forEach(function(checkbox) {
            checkbox.disabled = false;
            checkbox.removeAttribute('disabled');
        });

        // Log that checkboxes have been reset
        console.log('All checkboxes have been reset and enabled');
    }

    /**
     * Ensure Custom Exclusions section is properly hidden when Delay JavaScript is off
     */
    function ensureCustomExclusionsVisibility() {
        const $delayJsToggle = $('#delay_js');
        const $exclusionsRow = $('#delay-js-exclusions-row');
        const $safeModeRow = $('#delay-js-safe-mode-row');

        // Function to hide elements with !important
        function forceHideElement($element) {
            $element.hide();
            $element.css({
                'display': 'none !important',
                'visibility': 'hidden !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: none !important; visibility: hidden !important;';
            });
        }

        // Check if the toggle exists and is not checked
        if ($delayJsToggle.length && !$delayJsToggle.is(':checked')) {
            // Force hide the exclusions row with !important
            forceHideElement($exclusionsRow);

            // Force hide the safe mode row with !important
            forceHideElement($safeModeRow);

            console.log('Custom Exclusions and Safe Mode sections hidden');
        }

        // Add event listener to ensure they stay hidden when toggle changes
        $delayJsToggle.off('change.customExclusions').on('change.customExclusions', function() {
            if (!$(this).is(':checked')) {
                forceHideElement($exclusionsRow);
                forceHideElement($safeModeRow);
            } else {
                $exclusionsRow.css({
                    'display': 'block',
                    'visibility': 'visible'
                });

                $safeModeRow.css({
                    'display': 'flex',
                    'visibility': 'visible'
                });
            }
        });

        // Run again after a short delay to ensure it works
        setTimeout(function() {
            if ($delayJsToggle.length && !$delayJsToggle.is(':checked')) {
                forceHideElement($exclusionsRow);
                forceHideElement($safeModeRow);
            }
        }, 500);
    }

    /**
     * Ensure premium sections are properly collapsed by default
     * This function ensures that all premium sections have their content hidden
     */
    function ensurePremiumSectionsCollapsed() {
        // Find all premium sections
        const $premiumSections = $('.redco-card-header:has(.redco-premium-badge)').closest('.redco-card');

        // Also include Load CSS Asynchronously and Google Fonts Optimization sections
        const $cssAsyncSection = $('.redco-card-header:contains("Load CSS Asynchronously")').closest('.redco-card');
        const $googleFontsSection = $('.redco-card-header:contains("Google Fonts Optimization")').closest('.redco-card');

        // Combine all sections
        const $allPremiumSections = $premiumSections.add($cssAsyncSection).add($googleFontsSection);

        // Ensure they are collapsed
        $allPremiumSections.each(function() {
            const $section = $(this);
            const $content = $section.find('.redco-card-content');

            // If it's an expandable section
            if ($section.hasClass('redco-expandable-section')) {
                // Remove active class
                $section.removeClass('active');

                // Hide content
                $content.css({
                    'display': 'none',
                    'visibility': 'hidden'
                });
            } else {
                // For non-expandable sections, just hide the content
                $content.hide();
            }

            // Disable all form elements inside premium sections
            $content.find('input, textarea, select, button').prop('disabled', true);
        });

        // Run again after a short delay to ensure it works
        setTimeout(function() {
            $allPremiumSections.each(function() {
                const $section = $(this);
                const $content = $section.find('.redco-card-content');

                if ($section.hasClass('redco-expandable-section')) {
                    // Remove active class
                    $section.removeClass('active');

                    // Hide content
                    $content.css({
                        'display': 'none',
                        'visibility': 'hidden'
                    });
                } else {
                    // For non-expandable sections, just hide the content
                    $content.hide();
                }

                // Disable all form elements inside premium sections
                $content.find('input, textarea, select, button').prop('disabled', true);
            });
        }, 500);
    }



})( jQuery);
