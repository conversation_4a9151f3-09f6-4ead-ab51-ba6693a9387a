/**
 * Redco Optimizer - Toggle Test Script
 * Comprehensive testing for all toggle functionality
 */

(function($) {
    'use strict';

    // Test results object
    var testResults = {
        moduleToggles: [],
        settingToggles: [],
        ajaxHandlers: [],
        uiUpdates: []
    };

    // Run tests when document is ready
    $(document).ready(function() {
        console.log('=== REDCO OPTIMIZER TOGGLE FUNCTIONALITY TEST ===');
        
        // Wait a bit for all scripts to load
        setTimeout(function() {
            runAllTests();
        }, 2000);
    });

    function runAllTests() {
        console.log('Starting comprehensive toggle tests...');
        
        // Test 1: Check if all toggle elements exist
        testToggleElementsExist();
        
        // Test 2: Check if toggles are enabled
        testToggleEnablement();
        
        // Test 3: Check if toggle colors are correct
        testToggleColors();
        
        // Test 4: Test module toggle functionality
        testModuleToggles();
        
        // Test 5: Test setting toggle functionality
        testSettingToggles();
        
        // Test 6: Test AJAX handlers
        testAjaxHandlers();
        
        // Test 7: Test UI updates
        testUIUpdates();
        
        // Display results
        setTimeout(function() {
            displayTestResults();
        }, 3000);
    }

    function testToggleElementsExist() {
        console.log('Test 1: Checking toggle elements existence...');
        
        var moduleToggles = $('.redco-module-toggle').length;
        var settingToggles = $('.redco-form input[type="checkbox"]').length;
        var sliders = $('.redco-slider').length;
        var switches = $('.redco-switch').length;
        
        console.log('Module toggles found:', moduleToggles);
        console.log('Setting toggles found:', settingToggles);
        console.log('Sliders found:', sliders);
        console.log('Switches found:', switches);
        
        testResults.moduleToggles.push({
            test: 'Elements Exist',
            passed: moduleToggles > 0 && settingToggles > 0 && sliders > 0 && switches > 0,
            details: `Module: ${moduleToggles}, Settings: ${settingToggles}, Sliders: ${sliders}, Switches: ${switches}`
        });
    }

    function testToggleEnablement() {
        console.log('Test 2: Checking toggle enablement...');
        
        var disabledToggles = $('input[type="checkbox"]:disabled:not(.redco-premium-feature)').length;
        var totalToggles = $('input[type="checkbox"]:not(.redco-premium-feature)').length;
        
        console.log('Disabled toggles (non-premium):', disabledToggles);
        console.log('Total toggles (non-premium):', totalToggles);
        
        testResults.settingToggles.push({
            test: 'Toggle Enablement',
            passed: disabledToggles === 0,
            details: `${disabledToggles} of ${totalToggles} toggles are disabled`
        });
    }

    function testToggleColors() {
        console.log('Test 3: Checking toggle colors...');
        
        var correctColors = 0;
        var totalChecked = 0;
        
        $('input[type="checkbox"]:checked').each(function() {
            var $slider = $(this).next('.redco-slider');
            if ($slider.length > 0) {
                totalChecked++;
                var bgColor = $slider.css('background-color');
                // Check if it's green (various formats)
                if (bgColor.includes('rgb(0, 166, 107)') || bgColor.includes('#00A66B') || bgColor.includes('#00a66b')) {
                    correctColors++;
                }
            }
        });
        
        console.log('Checked toggles with correct color:', correctColors, 'of', totalChecked);
        
        testResults.uiUpdates.push({
            test: 'Toggle Colors',
            passed: totalChecked === 0 || correctColors === totalChecked,
            details: `${correctColors} of ${totalChecked} checked toggles have correct color`
        });
    }

    function testModuleToggles() {
        console.log('Test 4: Testing module toggle functionality...');
        
        var moduleToggleCount = $('.redco-module-toggle').length;
        var moduleCardsCount = $('.redco-module-card').length;
        
        console.log('Module toggles:', moduleToggleCount);
        console.log('Module cards:', moduleCardsCount);
        
        // Test if module toggles have proper data attributes
        var togglesWithModuleId = $('.redco-module-toggle[data-module]').length;
        
        testResults.moduleToggles.push({
            test: 'Module Toggle Setup',
            passed: togglesWithModuleId === moduleToggleCount,
            details: `${togglesWithModuleId} of ${moduleToggleCount} module toggles have data-module attribute`
        });
    }

    function testSettingToggles() {
        console.log('Test 5: Testing setting toggle functionality...');
        
        var formToggles = $('.redco-form input[type="checkbox"]').length;
        var togglesWithIds = $('.redco-form input[type="checkbox"][id]').length;
        
        console.log('Form toggles:', formToggles);
        console.log('Form toggles with IDs:', togglesWithIds);
        
        testResults.settingToggles.push({
            test: 'Setting Toggle Setup',
            passed: togglesWithIds === formToggles,
            details: `${togglesWithIds} of ${formToggles} setting toggles have ID attributes`
        });
    }

    function testAjaxHandlers() {
        console.log('Test 6: Testing AJAX handlers...');
        
        // Check if redco_optimizer object exists
        var redcoOptimizerExists = typeof redco_optimizer !== 'undefined';
        var hasAjaxUrl = redcoOptimizerExists && redco_optimizer.ajax_url;
        var hasNonce = redcoOptimizerExists && redco_optimizer.nonce;
        
        console.log('redco_optimizer object exists:', redcoOptimizerExists);
        console.log('Has AJAX URL:', hasAjaxUrl);
        console.log('Has nonce:', hasNonce);
        
        testResults.ajaxHandlers.push({
            test: 'AJAX Configuration',
            passed: redcoOptimizerExists && hasAjaxUrl && hasNonce,
            details: `Object: ${redcoOptimizerExists}, URL: ${hasAjaxUrl}, Nonce: ${hasNonce}`
        });
    }

    function testUIUpdates() {
        console.log('Test 7: Testing UI update functions...');
        
        // Check if key functions exist
        var resetAllTogglesExists = typeof resetAllToggles === 'function';
        var ensureToggleSwitchesColorExists = typeof ensureToggleSwitchesColor === 'function';
        var showNotificationExists = typeof showNotification === 'function';
        
        console.log('resetAllToggles function exists:', resetAllTogglesExists);
        console.log('ensureToggleSwitchesColor function exists:', ensureToggleSwitchesColorExists);
        console.log('showNotification function exists:', showNotificationExists);
        
        testResults.uiUpdates.push({
            test: 'UI Functions',
            passed: resetAllTogglesExists && ensureToggleSwitchesColorExists && showNotificationExists,
            details: `resetAllToggles: ${resetAllTogglesExists}, ensureToggleSwitchesColor: ${ensureToggleSwitchesColorExists}, showNotification: ${showNotificationExists}`
        });
    }

    function displayTestResults() {
        console.log('=== TEST RESULTS ===');
        
        var allCategories = [
            { name: 'Module Toggles', tests: testResults.moduleToggles },
            { name: 'Setting Toggles', tests: testResults.settingToggles },
            { name: 'AJAX Handlers', tests: testResults.ajaxHandlers },
            { name: 'UI Updates', tests: testResults.uiUpdates }
        ];
        
        var totalTests = 0;
        var passedTests = 0;
        
        allCategories.forEach(function(category) {
            console.log(`\n${category.name}:`);
            category.tests.forEach(function(test) {
                totalTests++;
                if (test.passed) passedTests++;
                
                console.log(`  ${test.passed ? '✓' : '✗'} ${test.test}: ${test.details}`);
            });
        });
        
        console.log(`\n=== SUMMARY ===`);
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${totalTests - passedTests}`);
        console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
        
        // Show notification with results
        if (typeof showNotification === 'function') {
            var message = `Toggle Test Complete: ${passedTests}/${totalTests} tests passed (${Math.round((passedTests / totalTests) * 100)}%)`;
            showNotification(passedTests === totalTests ? 'success' : 'info', message, {
                title: 'Toggle Functionality Test',
                duration: 10000
            });
        }
    }

})(jQuery);
