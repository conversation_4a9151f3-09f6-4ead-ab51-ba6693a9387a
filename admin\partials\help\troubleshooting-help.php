<?php
/**
 * Troubleshooting Help Documentation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials/help
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="redco-help-section" id="troubleshooting-help">
    <div class="redco-help-header">
        <h2><?php esc_html_e('Troubleshooting Guide', 'redco-optimizer'); ?></h2>
        <p class="redco-help-description"><?php esc_html_e('Solutions for common issues you might encounter when using Redco Optimizer.', 'redco-optimizer'); ?></p>
    </div>

    <div class="redco-help-content">
        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Common Issues and Solutions', 'redco-optimizer'); ?></h3>

            <h4><?php esc_html_e('White Screen or 500 Error After Enabling Caching', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('This usually indicates a conflict with your server configuration or another plugin.', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Access your site via FTP or your hosting file manager', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Navigate to wp-content/cache/redco-optimizer', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Delete all files in this directory', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('If you cannot access the admin area, add the following line to your wp-config.php file:', 'redco-optimizer'); ?></li>
                <li><code>define('REDCO_OPTIMIZER_DISABLE_CACHE', true);</code></li>
            </ol>

            <h4><?php esc_html_e('JavaScript Errors After Enabling File Optimization', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Some scripts may not work properly when minified or combined.', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Go to the File Optimization tab', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Click on "Exclude Files" next to JavaScript Minification or Combination', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Add the problematic script to the exclusion list (e.g., jquery.js)', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('If you\'re not sure which script is causing the issue, try disabling JavaScript Minification and Combination temporarily', 'redco-optimizer'); ?></li>
            </ol>

            <h4><?php esc_html_e('Broken Layout After Enabling CSS Optimization', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('CSS optimization can sometimes cause layout issues with certain themes.', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Go to the File Optimization tab', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Click on "Exclude Files" next to CSS Minification or Combination', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Add your theme\'s main CSS file to the exclusion list (e.g., style.css)', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('If that doesn\'t help, try disabling CSS Optimization features one by one to identify the cause', 'redco-optimizer'); ?></li>
            </ol>

            <h4><?php esc_html_e('Images Not Loading After Enabling Lazy Loading', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Some themes or plugins may have compatibility issues with lazy loading.', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Go to the Media tab', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Try disabling "Lazy Load CSS Background Images" if enabled', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('If the issue persists, add the specific image classes to the exclusion list', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('For slider images or critical above-the-fold images, always add them to the exclusion list', 'redco-optimizer'); ?></li>
            </ol>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Compatibility Issues', 'redco-optimizer'); ?></h3>

            <h4><?php esc_html_e('WooCommerce Compatibility', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Redco Optimizer is designed to work with WooCommerce, but some settings may need adjustment.', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('Cart, checkout, and my account pages are automatically excluded from caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('If you experience issues with add-to-cart buttons or AJAX functionality, exclude WooCommerce JavaScript files from optimization', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('For product image galleries, consider excluding them from lazy loading', 'redco-optimizer'); ?></li>
            </ul>

            <h4><?php esc_html_e('Membership or Login Plugins', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Sites with membership areas or user-specific content require special configuration.', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('Make sure "Cache Logged-in Users" is disabled in the Caching settings', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Add login and registration pages to the cache exclusion list', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('If using AJAX for login forms, exclude those scripts from JavaScript optimization', 'redco-optimizer'); ?></li>
            </ul>

            <h4><?php esc_html_e('Page Builders', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Some page builders may have compatibility issues with certain optimization features.', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('For Elementor, exclude elementor-frontend.min.js from JavaScript optimization if you experience issues', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('For Divi, you may need to exclude et-builder-modules-script.js', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('When using page builders, test thoroughly after enabling each optimization feature', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Advanced Troubleshooting', 'redco-optimizer'); ?></h3>

            <h4><?php esc_html_e('Enabling Debug Mode', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('For advanced troubleshooting, you can enable debug mode to log more information.', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Go to the Tools tab', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable "Debug Mode"', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Reproduce the issue you\'re experiencing', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Check the error logs in the Tools tab for detailed information', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Remember to disable Debug Mode after troubleshooting to avoid filling up your logs', 'redco-optimizer'); ?></li>
            </ol>

            <h4><?php esc_html_e('Testing in Safe Mode', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('If you\'re experiencing serious issues, you can use Safe Mode to temporarily disable all optimizations.', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Go to the Tools tab', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable "Safe Mode"', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('This will temporarily disable all optimization features without changing your settings', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('If your site works correctly in Safe Mode, re-enable features one by one to identify the cause', 'redco-optimizer'); ?></li>
            </ol>

            <h4><?php esc_html_e('Server Configuration Issues', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Some server configurations may require additional setup for optimal performance.', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('If using Nginx, make sure you have the proper rewrite rules for browser caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('For shared hosting, you may need to reduce the aggressiveness of optimization settings', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('If your server has limited resources, avoid enabling too many optimization features simultaneously', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Check with your hosting provider for specific recommendations for your server environment', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Getting Additional Help', 'redco-optimizer'); ?></h3>
            <p><?php esc_html_e('If you\'ve tried the troubleshooting steps above and still experience issues:', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('Check the FAQ section in this help documentation for answers to common questions', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Contact our support team with details about your issue, including any error messages', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Include information about your WordPress version, theme, active plugins, and server environment', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Export your Redco Optimizer settings from the Tools tab to share with support', 'redco-optimizer'); ?></li>
            </ul>
        </div>
    </div>
</div>
