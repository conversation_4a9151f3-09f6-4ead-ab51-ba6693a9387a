<?php
/**
 * AJAX handlers for Redco Optimizer
 *
 * @link       https://redco.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * AJAX handlers for Redco Optimizer
 *
 * This class defines all AJAX handlers for the plugin.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Ajax {

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        add_action('wp_ajax_redco_get_themes_plugins', array($this, 'get_themes_plugins'));
    }

    /**
     * Get themes and plugins for the Delay JavaScript Execution section
     *
     * @since    1.0.0
     */
    public function get_themes_plugins() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        $data = array(
            'themes' => $this->get_themes(),
            'plugins' => $this->get_plugins()
        );

        wp_send_json_success($data);
    }

    /**
     * Get themes for the Delay JavaScript Execution section
     *
     * @since    1.0.0
     * @return   array    Array of theme data
     */
    private function get_themes() {
        $themes = array();
        
        // Get current theme
        $current_theme = wp_get_theme();
        $theme_name = $current_theme->get('Name');
        $theme_slug = sanitize_title($theme_name);
        
        // Add current theme
        $themes[] = array(
            'name' => $theme_name,
            'slug' => $theme_slug,
            'parent' => false,
            'active' => true
        );
        
        // Check for parent theme
        if ($current_theme->parent()) {
            $parent_theme = $current_theme->parent();
            $parent_name = $parent_theme->get('Name');
            $parent_slug = sanitize_title($parent_name);
            
            $themes[] = array(
                'name' => $parent_name,
                'slug' => $parent_slug,
                'parent' => true,
                'active' => true
            );
        }
        
        // Common theme frameworks
        $theme_frameworks = array(
            'elementor' => 'Elementor',
            'avada' => 'Avada',
            'divi' => 'Divi',
            'astra' => 'Astra',
            'generatepress' => 'GeneratePress',
            'oceanwp' => 'OceanWP'
        );
        
        foreach ($theme_frameworks as $framework_slug => $framework_name) {
            // Check if the framework is likely being used
            $is_used = false;
            
            // Check if it's a plugin that's active
            if (function_exists('is_plugin_active')) {
                if (is_plugin_active($framework_slug . '/' . $framework_slug . '.php') || 
                    is_plugin_active($framework_slug . '-pro/' . $framework_slug . '-pro.php')) {
                    $is_used = true;
                }
            }
            
            // Check if it's the current theme or parent theme
            if (strpos(strtolower($theme_name), strtolower($framework_slug)) !== false ||
                (isset($parent_name) && strpos(strtolower($parent_name), strtolower($framework_slug)) !== false)) {
                $is_used = true;
            }
            
            if ($is_used) {
                $themes[] = array(
                    'name' => $framework_name . ' Framework',
                    'slug' => $framework_slug,
                    'parent' => false,
                    'active' => false
                );
            }
        }
        
        return $themes;
    }

    /**
     * Get plugins for the Delay JavaScript Execution section
     *
     * @since    1.0.0
     * @return   array    Array of plugin data
     */
    private function get_plugins() {
        $plugins = array();
        
        // Common plugins that might need JS exclusions
        $common_plugins = array(
            'woocommerce/woocommerce.php' => 'WooCommerce',
            'elementor/elementor.php' => 'Elementor',
            'contact-form-7/wp-contact-form-7.php' => 'Contact Form 7',
            'wp-rocket/wp-rocket.php' => 'WP Rocket',
            'wpforms-lite/wpforms.php' => 'WPForms Lite',
            'wpforms/wpforms.php' => 'WPForms Pro',
            'gravityforms/gravityforms.php' => 'Gravity Forms',
            'ninja-forms/ninja-forms.php' => 'Ninja Forms',
            'formidable/formidable.php' => 'Formidable Forms',
            'wordpress-seo/wp-seo.php' => 'Yoast SEO',
            'seo-by-rank-math/rank-math.php' => 'Rank Math SEO',
            'all-in-one-seo-pack/all_in_one_seo_pack.php' => 'All in One SEO',
            'jetpack/jetpack.php' => 'Jetpack',
            'wordfence/wordfence.php' => 'Wordfence Security',
            'wp-super-cache/wp-cache.php' => 'WP Super Cache',
            'w3-total-cache/w3-total-cache.php' => 'W3 Total Cache',
            'autoptimize/autoptimize.php' => 'Autoptimize',
            'litespeed-cache/litespeed-cache.php' => 'LiteSpeed Cache'
        );
        
        // Get active plugins
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        $all_plugins = get_plugins();
        $active_plugins = get_option('active_plugins', array());
        
        foreach ($active_plugins as $plugin_path) {
            if (isset($all_plugins[$plugin_path])) {
                $plugin_data = $all_plugins[$plugin_path];
                $plugin_slug = sanitize_title($plugin_data['Name']);
                
                $plugins[] = array(
                    'name' => $plugin_data['Name'],
                    'slug' => $plugin_slug,
                    'active' => true
                );
            }
        }
        
        // Add common plugins that aren't active but might be useful
        foreach ($common_plugins as $plugin_path => $plugin_name) {
            if (!in_array($plugin_path, $active_plugins) && !array_key_exists($plugin_path, $all_plugins)) {
                $plugin_slug = sanitize_title($plugin_name);
                
                // Check if we already added this plugin
                $exists = false;
                foreach ($plugins as $plugin) {
                    if ($plugin['slug'] === $plugin_slug) {
                        $exists = true;
                        break;
                    }
                }
                
                if (!$exists) {
                    $plugins[] = array(
                        'name' => $plugin_name,
                        'slug' => $plugin_slug,
                        'active' => false
                    );
                }
            }
        }
        
        return $plugins;
    }
}

// Initialize the class
new Redco_Optimizer_Ajax();
