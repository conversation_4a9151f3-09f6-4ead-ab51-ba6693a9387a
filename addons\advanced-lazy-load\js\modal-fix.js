/**
 * Advanced Lazy Load Modal Fix
 * Ensures all sections in the Advanced Lazy Load modal are visible
 */

(function($) {
    'use strict';

    // Run when document is ready
    $(document).ready(function() {
        // Add event listener for the Advanced Lazy Load settings button
        $(document).on('click', '.redco-addon-card:contains("Advanced Lazy Load") .redco-addon-settings', function() {
            console.log('Advanced Lazy Load settings button clicked');

            // Wait for the modal to open
            setTimeout(fixAdvancedLazyLoadModal, 300);
            setTimeout(fixAdvancedLazyLoadModal, 600);
            setTimeout(fixAdvancedLazyLoadModal, 1200);
        });

        // Also listen for AJAX success events
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('admin-ajax.php') !== -1) {
                if (settings.data && settings.data.indexOf('advanced-lazy-load') !== -1) {
                    console.log('Advanced Lazy Load AJAX detected');

                    // Wait for the modal to be populated
                    setTimeout(fixAdvancedLazyLoadModal, 300);
                    setTimeout(fixAdvancedLazyLoadModal, 600);
                    setTimeout(fixAdvancedLazyLoadModal, 1200);
                }
            }
        });
    });

    /**
     * Fix the Advanced Lazy Load modal
     */
    function fixAdvancedLazyLoadModal() {
        console.log('Fixing Advanced Lazy Load modal');

        // Check if we're in the Advanced Lazy Load modal
        if ($('.redco-modal:visible').length > 0 &&
            ($('.redco-modal-header h2:contains("Advanced Lazy Load")').length > 0 ||
             $('.redco-modal-content:contains("Lazy Load Settings")').length > 0)) {

            console.log('Advanced Lazy Load modal confirmed');

            // Make the modal taller
            $('.redco-modal').css({
                'max-height': '95vh',
                'height': 'auto',
                'width': '800px',
                'max-width': '95%',
                'display': 'flex',
                'flex-direction': 'column',
                'overflow': 'hidden'
            });

            // Make the content area scrollable
            $('.redco-modal-content').css({
                'max-height': 'calc(95vh - 140px)',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
                'flex': '1 1 auto',
                'padding': '25px',
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Check for missing sections
            var expectedSections = [
                'Lazy Load Settings',
                'Image Settings',
                'Iframe Settings',
                'Video Settings'
            ];

            var missingSections = [];

            expectedSections.forEach(function(section) {
                if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                    missingSections.push(section);
                }
            });

            if (missingSections.length > 0) {
                console.log('Missing sections detected: ' + missingSections.join(', '));

                // Add a debug message to the modal
                if ($('.redco-modal-debug-message').length === 0) {
                    $('<div>')
                        .addClass('redco-modal-debug-message')
                        .html('<div class="redco-modal-debug-warning">' +
                              '<p class="redco-modal-debug-text">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                              '<div class="redco-modal-debug-arrow">↓</div>' +
                              '</div>')
                        .prependTo('.redco-modal-content');
                }

                // Add missing sections
                injectMissingSections(missingSections);
            } else {
                console.log('All sections found');
            }

            // Force all sections to be visible
            $('.redco-modal-section').show().css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'margin-bottom': '25px !important'
            });

            // Add a scroll indicator at the bottom
            if ($('.redco-scroll-indicator-bottom').length === 0) {
                $('<div>')
                    .addClass('redco-scroll-indicator-bottom')
                    .html('<div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-top: 20px; text-align: center;">' +
                          '<p style="font-weight: bold; margin: 0;">End of settings</p>' +
                          '<div style="margin-top: 5px;">Don\'t forget to save your changes!</div>' +
                          '</div>')
                    .appendTo('.redco-modal-content');
            }
        }
    }

    /**
     * Inject missing sections into the modal
     */
    function injectMissingSections(missingSections) {
        console.log('Injecting missing sections: ' + missingSections.join(', '));

        // Get the modal content and footer
        var $modalContent = $('.redco-modal-content');
        var $footer = $('.redco-modal-footer');

        if ($modalContent.length === 0) {
            console.log('Modal content not found');
            return;
        }

        // Add each missing section
        missingSections.forEach(function(section) {
            console.log('Adding section: ' + section);

            var $section = $('<div class="redco-modal-section"></div>');
            $section.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'margin-bottom': '25px',
                'position': 'relative'
            });

            var $heading = $('<h3>' + section + '</h3>');
            $heading.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            $section.append($heading);

            // Add content based on the section
            if (section === 'Lazy Load Settings') {
                addLazyLoadSettingsContent($section);
            } else if (section === 'Image Settings') {
                addImageSettingsContent($section);
            } else if (section === 'Iframe Settings') {
                addIframeSettingsContent($section);
            } else if (section === 'Video Settings') {
                addVideoSettingsContent($section);
            }

            // Add the section to the modal
            if ($footer.length > 0) {
                $footer.before($section);
            } else {
                $modalContent.append($section);
            }
        });
    }

    /**
     * Add Lazy Load Settings content
     */
    function addLazyLoadSettingsContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Enable Lazy Loading</h4>' +
                    '<p>Delay loading of images, iframes, and videos until they are visible.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="enable_lazy_load" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Native Lazy Loading</h4>' +
                    '<p>Use browser\'s native lazy loading when available.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="native_lazy_load" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="lazy_load_threshold">Threshold</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="number" id="lazy_load_threshold" name="lazy_load_threshold" class="redco-input" value="200" min="0" max="1000">' +
                    '<p class="redco-form-help">Distance in pixels from the viewport to start loading.</p>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Image Settings content
     */
    function addImageSettingsContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Lazy Load Images</h4>' +
                    '<p>Enable lazy loading for images.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="lazy_load_images" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Lazy Load Background Images</h4>' +
                    '<p>Enable lazy loading for CSS background images.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="lazy_load_background_images" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="image_placeholder">Image Placeholder</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<select id="image_placeholder" name="image_placeholder" class="redco-select">' +
                        '<option value="transparent" selected>Transparent</option>' +
                        '<option value="low_quality">Low Quality</option>' +
                        '<option value="blur">Blur</option>' +
                        '<option value="spinner">Loading Spinner</option>' +
                    '</select>' +
                    '<p class="redco-form-help">Choose the placeholder to show while images are loading.</p>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Iframe Settings content
     */
    function addIframeSettingsContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Lazy Load Iframes</h4>' +
                    '<p>Enable lazy loading for iframes.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="lazy_load_iframes" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>YouTube Thumbnails</h4>' +
                    '<p>Replace YouTube iframes with thumbnails until clicked.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="youtube_thumbnails" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Video Settings content
     */
    function addVideoSettingsContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Lazy Load Videos</h4>' +
                    '<p>Enable lazy loading for HTML5 videos.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="lazy_load_videos" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Autoplay Videos</h4>' +
                    '<p>Autoplay videos when they enter the viewport.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="autoplay_videos" value="1">' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

})(jQuery);
