<?php
/**
 * Provide a admin area view for the plugin
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="wrap">
    <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
    
    <div class="redco-optimizer-admin-content">
        <div class="redco-optimizer-admin-section">
            <h2><?php esc_html_e( 'Redco Optimizer Settings', 'redco-optimizer' ); ?></h2>
            <p><?php esc_html_e( 'Configure the settings for Redco Optimizer.', 'redco-optimizer' ); ?></p>
            
            <div class="redco-optimizer-admin-card">
                <h3><?php esc_html_e( 'Caching', 'redco-optimizer' ); ?></h3>
                <p><?php esc_html_e( 'Configure caching settings to improve page load times.', 'redco-optimizer' ); ?></p>
                <a href="#" class="button button-primary"><?php esc_html_e( 'Configure', 'redco-optimizer' ); ?></a>
            </div>
            
            <div class="redco-optimizer-admin-card">
                <h3><?php esc_html_e( 'File Optimization', 'redco-optimizer' ); ?></h3>
                <p><?php esc_html_e( 'Configure file optimization settings to reduce file sizes.', 'redco-optimizer' ); ?></p>
                <a href="#" class="button button-primary"><?php esc_html_e( 'Configure', 'redco-optimizer' ); ?></a>
            </div>
            
            <div class="redco-optimizer-admin-card">
                <h3><?php esc_html_e( 'Media Optimization', 'redco-optimizer' ); ?></h3>
                <p><?php esc_html_e( 'Configure media optimization settings to reduce image sizes.', 'redco-optimizer' ); ?></p>
                <a href="#" class="button button-primary"><?php esc_html_e( 'Configure', 'redco-optimizer' ); ?></a>
            </div>
            
            <div class="redco-optimizer-admin-card">
                <h3><?php esc_html_e( 'Database Optimization', 'redco-optimizer' ); ?></h3>
                <p><?php esc_html_e( 'Configure database optimization settings to improve database performance.', 'redco-optimizer' ); ?></p>
                <a href="#" class="button button-primary"><?php esc_html_e( 'Configure', 'redco-optimizer' ); ?></a>
            </div>
        </div>
    </div>
</div>

<style>
.redco-optimizer-admin-content {
    margin-top: 20px;
}

.redco-optimizer-admin-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    padding: 20px;
    margin-bottom: 20px;
}

.redco-optimizer-admin-card {
    background: #f9f9f9;
    border: 1px solid #e5e5e5;
    padding: 15px;
    margin-bottom: 15px;
}

.redco-optimizer-admin-card h3 {
    margin-top: 0;
}
</style>
