/**
 * Font Optimizer Modal Fix
 * Ensures all sections in the Font Optimizer modal are visible
 * and settings are properly saved
 */

(function($) {
    'use strict';

    // Store the current settings to avoid overriding with test values
    var currentSettings = {};

    // Run when document is ready
    $(document).ready(function() {
        // Add event listener for the Font Optimizer settings button
        $(document).on('click', '.redco-addon-card:contains("Font Optimizer") .redco-addon-settings', function() {
            console.log('Font Optimizer settings button clicked');

            // Wait for the modal to open
            setTimeout(fixFontOptimizerModal, 300);
            setTimeout(fixFontOptimizerModal, 600);
            setTimeout(fixFontOptimizerModal, 1200);
        });

        // Also listen for AJAX success events
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('admin-ajax.php') !== -1) {
                if (settings.data && (
                    settings.data.indexOf('font-optimizer') !== -1 ||
                    settings.data.indexOf('redco_optimizer_load_addon_settings') !== -1
                )) {
                    console.log('Font Optimizer AJAX detected');

                    // Store the current settings from the response if available
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response && response.success && response.data && response.data.content) {
                            console.log('Settings data found in AJAX response');
                            // Wait for the modal to be populated
                            setTimeout(function() {
                                fixFontOptimizerModal(true);
                            }, 300);
                            setTimeout(function() {
                                fixFontOptimizerModal(true);
                            }, 600);
                            setTimeout(function() {
                                fixFontOptimizerModal(true);
                            }, 1200);
                        } else {
                            // Regular fix without preserving settings
                            setTimeout(fixFontOptimizerModal, 300);
                            setTimeout(fixFontOptimizerModal, 600);
                            setTimeout(fixFontOptimizerModal, 1200);
                        }
                    } catch (e) {
                        console.error('Error parsing AJAX response:', e);
                        // Regular fix without preserving settings
                        setTimeout(fixFontOptimizerModal, 300);
                        setTimeout(fixFontOptimizerModal, 600);
                        setTimeout(fixFontOptimizerModal, 1200);
                    }
                }
            }
        });

        // Handle form submission
        $(document).on('submit', '.redco-addon-settings-form[data-addon="font-optimizer"]', function(e) {
            console.log('Font Optimizer form submitted');

            // Ensure all form fields are included in the submission
            var $form = $(this);
            var formData = $form.serialize();
            console.log('Form data:', formData);

            // Don't prevent default - let the regular submission handler work
        });
    });

    /**
     * Fix the Font Optimizer modal
     *
     * @param {boolean} preserveSettings - Whether to preserve existing settings
     */
    function fixFontOptimizerModal(preserveSettings) {
        console.log('Fixing Font Optimizer modal, preserveSettings:', preserveSettings);

        // Check if we're in the Font Optimizer modal
        if ($('.redco-modal:visible').length > 0 &&
            ($('.redco-modal-header h2:contains("Font Optimizer")').length > 0 ||
             $('.redco-modal-content:contains("Font Optimization")').length > 0)) {

            console.log('Font Optimizer modal confirmed');

            // Capture existing settings before making changes if preserveSettings is true
            if (preserveSettings) {
                captureExistingSettings();
            }

            // Make the modal taller
            $('.redco-modal').css({
                'max-height': '95vh',
                'height': 'auto',
                'width': '800px',
                'max-width': '95%',
                'display': 'flex',
                'flex-direction': 'column',
                'overflow': 'hidden'
            });

            // Make the content area scrollable
            $('.redco-modal-content').css({
                'max-height': 'calc(95vh - 140px)',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
                'flex': '1 1 auto',
                'padding': '25px',
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Check for missing sections
            var expectedSections = [
                'Font Optimization Settings',
                'Google Fonts Settings',
                'Local Fonts Settings',
                'Font Display Settings'
            ];

            var missingSections = [];

            expectedSections.forEach(function(section) {
                if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                    missingSections.push(section);
                }
            });

            if (missingSections.length > 0) {
                console.log('Missing sections detected: ' + missingSections.join(', '));

                // Add a debug message to the modal
                if ($('.redco-modal-debug-message').length === 0) {
                    $('<div>')
                        .addClass('redco-modal-debug-message')
                        .html('<div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; text-align: center;">' +
                              '<p style="font-weight: bold; margin: 0;">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                              '<div style="margin-top: 10px; font-size: 24px; animation: bounce 1s infinite;">↓</div>' +
                              '</div>' +
                              '<style>@keyframes bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }</style>')
                        .prependTo('.redco-modal-content');
                }

                // Add missing sections
                injectMissingSections(missingSections);
            } else {
                console.log('All sections found');
            }

            // Force all sections to be visible
            $('.redco-modal-section').show().css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'margin-bottom': '25px !important'
            });

            // Restore settings if we captured them
            if (preserveSettings && Object.keys(currentSettings).length > 0) {
                restoreSettings();
            }

            // Add a scroll indicator at the bottom
            if ($('.redco-scroll-indicator-bottom').length === 0) {
                $('<div>')
                    .addClass('redco-scroll-indicator-bottom')
                    .html('<div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-top: 20px; text-align: center;">' +
                          '<p style="font-weight: bold; margin: 0;">End of settings</p>' +
                          '<div style="margin-top: 5px;">Don\'t forget to save your changes!</div>' +
                          '</div>')
                    .appendTo('.redco-modal-content');
            }

            // Add a footer with save button if it doesn't exist
            if ($('.redco-modal-footer').length === 0) {
                console.log('Adding modal footer with save button');

                var $footer = $('<div class="redco-modal-footer"></div>');
                $footer.css({
                    'display': 'flex',
                    'justify-content': 'flex-end',
                    'gap': '10px',
                    'padding': '20px',
                    'border-top': '1px solid var(--border-color)',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                var $saveButton = $('<button type="button" class="redco-button redco-button-primary redco-modal-save">Save Settings</button>');
                var $cancelButton = $('<button type="button" class="redco-button redco-modal-cancel">Cancel</button>');

                $footer.append($cancelButton).append($saveButton);
                $('.redco-modal').append($footer);
            }

            // Ensure the form has the correct data-addon attribute
            $('.redco-addon-settings-form').attr('data-addon', 'font-optimizer');
        }
    }

    /**
     * Capture existing settings from the form
     */
    function captureExistingSettings() {
        console.log('Capturing existing settings');

        // Reset current settings
        currentSettings = {};

        // Get all form inputs
        $('.redco-addon-settings-form input, .redco-addon-settings-form select, .redco-addon-settings-form textarea').each(function() {
            var $input = $(this);
            var name = $input.attr('name');

            if (name) {
                if ($input.is(':checkbox')) {
                    currentSettings[name] = $input.is(':checked');
                } else {
                    currentSettings[name] = $input.val();
                }
            }
        });

        console.log('Captured settings:', currentSettings);
    }

    /**
     * Restore settings to the form
     */
    function restoreSettings() {
        console.log('Restoring settings:', currentSettings);

        // Apply settings to form inputs
        for (var name in currentSettings) {
            var value = currentSettings[name];
            var $input = $('.redco-addon-settings-form [name="' + name + '"]');

            if ($input.length > 0) {
                if ($input.is(':checkbox')) {
                    $input.prop('checked', value);
                } else {
                    $input.val(value);
                }
            }
        }
    }

    /**
     * Inject missing sections into the modal
     */
    function injectMissingSections(missingSections) {
        console.log('Injecting missing sections: ' + missingSections.join(', '));

        // Get the modal content and footer
        var $modalContent = $('.redco-modal-content');
        var $footer = $('.redco-modal-footer');

        if ($modalContent.length === 0) {
            console.log('Modal content not found');
            return;
        }

        // Add each missing section
        missingSections.forEach(function(section) {
            console.log('Adding section: ' + section);

            var $section = $('<div class="redco-modal-section"></div>');
            $section.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'margin-bottom': '25px',
                'position': 'relative'
            });

            var $heading = $('<h3>' + section + '</h3>');
            $heading.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            $section.append($heading);

            // Add content based on the section
            if (section === 'Font Optimization Settings') {
                addFontOptimizationSettingsContent($section);
            } else if (section === 'Google Fonts Settings') {
                addGoogleFontsSettingsContent($section);
            } else if (section === 'Local Fonts Settings') {
                addLocalFontsSettingsContent($section);
            } else if (section === 'Font Display Settings') {
                addFontDisplaySettingsContent($section);
            }

            // Add the section to the modal
            if ($footer.length > 0) {
                $footer.before($section);
            } else {
                $modalContent.append($section);
            }
        });
    }

    /**
     * Add Font Optimization Settings content
     */
    function addFontOptimizationSettingsContent($section) {
        // Get values from current settings or use defaults
        var enableFontOptimizationChecked = currentSettings.enable_font_optimization !== false ? 'checked' : '';
        var optimizeGoogleFontsChecked = currentSettings.optimize_google_fonts !== false ? 'checked' : '';
        var optimizeLocalFontsChecked = currentSettings.optimize_local_fonts !== false ? 'checked' : '';

        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Enable Font Optimization</h4>' +
                    '<p>Optimize web fonts for better performance.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="enable_font_optimization" value="1" ' + enableFontOptimizationChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Optimize Google Fonts</h4>' +
                    '<p>Optimize Google Fonts loading.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="optimize_google_fonts" value="1" ' + optimizeGoogleFontsChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Optimize Local Fonts</h4>' +
                    '<p>Optimize locally hosted fonts.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="optimize_local_fonts" value="1" ' + optimizeLocalFontsChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Google Fonts Settings content
     */
    function addGoogleFontsSettingsContent($section) {
        // Get values from current settings or use defaults
        var selfHostGoogleFontsChecked = currentSettings.self_host_google_fonts ? 'checked' : '';
        var preconnectGoogleFontsChecked = currentSettings.preconnect_google_fonts !== false ? 'checked' : '';

        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Self-host Google Fonts</h4>' +
                    '<p>Download and host Google Fonts locally.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="self_host_google_fonts" value="1" ' + selfHostGoogleFontsChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Preconnect to Google Fonts</h4>' +
                    '<p>Add preconnect for Google Fonts domains.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="preconnect_google_fonts" value="1" ' + preconnectGoogleFontsChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Local Fonts Settings content
     */
    function addLocalFontsSettingsContent($section) {
        // Get values from current settings or use defaults
        var optimizeWoff2Checked = currentSettings.optimize_woff2 !== false ? 'checked' : '';
        var preloadKeyFontsChecked = currentSettings.preload_key_fonts !== false ? 'checked' : '';

        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Optimize WOFF2 Fonts</h4>' +
                    '<p>Prioritize WOFF2 font format for better compression.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="optimize_woff2" value="1" ' + optimizeWoff2Checked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Preload Key Fonts</h4>' +
                    '<p>Preload critical font files.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="preload_key_fonts" value="1" ' + preloadKeyFontsChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Font Display Settings content
     */
    function addFontDisplaySettingsContent($section) {
        // Get values from current settings or use defaults
        var fontDisplayValue = currentSettings.font_display || 'swap';

        // Create option selections based on current values
        var fontDisplayOptions = {
            'swap': fontDisplayValue === 'swap' ? 'selected' : '',
            'auto': fontDisplayValue === 'auto' ? 'selected' : '',
            'block': fontDisplayValue === 'block' ? 'selected' : '',
            'fallback': fontDisplayValue === 'fallback' ? 'selected' : '',
            'optional': fontDisplayValue === 'optional' ? 'selected' : ''
        };

        $section.append(
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="font_display">Font Display Strategy</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<select id="font_display" name="font_display" class="redco-select">' +
                        '<option value="swap" ' + fontDisplayOptions.swap + '>swap</option>' +
                        '<option value="auto" ' + fontDisplayOptions.auto + '>auto</option>' +
                        '<option value="block" ' + fontDisplayOptions.block + '>block</option>' +
                        '<option value="fallback" ' + fontDisplayOptions.fallback + '>fallback</option>' +
                        '<option value="optional" ' + fontDisplayOptions.optional + '>optional</option>' +
                    '</select>' +
                    '<p class="redco-form-help">Choose how fonts are displayed while loading.</p>' +
                '</div>' +
            '</div>'
        );
    }

})(jQuery);
