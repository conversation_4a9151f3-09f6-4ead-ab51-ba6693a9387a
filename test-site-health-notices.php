<?php
/**
 * Test file to verify Site Health Inspector notices are working correctly
 * 
 * This file can be run to test the notice functionality by simulating critical issues.
 * Place this file in the WordPress root directory and access it via browser.
 * 
 * IMPORTANT: Remove this file after testing!
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Site Health Inspector Notice Test</h1>';

// Test 1: Create fake critical issues
echo '<h2>Test 1: Creating fake critical issues</h2>';

$test_results = array(
    'last_scan' => current_time('mysql'),
    'total_issues' => 3,
    'critical_issues' => 2,
    'warning_issues' => 1,
    'info_issues' => 0,
    'categories' => array(
        'plugins' => array(
            array(
                'severity' => 'critical',
                'title' => 'Test Critical Issue 1',
                'description' => 'This is a test critical issue for notice testing.',
                'data' => array()
            )
        ),
        'security' => array(
            array(
                'severity' => 'critical',
                'title' => 'Test Critical Issue 2',
                'description' => 'This is another test critical issue for notice testing.',
                'data' => array()
            )
        ),
        'wordpress' => array(
            array(
                'severity' => 'warning',
                'title' => 'Test Warning Issue',
                'description' => 'This is a test warning issue.',
                'data' => array()
            )
        )
    )
);

// Save test results
update_option('redco_site_scan_results', $test_results);
echo '<p>✅ Created test scan results with 2 critical issues.</p>';

// Test 2: Display current results
echo '<h2>Test 2: Current scan results</h2>';
$current_results = get_option('redco_site_scan_results', array());
echo '<pre>' . print_r($current_results, true) . '</pre>';

// Test 3: Test notice display methods
echo '<h2>Test 3: Testing notice display methods</h2>';

// Load the Site Health Inspector module
$site_health_module = new Redco_Optimizer_Site_Health_Inspector_Module('redco-optimizer', '1.0.0');

echo '<h3>WordPress Global Notice (admin_notices):</h3>';
echo '<div style="background: #f1f1f1; padding: 10px; margin: 10px 0;">';
ob_start();
$site_health_module->display_admin_notice();
$global_notice = ob_get_clean();
echo $global_notice ?: '<p>No global notice displayed (no critical issues or notice conditions not met)</p>';
echo '</div>';

echo '<h3>Plugin Page Notice (redco_optimizer_plugin_notices):</h3>';
echo '<div style="background: #f1f1f1; padding: 10px; margin: 10px 0;">';
ob_start();
$site_health_module->display_plugin_notice();
$plugin_notice = ob_get_clean();
echo $plugin_notice ?: '<p>No plugin notice displayed (no critical issues or notice conditions not met)</p>';
echo '</div>';

// Test 4: Clear test data
echo '<h2>Test 4: Cleanup</h2>';
echo '<p><a href="?clear=1" style="background: #dc3232; color: white; padding: 10px; text-decoration: none;">Clear Test Data</a></p>';

if (isset($_GET['clear']) && $_GET['clear'] == '1') {
    delete_option('redco_site_scan_results');
    echo '<p>✅ Test data cleared. Notices should no longer appear.</p>';
    echo '<p><a href="?" style="background: #0073aa; color: white; padding: 10px; text-decoration: none;">Run Test Again</a></p>';
}

echo '<h2>Instructions:</h2>';
echo '<ol>';
echo '<li>After running this test, visit your WordPress admin area to see the global notice</li>';
echo '<li>Visit the Redco Optimizer plugin page to see the plugin-specific notice at the top</li>';
echo '<li>Click "Clear Test Data" to remove the fake critical issues</li>';
echo '<li><strong>IMPORTANT:</strong> Delete this test file after testing!</li>';
echo '</ol>';

echo '<p><strong>Plugin Page URL:</strong> <a href="' . admin_url('admin.php?page=redco-optimizer') . '" target="_blank">' . admin_url('admin.php?page=redco-optimizer') . '</a></p>';
echo '<p><strong>WordPress Admin:</strong> <a href="' . admin_url() . '" target="_blank">' . admin_url() . '</a></p>';
?>
