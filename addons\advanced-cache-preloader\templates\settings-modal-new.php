<?php
/**
 * Advanced Cache Preloader - Settings Modal Template
 *
 * @package Redco_Optimizer
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Get settings
$settings = get_option('redco_advanced_cache_preloader_settings', array(
    'enabled' => false,
    'preload_homepage' => true,
    'preload_posts' => true,
    'preload_pages' => true,
    'preload_categories' => true,
    'preload_tags' => false,
    'preload_custom_post_types' => false,
    'custom_post_types' => array(),
    'preload_schedule' => 'daily',
    'preload_on_save' => true,
    'preload_throttle' => 500,
    'preload_limit' => 100,
    'sitemap_based' => false,
    'sitemap_url' => '',
    'url_list' => '',
    'exclude_urls' => '',
    'mobile_user_agent' => false,
    'user_agent_string' => 'Mozilla/5.0 (compatible; Redco-Preloader/1.0; +' . home_url() . ')',
    'preload_priority' => 'standard',
    'log' => array(),
));
?>

<form method="post" action="" class="redco-addon-settings-form" data-addon="advanced-cache-preloader">
    <?php wp_nonce_field('redco_advanced_cache_preloader_save_settings', 'redco_advanced_cache_preloader_nonce'); ?>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Cache Preloader Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Enable Cache Preloader', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Automatically preload your cache to ensure fast page loading for your visitors.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="enabled" value="1" <?php checked($settings['enabled'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Homepage', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload your homepage to ensure it loads quickly for visitors.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_homepage" value="1" <?php checked($settings['preload_homepage'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload on Save', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Automatically preload cache when content is saved or updated.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_on_save" value="1" <?php checked($settings['preload_on_save'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Use Sitemap for Preloading', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Use your XML sitemap to determine which URLs to preload.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="sitemap_based" value="1" <?php checked($settings['sitemap_based'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row" id="sitemap-url-row" style="<?php echo $settings['sitemap_based'] ? '' : 'display: none;'; ?>">
            <div class="redco-form-label">
                <label for="sitemap_url"><?php esc_html_e('Sitemap URL', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="text" id="sitemap_url" name="sitemap_url" class="redco-input" value="<?php echo esc_attr($settings['sitemap_url']); ?>" placeholder="https://example.com/sitemap.xml">
                <p class="redco-form-help"><?php esc_html_e('Enter the full URL to your XML sitemap.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Schedule Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_schedule"><?php esc_html_e('Preload Schedule', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_schedule" name="preload_schedule" class="redco-select">
                    <option value="hourly" <?php selected($settings['preload_schedule'], 'hourly'); ?>><?php esc_html_e('Hourly', 'redco-optimizer'); ?></option>
                    <option value="twicedaily" <?php selected($settings['preload_schedule'], 'twicedaily'); ?>><?php esc_html_e('Twice Daily', 'redco-optimizer'); ?></option>
                    <option value="daily" <?php selected($settings['preload_schedule'], 'daily'); ?>><?php esc_html_e('Daily', 'redco-optimizer'); ?></option>
                    <option value="weekly" <?php selected($settings['preload_schedule'], 'weekly'); ?>><?php esc_html_e('Weekly', 'redco-optimizer'); ?></option>
                </select>
                <p class="redco-form-help"><?php esc_html_e('Select how often the cache preloader should run.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Advanced Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_throttle"><?php esc_html_e('Preload Throttle', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="number" id="preload_throttle" name="preload_throttle" class="redco-input" value="<?php echo esc_attr($settings['preload_throttle']); ?>" min="0" step="100">
                <p class="redco-form-help"><?php esc_html_e('Time in milliseconds to wait between preloading URLs. Higher values reduce server load.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_limit"><?php esc_html_e('Preload Limit', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="number" id="preload_limit" name="preload_limit" class="redco-input" value="<?php echo esc_attr($settings['preload_limit']); ?>" min="1">
                <p class="redco-form-help"><?php esc_html_e('Maximum number of URLs to preload in each batch. Lower values reduce server load.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Mobile User Agent', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Use a mobile user agent when preloading to generate mobile-specific cache.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="mobile_user_agent" value="1" <?php checked($settings['mobile_user_agent'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="user_agent_string"><?php esc_html_e('User Agent String', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="text" id="user_agent_string" name="user_agent_string" class="redco-input" value="<?php echo esc_attr($settings['user_agent_string']); ?>">
                <p class="redco-form-help"><?php esc_html_e('Custom user agent string for the preloader.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="exclude_urls"><?php esc_html_e('Exclude URLs', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <textarea id="exclude_urls" name="exclude_urls" class="redco-textarea" placeholder="/example-page/&#10;/another-page/"><?php echo esc_textarea($settings['exclude_urls']); ?></textarea>
                <p class="redco-form-help"><?php esc_html_e('Enter URLs to exclude from preloading, one per line. Use relative paths (e.g., /example-page/).', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="url_list"><?php esc_html_e('Additional URLs', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <textarea id="url_list" name="url_list" class="redco-textarea" placeholder="<?php echo esc_url(home_url('/sample-page/')); ?>&#10;<?php echo esc_url(home_url('/about/')); ?>"><?php echo esc_textarea($settings['url_list']); ?></textarea>
                <p class="redco-form-help"><?php echo sprintf(esc_html__('Enter additional URLs to preload, one per line. Use full URLs (e.g., %s).', 'redco-optimizer'), esc_url(home_url('/page/'))); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Content Type Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Posts', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload all published posts.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_posts" value="1" <?php checked($settings['preload_posts'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Pages', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload all published pages.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_pages" value="1" <?php checked($settings['preload_pages'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Categories', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload all category archives.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_categories" value="1" <?php checked($settings['preload_categories'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Tags', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload all tag archives.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_tags" value="1" <?php checked($settings['preload_tags'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Custom Post Types', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload custom post types.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_custom_post_types" value="1" <?php checked($settings['preload_custom_post_types'], true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <?php
        // Get all public custom post types
        $custom_post_types = get_post_types(array('public' => true, '_builtin' => false), 'objects');

        if (!empty($custom_post_types)) {
            echo '<div id="custom-post-types-row" style="' . ($settings['preload_custom_post_types'] ? '' : 'display: none;') . '">';

            foreach ($custom_post_types as $post_type) {
                $checked = in_array($post_type->name, (array) $settings['custom_post_types']) ? 'checked' : '';
                ?>
                <div class="redco-toggle-row" style="padding-left: 20px;">
                    <div class="redco-toggle-info">
                        <h4><?php echo esc_html($post_type->labels->name); ?></h4>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="custom_post_types[]" value="<?php echo esc_attr($post_type->name); ?>" <?php echo $checked; ?>>
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>
                <?php
            }

            echo '</div>';
        }
        ?>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Priority Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_priority"><?php esc_html_e('Preload Priority', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_priority" name="preload_priority" class="redco-select">
                    <option value="low" <?php selected($settings['preload_priority'], 'low'); ?>><?php esc_html_e('Low - Less server load', 'redco-optimizer'); ?></option>
                    <option value="standard" <?php selected($settings['preload_priority'], 'standard'); ?>><?php esc_html_e('Standard - Balanced', 'redco-optimizer'); ?></option>
                    <option value="high" <?php selected($settings['preload_priority'], 'high'); ?>><?php esc_html_e('High - Faster preloading', 'redco-optimizer'); ?></option>
                </select>
                <p class="redco-form-help"><?php esc_html_e('Set the priority for the preloader. Higher priority means faster preloading but more server load.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-footer">
        <button type="submit" class="redco-button redco-button-primary">
            <span class="dashicons dashicons-yes"></span>
            <?php esc_html_e('Save Settings', 'redco-optimizer'); ?>
        </button>
        <button type="button" class="redco-button redco-button-secondary redco-modal-cancel">
            <?php esc_html_e('Cancel', 'redco-optimizer'); ?>
        </button>
    </div>
</form>

<script>
jQuery(document).ready(function($) {
    // Toggle sitemap URL field
    $('input[name="sitemap_based"]').on('change', function() {
        if ($(this).is(':checked')) {
            $('#sitemap-url-row').show();
        } else {
            $('#sitemap-url-row').hide();
        }
    });

    // Toggle custom post types
    $('input[name="preload_custom_post_types"]').on('change', function() {
        if ($(this).is(':checked')) {
            $('#custom-post-types-row').show();
        } else {
            $('#custom-post-types-row').hide();
        }
    });
});
</script>
