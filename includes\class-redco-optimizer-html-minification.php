<?php
/**
 * The HTML minification functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The HTML minification functionality of the plugin.
 *
 * Handles HTML minification to reduce file size and improve page load times.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_HTML_Minification {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for HTML minification.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for HTML minification.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
    }

    /**
     * Get HTML minification settings.
     *
     * @since    1.0.0
     * @return   array    The HTML minification settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_file_optimization_settings', array() );
        
        // Default settings
        $defaults = array(
            'minify_html' => 0,
            'minify_html_comments' => 1,
            'minify_html_inline_css' => 1,
            'minify_html_inline_js' => 1,
            'minify_html_exclusions' => '',
        );
        
        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize HTML minification.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if HTML minification is enabled
        if ( ! $this->settings['minify_html'] ) {
            return;
        }

        // Add filter to process HTML
        add_filter( 'redco_buffer', array( $this, 'process_html' ), 20 );
    }

    /**
     * Process HTML to minify it.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The minified HTML content.
     */
    public function process_html( $html ) {
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }
        
        // Check if URL is excluded
        if ( $this->is_url_excluded() ) {
            return $html;
        }
        
        // Minify HTML
        $html = $this->minify_html( $html );
        
        return $html;
    }

    /**
     * Check if current URL is excluded from HTML minification.
     *
     * @since    1.0.0
     * @return   bool    True if URL is excluded, false otherwise.
     */
    private function is_url_excluded() {
        // Get current URL
        $url = $_SERVER['REQUEST_URI'];
        
        // Get exclusions
        $exclusions = array();
        
        if ( ! empty( $this->settings['minify_html_exclusions'] ) ) {
            $exclusions = explode( "\n", $this->settings['minify_html_exclusions'] );
            $exclusions = array_map( 'trim', $exclusions );
        }
        
        // Check if URL matches any exclusion
        foreach ( $exclusions as $exclusion ) {
            if ( empty( $exclusion ) ) {
                continue;
            }
            
            if ( strpos( $url, $exclusion ) !== false ) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Minify HTML content.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content to minify.
     * @return   string    The minified HTML content.
     */
    private function minify_html( $html ) {
        // Remove HTML comments if enabled
        if ( $this->settings['minify_html_comments'] ) {
            $html = $this->remove_html_comments( $html );
        }
        
        // Minify inline CSS if enabled
        if ( $this->settings['minify_html_inline_css'] ) {
            $html = $this->minify_inline_css( $html );
        }
        
        // Minify inline JS if enabled
        if ( $this->settings['minify_html_inline_js'] ) {
            $html = $this->minify_inline_js( $html );
        }
        
        // Minify HTML
        $html = $this->minify_html_content( $html );
        
        return $html;
    }

    /**
     * Remove HTML comments.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The HTML content without comments.
     */
    private function remove_html_comments( $html ) {
        // Preserve conditional comments and MSIE conditional statements
        $html = preg_replace_callback(
            '/<!--([\s\S]*?)-->/',
            function( $matches ) {
                // Skip conditional comments
                if ( preg_match( '/^\[if|<!\[endif\]/', trim( $matches[1] ) ) ) {
                    return $matches[0];
                }
                
                // Remove normal comments
                return '';
            },
            $html
        );
        
        return $html;
    }

    /**
     * Minify inline CSS.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The HTML content with minified inline CSS.
     */
    private function minify_inline_css( $html ) {
        // Find all style tags
        return preg_replace_callback(
            '/<style[^>]*>([\s\S]*?)<\/style>/',
            function( $matches ) {
                // Minify CSS content
                $css = $matches[1];
                
                // Remove comments
                $css = preg_replace( '/\/\*[\s\S]*?\*\//', '', $css );
                
                // Remove whitespace
                $css = preg_replace( '/\s+/', ' ', $css );
                $css = preg_replace( '/\s*({|}|;|:|,)\s*/', '$1', $css );
                $css = preg_replace( '/;}/', '}', $css );
                
                return '<style>' . trim( $css ) . '</style>';
            },
            $html
        );
    }

    /**
     * Minify inline JavaScript.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The HTML content with minified inline JavaScript.
     */
    private function minify_inline_js( $html ) {
        // Find all script tags
        return preg_replace_callback(
            '/<script[^>]*>([\s\S]*?)<\/script>/',
            function( $matches ) {
                // Skip if it has src attribute
                if ( strpos( $matches[0], 'src=' ) !== false ) {
                    return $matches[0];
                }
                
                // Minify JS content
                $js = $matches[1];
                
                // Remove comments (single line and multi-line)
                $js = preg_replace( '/(\/\/[^\n]*|\/\*[\s\S]*?\*\/)/', '', $js );
                
                // Remove whitespace
                $js = preg_replace( '/\s+/', ' ', $js );
                
                return '<script>' . trim( $js ) . '</script>';
            },
            $html
        );
    }

    /**
     * Minify HTML content.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The minified HTML content.
     */
    private function minify_html_content( $html ) {
        // Remove whitespace between HTML tags
        $html = preg_replace( '/>\s+</', '><', $html );
        
        // Remove whitespace at the start of tags
        $html = preg_replace( '/\s+>/', '>', $html );
        
        // Remove whitespace at the end of tags
        $html = preg_replace( '/<\s+/', '<', $html );
        
        // Remove multiple whitespace
        $html = preg_replace( '/\s{2,}/', ' ', $html );
        
        return $html;
    }
}
