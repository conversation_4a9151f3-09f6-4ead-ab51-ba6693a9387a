/**
 * Help Documentation Styles
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/css
 */

/* Help Container */
.redco-help-container {
    display: flex;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    overflow: hidden;
    min-height: 600px;
}

/* Help Sidebar */
.redco-help-sidebar {
    width: 280px;
    background: #f8f9fa;
    border-right: 1px solid #e2e4e7;
    padding: 0;
    flex-shrink: 0;
}

.redco-help-search {
    padding: 15px;
    border-bottom: 1px solid #e2e4e7;
}

.redco-help-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.redco-help-topics {
    margin: 0;
    padding: 0;
    list-style: none;
}

.redco-help-topic {
    padding: 12px 15px;
    border-bottom: 1px solid #e2e4e7;
    cursor: pointer;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
}

.redco-help-topic:hover {
    background: #f0f0f1;
}

.redco-help-topic.active {
    background: #00A66B;
    color: #fff;
}

.redco-help-topic .dashicons {
    margin-right: 10px;
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Help Content Container */
.redco-help-content-container {
    flex-grow: 1;
    padding: 0;
    overflow-y: auto;
    position: relative;
}

.redco-help-content {
    display: none !important;
    padding: 30px;
}

.redco-help-content.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-help-placeholder {
    padding: 30px;
    text-align: center;
    color: #666;
}

/* Help Section Styling */
.redco-help-section {
    margin-bottom: 40px;
}

.redco-help-header {
    margin-bottom: 25px;
    border-bottom: 1px solid #e2e4e7;
    padding-bottom: 15px;
}

.redco-help-header h2 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 24px;
    color: #23282d;
}

.redco-help-description {
    font-size: 16px;
    color: #555;
    margin-bottom: 0;
}

.redco-help-subsection {
    margin-bottom: 30px;
}

.redco-help-subsection h3 {
    font-size: 20px;
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

.redco-help-subsection h4 {
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 10px;
    color: #23282d;
}

.redco-help-subsection p {
    margin-top: 0;
    margin-bottom: 15px;
    line-height: 1.6;
    color: #444;
}

.redco-help-subsection ul,
.redco-help-subsection ol {
    margin-top: 0;
    margin-bottom: 20px;
    padding-left: 20px;
}

.redco-help-subsection li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.redco-help-subsection strong {
    font-weight: 600;
    color: #23282d;
}

/* Image styling */
.redco-help-image {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 15px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.redco-help-image-caption {
    font-size: 13px;
    color: #666;
    text-align: center;
    margin-top: 5px;
    margin-bottom: 20px;
}

/* Table styling */
.redco-help-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.redco-help-table th,
.redco-help-table td {
    padding: 10px;
    text-align: left;
    border: 1px solid #e2e4e7;
}

.redco-help-table th {
    background: #f8f9fa;
    font-weight: 600;
}

.redco-help-table tr:nth-child(even) {
    background: #f8f9fa;
}

/* Note boxes */
.redco-help-note {
    background: #f0f6fc;
    border-left: 4px solid #72aee6;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 4px 4px 0;
}

.redco-help-warning {
    background: #fcf9e8;
    border-left: 4px solid #dba617;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 4px 4px 0;
}

.redco-help-tip {
    background: #e6f6ef;
    border-left: 4px solid #00A66B;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 4px 4px 0;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .redco-help-container {
        flex-direction: column;
    }

    .redco-help-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e2e4e7;
    }

    .redco-help-content {
        padding: 20px;
    }
}
