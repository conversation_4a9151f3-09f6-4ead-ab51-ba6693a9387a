<?php
/**
 * Premium features management class
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Premium features management class.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Team <<EMAIL>>
 */
class Redco_Optimizer_Premium {

    /**
     * The single instance of the class.
     *
     * @since    1.0.0
     * @access   private
     * @var      Redco_Optimizer_Premium    $instance    The single instance of the class.
     */
    private static $instance = null;

    /**
     * Premium features configuration.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $premium_features    Array of premium features.
     */
    private $premium_features = array();

    /**
     * Get the single instance of the class.
     *
     * @since    1.0.0
     * @return   Redco_Optimizer_Premium    The single instance of the class.
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    private function __construct() {
        $this->define_premium_features();
    }

    /**
     * Define premium features configuration.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_premium_features() {
        $this->premium_features = array(
            // Cache Module - Enterprise Features
            'cache' => array(
                'separate_mobile_cache' => array(
                    'title' => __('Separate Mobile Cache', 'redco-optimizer'),
                    'description' => __('Create separate cache files for mobile devices for optimal performance.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'cache_logged_in_users' => array(
                    'title' => __('Cache for Logged-in Users', 'redco-optimizer'),
                    'description' => __('Enable caching for logged-in users with advanced user role management.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'cache_preloading' => array(
                    'title' => __('Advanced Cache Preloading', 'redco-optimizer'),
                    'description' => __('Intelligent cache warming and preloading strategies.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                )
            ),

            // File Optimization Module - Enterprise Features
            'file-optimization' => array(
                'critical_css' => array(
                    'title' => __('Critical CSS Generation', 'redco-optimizer'),
                    'description' => __('Automatically generate and inline critical CSS for above-the-fold content.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'async_css' => array(
                    'title' => __('Async CSS Loading', 'redco-optimizer'),
                    'description' => __('Load CSS files asynchronously to prevent render-blocking.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'self_host_google_fonts' => array(
                    'title' => __('Self-Host Google Fonts', 'redco-optimizer'),
                    'description' => __('Download and serve Google Fonts locally for better performance and privacy.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'advanced_minification' => array(
                    'title' => __('Advanced Minification', 'redco-optimizer'),
                    'description' => __('Enterprise-grade minification with advanced optimization algorithms.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'optimize_css_delivery' => array(
                    'title' => __('Optimize CSS Delivery', 'redco-optimizer'),
                    'description' => __('Advanced CSS delivery optimization for improved page load times.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                )
            ),

            // Media Module - Enterprise Features
            'media' => array(
                'bulk_optimization' => array(
                    'title' => __('Bulk Image Optimization', 'redco-optimizer'),
                    'description' => __('Optimize all existing images in your media library with advanced algorithms.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'lazyload_videos' => array(
                    'title' => __('Lazy Load Videos', 'redco-optimizer'),
                    'description' => __('Lazy load embedded videos to improve page load times.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'lazyload_iframes' => array(
                    'title' => __('Lazy Load iFrames', 'redco-optimizer'),
                    'description' => __('Lazy load iframes and embedded content for better performance.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'lazyload_css_bg' => array(
                    'title' => __('Lazy Load CSS Backgrounds', 'redco-optimizer'),
                    'description' => __('Lazy load CSS background images for improved loading performance.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'advanced_webp' => array(
                    'title' => __('Advanced WebP Conversion', 'redco-optimizer'),
                    'description' => __('Advanced WebP conversion with fallback support and quality optimization.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                )
            ),

            // CDN Module - Enterprise Features
            'cdn' => array(
                'multi_cdn_providers' => array(
                    'title' => __('Multiple CDN Providers', 'redco-optimizer'),
                    'description' => __('Support for Cloudflare, BunnyCDN, KeyCDN, and other enterprise CDN providers.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'cdn_analytics' => array(
                    'title' => __('CDN Analytics', 'redco-optimizer'),
                    'description' => __('Detailed analytics and monitoring for your CDN usage and performance.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'advanced_cdn_rules' => array(
                    'title' => __('Advanced CDN Rules', 'redco-optimizer'),
                    'description' => __('Create custom rules for different file types and content delivery strategies.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                )
            ),

            // Database Module - Enterprise Features
            'database' => array(
                'schedule_cleanup' => array(
                    'title' => __('Scheduled Database Cleanup', 'redco-optimizer'),
                    'description' => __('Automatically clean and optimize your database on a scheduled basis.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'advanced_optimization' => array(
                    'title' => __('Advanced Database Optimization', 'redco-optimizer'),
                    'description' => __('Enterprise-level database optimization with detailed analysis and reporting.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'cleanup_orphaned_data' => array(
                    'title' => __('Orphaned Data Cleanup', 'redco-optimizer'),
                    'description' => __('Clean up orphaned meta data, term relationships, and unused database entries.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                )
            ),

            // Preload Module - Enterprise Features
            'preload' => array(
                'prefetch_dns' => array(
                    'title' => __('DNS Prefetching', 'redco-optimizer'),
                    'description' => __('Prefetch DNS lookups for external resources to reduce latency.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'preload_links' => array(
                    'title' => __('Link Preloading', 'redco-optimizer'),
                    'description' => __('Intelligently preload links that users are likely to visit next.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'advanced_preload_strategies' => array(
                    'title' => __('Advanced Preload Strategies', 'redco-optimizer'),
                    'description' => __('Machine learning-based preloading with user behavior analysis.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                )
            ),

            // Site Health Module - Enterprise Features
            'site-health-inspector' => array(
                'advanced_security_scans' => array(
                    'title' => __('Advanced Security Scans', 'redco-optimizer'),
                    'description' => __('Comprehensive security scanning with malware detection and vulnerability assessment.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'performance_monitoring' => array(
                    'title' => __('Performance Monitoring', 'redco-optimizer'),
                    'description' => __('Real-time performance monitoring with detailed analytics and alerts.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                ),
                'detailed_reports' => array(
                    'title' => __('Detailed Reports', 'redco-optimizer'),
                    'description' => __('Generate comprehensive reports with actionable insights and recommendations.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                )
            ),

            // Heartbeat Module - Enterprise Features
            'heartbeat' => array(
                'advanced_heartbeat_control' => array(
                    'title' => __('Advanced Heartbeat Control', 'redco-optimizer'),
                    'description' => __('Fine-grained control over WordPress Heartbeat API with custom intervals and conditions.', 'redco-optimizer'),
                    'status' => 'coming_soon'
                )
            )
        );

        // Allow developers to filter premium features
        $this->premium_features = apply_filters('redco_optimizer_premium_features', $this->premium_features);
    }

    /**
     * Check if user has premium access.
     *
     * @since    1.0.0
     * @return   bool    True if user has premium access, false otherwise.
     */
    public function has_premium_access() {
        // Check license status
        $license_status = get_option('redco_optimizer_license_status', '');
        $license_key = get_option('redco_optimizer_license_key', '');

        // For initial launch, always return false (completely free)
        // Later this will check for valid premium subscription
        if ($license_status === 'valid' && !empty($license_key)) {
            return true;
        }

        return false;
    }

    /**
     * Check if a specific feature is premium.
     *
     * @since    1.0.0
     * @param    string    $module     The module name.
     * @param    string    $feature    The feature name.
     * @return   bool      True if feature is premium, false otherwise.
     */
    public function is_premium_feature($module, $feature) {
        return isset($this->premium_features[$module][$feature]);
    }

    /**
     * Get premium feature info.
     *
     * @since    1.0.0
     * @param    string    $module     The module name.
     * @param    string    $feature    The feature name.
     * @return   array|false    Feature info array or false if not found.
     */
    public function get_premium_feature_info($module, $feature) {
        if ($this->is_premium_feature($module, $feature)) {
            return $this->premium_features[$module][$feature];
        }
        return false;
    }

    /**
     * Get all premium features for a module.
     *
     * @since    1.0.0
     * @param    string    $module    The module name.
     * @return   array     Array of premium features for the module.
     */
    public function get_module_premium_features($module) {
        return isset($this->premium_features[$module]) ? $this->premium_features[$module] : array();
    }

    /**
     * Get premium feature status (coming_soon or premium).
     *
     * @since    1.0.0
     * @param    string    $module     The module name.
     * @param    string    $feature    The feature name.
     * @return   string    Feature status.
     */
    public function get_feature_status($module, $feature) {
        $feature_info = $this->get_premium_feature_info($module, $feature);
        if ($feature_info) {
            return $feature_info['status'];
        }
        return 'free';
    }

    /**
     * Check if feature should be disabled.
     *
     * @since    1.0.0
     * @param    string    $module     The module name.
     * @param    string    $feature    The feature name.
     * @return   bool      True if feature should be disabled, false otherwise.
     */
    public function should_disable_feature($module, $feature) {
        if (!$this->is_premium_feature($module, $feature)) {
            return false; // Free feature, don't disable
        }

        // If user has premium access, don't disable
        if ($this->has_premium_access()) {
            return false;
        }

        // Premium feature without premium access, should be disabled
        return true;
    }

    /**
     * Get premium badge HTML.
     *
     * @since    1.0.0
     * @param    string    $module     The module name.
     * @param    string    $feature    The feature name.
     * @return   string    Premium badge HTML.
     */
    public function get_premium_badge($module, $feature) {
        if (!$this->is_premium_feature($module, $feature)) {
            return '';
        }

        $status = $this->get_feature_status($module, $feature);
        $badge_class = 'redco-premium-badge';
        $badge_text = '';

        switch ($status) {
            case 'coming_soon':
                $badge_class .= ' redco-coming-soon-badge';
                $badge_text = __('Coming Soon', 'redco-optimizer');
                break;
            case 'premium':
                $badge_class .= ' redco-premium-only-badge';
                $badge_text = __('Pro', 'redco-optimizer');
                break;
        }

        return '<span class="' . esc_attr($badge_class) . '">' . esc_html($badge_text) . '</span>';
    }

    /**
     * Get all premium features across all modules.
     *
     * @since    1.0.0
     * @return   array    Array of all premium features organized by module.
     */
    public function get_all_premium_features() {
        $organized_features = array();

        foreach ($this->premium_features as $module => $features) {
            $organized_features[$module] = array();

            foreach ($features as $feature_key => $feature_config) {
                $organized_features[$module][$feature_key] = array(
                    'name' => $feature_config['title'],
                    'description' => $feature_config['description'],
                    'status' => $feature_config['status']
                );
            }
        }

        return $organized_features;
    }
}
