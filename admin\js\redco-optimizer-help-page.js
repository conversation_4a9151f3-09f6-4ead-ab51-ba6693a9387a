/**
 * Help page specific JavaScript.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Prevent any add-on settings modals from appearing
        $('.redco-modal-overlay').remove();
        $(document).off('click', '.redco-addon-configure');
        
        // Help topic navigation
        $('.redco-help-topic').on('click', function() {
            var topic = $(this).data('topic');
            
            // Update active topic in sidebar
            $('.redco-help-topic').removeClass('active').css('background', '').css('color', '');
            $(this).addClass('active').css('background', '#00A66B').css('color', '#fff');
            
            // Show selected content
            $('.redco-help-content-section').hide();
            $('#redco-help-' + topic).show();
        });
        
        // Search functionality
        $('#redco-help-search-input').on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();
            
            if (searchTerm.length < 2) {
                // Reset if search term is too short
                $('.redco-help-topic:first').click();
                return;
            }
            
            // Search in all content sections
            var foundInSection = false;
            $('.redco-help-content-section').each(function() {
                var sectionText = $(this).text().toLowerCase();
                var sectionId = $(this).attr('id');
                var topicId = sectionId.replace('redco-help-', '');
                
                if (sectionText.indexOf(searchTerm) !== -1) {
                    // Show this section if it contains the search term
                    $('.redco-help-content-section').hide();
                    $(this).show();
                    
                    // Update sidebar active state
                    $('.redco-help-topic').removeClass('active').css('background', '').css('color', '');
                    $('.redco-help-topic[data-topic="' + topicId + '"]').addClass('active').css('background', '#00A66B').css('color', '#fff');
                    
                    foundInSection = true;
                    return false; // Break the loop after finding the first match
                }
            });
            
            // If no matches found, show a message
            if (!foundInSection) {
                $('.redco-help-content-section').hide();
                
                // Remove existing no results message if it exists
                $('#redco-help-no-results').remove();
                
                // Add no results message
                $('.redco-help-content').append('<div id="redco-help-no-results" class="redco-help-content-section" style="display:block;"><h3>No results found</h3><p>Try a different search term.</p></div>');
            } else {
                $('#redco-help-no-results').remove();
            }
        });
        
        console.log('Help page JavaScript initialized');
    });

})(jQuery);
