<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redco Optimizer Dashboard</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            background-color: #f0f0f1;
        }
        .dashboard {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .dashboard-header {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 26px 30px;
            margin-bottom: 20px;
            position: relative;
        }
        .dashboard-header h1 {
            margin: 0;
            font-size: 23px;
            font-weight: 400;
            color: #1d2327;
        }
        .dashboard-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #00A66B 0%, #00c77f 100%);
        }
        .dashboard-content {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }
        .dashboard-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            position: relative;
        }
        .dashboard-card h2 {
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1d2327;
            display: flex;
            align-items: center;
        }
        .dashboard-card-icon {
            width: 24px;
            height: 24px;
            background-color: #00A66B;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: white;
            font-weight: bold;
        }
        .dashboard-card-content {
            margin-top: 15px;
        }
        .dashboard-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        .dashboard-stat {
            text-align: center;
            flex: 1;
        }
        .dashboard-stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #00A66B;
        }
        .dashboard-stat-label {
            font-size: 12px;
            color: #646970;
        }
        .dashboard-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        .dashboard-button {
            padding: 8px 12px;
            background-color: #f0f0f1;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            text-align: center;
            flex: 1;
        }
        .dashboard-button-primary {
            background-color: #00A66B;
            color: white;
            border-color: #00A66B;
        }
        .performance-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#00A66B 0% 75%, #f0f0f1 75% 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            position: relative;
        }
        .performance-circle::before {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 50%;
        }
        .performance-score {
            position: relative;
            font-size: 24px;
            font-weight: 600;
            color: #00A66B;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .toggle-slider {
            background-color: #00A66B;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        .module-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .module-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f1;
        }
        .module-item:last-child {
            border-bottom: none;
        }
        .module-name {
            display: flex;
            align-items: center;
        }
        .module-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            color: #00A66B;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="dashboard-header">
            <h1>Redco Optimizer</h1>
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-card">
                <h2><span class="dashboard-card-icon">P</span>Performance Score</h2>
                <div class="dashboard-card-content">
                    <div class="performance-circle">
                        <div class="performance-score">75%</div>
                    </div>
                    <div class="dashboard-actions">
                        <div class="dashboard-button">View Details</div>
                        <div class="dashboard-button dashboard-button-primary">Optimize</div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card">
                <h2><span class="dashboard-card-icon">Q</span>Quick Actions</h2>
                <div class="dashboard-card-content">
                    <div class="dashboard-actions">
                        <div class="dashboard-button">Clear Cache</div>
                        <div class="dashboard-button">Optimize Images</div>
                    </div>
                    <div class="dashboard-actions" style="margin-top: 10px;">
                        <div class="dashboard-button">Clean Database</div>
                        <div class="dashboard-button">Preload Cache</div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card">
                <h2><span class="dashboard-card-icon">S</span>Optimization Stats</h2>
                <div class="dashboard-card-content">
                    <div class="dashboard-stats">
                        <div class="dashboard-stat">
                            <div class="dashboard-stat-value">128</div>
                            <div class="dashboard-stat-label">Cache Hits</div>
                        </div>
                        <div class="dashboard-stat">
                            <div class="dashboard-stat-value">45</div>
                            <div class="dashboard-stat-label">Images Optimized</div>
                        </div>
                        <div class="dashboard-stat">
                            <div class="dashboard-stat-value">2.4MB</div>
                            <div class="dashboard-stat-label">Space Saved</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card">
                <h2><span class="dashboard-card-icon">M</span>Active Modules</h2>
                <div class="dashboard-card-content">
                    <ul class="module-list">
                        <li class="module-item">
                            <div class="module-name">
                                <span class="module-icon">C</span>
                                Caching
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </li>
                        <li class="module-item">
                            <div class="module-name">
                                <span class="module-icon">F</span>
                                File Optimization
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </li>
                        <li class="module-item">
                            <div class="module-name">
                                <span class="module-icon">M</span>
                                Media Optimization
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
