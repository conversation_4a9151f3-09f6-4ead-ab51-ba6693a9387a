# Redco Optimizer

A comprehensive WordPress optimization plugin that improves website performance through caching, file optimization, database cleanup, and more.

## 🧹 Project Structure (v2.0.0 - Cleaned & Organized)

### **Core Files**
```
redco-optimizer.php          # Main plugin file
README.md                    # This documentation
```

### **Admin Interface**
```
admin/
├── css/                     # Stylesheets (consolidated)
│   ├── redco-optimizer-admin.css           # Main admin styles + checkboxes
│   ├── redco-optimizer-modules.css         # Module-specific styles
│   ├── redco-optimizer-top-nav.css         # Navigation styles
│   ├── redco-optimizer-modern-layout.css   # Modern layout
│   ├── redco-optimizer-components.css      # UI components
│   ├── redco-optimizer-overrides.css       # WordPress overrides
│   ├── redco-optimizer-addons.css          # Add-ons styling
│   ├── redco-notification-system.css       # Notifications
│   └── redco-help-page.css                 # Help page styles
├── js/                      # JavaScript (consolidated)
│   ├── redco-optimizer-admin.js             # Main admin JS + UI fixes
│   ├── redco-modal-system.js                # Modal functionality
│   ├── redco-optimizer-premium.js           # Premium features
│   ├── redco-optimizer-top-nav.js           # Navigation JS
│   ├── redco-heartbeat.js                   # Heartbeat module
│   ├── redco-optimizer-addons.js            # Add-ons functionality
│   ├── redco-optimizer-error-logs.js        # Error logging
│   └── redco-optimizer-help-page.js         # Help page JS
├── partials/                # PHP templates
│   ├── redco-optimizer-admin-display.php   # Main admin interface
│   ├── redco-optimizer-admin-modules-tab.php
│   ├── redco-optimizer-admin-addons-tab.php
│   ├── redco-optimizer-admin-premium-tab.php
│   ├── redco-optimizer-admin-error-logs.php
│   └── redco-optimizer-admin-help.php
├── images/                  # Admin images
└── help-page.php           # Help page handler
```

## Description

Redco Optimizer is a comprehensive WordPress plugin designed to optimize your website's performance and monitor its health. With a modular structure, it allows you to enable or disable specific optimization features based on your needs.

## Features

### Free Features

- **Cache Optimization**: Improve site loading times with advanced caching.
- **Image Optimization**: Reduce image sizes without losing quality.
- **Database Optimization**: Clean and optimize your WordPress database.
- **Modern Admin Interface**: User-friendly, professional design for easy management.

### Premium Features

- **Performance Monitoring**: Track your site's performance and get insights.
- **Advanced Caching**: Browser caching, object caching, and more.
- **Image Optimization Pro**: WebP conversion, automatic resizing, and bulk optimization.
- **Database Optimizer Pro**: Scheduled cleanup, table optimization, and database backup.
- **Premium Support**: Get priority support from our team of experts.

## Installation

1. Upload the `redco-optimizer` folder to the `/wp-content/plugins/` directory.
2. Activate the plugin through the 'Plugins' menu in WordPress.
3. Configure the plugin settings through the 'Redco Optimizer' menu in the WordPress admin.

## Usage

### Cache Optimization

The Cache Optimization module improves your site's loading times by caching pages and assets. To use this module:

1. Go to Redco Optimizer > Modules.
2. Enable the Cache Optimizer module.
3. Configure the cache settings in Redco Optimizer > Settings.
4. Clear the cache after making changes to your site.

### Image Optimization

The Image Optimization module reduces image sizes without losing quality. To use this module:

1. Go to Redco Optimizer > Modules.
2. Enable the Image Optimizer module.
3. Configure the image optimization settings in Redco Optimizer > Settings.
4. Upload new images or optimize existing ones.

### Database Optimization

The Database Optimization module cleans and optimizes your WordPress database. To use this module:

1. Go to Redco Optimizer > Modules.
2. Enable the Database Optimizer module.
3. Configure the database optimization settings in Redco Optimizer > Settings.
4. Run the database cleanup and optimization tools.

### Performance Monitoring (Premium)

The Performance Monitoring module tracks your site's performance and provides insights. To use this module:

1. Go to Redco Optimizer > Modules.
2. Enable the Performance Monitor module (requires premium license).
3. Configure the performance monitoring settings in Redco Optimizer > Settings.
4. View performance data and insights in the dashboard.

## Extending with Addons

Redco Optimizer supports addons to extend its functionality. Developers can create addons using the Redco Optimizer API.

## Frequently Asked Questions

### Is Redco Optimizer compatible with all themes?

Yes, Redco Optimizer is designed to be compatible with all WordPress themes.

### Will Redco Optimizer slow down my site?

No, Redco Optimizer is designed to improve your site's performance, not slow it down. The plugin is lightweight and optimized for performance.

### Can I use Redco Optimizer with other caching plugins?

It's generally not recommended to use multiple caching plugins at the same time, as they may conflict with each other. We recommend disabling other caching plugins when using Redco Optimizer.

### How do I get support?

For free users, support is available through the WordPress.org plugin support forums. Premium users get priority support through our support portal.

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Redco Optimizer is developed and maintained by Redco.
