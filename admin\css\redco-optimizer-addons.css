/**
 * Redco Optimizer Add-Ons CSS
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/css
 */

/* Add-Ons Container */
.redco-addons-container {
    margin: 15px 0;
    max-width: 1400px;
    position: relative;
    z-index: 1;
    overflow: visible;
}

/* Add-Ons Header */
.redco-addons-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 18px;
    border-bottom: 1px solid #e5e5e5;
    position: relative;
}

.redco-addons-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0; /* Changed from 100px to 0 to remove the green line */
    height: 0; /* Changed from 3px to 0 */
    background: transparent; /* Changed from gradient to transparent */
    opacity: 0;
}

.redco-addons-header-content h2 {
    margin: 0 0 8px;
    font-size: 22px;
    font-weight: 600;
    color: #23282d;
}

.redco-addons-header-content p {
    margin: 0;
    font-size: 13px;
    color: #646970;
    max-width: 600px;
}

.redco-addons-header-actions {
    display: flex;
    align-items: center;
}

.redco-refresh-addons {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    font-size: 13px;
    background: #f6f7f7;
    border: 1px solid #c5c5c5;
    border-radius: 3px;
    color: #50575e;
    text-decoration: none;
    transition: all 0.2s ease;
}

.redco-refresh-addons:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
    color: #3c434a;
}

.redco-refresh-addons svg {
    width: 14px;
    height: 14px;
}

/* Add-Ons Grid */
.redco-addons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
    overflow: visible;
}

/* Add-On Card */
.redco-addon-card {
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    transition: all 0.25s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.redco-addon-card:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
    border-color: #d0d5dd;
}

.redco-addon-active {
    border-color: #00a66b;
    box-shadow: 0 1px 3px rgba(0, 166, 107, 0.1);
}

.redco-addon-premium {
    border-color: #8c5cdd;
}

.redco-addon-coming-soon {
    border-color: #6c757d;
    opacity: 0.85;
}

/* Add-On Header */
.redco-addon-header {
    padding: 10px 12px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #e5e5e5;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.redco-addon-active .redco-addon-header {
    background-color: #f0f7f2;
    border-bottom-color: #d1e7db;
}

.redco-addon-premium .redco-addon-header {
    background-color: #f6f2ff;
    border-bottom-color: #e0d6f9;
}

.redco-addon-coming-soon .redco-addon-header {
    background-color: #f8f9fa;
    border-bottom-color: #dee2e6;
}

.redco-addon-icon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    flex-shrink: 0;
}

.redco-addon-icon svg {
    width: 16px;
    height: 16px;
    color: #3c434a;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-addon-active .redco-addon-icon svg {
    color: #00a66b;
}

.redco-addon-premium .redco-addon-icon svg {
    color: #8c5cdd;
}

.redco-addon-coming-soon .redco-addon-icon svg {
    color: #6c757d;
}

.redco-addon-icon img {
    max-width: 20px;
    max-height: 20px;
}

.redco-addon-icon .dashicons {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
    color: #3c434a !important;
    display: inline-block !important; /* Ensure dashicons are always visible */
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
    font-family: dashicons, sans-serif !important; /* Add fallback font */
    line-height: 1 !important;
    text-align: center !important;
    vertical-align: middle !important;
}

/* Fallback for dashicons */
.redco-addon-icon .dashicons:before {
    content: attr(data-icon);
    display: inline-block;
}

/* Additional fallback for dashicons */
.redco-addon-icon .dashicons-images-alt:empty:before {
    content: "🖼️";
}

.redco-addon-icon .dashicons-admin-media:empty:before {
    content: "📷";
}

.redco-addon-icon .dashicons-admin-appearance:empty:before {
    content: "🎨";
}

.redco-addon-icon .dashicons-update:empty:before {
    content: "🔄";
}

.redco-addon-active .redco-addon-icon .dashicons {
    color: #00a32a !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
}

.redco-addon-premium .redco-addon-icon .dashicons {
    color: #8c5cdd !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
}

.redco-addon-title {
    margin: 0;
    font-size: 13px;
    font-weight: 600;
    flex-grow: 1;
    line-height: 1.2;
}

.redco-addon-version {
    font-size: 10px;
    color: #646970;
    margin-left: 4px;
}

.redco-addon-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    padding: 1px 5px;
    font-size: 9px;
    font-weight: 500;
    border-radius: 3px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.redco-addon-badge-premium {
    background-color: #8c5cdd;
    color: #fff;
}

.redco-addon-badge-coming-soon {
    background-color: #6c757d;
    color: #fff;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.7;
    }
}

/* Add-On Content */
.redco-addon-content {
    padding: 10px 12px;
    flex-grow: 1;
    min-height: 50px;
    display: flex;
    align-items: center;
}

.redco-addon-description {
    margin: 0;
    font-size: 11px;
    line-height: 1.3;
    color: #646970;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Add-On Footer */
.redco-addon-footer {
    padding: 8px 12px;
    border-top: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.redco-addon-status {
    font-size: 11px;
    font-weight: 500;
}

.redco-addon-status-active {
    color: #00a66b;
}

.redco-addon-status-inactive {
    color: #646970;
}

.redco-addon-actions {
    display: flex;
    gap: 6px;
}

.redco-addon-settings {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 11px;
}

.redco-addon-settings .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

/* Add-on Buttons */
.redco-addon-actions .redco-button {
    padding: 2px 8px;
    font-size: 11px;
    min-height: 24px;
    line-height: 1.2;
    border-radius: 3px;
    font-weight: normal;
    text-transform: none;
    letter-spacing: normal;
}

.redco-addon-actions .redco-button-secondary {
    background: #f6f7f7;
    border-color: #c5c5c5;
}

.redco-addon-actions .redco-button-primary {
    background: #00a66b;
    border-color: #00a66b;
}

.redco-addon-actions .redco-button-premium {
    background: #8c5cdd;
    border-color: #8c5cdd;
}

/* No Add-Ons */
.redco-no-addons {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px 20px;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
}

.redco-no-addons-icon {
    margin-bottom: 12px;
}

.redco-no-addons-icon svg {
    width: 40px;
    height: 40px;
    color: #646970;
    opacity: 0.7;
}

.redco-no-addons h3 {
    margin: 0 0 8px;
    font-size: 16px;
    font-weight: 600;
}

.redco-no-addons p {
    margin: 0;
    font-size: 13px;
    color: #646970;
}

/* Add-Ons Sections */
.redco-addons-section {
    margin-bottom: 35px;
    position: relative;
}

.redco-addons-section h3 {
    margin: 0 0 10px;
    font-size: 18px;
    font-weight: 600;
    color: #23282d;
    padding-left: 14px;
    position: relative;
    display: inline-block;
}

.redco-addons-section h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    bottom: 2px;
    width: 4px;
    background: var(--primary-gradient, linear-gradient(135deg, #00A66B, #008F5B));
    border-radius: 2px;
}

.redco-addons-section h3::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 14px;
    width: 0; /* Changed from 30px to 0 to remove the green line */
    height: 0; /* Changed from 2px to 0 */
    background: transparent; /* Changed from gradient to transparent */
    opacity: 0;
}

.redco-addons-section p {
    margin: 0 0 15px;
    font-size: 13px;
    color: #646970;
    padding-left: 14px;
    max-width: 700px;
}

/* Ensure dashicons in available add-ons are visible */
.redco-addon-available .redco-addon-icon .dashicons {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
    color: #3c434a !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
    font-family: dashicons !important;
    line-height: 1 !important;
    text-align: center !important;
    vertical-align: middle !important;
}

/* Spinner Animation */
.redco-spin {
    animation: redco-spin 2s linear infinite;
}

@keyframes redco-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Add-on Settings Modal */
.redco-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-in-out;
}

.redco-modal-overlay.active {
    visibility: visible;
    opacity: 1;
}

.redco-modal {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    animation: slideIn 0.3s ease-in-out;
}

.redco-addon-settings-modal {
    width: 800px;
    max-width: 90%;
    min-width: 600px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevent double scrollbars */
}

.redco-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #e5e5e5;
    background-color: #f9f9f9;
}

.redco-modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #23282d;
}

.redco-modal-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #646970;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.redco-modal-close svg {
    width: 24px;
    height: 24px;
}

.redco-modal-close:hover {
    background-color: #f0f0f0;
    color: #d63638;
}

.redco-modal-content {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 130px);
    background-color: #fff;
    position: relative;
    z-index: 10;
    min-height: 300px;
    scrollbar-width: thin;
    scrollbar-color: #ccc #f1f1f1;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-modal-content::-webkit-scrollbar {
    width: 8px;
}

.redco-modal-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.redco-modal-content::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.redco-modal-content::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}

.redco-modal-loading {
    text-align: center;
    padding: 40px 20px;
}

.redco-modal-loading svg {
    width: 40px;
    height: 40px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.redco-modal-loading p {
    margin: 0;
    font-size: 16px;
    color: #646970;
}

.redco-addon-settings-form {
    padding: 0 10px 30px;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-addon-settings-form::-webkit-scrollbar {
    width: 8px;
}

.redco-addon-settings-form::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.redco-addon-settings-form::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.redco-addon-settings-form::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}

.redco-addon-settings-form .redco-card {
    margin-bottom: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
}

.redco-card-header {
    background-color: #f9f9f9;
    padding: 12px 15px;
    border-bottom: 1px solid #e5e5e5;
}

.redco-card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.redco-card-content {
    padding: 15px;
    background-color: #fff;
}

.redco-modal-section {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100%;
}

.redco-modal-section h3,
.redco-settings-section-title {
    margin: 0 0 15px;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.redco-settings-section-title {
    margin: 25px 0 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e5e5;
}

.redco-toggle-row,
.redco-setting-row {
    display: flex !important;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f9f9f9;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100%;
}

.redco-toggle-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.redco-toggle-info,
.redco-setting-label {
    flex: 1;
}

.redco-toggle-info h4,
.redco-setting-label label {
    margin: 0 0 5px;
    font-size: 14px;
    font-weight: 600;
    display: block;
}

.redco-toggle-info p,
.redco-setting-label p,
.redco-setting-description {
    margin: 0;
    font-size: 13px;
    color: #646970;
}

.redco-toggle-control,
.redco-setting-field {
    margin-left: 15px;
    min-width: 120px;
}

/* Switch Toggle */
.redco-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.redco-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.redco-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.redco-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .redco-slider {
    background-color: #00A66B !important;
}

input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

input:checked + .redco-slider:before {
    transform: translateX(26px);
}

.redco-form-row {
    margin-bottom: 15px;
    display: flex !important;
    flex-wrap: wrap;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100%;
    position: relative;
}

.redco-form-label {
    flex-basis: 200px;
    padding-right: 20px;
    width: 100%;
    order: 1;
}

.redco-form-label label {
    font-weight: 600;
    font-size: 14px;
}

.redco-form-field {
    flex: 1;
    min-width: 200px;
    order: 2;
}

.redco-form-help {
    margin: 5px 0 0;
    font-size: 12px;
    color: #646970;
    display: block;
    width: 100%;
    clear: both;
    order: 3;
    padding-left: 0 !important;
}

.redco-input, .redco-select,
input[type="text"], input[type="number"] {
    width: 100%;
    max-width: 100%;
    padding: 8px 12px;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    box-shadow: 0 0 0 transparent;
    font-size: 14px;
}

.redco-textarea, textarea {
    width: 100%;
    max-width: 100%;
    padding: 10px 10px;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    box-shadow: 0 0 0 transparent;
    font-size: 14px;
    min-height: 100px;
}

.redco-setting-field input[type="number"] {
    width: 120px;
}

.redco-modal-footer,
.redco-form-actions {
    position: sticky;
    bottom: 0;
    background: #fff;
    padding: 15px;
    border-top: 1px solid #e5e5e5;
    margin-top: 20px;
    z-index: 10;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Modal Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Simple Notification */
.redco-simple-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 999999;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    width: 300px;
    max-width: 90%;
    display: none;
    border-left: 4px solid #00a32a;
    animation: slideIn 0.3s ease-in-out;
}

.redco-simple-notification.redco-notification-error {
    border-left-color: #d63638;
}

.redco-simple-notification.redco-notification-warning {
    border-left-color: #dba617;
}

.redco-simple-notification.redco-notification-info {
    border-left-color: #2271b1;
}

.redco-notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    padding-right: 20px;
    font-size: 14px;
}

.redco-notification-message {
    font-size: 13px;
    line-height: 1.4;
    color: #646970;
}

.redco-notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    color: #646970;
    transition: color 0.2s;
}

.redco-notification-close:hover {
    color: #d63638;
}

@keyframes slideOut {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-20px);
        opacity: 0;
    }
}

/* Simple Notification for Fallback */
.redco-simple-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    width: 300px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    padding: 15px;
    z-index: 999999;
    display: none;
    border-left: 4px solid #00a32a;
}

.redco-simple-notification.redco-notification-error {
    border-left-color: #d63638;
}

.redco-simple-notification.redco-notification-warning {
    border-left-color: #dba617;
}

.redco-simple-notification.redco-notification-info {
    border-left-color: #72aee6;
}

/* Ensure Coming Soon tab is not affected by add-ons CSS */
#redco-coming-soon-tab {
    position: relative !important;
    z-index: 5 !important;
    background: white !important;
    overflow: visible !important;
}

#redco-coming-soon-tab.redco-tab-content {
    display: none !important;
}

#redco-coming-soon-tab.redco-tab-content:target,
#redco-coming-soon-tab.redco-tab-content.active {
    display: block !important;
}

#redco-coming-soon-tab .redco-coming-soon-container {
    position: relative !important;
    z-index: 5 !important;
    background: white !important;
    overflow: visible !important;
}

/* Ensure proper tab content isolation */
.redco-tab-content {
    position: relative;
    z-index: 1;
    background: white;
    min-height: 400px;
    width: 100%;
    clear: both;
}

.redco-tab-content:not(.active) {
    display: none !important;
}

/* Prevent add-ons content from bleeding into other tabs */
#redco-addons-tab {
    position: relative;
    z-index: 2;
    background: white;
    overflow: hidden;
}

#redco-addons-tab .redco-addons-container {
    position: relative;
    z-index: 2;
    background: white;
    overflow: visible;
    width: 100%;
    max-width: 100%;
}

.redco-notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    padding-right: 20px;
    font-size: 14px;
}

.redco-notification-message {
    font-size: 13px;
    line-height: 1.4;
    color: #646970;
    margin: 0;
}

.redco-notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    color: #646970;
}

.redco-notification-close:hover {
    color: #d63638;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .redco-addons-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .redco-addons-header-actions {
        margin-top: 15px;
    }

    .redco-addons-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .redco-addon-settings-modal {
        width: 95%;
        min-width: auto;
    }

    .redco-simple-notification {
        width: calc(100% - 40px);
        top: 46px;
    }
}

@media screen and (max-width: 600px) {
    .redco-addons-grid {
        grid-template-columns: 1fr;
    }

    .redco-addon-actions {
        flex-wrap: wrap;
    }

    .redco-addon-actions .redco-button {
        flex: 1;
        text-align: center;
        justify-content: center;
    }
}
