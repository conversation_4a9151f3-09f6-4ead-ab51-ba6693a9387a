/**
 * Redco Optimizer - Main Admin Styles
 * Consolidated and optimized CSS for the admin interface
 * Version: 2.0.0 - Cleaned and reorganized
 */

/* ===================================
   CSS VARIABLES & RESET
   =================================== */
:root {
    --primary-color: #00A66B;
    --primary-hover: #008F5B;
    --secondary-color: #00A66B;
    --secondary-hover: #008F5B;
    --success-color: #00A66B;
    --warning-color: #667eea;
    --error-color: #E94B35;
    --text-color: #23282D;
    --text-light: #646970;
    --border-color: #E2E4E7;
    --bg-light: #F8F9FA;
    --bg-lighter: #F0F0F1;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.1);
    --border-radius: 4px;
    --transition: all 0.2s ease;
    --primary-gradient: linear-gradient(135deg, #00A66B, #008F5B);
    --premium-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-color);
}

/* Redco Optimizer specific styles */
.wrap h1 {
    color: var(--text-color);
    font-weight: 600;
}

/* Layout - More WordPress admin friendly */
.redco-layout {
    display: flex;
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    min-height: 600px;
    margin-top: 20px;
}

/* Sidebar */
.redco-sidebar {
    width: 280px;
    background: #fff;
    color: #23282D;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    border-right: 1px solid #e2e4e7;
}

.redco-logo {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-bottom: 1px solid #e2e4e7;
}

.redco-logo-text {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 10px;
    text-align: center;
    line-height: 1.2;
    position: relative;
    display: flex;
    align-items: baseline;
    justify-content: center;
}

.redco-logo-part-1 {
    color: #23282D;
}

.redco-logo-part-2 {
    color: var(--primary-color);
}

.redco-version {
    font-size: 12px;
    color: #646970;
    margin-left: 5px;
}

.redco-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    flex: 1;
}

.redco-nav-item {
    margin: 0;
    padding: 0;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f1;
    position: relative;
}

.redco-nav-item a,
.redco-nav-item {
    display: flex;
    flex-direction: column;
    padding: 16px 20px;
    padding-right: 60px; /* Make more room for the icon */
    color: #23282D;
    text-decoration: none;
    transition: all 0.25s ease;
    position: relative;
    overflow: hidden;
}

.redco-nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background-color: var(--primary-color);
    opacity: 0;
    transition: width 0.25s ease, opacity 0.25s ease;
}

/* Hide module tabs when their module is disabled */
.redco-nav-item.redco-module-tab-disabled {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    left: -9999px !important;
}



/* Enable toggles for specific free features */
.redco-toggle-info h4:contains('Combine JavaScript Files') ~ .redco-toggle-control .redco-switch-disabled,
.redco-toggle-info h4:contains('Defer JavaScript') ~ .redco-toggle-control .redco-switch-disabled,
.redco-toggle-info h4:contains('Remove Unused CSS') ~ .redco-toggle-control .redco-switch-disabled,
.redco-toggle-info h4:contains('Delay JavaScript') ~ .redco-toggle-control .redco-switch-disabled {
    pointer-events: auto !important;
}

.redco-toggle-info h4:contains('Combine JavaScript Files') ~ .redco-toggle-control .redco-switch-disabled input,
.redco-toggle-info h4:contains('Defer JavaScript') ~ .redco-toggle-control .redco-switch-disabled input,
.redco-toggle-info h4:contains('Remove Unused CSS') ~ .redco-toggle-control .redco-switch-disabled input,
.redco-toggle-info h4:contains('Delay JavaScript') ~ .redco-toggle-control .redco-switch-disabled input {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
    disabled: false !important;
}

/* Force enable specific free features */
#combine_js, #defer_js, #remove_unused_css, #delay_js {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
    disabled: false !important;
}

/* Remove disabled class from switches for free features */
#combine_js + .redco-slider,
#defer_js + .redco-slider,
#remove_unused_css + .redco-slider,
#delay_js + .redco-slider {
    background-color: #ccc !important;
    cursor: pointer !important;
}

/* Ensure checked toggles have the correct color */
#combine_js:checked + .redco-slider,
#defer_js:checked + .redco-slider,
#remove_unused_css:checked + .redco-slider,
#delay_js:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-nav-item:hover,
.redco-nav-item a:hover {
    background: #f8f9fa;
    color: var(--primary-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.redco-nav-item:hover::before {
    width: 4px;
    opacity: 0.7; /* Slightly transparent to differentiate from active */
}

.redco-nav-item.active {
    background: linear-gradient(to right, rgba(0, 166, 107, 0.05), transparent);
    color: var(--primary-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.redco-nav-item.active::before {
    width: 4px;
    opacity: 1; /* Full opacity for active state */
}

.redco-nav-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 60px;
}

.redco-nav-icon {
    font-size: 22px;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #646970;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.25s ease;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 50%;
}

.redco-nav-item:hover .redco-nav-icon {
    color: var(--primary-color);
    background-color: rgba(0, 166, 107, 0.08);
    transform: scale(1.05);
}

.redco-nav-item.active .redco-nav-icon {
    color: var(--primary-color);
    background-color: rgba(0, 166, 107, 0.1);
}

.redco-nav-text {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    transition: all 0.25s ease;
}

.redco-nav-description {
    font-size: 12px;
    color: #646970;
    margin-top: 2px;
    line-height: 1.3;
    max-width: 240px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.25s ease;
    opacity: 0.85;
}

.redco-nav-item:hover .redco-nav-description {
    color: var(--text-color);
    opacity: 1;
}

.redco-nav-item.active .redco-nav-text {
    transform: translateY(-1px);
}

.redco-nav-item.active .redco-nav-description {
    color: var(--text-color);
    opacity: 1;
}

.redco-nav-help {
    margin-top: auto;
    border-top: 1px solid #e2e4e7;
}

.redco-nav-help-link {
    color: #23282D;
    text-decoration: none;
}

.redco-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e2e4e7;
}

.redco-upgrade-button {
    display: block;
    background: var(--primary-color);
    color: #fff;
    text-align: center;
    padding: 10px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.redco-upgrade-button:hover {
    background: var(--primary-hover);
    color: #fff;
}

/* Main Content */
.redco-main-content {
    flex: 1;
    padding: 0;
    overflow: auto;
}

.redco-page-header {
    padding: 30px 35px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(to right, #ffffff, #f8f9fa);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.redco-page-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: transparent;
    opacity: 0;
}

.redco-page-header-left {
    display: flex;
    align-items: center;
}

.redco-page-header-right {
    display: flex;
    align-items: center;
}

.redco-page-header-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.redco-page-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    line-height: 1.3;
    position: relative;
    display: inline-block;
}

.redco-page-header h1::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 3px;
}

.redco-page-header-description {
    margin: 8px 0 0 0;
    font-size: 13px;
    color: var(--text-light);
    line-height: 1.3;
    max-width: 600px;
}

.redco-page-header-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 8px;
    margin-right: 15px;
    display: flex !important; /* Force display to ensure visibility */
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    visibility: visible !important; /* Ensure visibility */
    opacity: 1 !important; /* Ensure opacity */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative; /* Ensure proper stacking context */
    z-index: 10; /* Higher z-index to prevent being hidden */
    min-width: 40px; /* Ensure minimum width */
    min-height: 40px; /* Ensure minimum height */
}

.redco-page-header-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
}

.redco-page-header-icon .dashicons {
    display: inline-block !important; /* Force display of dashicons */
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 20px;
    width: 20px;
    height: 20px;
    line-height: 1;
    color: white;
    position: relative; /* Ensure proper stacking context */
    z-index: 11; /* Higher z-index than parent */
}

/* Fallback icon in case the dashicon disappears */
.redco-page-header-icon::before {
    content: '\f102'; /* Dashicons code for dashboard icon */
    font-family: dashicons;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 20px;
    z-index: 9; /* Lower than the actual icon */
    opacity: 0; /* Hidden by default */
    transition: opacity 0.2s ease;
}

/* Show fallback if the main icon disappears */
.redco-page-header-icon:not(:has(.dashicons:visible))::before {
    opacity: 1;
}

.redco-header-action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background: var(--primary-gradient);
    color: #fff;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-header-action-button:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    color: #fff;
}

.redco-header-action-button .dashicons {
    margin-right: 8px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Tab Content */
.redco-tab-content {
    padding: 30px;
    display: none; /* Hide all tabs by default */
}

/* Show dashboard tab by default */
#redco-dashboard-tab {
    display: block;
}

/* Success Message */
.redco-success-message {
    background-color: #F0F9EB;
    border: 1px solid #E1F3D8;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 30px;
    position: relative;
    display: flex;
    align-items: flex-start;
}

.redco-success-icon {
    color: var(--success-color);
    font-size: 24px;
    margin-right: 15px;
    flex-shrink: 0;
}

.redco-success-content {
    flex: 1;
}

.redco-success-content h2 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: var(--success-color);
    font-weight: 600;
}

.redco-success-content p {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: var(--text-color);
    line-height: 1.5;
}

.redco-success-content p:last-child {
    margin-bottom: 0;
}

.redco-success-details {
    color: var(--text-light);
    font-size: 13px;
}

.redco-success-cta {
    font-weight: 500;
}

.redco-close-message {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    color: var(--text-light);
    background: none;
    border: none;
    padding: 0;
    font-size: 16px;
}

.redco-close-message:hover {
    color: var(--text-color);
}

/* Account Section */
.redco-account-section {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 30px;
}

.redco-section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 22px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(to right, #ffffff, #f8f9fa);
    position: relative;
}

/* Removed green color under section title */

.redco-section-title h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    position: relative;
    padding-left: 2px;
}

.redco-refresh-info {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
}

.redco-refresh-info:hover {
    color: var(--primary-color);
}

.redco-refresh-info .dashicons {
    font-size: 16px;
    margin-right: 5px;
}

.redco-account-info {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.redco-info-row {
    display: flex;
    align-items: center;
}

.redco-info-label {
    font-weight: 600;
    margin-right: 10px;
    color: var(--text-color);
}

.redco-info-value {
    color: var(--text-light);
}

.redco-license-free {
    color: var(--text-light);
}

.redco-view-account {
    text-align: right;
}

/* Dashboard Columns */
.redco-dashboard-columns {
    display: flex;
    gap: 30px;
}

.redco-column-main {
    flex: 2;
}

.redco-column-side {
    flex: 1;
}

/* Cards */
.redco-card {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    overflow: hidden;
}

.redco-card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-lighter);
}

.redco-card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-card-content {
    padding: 20px;
}

/* Compact card content for optimization status */
.redco-optimization-status .redco-card-content {
    padding: 15px;
}

.redco-card-content > .redco-form-help {
    margin-top: 5px;
    margin-bottom: 15px;
    display: block;
    width: 100%;
    padding-left: 0;
}

/* Dashboard Overview */
.redco-dashboard-overview {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.redco-overview-score {
    display: flex;
    align-items: stretch;
    padding: 0;
}

.redco-score-container {
    flex: 0 0 200px;
    background: var(--primary-gradient);
    color: #fff;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.redco-score-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(30deg);
    pointer-events: none;
}

.redco-score-circle {
    position: relative;
    width: 120px;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.redco-score-svg {
    width: 120px;
    height: 120px;
    transform: rotate(-90deg);
}

.redco-score-circle-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 2.5;
}

.redco-score-circle-fill {
    fill: none;
    stroke: #fff;
    stroke-width: 2.5;
    stroke-linecap: round;
}

.redco-score-text {
    fill: #fff;
    font-size: 14px;
    font-weight: 700;
    text-anchor: middle;
    dominant-baseline: middle;
    transform: rotate(90deg);
}

.redco-score-label {
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    margin-top: 10px;
    opacity: 0.9;
}

.redco-score-details {
    flex: 1;
    padding: 25px;
    display: flex;
    flex-direction: column;
}

.redco-score-item {
    margin-bottom: 20px;
}

.redco-score-item:last-child {
    margin-bottom: 0;
}

.redco-score-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.redco-score-item-name {
    font-size: 14px;
    color: var(--text-light);
}

.redco-score-item-value {
    font-size: 14px;
    font-weight: 700;
    padding: 3px 8px;
    border-radius: 20px;
    background: rgba(46, 204, 113, 0.15);
    color: var(--success-color);
}

.redco-score-item-value.warning {
    background: rgba(243, 156, 18, 0.15);
    color: var(--warning-color);
}

.redco-score-item-value.error {
    background: rgba(231, 76, 60, 0.15);
    color: var(--error-color);
}

.redco-score-item-bar {
    height: 6px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
    overflow: hidden;
}

.redco-score-item-progress {
    height: 100%;
    background: var(--success-color);
    border-radius: 3px;
}

.redco-score-item-progress.warning {
    background: var(--warning-color);
}

.redco-score-item-progress.error {
    background: var(--error-color);
}

.redco-view-report {
    margin-top: auto;
    align-self: flex-start;
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 16px;
    border: 1px solid var(--primary-color);
    border-radius: 20px;
    transition: var(--transition);
}

.redco-view-report:hover {
    background: var(--primary-color);
    color: #fff;
}

/* Module styles are now in redco-optimizer-modules.css */

/* Premium badge styles are now in redco-optimizer-modules.css */

/* Module Content Styles */

/* Module content styles are now in redco-optimizer-modules.css */

.redco-modules-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.redco-modules-intro h2 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 24px;
    color: var(--text-color);
}

.redco-modules-intro p {
    margin-top: 0;
    color: var(--text-light);
    font-size: 15px;
    max-width: 600px;
}

.redco-modules-actions {
    display: flex;
    gap: 15px;
}

/* Module features styles are now in redco-optimizer-modules.css */

/* Module enabled styles are already defined above */

.redco-module-premium .redco-module-feature-icon {
    opacity: 0.5;
}

/* Module footer styles are now in redco-optimizer-modules.css */

/* Premium badge styles are already defined above */

/* Toggle Switch styles are now in redco-optimizer-modules.css */

/* Buttons */
.redco-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background: var(--primary-gradient);
    color: #fff;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.redco-button:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.redco-button:hover::before {
    left: 100%;
}

.redco-button-secondary {
    background: #fff;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.redco-button-secondary:hover {
    background: var(--bg-light);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border-color: var(--border-color);
}

.redco-button-premium {
    background: var(--premium-gradient);
    box-shadow: 0 2px 5px rgba(245, 208, 32, 0.3);
}

.redco-button-premium:hover {
    box-shadow: 0 5px 15px rgba(245, 208, 32, 0.4);
}

.redco-button-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-size: contain;
    background-repeat: no-repeat;
}

/* Settings */
.redco-settings-form {
    max-width: 800px;
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: 30px;
    border: 1px solid var(--border-color);
}

.redco-settings-section {
    margin-bottom: 40px;
}

.redco-settings-section-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 22px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--border-color);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    position: relative;
}

.redco-settings-section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0; /* Changed from 60px to 0 to remove the green line */
    height: 0; /* Changed from 2px to 0 */
    background: transparent; /* Changed from gradient to transparent */
    opacity: 0;
}

.redco-settings-section-title::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 10px;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.9;
}

.redco-settings-section:nth-child(1) .redco-settings-section-title::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300A66B"><path d="M12 16c2.206 0 4-1.794 4-4s-1.794-4-4-4-4 1.794-4 4 1.794 4 4 4zm0-6c1.084 0 2 .916 2 2s-.916 2-2 2-2-.916-2-2 .916-2 2-2z"/><path d="m2.845 16.136 1 1.73c.531.917 1.809 1.261 2.73.73l.529-.306A8.1 8.1 0 0 0 9 19.402V20c0 1.103.897 2 2 2h2c1.103 0 2-.897 2-2v-.598a8.132 8.132 0 0 0 1.896-1.111l.529.306c.923.53 2.198.188 2.731-.731l.999-1.729a2.001 2.001 0 0 0-.731-2.732l-.505-.292a7.718 7.718 0 0 0 0-2.224l.505-.292a2.002 2.002 0 0 0 .731-2.732l-.999-1.729c-.531-.92-1.808-1.265-2.731-.732l-.529.306A8.1 8.1 0 0 0 15 4.598V4c0-1.103-.897-2-2-2h-2c-1.103 0-2 .897-2 2v.598a8.132 8.132 0 0 0-1.896 1.111l-.529-.306c-.924-.531-2.2-.187-2.731.732l-.999 1.729a2.001 2.001 0 0 0 .731 2.732l.505.292a7.683 7.683 0 0 0 0 2.223l-.505.292a2.003 2.003 0 0 0-.731 2.733z"/></svg>');
}

.redco-settings-section:nth-child(2) .redco-settings-section-title::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300A66B"><path d="M5.433 21a.12.12 0 0 1-.118-.141L6 17H2.143a.12.12 0 0 1-.118-.141l.862-4a.12.12 0 0 1 .118-.1H7l.8-3.175a.12.12 0 0 1 .118-.1h3.764a.12.12 0 0 1 .118.141l-.713 3.234h4a.12.12 0 0 1 .118.141L14.2 17h3.822a.12.12 0 0 1 .118.141l-.692 3.659a.12.12 0 0 1-.118.1H13.6a.12.12 0 0 1-.118-.141l.8-3.759h-4a.12.12 0 0 1-.118-.141l.8-3.759H8.428l-.8 3.759a.12.12 0 0 1-.118.141H3.673l-.431 2h3.8a.12.12 0 0 1 .118.141l-.685 3.759H5.433z"/></svg>');
}

.redco-settings-section:nth-child(3) .redco-settings-section-title::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300A66B"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"/><path d="M11 11h2v6h-2zm0-4h2v2h-2z"/></svg>');
}

.redco-settings-field {
    margin-bottom: 25px;
    position: relative;
    padding-left: 15px;
    border-left: 3px solid var(--border-color);
    transition: var(--transition);
}

.redco-settings-field:hover {
    border-left-color: var(--primary-color);
}

.redco-settings-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color);
    font-size: 15px;
}

.redco-settings-field input[type="text"],
.redco-settings-field input[type="email"],
.redco-settings-field input[type="number"],
.redco-settings-field select,
.redco-settings-field textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.redco-settings-field input[type="text"]:focus,
.redco-settings-field input[type="email"]:focus,
.redco-settings-field input[type="number"]:focus,
.redco-settings-field select:focus,
.redco-settings-field textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
    outline: none;
}

.redco-settings-field .description {
    font-size: 13px;
    color: var(--text-light);
    margin-top: 8px;
    line-height: 1.5;
}

.redco-settings-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Premium Page */
.redco-premium-features {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.redco-premium-feature {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    position: relative;
}

.redco-premium-feature:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
    border-color: rgba(245, 208, 32, 0.3);
}

.redco-premium-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--premium-gradient);
    opacity: 0;
    transition: var(--transition);
}

.redco-premium-feature:hover::before {
    opacity: 1;
}

.redco-premium-feature-header {
    background: var(--premium-gradient);
    color: #fff;
    padding: 20px;
    font-weight: 700;
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.redco-premium-feature-header::before {
    content: '★';
    display: inline-block;
    margin-right: 10px;
    font-size: 18px;
}

.redco-premium-feature-content {
    padding: 20px;
}

.redco-premium-feature-content ul {
    margin: 15px 0 0;
    padding: 0 0 0 20px;
    list-style-type: none;
}

.redco-premium-feature-content ul li {
    position: relative;
    padding: 5px 0 5px 25px;
    font-size: 14px;
    color: var(--text-light);
}

.redco-premium-feature-content ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 5px;
    color: var(--success-color);
    font-weight: bold;
}

.redco-premium-hero {
    display: flex;
    align-items: center;
    margin-bottom: 50px;
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.redco-premium-hero-content {
    flex: 1;
    padding: 40px;
}

.redco-premium-hero-content h2 {
    margin-top: 0;
    font-size: 32px;
    color: var(--text-color);
    margin-bottom: 20px;
    line-height: 1.2;
}

.redco-premium-hero-content p {
    font-size: 16px;
    color: var(--text-light);
    margin-bottom: 30px;
    line-height: 1.6;
    max-width: 600px;
}

.redco-premium-hero-actions {
    display: flex;
    gap: 15px;
}

.redco-premium-hero-image {
    flex: 0 0 300px;
    height: 300px;
    background: var(--premium-gradient);
    position: relative;
    overflow: hidden;
}

.redco-premium-hero-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" opacity="0.1"><path d="M12 16c2.206 0 4-1.794 4-4s-1.794-4-4-4-4 1.794-4 4 1.794 4 4 4zm0-6c1.084 0 2 .916 2 2s-.916 2-2 2-2-.916-2-2 .916-2 2-2z"/><path d="m2.845 16.136 1 1.73c.531.917 1.809 1.261 2.73.73l.529-.306A8.1 8.1 0 0 0 9 19.402V20c0 1.103.897 2 2 2h2c1.103 0 2-.897 2-2v-.598a8.132 8.132 0 0 0 1.896-1.111l.529.306c.923.53 2.198.188 2.731-.731l.999-1.729a2.001 2.001 0 0 0-.731-2.732l-.505-.292a7.718 7.718 0 0 0 0-2.224l.505-.292a2.002 2.002 0 0 0 .731-2.732l-.999-1.729c-.531-.92-1.808-1.265-2.731-.732l-.529.306A8.1 8.1 0 0 0 15 4.598V4c0-1.103-.897-2-2-2h-2c-1.103 0-2 .897-2 2v.598a8.132 8.132 0 0 0-1.896 1.111l-.529-.306c-.924-.531-2.2-.187-2.731.732l-.999 1.729a2.001 2.001 0 0 0 .731 2.732l.505.292a7.683 7.683 0 0 0 0 2.223l-.505.292a2.003 2.003 0 0 0-.731 2.733z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 200px;
    opacity: 0.2;
}

.redco-premium-hero-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.redco-premium-hero-badge-inner {
    width: 130px;
    height: 130px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: rotate(-10deg);
}

.redco-premium-hero-badge-text {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.redco-premium-hero-badge-discount {
    font-size: 24px;
    font-weight: 800;
    color: #e74c3c;
}

.redco-premium-comparison {
    margin-bottom: 50px;
}

.redco-premium-comparison h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: var(--text-color);
}

.redco-premium-comparison-table {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.redco-premium-comparison-header {
    display: flex;
    background: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

.redco-premium-comparison-cell {
    flex: 1;
    padding: 15px 20px;
    font-weight: 600;
    text-align: center;
}

.redco-premium-comparison-cell:first-child {
    flex: 2;
    text-align: left;
}

.redco-premium-cell {
    background: rgba(74, 0, 224, 0.05);
    color: var(--primary-color);
}

.redco-premium-comparison-row {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.redco-premium-comparison-row:last-child {
    border-bottom: none;
}

.redco-premium-check {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300A66B"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

.redco-premium-cross {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e74c3c"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm4.207 12.793-1.414 1.414L12 13.414l-2.793 2.793-1.414-1.414L10.586 12 7.793 9.207l1.414-1.414L12 10.586l2.793-2.793 1.414 1.414L13.414 12l2.793 2.793z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

.redco-premium-pricing {
    margin-bottom: 50px;
}

.redco-premium-pricing h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: var(--text-color);
}

.redco-premium-pricing-plans {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.redco-premium-pricing-plan {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
}

.redco-premium-pricing-plan:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-5px);
}

.redco-premium-pricing-plan-featured {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: scale(1.05);
}

.redco-premium-pricing-plan-featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.redco-premium-pricing-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-gradient);
    color: #fff;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.redco-premium-pricing-header {
    padding: 30px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.redco-premium-pricing-header h4 {
    margin: 0 0 15px;
    font-size: 22px;
    color: var(--text-color);
}

.redco-premium-pricing-price {
    margin-bottom: 15px;
}

.redco-premium-pricing-amount {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-color);
}

.redco-premium-pricing-period {
    font-size: 16px;
    color: var(--text-light);
}

.redco-premium-pricing-sites {
    font-size: 14px;
    color: var(--text-light);
    padding: 5px 10px;
    background: var(--bg-light);
    border-radius: 20px;
    display: inline-block;
}

.redco-premium-pricing-features {
    padding: 20px 30px;
}

.redco-premium-pricing-features ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.redco-premium-pricing-features li {
    padding: 10px 0 10px 30px;
    position: relative;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-light);
}

.redco-premium-pricing-features li:last-child {
    border-bottom: none;
}

.redco-premium-pricing-features li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    width: 20px;
    height: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234a00e0"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

.redco-premium-pricing-action {
    padding: 0 30px 30px;
    text-align: center;
}

.redco-premium-cta {
    text-align: center;
    margin-top: 40px;
    padding: 40px;
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.redco-premium-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--premium-gradient);
}

.redco-premium-cta h2 {
    margin-top: 0;
    font-size: 28px;
    color: var(--text-color);
    margin-bottom: 15px;
}

.redco-premium-cta p {
    font-size: 16px;
    color: var(--text-light);
    margin-bottom: 25px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.redco-premium-guarantee {
    margin-top: 20px;
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    font-size: 14px;
}

.redco-premium-guarantee-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234a00e0"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"/><path d="m9.707 11.293-1.414 1.414L11.586 16l4.707-4.707-1.414-1.414L11.586 13.172z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

/* Notifications */
.redco-notification-container {
    position: fixed;
    top: 50px;
    right: 20px;
    width: 350px;
    max-width: 90%;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.redco-notification {
    padding: 18px 20px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: flex-start;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    animation: slideIn 0.3s ease-out forwards;
    background: #fff;
    border-left: 4px solid;
    margin-bottom: 0;
}

.redco-notification.redco-notification-inline {
    margin-bottom: 25px;
    position: relative;
    top: auto;
    right: auto;
    width: 100%;
}

@keyframes slideIn {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(30px);
    }
}

.redco-notification-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    margin-right: 15px;
    background-size: contain;
    background-repeat: no-repeat;
}

.redco-notification-content {
    flex: 1;
}

.redco-notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 14px;
}

.redco-notification-message {
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
}

.redco-notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    opacity: 0.5;
    transition: opacity 0.2s;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23333"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

.redco-notification-close:hover {
    opacity: 1;
}

.redco-notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    width: 100%;
}

.redco-notification-progress-bar {
    height: 100%;
    width: 100%;
    transform-origin: left;
    animation: progress 5s linear forwards;
}

@keyframes progress {
    0% {
        transform: scaleX(1);
    }
    100% {
        transform: scaleX(0);
    }
}

/* Notification Types */
.redco-notification-success {
    border-color: #2ecc71;
}

.redco-notification-success .redco-notification-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232ecc71"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"/></svg>');
}

.redco-notification-success .redco-notification-title {
    color: #27ae60;
}

.redco-notification-success .redco-notification-progress-bar {
    background-color: #2ecc71;
}

.redco-notification-error {
    border-color: #e74c3c;
}

.redco-notification-error .redco-notification-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e74c3c"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm4.207 12.793-1.414 1.414L12 13.414l-2.793 2.793-1.414-1.414L10.586 12 7.793 9.207l1.414-1.414L12 10.586l2.793-2.793 1.414 1.414L13.414 12l2.793 2.793z"/></svg>');
}

.redco-notification-error .redco-notification-title {
    color: #c0392b;
}

.redco-notification-error .redco-notification-progress-bar {
    background-color: #e74c3c;
}

.redco-notification-warning {
    border-color: #f39c12;
}

.redco-notification-warning .redco-notification-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23f39c12"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 15c-.744 0-1.35-.595-1.35-1.33s.606-1.33 1.35-1.33c.745 0 1.35.595 1.35 1.33S12.745 17 12 17zm1.5-5h-3c-.413 0-.75-.337-.75-.75v-5.5c0-.413.337-.75.75-.75h3c.413 0 .75.337.75.75v5.5c0 .413-.337.75-.75.75z"/></svg>');
}

.redco-notification-warning .redco-notification-title {
    color: #d35400;
}

.redco-notification-warning .redco-notification-progress-bar {
    background-color: #f39c12;
}

.redco-notification-info {
    border-color: #3498db;
}

.redco-notification-info .redco-notification-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233498db"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 15c-.744 0-1.35-.595-1.35-1.33s.606-1.33 1.35-1.33c.745 0 1.35.595 1.35 1.33S12.745 17 12 17zm1.5-5h-3c-.413 0-.75-.337-.75-.75v-5.5c0-.413.337-.75.75-.75h3c.413 0 .75.337.75.75v5.5c0 .413-.337.75-.75.75z"/></svg>');
}

.redco-notification-info .redco-notification-title {
    color: #2980b9;
}

.redco-notification-info .redco-notification-progress-bar {
    background-color: #3498db;
}

/* Dashboard Overview */
.redco-dashboard-overview {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.redco-overview-score {
    display: flex;
    align-items: stretch;
    padding: 0;
}

.redco-score-container {
    flex: 0 0 200px;
    background: var(--primary-gradient);
    color: #fff;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.redco-score-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" opacity="0.1"><path d="M12 16c2.206 0 4-1.794 4-4s-1.794-4-4-4-4 1.794-4 4 1.794 4 4 4zm0-6c1.084 0 2 .916 2 2s-.916 2-2 2-2-.916-2-2 .916-2 2-2z"/><path d="m2.845 16.136 1 1.73c.531.917 1.809 1.261 2.73.73l.529-.306A8.1 8.1 0 0 0 9 19.402V20c0 1.103.897 2 2 2h2c1.103 0 2-.897 2-2v-.598a8.132 8.132 0 0 0 1.896-1.111l.529.306c.923.53 2.198.188 2.731-.731l.999-1.729a2.001 2.001 0 0 0-.731-2.732l-.505-.292a7.718 7.718 0 0 0 0-2.224l.505-.292a2.002 2.002 0 0 0 .731-2.732l-.999-1.729c-.531-.92-1.808-1.265-2.731-.732l-.529.306A8.1 8.1 0 0 0 15 4.598V4c0-1.103-.897-2-2-2h-2c-1.103 0-2 .897-2 2v.598a8.132 8.132 0 0 0-1.896 1.111l-.529-.306c-.924-.531-2.2-.187-2.731.732l-.999 1.729a2.001 2.001 0 0 0 .731 2.732l.505.292a7.683 7.683 0 0 0 0 2.223l-.505.292a2.003 2.003 0 0 0-.731 2.733z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 120px;
    opacity: 0.2;
}

.redco-score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.redco-score-value {
    font-size: 36px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.redco-score-label {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

.redco-score-details {
    flex: 1;
    padding: 30px;
    display: flex;
    flex-direction: column;
}

.redco-score-item {
    margin-bottom: 20px;
}

.redco-score-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.redco-score-item-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-score-item-value {
    font-size: 14px;
    font-weight: 700;
    color: var(--primary-color);
}

.redco-score-item-value.good {
    color: var(--success-color);
}

.redco-score-item-value.warning {
    color: var(--warning-color);
}

.redco-score-item-bar {
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.redco-score-item-progress {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
}

.redco-score-item-progress.warning {
    background: linear-gradient(90deg, #f39c12, #e74c3c);
}

.redco-view-report {
    margin-top: auto;
    align-self: flex-start;
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 16px;
    border: 1px solid var(--primary-color);
    border-radius: 20px;
    transition: var(--transition);
}

.redco-view-report:hover {
    background: var(--primary-color);
    color: #fff;
}

/* Dashboard Sections */
.redco-dashboard-sections {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    margin-bottom: 30px;
}

.redco-dashboard-section {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.redco-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 22px;
    background: linear-gradient(to right, var(--bg-light), #ffffff);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.redco-section-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0; /* Changed from 60px to 0 to remove the green line */
    height: 0; /* Changed from 2px to 0 */
    background: transparent; /* Changed from gradient to transparent */
    opacity: 0;
}

.redco-section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    position: relative;
    padding-left: 2px;
}

.redco-section-action {
    font-size: 13px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.redco-section-action:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

.redco-section-content {
    padding: 20px;
}

/* Action Items */
.redco-action-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.redco-action-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.redco-action-item:first-child {
    padding-top: 0;
}

.redco-action-info {
    flex: 1;
}

.redco-action-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-action-info p {
    margin: 0;
    font-size: 13px;
    color: var(--text-light);
}

.redco-action-button {
    margin-left: 20px;
}

/* Status Grid */
.redco-status-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
}

.redco-status-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    background: var(--bg-lighter);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.redco-status-item:hover {
    background: #fff;
    box-shadow: var(--shadow-sm);
}

.redco-status-icon {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    flex-shrink: 0;
}

.redco-status-icon .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
    line-height: 1;
}

.redco-status-icon.active {
    background-color: var(--success-color);
    color: #fff;
}

.redco-status-icon.inactive {
    background-color: var(--text-light);
    color: #fff;
}

.redco-status-icon.premium {
    background-color: var(--warning-color);
    color: #fff;
}

.redco-status-info {
    flex: 1;
    min-width: 0; /* Allows text to truncate properly */
}

.redco-status-info h4 {
    margin: 0 0 1px 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.redco-status-info p {
    margin: 0;
    font-size: 10px;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Buttons */
.redco-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.redco-button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    line-height: 1;
    vertical-align: text-bottom;
}

.redco-button-primary {
    background-color: var(--primary-color);
    color: #fff;
}

.redco-button-primary:hover {
    background-color: var(--primary-hover);
    color: #fff;
}

.redco-button-secondary {
    background-color: #f0f0f1;
    color: var(--text-color);
    border: 1px solid #d5d5d5;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.redco-button-secondary:hover {
    background-color: #f8f8f8;
    color: var(--text-color);
    border-color: #c3c4c7;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* Disabled buttons */
.redco-button:disabled,
.redco-button.disabled,
.redco-button[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f0f0f1;
    color: #a7aaad;
    border-color: #dcdcde;
    box-shadow: none;
    transform: none;
}

.redco-button:disabled:hover,
.redco-button.disabled:hover,
.redco-button[disabled]:hover {
    background: #f0f0f1;
    color: #a7aaad;
    transform: none;
    box-shadow: none;
}

.redco-text-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 13px;
    transition: var(--transition);
}

.redco-text-link:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Performance Score */
.redco-performance-card .redco-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.redco-score-circle {
    position: relative;
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-score-svg {
    width: 100px;
    height: 100px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.redco-score-circle-bg {
    fill: none;
    stroke: #f0f0f1;
    stroke-width: 3.5;
}

.redco-score-circle-fill {
    fill: none;
    stroke: var(--success-color);
    stroke-width: 3.5;
    stroke-linecap: round;
}

.redco-score-text-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.redco-score-text {
    color: var(--text-color);
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    display: inline-block;
}

.redco-score-percentage {
    color: var(--text-color);
    font-size: 8px;
    font-weight: 500;
    line-height: 1;
    display: inline-block;
    margin-left: 1px;
    position: relative;
    top: -1px;
}

.redco-score-info {
    text-align: center;
}

.redco-score-info p {
    margin: 0 0 15px 0;
    font-size: 13px;
    color: var(--text-light);
    line-height: 1.5;
}

/* Tips */
.redco-tips-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.redco-tip-item {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.redco-tip-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: var(--bg-lighter);
    border-bottom: 1px solid var(--border-color);
}

.redco-tip-icon {
    margin-right: 10px;
    color: var(--primary-color);
}

.redco-tip-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-tip-content {
    padding: 15px;
}

.redco-tip-content p {
    margin: 0;
    font-size: 13px;
    color: var(--text-light);
    line-height: 1.5;
}

.redco-tip-item.priority-high .redco-tip-icon {
    color: var(--error-color);
}

.redco-tip-item.priority-medium .redco-tip-icon {
    color: var(--warning-color);
}

/* Help Card */
.redco-help-card .redco-card-content {
    text-align: center;
}

.redco-help-card p {
    margin: 0 0 15px 0;
    font-size: 13px;
    color: var(--text-light);
    line-height: 1.5;
}

/* Form Elements */
.redco-section-intro {
    margin-bottom: 30px;
}

.redco-section-intro h2 {
    margin: 0 0 10px 0;
    font-size: 23px;
    font-weight: 400;
    color: var(--text-color);
}

.redco-section-intro p {
    margin: 0;
    font-size: 14px;
    color: var(--text-light);
}

/* Settings Section Title */
.redco-settings-section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin: 25px 0 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

/* Expandable Section Styles */
.redco-card.redco-expandable-section {
    margin-bottom: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.redco-card-header.redco-expandable-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.redco-card-header.redco-expandable-header:hover {
    background-color: #f0f0f1;
}

.redco-card-header.redco-expandable-header h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.redco-card-header.redco-expandable-header h3 .dashicons {
    margin-right: 10px;
    color: #646970;
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.redco-expandable-toggle {
    transition: transform 0.3s ease;
    color: #646970;
}

.redco-card.redco-expandable-section.active .redco-expandable-toggle {
    transform: rotate(180deg);
}

.redco-card-content.redco-expandable-content {
    padding: 20px;
    display: none;
}

.redco-toggle-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f3f4;
    min-height: 44px;
}

.redco-toggle-row:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.redco-toggle-row:first-child {
    padding-top: 0;
}

.redco-toggle-info {
    flex: 1;
    padding-right: 16px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.redco-toggle-info h4 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    color: #1f2937;
    line-height: 1.2;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.redco-toggle-info p {
    margin: 0;
    font-size: 13px;
    color: #6b7280;
    line-height: 1.3;
}

/* Removed duplicate badge styling - using unified system in modules.css */



/* Specific rule for premium badges in headings */
h4 .redco-premium-badge {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
    margin-left: 10px !important;
    margin-right: 0 !important;
    vertical-align: middle !important;
    position: relative !important;
    top: -1px !important;
}

.redco-toggle-control {
    flex-shrink: 0;
    display: flex;
    align-items: center;
}

.redco-toggle-control label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    margin: 0;
}

.redco-toggle-control input[type="checkbox"] {
    margin: 0;
    width: 16px;
    height: 16px;
    accent-color: #2563eb;
}

/* Switch */
.redco-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.redco-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.redco-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: var(--transition);
    border-radius: 20px;
}

.redco-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .redco-slider {
    background-color: #00A66B !important; /* Explicit green color for consistency */
}

input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

input:checked + .redco-slider:before {
    transform: translateX(20px);
}

.redco-switch-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Premium feature styling */
select[disabled],
input[disabled],
textarea[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: rgba(0, 0, 0, 0.03);
}

.redco-switch-disabled .redco-slider {
    cursor: not-allowed;
}

/* Premium module toggle styles */
.redco-module-premium .redco-slider {
    background-color: #e0e0e0;
}

.redco-module-premium input:checked + .redco-slider {
    background-color: var(--warning-color);
}

/* Form Rows */
.redco-form-row {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    position: relative;
}

.redco-form-row:last-child {
    margin-bottom: 0;
}

.redco-form-label {
    margin-bottom: 5px;
    width: 100%;
    order: 1;
}

.redco-form-label label {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.redco-form-field {
    width: 100%;
    order: 2;
}

.redco-select {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: #fff;
    color: var(--text-color);
    font-size: 14px;
}

.redco-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: #fff;
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.5;
    min-height: 120px;
    resize: vertical;
    font-family: monospace;
}

.redco-form-help {
    font-size: 12px;
    color: var(--text-light);
    margin: 5px 0 0 0;
    display: block;
    width: 100%;
    clear: both;
    order: 3;
    padding-left: 0 !important;
}

.redco-form-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
}

/* Checkbox Group */
.redco-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.redco-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.redco-checkbox input {
    margin-right: 10px;
}

.redco-checkbox-label {
    font-size: 14px;
    color: var(--text-color);
}

.redco-checkbox-count {
    margin-left: auto;
    background-color: var(--bg-lighter);
    color: var(--text-light);
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
}

/* Stats Row */
.redco-stats-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.redco-stat {
    text-align: center;
    flex: 1;
}

.redco-stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 5px;
}

.redco-stat-label {
    font-size: 13px;
    color: var(--text-light);
}

/* Tools Tab Styles */
.redco-tools-section {
    max-width: 900px;
    margin: 0 auto;
}

/* Ensure Tools tab is always visible */
#redco-tools-tab {
    display: block;
}

.redco-nav-item[data-tab="redco-tools-tab"] {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-tools-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.redco-tools-col {
    flex: 1;
    padding: 0 15px;
    min-width: 250px;
    margin-bottom: 20px;
}

.redco-import-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.redco-import-file {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* Error Logs Styles */
.redco-error-log-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.redco-error-log-container {
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-top: 15px;
}

.redco-log-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 10px 15px;
    background-color: #f0f0f1;
    border-bottom: 1px solid var(--border-color);
}

.redco-log-filter {
    display: flex;
    align-items: center;
    gap: 8px;
}

#redco-reset-filters {
    padding: 4px 10px;
    height: auto;
    font-size: 12px;
    margin-left: auto;
}

#redco-reset-filters .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-right: 4px;
}

.redco-log-content-wrapper {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
}

.redco-log-content {
    margin: 0;
    padding: 15px;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.5;
    color: var(--text-color);
    background-color: #f8f9fa;
    overflow-x: auto;
}

.redco-log-content .error {
    color: #d63638;
}

.redco-log-content .warning {
    color: #dba617;
}

.redco-log-content .info {
    color: #2271b1;
}

.redco-log-info {
    padding: 10px 15px;
    font-size: 12px;
    color: var(--text-light);
    border-top: 1px solid var(--border-color);
    background-color: #f0f0f1;
}

.redco-empty-logs-message {
    padding: 30px;
    text-align: center;
    color: var(--text-light);
}

/* Hidden iframe for downloads */
iframe.redco-download-frame {
    position: absolute;
    width: 0;
    height: 0;
    border: 0;
    visibility: hidden;
}

.redco-import-file-name {
    margin-top: 10px;
    font-size: 13px;
    color: #646970;
}

.redco-import-settings {
    margin-left: 10px;
}

.redco-rollback-warning,
.redco-reset-warning {
    background-color: #fcf8e3;
    border-left: 4px solid #f0ad4e;
    padding: 10px 15px;
    margin: 15px 0;
    border-radius: 3px;
}

.redco-rollback-warning p,
.redco-reset-warning p {
    margin: 0;
    display: flex;
    align-items: center;
}

.redco-rollback-warning .dashicons,
.redco-reset-warning .dashicons {
    color: #f0ad4e;
    margin-right: 10px;
}

.redco-button-danger {
    background-color: #d9534f;
    color: #fff;
    border-color: #d43f3a;
}

.redco-button-danger:hover {
    background-color: #c9302c;
    border-color: #ac2925;
}

/* Premium Tab Styles */
/* Premium Container */
.redco-premium-container {
    max-width: 1000px;
    margin: 0 auto;
}

/* Premium Hero Section */
.redco-premium-hero {
    background: #fff;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    display: flex;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    align-items: center;
}

.redco-premium-hero-content {
    flex: 1;
    padding: 30px;
}

.redco-premium-hero-content h2 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-premium-hero-content p {
    margin: 0 0 20px 0;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-light);
}

/* Premium Features Section */
.redco-premium-features-section {
    margin-bottom: 30px;
}

.redco-premium-features-section h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.redco-premium-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.redco-premium-feature-card {
    background: #fff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: flex-start;
    padding: 20px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.redco-premium-feature-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.redco-premium-feature-icon {
    margin-right: 15px;
    color: var(--primary-color);
}

.redco-premium-feature-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
}

.redco-premium-feature-content {
    flex: 1;
}

.redco-premium-feature-content h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-premium-feature-content p {
    margin: 0;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-light);
}

/* Premium Plans Section */
.redco-premium-plans-section {
    margin-bottom: 30px;
}

.redco-premium-plans-section h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.redco-premium-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.redco-premium-plan-card {
    background: #fff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    position: relative;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.redco-premium-plan-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.redco-premium-plan-featured {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 166, 107, 0.2);
}

.redco-premium-plan-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--primary-color);
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 0 var(--border-radius) 0 var(--border-radius);
}

.redco-premium-plan-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.redco-premium-plan-header h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-premium-plan-price {
    margin-bottom: 5px;
}

.redco-premium-plan-amount {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
}

.redco-premium-plan-period {
    font-size: 14px;
    color: var(--text-light);
}

.redco-premium-plan-sites {
    font-size: 13px;
    color: var(--text-light);
}

.redco-premium-plan-features {
    padding: 15px 20px;
}

.redco-premium-plan-features ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.redco-premium-plan-features li {
    position: relative;
    padding-left: 22px;
    margin-bottom: 10px;
    font-size: 13px;
    color: var(--text-color);
}

.redco-premium-plan-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.redco-premium-plan-action {
    padding: 0 20px 20px 20px;
    text-align: center;
}

/* Premium CTA */
.redco-premium-cta {
    background: #fff;
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.redco-premium-cta h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-premium-cta p {
    margin: 0 0 20px 0;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-light);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.redco-premium-guarantee {
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.redco-premium-guarantee-icon {
    width: 18px;
    height: 18px;
    background-color: var(--success-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 11px;
}

.redco-premium-guarantee-icon:before {
    content: "✓";
}

.redco-premium-guarantee-text {
    font-size: 13px;
    color: var(--text-light);
    font-weight: 500;
}

/* FAQ Section */
.redco-faq-section {
    margin-bottom: 30px;
}

.redco-faq-section h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.redco-faq-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.redco-faq-item {
    background: #fff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: box-shadow 0.2s ease;
}

.redco-faq-item:hover {
    box-shadow: var(--shadow-md);
}

.redco-faq-question {
    padding: 15px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--text-color);
    background-color: #fff;
    transition: background-color 0.2s ease;
}

.redco-faq-question:hover {
    background-color: var(--bg-lighter);
}

.redco-faq-toggle {
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.redco-faq-item.active .redco-faq-toggle {
    transform: rotate(180deg);
}

.redco-faq-answer {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.redco-faq-item.active .redco-faq-answer {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    max-height: 1000px;
}

.redco-faq-answer p {
    margin: 0 0 10px 0;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-light);
}

.redco-faq-answer p:last-child {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .redco-premium-hero {
        flex-direction: column;
        align-items: center;
    }

    .redco-premium-hero-content {
        padding: 20px;
        text-align: center;
    }

    .redco-premium-features-grid,
    .redco-premium-plans-grid {
        grid-template-columns: 1fr;
    }
}

/* Add-Ons Tab Styles */
.redco-addons-container {
    max-width: 1200px;
    margin: 0 auto;
}

.redco-addons-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.redco-addons-header-content h2 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-addons-header-content p {
    margin: 0;
    font-size: 14px;
    color: var(--text-light);
}

.redco-addons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.redco-addon-card {
    background: #fff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
}

.redco-addon-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.redco-addon-active {
    border-color: var(--success-color);
}

.redco-addon-premium {
    border-color: var(--primary-color);
}

.redco-addon-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

/* Removed duplicate orange badge classes - using unified purple badge system */

.redco-button-disabled {
    background-color: #e9ecef !important;
    color: #6c757d !important;
    cursor: not-allowed !important;
    border: 1px solid #ced4da !important;
    pointer-events: none !important;
}

.redco-addon-icon {
    margin-bottom: 10px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-lighter);
    border-radius: 50%;
}

.redco-addon-icon .dashicons {
    font-size: 30px;
    width: 30px;
    height: 30px;
    color: var(--primary-color);
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
}

.redco-addon-icon img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 50%;
}

.redco-addon-title {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-addon-version {
    font-size: 12px;
    color: var(--text-light);
}

.redco-addon-content {
    padding: 15px;
    flex-grow: 1;
}

.redco-addon-description {
    margin: 0;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-light);
}

.redco-addon-footer {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.redco-addon-status {
    font-size: 12px;
    font-weight: 600;
}

/* Ensure dashicons in add-on cards are always visible */
.redco-addon-card .redco-addon-icon .dashicons,
.redco-addon-available .redco-addon-icon .dashicons {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
}

.redco-addon-status-active {
    color: var(--success-color);
}

.redco-addon-status-inactive {
    color: var(--text-light);
}

.redco-addon-actions {
    display: flex;
    gap: 10px;
}

.redco-addon-actions .redco-button {
    font-size: 12px;
    padding: 5px 10px;
}

/* Ensure dashicons in available add-ons section are always visible */
.redco-addons-available .redco-addon-icon .dashicons {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
}

.redco-addon-settings .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-right: 3px;
}

.redco-no-addons {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.redco-no-addons-icon {
    margin-bottom: 15px;
}

.redco-no-addons-icon .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: var(--text-light);
}

.redco-no-addons h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-no-addons p {
    margin: 0;
    font-size: 14px;
    color: var(--text-light);
}

.redco-addons-available,
.redco-addons-coming-soon {
    margin-top: 30px;
}

.redco-addons-available h3,
.redco-addons-coming-soon h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.redco-addons-available p,
.redco-addons-coming-soon p {
    margin: 0 0 20px 0;
    font-size: 14px;
    color: var(--text-light);
}

.redco-addon-available {
    background-color: #f9f9f9;
}

/* Input Field */
.redco-input {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: #fff;
    color: var(--text-color);
    font-size: 14px;
}

.redco-input:disabled {
    background-color: var(--bg-lighter);
    cursor: not-allowed;
}

/* Disabled Form Elements */
.redco-select-disabled,
.redco-input-disabled,
input[type="text"]:disabled,
textarea:disabled,
select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f0f0f1;
    color: #8c8f94;
}

/* Premium disabled elements */
.redco-switch-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
    .redco-dashboard-columns {
        flex-direction: column;
    }
}

@media screen and (max-width: 1100px) {
    .redco-status-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media screen and (max-width: 900px) {
    .redco-status-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 600px) {
    .redco-status-grid {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 782px) {
    .redco-layout {
        flex-direction: column;
    }

    .redco-sidebar {
        width: 100%;
    }

    .redco-nav {
        display: flex;
        flex-wrap: wrap;
    }

    .redco-nav-item {
        flex: 1 0 auto;
    }

    .redco-account-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .redco-view-account {
        align-self: stretch;
        text-align: left;
    }

    .redco-toggle-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .redco-toggle-info {
        padding-right: 0;
    }

    .redco-toggle-control {
        align-self: flex-start;
    }
}

@media screen and (max-width: 600px) {
    .redco-tab-content {
        padding: 20px 15px;
    }

    .redco-nav {
        overflow-x: auto;
        flex-wrap: nowrap;
    }

    .redco-nav-item {
        flex: 0 0 auto;
    }

    .redco-stats-row {
        flex-direction: column;
        gap: 15px;
    }
}

/* Notifications */
.redco-notification {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    position: relative;
    display: flex;
    align-items: center;
    border-left: 4px solid transparent;
}

.redco-notification-success {
    background-color: #F0F9EB;
    border-color: var(--success-color);
    color: var(--success-color);
}

.redco-notification-error {
    background-color: #FEF0F0;
    border-color: var(--error-color);
    color: var(--error-color);
}

.redco-notification-warning {
    background-color: #FDF6EC;
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.redco-notification-info {
    background-color: #F4F4F5;
    border-color: var(--text-light);
    color: var(--text-light);
}

/* Modal */
.redco-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100000;
}

.redco-modal {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    width: 700px;
    max-width: 90%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.redco-modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.redco-modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
}

.redco-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    line-height: 1;
    color: var(--text-light);
    cursor: pointer;
    padding: 0;
}

.redco-modal-close:hover {
    color: var(--text-color);
}

.redco-modal-content {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    max-height: calc(90vh - 140px); /* Account for header and footer */
}

/* Modal section styles */
.redco-modal-section {
    margin-bottom: 25px;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative;
}

.redco-modal-section:last-child {
    margin-bottom: 0;
}

.redco-modal-section h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    position: relative;
}

.redco-modal-section h3::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0; /* Changed from 50px to 0 to remove the green line */
    height: 0; /* Changed from 2px to 0 */
    background: transparent; /* Changed from gradient to transparent */
    opacity: 0;
}

/* Ensure form elements in modals are visible */
.redco-addon-settings-form {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    overflow: visible !important;
}

.redco-addon-settings-form .redco-form-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 15px !important;
    align-items: flex-start !important;
}

.redco-addon-settings-form .redco-toggle-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 15px 0 !important;
    border-bottom: 1px solid var(--border-color) !important;
}

.redco-addon-settings-form .redco-toggle-row:last-child {
    border-bottom: none !important;
}

/* Fix for form fields in modals */
.redco-addon-settings-form .redco-form-label {
    margin-bottom: 5px;
    width: 100%;
}

.redco-addon-settings-form .redco-form-field {
    width: 100%;
}

.redco-modal-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.redco-button-premium {
    background: #00A66B;
    color: #fff;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.redco-button-premium:hover,
.redco-button-premium:focus {
    background: #00C67F;
    color: #fff;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
    outline: none;
}

.redco-button-premium:focus {
    box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(0, 166, 107, 0.4);
}

/* Optimization Tips */
.redco-tips-list {
    margin: 0;
    padding: 0;
}

.redco-tip-item {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: var(--border-radius);
    border-left: 4px solid transparent;
    background-color: #f9f9f9;
}

.redco-tip-item:last-child {
    margin-bottom: 0;
}

.redco-tip-item.priority-high {
    border-left-color: var(--error-color);
}

.redco-tip-item.priority-medium {
    border-left-color: var(--warning-color);
}

.redco-tip-item.priority-low {
    border-left-color: var(--success-color);
}

.redco-tip-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.redco-tip-icon {
    margin-right: 10px;
    color: var(--primary-color);
}

.redco-tip-title {
    font-weight: 600;
    font-size: 15px;
    color: var(--text-color);
}

.redco-tip-content p {
    margin: 0;
    color: var(--text-light);
    font-size: 14px;
    line-height: 1.5;
}

/* Animations */
@keyframes redco-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.redco-spin {
    animation: redco-spin 1s infinite linear;
    display: inline-block;
}

/* Module Cards */
.redco-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.redco-module-card {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    display: flex;
    flex-direction: column;
}

.redco-module-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.redco-module-enabled {
    border-color: var(--success-color);
}

.redco-module-premium {
    border-color: var(--warning-color);
}

/* Removed conflicting badge definition - using unified system in modules.css */

.redco-module-header {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-lighter);
    flex-wrap: wrap;
    gap: 10px;
}

.redco-module-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Module title dashicons styles are now in redco-optimizer-modules.css */

.redco-premium-tag {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #00C67F 0%, #00A66B 100%);
    color: #fff;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
}

.redco-module-content {
    padding: 20px;
    flex: 1;
}

.redco-module-description {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: var(--text-light);
    line-height: 1.5;
}

/* Module feature styles are now in redco-optimizer-modules.css */

/* Configure Button in Module Footer */
.redco-module-footer .redco-button {
    padding: 8px 16px;
    font-weight: 600;
    letter-spacing: 0.3px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

/* Module footer button styles are now in redco-optimizer-modules.css */

/* Removed the :before pseudo-element since we're using dashicons directly */

/* This section is now removed as we're using the .redco-switch and .redco-slider classes for all toggle switches */

/* Responsive Design */
@media screen and (max-width: 1200px) {
    .redco-dashboard-grid {
        grid-template-columns: 1fr;
    }

    .redco-overview-score {
        flex-direction: column;
    }

    .redco-score-container {
        flex: 0 0 auto;
        padding: 20px;
    }

    .redco-score-circle {
        width: 100px;
        height: 100px;
    }

    .redco-score-svg {
        width: 100px;
        height: 100px;
    }
}

@media screen and (max-width: 782px) {
    .redco-optimizer-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .redco-header-actions {
        align-self: flex-start;
    }

    .redco-welcome-banner {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }

    .redco-welcome-cta {
        align-self: flex-start;
    }

    .redco-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .redco-stats-grid {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 600px) {
    .redco-optimizer-content {
        padding: 20px 15px;
    }

    .redco-optimizer-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 5px;
    }

    .redco-optimizer-tabs .redco-tab {
        padding: 10px 15px;
        font-size: 13px;
    }

    .redco-actions-grid {
        grid-template-columns: 1fr;
    }

    .redco-action-button:hover::after,
    .redco-action-button:hover::before {
        display: none;
    }
}

.redco-action-icon::before {
    content: '';
    width: 24px;
    height: 24px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    filter: brightness(0) invert(1);
}

.redco-action-icon.cache::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M5 20a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V8h2V6h-4V4a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2v2H3v2h2zM9 4h6v2H9zM8 8h9v12H7V8z"/><path d="M9 10h2v8H9zm4 0h2v8h-2z"/></svg>');
}

.redco-action-icon.image::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19.999 4h-16c-1.103 0-2 .897-2 2v12c0 1.103.897 2 2 2h16c1.103 0 2-.897 2-2V6c0-1.103-.897-2-2-2zm-13.5 3a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3zm5.5 10h-7l4-5 1.5 2 3-4 5.5 7h-7z"/></svg>');
}

.redco-action-icon.database::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 6c0-2.168-3.663-4-8-4S4 3.832 4 6v2c0 2.168 3.663 4 8 4s8-1.832 8-4V6zm-8 13c-4.337 0-8-1.832-8-4v3c0 2.168 3.663 4 8 4s8-1.832 8-4v-3c0 2.168-3.663 4-8 4z"/><path d="M20 10c0 2.168-3.663 4-8 4s-8-1.832-8-4v3c0 2.168 3.663 4 8 4s8-1.832 8-4v-3z"/></svg>');
}

.redco-action-icon.scan::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"/><path d="M11 11h2v6h-2zm0-4h2v2h-2z"/></svg>');
}

.redco-action-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-top: 5px;
}

/* Global notification area */
#redco-global-notification-area {
    position: fixed;
    top: 32px;
    left: 160px;
    right: 0;
    z-index: 9999;
    padding: 15px;
    display: none; /* Hidden by default */
}

#redco-global-notification-area .notice {
    margin: 5px 15px 15px;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media screen and (max-width: 782px) {
    #redco-global-notification-area {
        left: 0;
        top: 46px;
    }
}

/* Expandable Sections for Delay JavaScript */
.redco-expandable-sections {
    margin: 25px 0;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.redco-expandable-sections.hidden {
    display: none;
}

.redco-expandable-section {
    border-bottom: 1px solid var(--border-color);
}

.redco-expandable-section:last-child {
    border-bottom: none;
}

.redco-expandable-header {
    padding: 16px 18px;
    background-color: var(--bg-light);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.redco-expandable-header:hover {
    background-color: #f5f5f5;
}

.redco-expandable-section.active .redco-expandable-header {
    background-color: #f8f8f8;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.redco-expandable-header h4 {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.redco-expandable-header h4 .dashicons {
    margin-right: 10px;
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.redco-expandable-toggle {
    color: var(--text-light);
    transition: transform 0.2s ease;
}

.redco-expandable-section.active .redco-expandable-toggle {
    transform: rotate(180deg);
}

.redco-expandable-content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    background-color: #fff;
    display: none; /* Hide content by default */
    visibility: hidden; /* Ensure it's not visible */
}

.redco-expandable-section.active .redco-expandable-content {
    padding: 18px 20px;
    max-height: 500px;
    overflow-y: auto;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: block; /* Show content when active */
    visibility: visible; /* Ensure it's visible */
}

.redco-checkbox-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 5px 0;
}

.redco-checkbox-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    cursor: pointer;
    padding: 2px 0;
}

.redco-checkbox-item input[type="checkbox"] {
    margin-right: 10px;
    width: 16px;
    height: 16px;
}

.redco-checkbox-text {
    font-size: 14px;
    color: var(--text-color);
    font-weight: 400;
}

/* Removed duplicate badge styling - using unified system in modules.css */

/* Badge type styles - inherit from unified .redco-premium-badge system */
.redco-coming-soon-badge,
.redco-premium-only-badge {
    /* All styling inherited from .redco-premium-badge in modules.css */
}

/* Advanced feature styling */
.redco-premium-feature {
    position: relative;
}

.redco-premium-feature.redco-disabled {
    opacity: 0.6;
    pointer-events: none;
}

.redco-premium-feature.redco-disabled input[type="checkbox"] {
    cursor: not-allowed;
}

.redco-premium-feature.redco-disabled label {
    cursor: not-allowed;
    color: #999;
}

/* Overlay for disabled features */
.redco-premium-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 4px;
}

.redco-premium-overlay-content {
    text-align: center;
    padding: 10px;
}

.redco-premium-overlay-content .redco-premium-badge {
    margin: 0 0 5px 0;
    display: block;
}

.redco-premium-overlay-content p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

/* Premium Tab Styles */
.redco-premium-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Premium Hero Section */
.redco-premium-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 40px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.redco-premium-hero h2 {
    font-size: 2.5em;
    margin-bottom: 20px;
    font-weight: 700;
}

.redco-premium-hero p {
    font-size: 1.2em;
    margin-bottom: 30px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.redco-premium-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.redco-premium-stat {
    text-align: center;
}

.redco-premium-stat-number {
    display: block;
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 5px;
}

.redco-premium-stat-label {
    font-size: 0.9em;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Premium Features Overview */
.redco-premium-features-overview {
    background: white;
    padding: 40px;
    border-radius: 12px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.redco-premium-features-overview h3 {
    font-size: 2em;
    margin-bottom: 15px;
    color: #2c3e50;
}

.redco-premium-features-overview > p {
    font-size: 1.1em;
    color: #7f8c8d;
    margin-bottom: 30px;
}

.redco-premium-features-list {
    display: grid;
    gap: 30px;
}

.redco-premium-module-group {
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    overflow: hidden;
}

.redco-premium-module-header {
    background: #f8f9fa;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    border-bottom: 1px solid #e1e8ed;
}

.redco-premium-module-icon {
    font-size: 24px;
    color: #667eea;
}

.redco-premium-module-header h4 {
    margin: 0;
    font-size: 1.3em;
    color: #2c3e50;
    flex: 1;
}

.redco-premium-module-count {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
}

.redco-premium-module-features {
    padding: 0;
}

.redco-premium-feature-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #f1f3f4;
}

.redco-premium-feature-item:last-child {
    border-bottom: none;
}

.redco-premium-feature-info h5 {
    margin: 0 0 8px 0;
    font-size: 1.1em;
    color: #2c3e50;
}

.redco-premium-feature-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.95em;
}

.redco-premium-feature-status {
    flex-shrink: 0;
    margin-left: 20px;
}

/* Premium Benefits Section */
.redco-premium-benefits-section {
    background: white;
    padding: 40px;
    border-radius: 12px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.redco-premium-benefits-section h3 {
    font-size: 2em;
    margin-bottom: 30px;
    color: #2c3e50;
    text-align: center;
}

.redco-premium-benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.redco-premium-benefit-card {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.redco-premium-benefit-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.redco-premium-benefit-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.redco-premium-benefit-content h4 {
    margin: 0 0 10px 0;
    font-size: 1.2em;
    color: #2c3e50;
}

.redco-premium-benefit-content p {
    margin: 0;
    color: #7f8c8d;
    line-height: 1.6;
}

/* Removed orange coming soon section - using unified purple badge system */

/* Newsletter Section */
.redco-premium-newsletter {
    background: white;
    padding: 40px;
    border-radius: 12px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.redco-premium-newsletter h3 {
    font-size: 2em;
    margin-bottom: 15px;
    color: #2c3e50;
}

.redco-premium-newsletter p {
    font-size: 1.1em;
    color: #7f8c8d;
    margin-bottom: 30px;
}

.redco-premium-newsletter-form {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.redco-premium-email-input {
    padding: 12px 20px;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    font-size: 16px;
    min-width: 300px;
    flex: 1;
    max-width: 400px;
}

.redco-premium-email-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.redco-premium-notify-btn {
    padding: 12px 30px;
    font-size: 16px;
    white-space: nowrap;
}

.redco-premium-newsletter-benefits {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.redco-premium-newsletter-benefit {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7f8c8d;
    font-size: 0.9em;
}

.redco-premium-newsletter-benefit .dashicons {
    color: #667eea;
    font-size: 16px;
}

/* Free Features Section */
.redco-premium-free-features {
    background: white;
    padding: 40px;
    border-radius: 12px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.redco-premium-free-features h3 {
    font-size: 2em;
    margin-bottom: 15px;
    color: #2c3e50;
    text-align: center;
}

.redco-premium-free-features > p {
    font-size: 1.1em;
    color: #7f8c8d;
    margin-bottom: 30px;
    text-align: center;
}

.redco-premium-free-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.redco-premium-free-feature {
    text-align: center;
    padding: 20px;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.redco-premium-free-feature:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.redco-premium-free-feature .dashicons {
    font-size: 40px;
    color: #00A66B;
    margin-bottom: 15px;
}

.redco-premium-free-feature h4 {
    margin: 0 0 10px 0;
    font-size: 1.1em;
    color: #2c3e50;
}

.redco-premium-free-feature p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9em;
    line-height: 1.5;
}

.redco-premium-free-cta {
    text-align: center;
}

/* Premium Tab Responsive */
@media (max-width: 768px) {
    .redco-premium-hero {
        padding: 40px 20px;
    }

    .redco-premium-hero h2 {
        font-size: 2em;
    }

    .redco-premium-stats {
        gap: 20px;
    }

    .redco-premium-benefits-grid {
        grid-template-columns: 1fr;
    }

    .redco-premium-coming-soon-features {
        flex-direction: column;
        gap: 15px;
    }

    .redco-premium-newsletter-form {
        flex-direction: column;
        align-items: center;
    }

    .redco-premium-email-input {
        min-width: 250px;
        max-width: 100%;
    }

    .redco-premium-free-grid {
        grid-template-columns: 1fr;
    }
}

/* Database Tab Styles */
.redco-database-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 0;
}

.redco-stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.redco-stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #00a66b;
    line-height: 1;
    margin-bottom: 8px;
}

.redco-stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.redco-cleanup-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.redco-cleanup-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    padding-bottom: 8px;
    border-bottom: 2px solid #00a66b;
}

.redco-cleanup-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.redco-cleanup-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0;
}

.redco-cleanup-item:hover {
    border-color: #00a66b;
    box-shadow: 0 2px 4px rgba(0, 166, 107, 0.1);
}

.redco-cleanup-item input[type="checkbox"] {
    margin: 0;
    flex-shrink: 0;
}

.redco-cleanup-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.redco-cleanup-title {
    font-weight: 600;
    color: #1d2327;
    font-size: 14px;
}

.redco-cleanup-count {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.redco-cleanup-description {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.redco-warning-box {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    margin-bottom: 20px;
}

.redco-warning-box p {
    margin: 0;
    color: #856404;
    font-size: 14px;
    line-height: 1.4;
}

.redco-advanced-cleanup .redco-cleanup-section {
    margin-bottom: 0;
}

.redco-cleanup-item.redco-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.redco-cleanup-item.redco-disabled:hover {
    border-color: #e0e0e0;
    box-shadow: none;
}

/* ===================================
   CHECKBOX STYLES - CONSOLIDATED
   =================================== */

/* Custom checkbox styling */
input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e1;
    border-radius: 3px;
    outline: none;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
    margin-right: 8px;
    background-color: #fff;
}

/* Checked state */
input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Checkmark */
input[type="checkbox"]:checked::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Focus state */
input[type="checkbox"]:focus {
    box-shadow: 0 0 0 2px rgba(0, 166, 107, 0.2);
}

/* Hover state */
input[type="checkbox"]:hover {
    border-color: var(--primary-color);
}

/* Disabled state */
input[type="checkbox"]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f0f0f1;
    border-color: #dcdcde;
}

/* Database tab uses the standard badge system defined earlier in this file */

.redco-scheduled-cleanup .redco-toggle-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.redco-scheduled-cleanup .redco-cleanup-item {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
}

.redco-scheduled-cleanup .redco-cleanup-item:hover {
    border: none;
    box-shadow: none;
}

.redco-scheduled-cleanup .redco-cleanup-content {
    flex-direction: row;
    align-items: center;
    gap: 12px;
}

/* Database Tab Responsive */
@media (max-width: 768px) {
    .redco-database-stats {
        grid-template-columns: 1fr;
    }

    .redco-cleanup-grid {
        grid-template-columns: 1fr;
    }
}
