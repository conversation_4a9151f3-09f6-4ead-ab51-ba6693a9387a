<?php
/**
 * Error Logger Class
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Error Logger Class
 *
 * This class handles error logging for the Redco Optimizer plugin.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Team
 */
class Redco_Optimizer_Error_Logger {

    /**
     * The log file path.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $log_file    The path to the log file.
     */
    private $log_file;

    /**
     * The maximum log file size in bytes.
     *
     * @since    1.0.0
     * @access   private
     * @var      int    $max_file_size    The maximum log file size in bytes.
     */
    private $max_file_size = 5242880; // 5MB

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $upload_dir = wp_upload_dir();
        $this->log_file = $upload_dir['basedir'] . '/redco-optimizer-logs/error-log.txt';

        // Create logs directory if it doesn't exist
        $logs_dir = dirname($this->log_file);
        if (!file_exists($logs_dir)) {
            wp_mkdir_p($logs_dir);

            // Create .htaccess file to protect logs
            $htaccess_file = $logs_dir . '/.htaccess';
            if (!file_exists($htaccess_file)) {
                $htaccess_content = "# Deny access to all files in this directory\n";
                $htaccess_content .= "<Files \"*\">\n";
                $htaccess_content .= "Order Allow,Deny\n";
                $htaccess_content .= "Deny from all\n";
                $htaccess_content .= "</Files>\n";
                file_put_contents($htaccess_file, $htaccess_content);
            }

            // Create index.php file for extra security
            $index_file = $logs_dir . '/index.php';
            if (!file_exists($index_file)) {
                file_put_contents($index_file, "<?php\n// Silence is golden.");
            }
        }
    }

    /**
     * Log an error message.
     *
     * @since    1.0.0
     * @param    string    $message    The error message.
     * @param    string    $level      The error level (error, warning, info).
     * @param    string    $context    The context of the error.
     */
    public function log($message, $level = 'error', $context = '') {
        // Check if log file is too large
        $this->rotate_log_if_needed();

        // Format the log entry
        $timestamp = current_time('mysql');
        $ip = isset($_SERVER['REMOTE_ADDR']) ? sanitize_text_field($_SERVER['REMOTE_ADDR']) : 'unknown';
        $user_id = get_current_user_id();
        $user_info = ($user_id > 0) ? " | User ID: {$user_id}" : " | User: Not logged in";

        // Format context
        $context_info = !empty($context) ? " | Context: {$context}" : "";

        // Ensure consistent level formatting (uppercase with consistent spacing)
        $level = strtoupper(trim($level));

        // Format the log entry
        $log_entry = "[{$timestamp}] | {$level} | IP: {$ip}{$user_info}{$context_info} | {$message}\n";

        // Write to log file
        file_put_contents($this->log_file, $log_entry, FILE_APPEND);
    }

    /**
     * Log an error.
     *
     * @since    1.0.0
     * @param    string    $message    The error message.
     * @param    string    $context    The context of the error.
     */
    public function error($message, $context = '') {
        $this->log($message, 'ERROR', $context);
    }

    /**
     * Log a warning.
     *
     * @since    1.0.0
     * @param    string    $message    The warning message.
     * @param    string    $context    The context of the warning.
     */
    public function warning($message, $context = '') {
        $this->log($message, 'WARNING', $context);
    }

    /**
     * Log an info message.
     *
     * @since    1.0.0
     * @param    string    $message    The info message.
     * @param    string    $context    The context of the info message.
     */
    public function info($message, $context = '') {
        $this->log($message, 'INFO', $context);
    }

    /**
     * Get the log file content.
     *
     * @since    1.0.0
     * @return   string    The log file content.
     */
    public function get_log_content() {
        if (file_exists($this->log_file)) {
            return file_get_contents($this->log_file);
        }
        return '';
    }

    /**
     * Clear the log file.
     *
     * @since    1.0.0
     * @return   boolean   Whether the log file was cleared.
     */
    public function clear_log() {
        if (file_exists($this->log_file)) {
            return file_put_contents($this->log_file, '') !== false;
        }
        return true;
    }

    /**
     * Get the log file path.
     *
     * @since    1.0.0
     * @return   string    The log file path.
     */
    public function get_log_file_path() {
        return $this->log_file;
    }

    /**
     * Rotate the log file if it's too large.
     *
     * @since    1.0.0
     */
    private function rotate_log_if_needed() {
        if (file_exists($this->log_file) && filesize($this->log_file) > $this->max_file_size) {
            $backup_file = $this->log_file . '.' . date('Y-m-d-H-i-s') . '.bak';
            rename($this->log_file, $backup_file);

            // Keep only the 5 most recent backup files
            $logs_dir = dirname($this->log_file);
            $backup_files = glob($logs_dir . '/*.bak');

            if (count($backup_files) > 5) {
                usort($backup_files, function($a, $b) {
                    return filemtime($a) - filemtime($b);
                });

                $files_to_delete = array_slice($backup_files, 0, count($backup_files) - 5);
                foreach ($files_to_delete as $file) {
                    unlink($file);
                }
            }
        }
    }
}
