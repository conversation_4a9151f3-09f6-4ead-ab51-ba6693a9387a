<?php
/**
 * Schema Markup Generator Settings Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/schema-markup-generator/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
?>

<div class="redco-addon-settings-form">
    <form method="post" action="">
        <?php wp_nonce_field('redco_schema_markup_generator_save_settings', 'redco_schema_markup_generator_nonce'); ?>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Schema Markup Settings', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Schema Markup', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Enable structured data markup for better SEO.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label>
                            <input type="checkbox" name="enabled" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : 0, 1); ?> value="1">
                            <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Organization Schema', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Organization Schema', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Add organization schema to your website.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label>
                            <input type="checkbox" name="organization_schema" <?php checked(isset($settings['organization_schema']) ? $settings['organization_schema'] : 1, 1); ?> value="1">
                            <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="organization_name"><?php esc_html_e('Organization Name', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="text" id="organization_name" name="organization_name" class="redco-input" value="<?php echo esc_attr(isset($settings['organization_name']) ? $settings['organization_name'] : get_bloginfo('name')); ?>">
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="organization_logo"><?php esc_html_e('Organization Logo', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="url" id="organization_logo" name="organization_logo" class="redco-input" value="<?php echo esc_url(isset($settings['organization_logo']) ? $settings['organization_logo'] : ''); ?>">
                        <p class="redco-form-help"><?php esc_html_e('URL to your organization logo. Recommended size: 600x60px.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="organization_type"><?php esc_html_e('Organization Type', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <select id="organization_type" name="organization_type" class="redco-select">
                            <option value="Organization" <?php selected(isset($settings['organization_type']) ? $settings['organization_type'] : 'Organization', 'Organization'); ?>><?php esc_html_e('Organization', 'redco-optimizer'); ?></option>
                            <option value="Corporation" <?php selected(isset($settings['organization_type']) ? $settings['organization_type'] : 'Organization', 'Corporation'); ?>><?php esc_html_e('Corporation', 'redco-optimizer'); ?></option>
                            <option value="EducationalOrganization" <?php selected(isset($settings['organization_type']) ? $settings['organization_type'] : 'Organization', 'EducationalOrganization'); ?>><?php esc_html_e('Educational Organization', 'redco-optimizer'); ?></option>
                            <option value="GovernmentOrganization" <?php selected(isset($settings['organization_type']) ? $settings['organization_type'] : 'Organization', 'GovernmentOrganization'); ?>><?php esc_html_e('Government Organization', 'redco-optimizer'); ?></option>
                            <option value="MedicalOrganization" <?php selected(isset($settings['organization_type']) ? $settings['organization_type'] : 'Organization', 'MedicalOrganization'); ?>><?php esc_html_e('Medical Organization', 'redco-optimizer'); ?></option>
                            <option value="NGO" <?php selected(isset($settings['organization_type']) ? $settings['organization_type'] : 'Organization', 'NGO'); ?>><?php esc_html_e('NGO', 'redco-optimizer'); ?></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Website Schema', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Website Schema', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Add website schema to your website.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label>
                            <input type="checkbox" name="website_schema" <?php checked(isset($settings['website_schema']) ? $settings['website_schema'] : 1, 1); ?> value="1">
                            <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Breadcrumbs Schema', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Breadcrumbs Schema', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Add breadcrumbs schema to your website.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label>
                            <input type="checkbox" name="breadcrumbs_schema" <?php checked(isset($settings['breadcrumbs_schema']) ? $settings['breadcrumbs_schema'] : 1, 1); ?> value="1">
                            <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Article Schema', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Article Schema', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Add article schema to your posts.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label>
                            <input type="checkbox" name="article_schema" <?php checked(isset($settings['article_schema']) ? $settings['article_schema'] : 1, 1); ?> value="1">
                            <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="article_post_types"><?php esc_html_e('Article Post Types', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="text" id="article_post_types" name="article_post_types" class="redco-input" value="<?php echo esc_attr(isset($settings['article_post_types']) ? $settings['article_post_types'] : 'post'); ?>" placeholder="post">
                        <p class="redco-form-help"><?php esc_html_e('Comma-separated list of post types to add article schema to.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Product Schema', 'redco-optimizer'); ?></h3>
                <span class="redco-premium-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Product Schema', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Add product schema to your products.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label>
                            <input type="checkbox" name="product_schema" <?php checked(isset($settings['product_schema']) ? $settings['product_schema'] : 0, 1); ?> value="1" <?php echo !defined('REDCO_PREMIUM') ? 'disabled' : ''; ?>>
                            <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="product_post_types"><?php esc_html_e('Product Post Types', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="text" id="product_post_types" name="product_post_types" class="redco-input" value="<?php echo esc_attr(isset($settings['product_post_types']) ? $settings['product_post_types'] : 'product'); ?>" placeholder="product" <?php echo !defined('REDCO_PREMIUM') ? 'disabled' : ''; ?>>
                        <p class="redco-form-help"><?php esc_html_e('Comma-separated list of post types to add product schema to.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <?php if (!defined('REDCO_PREMIUM')) : ?>
                <div class="redco-premium-notice">
                    <p><?php esc_html_e('Product Schema is a premium feature. Please upgrade to unlock this feature.', 'redco-optimizer'); ?></p>
                    <a href="#" class="redco-button redco-button-primary redco-upgrade-button"><?php esc_html_e('Upgrade to Premium', 'redco-optimizer'); ?></a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('FAQ Schema', 'redco-optimizer'); ?></h3>
                <span class="redco-premium-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable FAQ Schema', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Add FAQ schema to your content.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label>
                            <input type="checkbox" name="faq_schema" <?php checked(isset($settings['faq_schema']) ? $settings['faq_schema'] : 0, 1); ?> value="1" <?php echo !defined('REDCO_PREMIUM') ? 'disabled' : ''; ?>>
                            <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="faq_post_types"><?php esc_html_e('FAQ Post Types', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="text" id="faq_post_types" name="faq_post_types" class="redco-input" value="<?php echo esc_attr(isset($settings['faq_post_types']) ? $settings['faq_post_types'] : 'post,page'); ?>" placeholder="post,page" <?php echo !defined('REDCO_PREMIUM') ? 'disabled' : ''; ?>>
                        <p class="redco-form-help"><?php esc_html_e('Comma-separated list of post types to add FAQ schema to.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <?php if (!defined('REDCO_PREMIUM')) : ?>
                <div class="redco-premium-notice">
                    <p><?php esc_html_e('FAQ Schema is a premium feature. Please upgrade to unlock this feature.', 'redco-optimizer'); ?></p>
                    <a href="#" class="redco-button redco-button-primary redco-upgrade-button"><?php esc_html_e('Upgrade to Premium', 'redco-optimizer'); ?></a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Local Business Schema', 'redco-optimizer'); ?></h3>
                <span class="redco-premium-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Local Business Schema', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Add local business schema to your website.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label>
                            <input type="checkbox" name="local_business_schema" <?php checked(isset($settings['local_business_schema']) ? $settings['local_business_schema'] : 0, 1); ?> value="1" <?php echo !defined('REDCO_PREMIUM') ? 'disabled' : ''; ?>>
                            <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                        </label>
                    </div>
                </div>

                <?php if (!defined('REDCO_PREMIUM')) : ?>
                <div class="redco-premium-notice">
                    <p><?php esc_html_e('Local Business Schema is a premium feature. Please upgrade to unlock this feature.', 'redco-optimizer'); ?></p>
                    <a href="#" class="redco-button redco-button-primary redco-upgrade-button"><?php esc_html_e('Upgrade to Premium', 'redco-optimizer'); ?></a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Custom Schema', 'redco-optimizer'); ?></h3>
                <span class="redco-premium-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span>
            </div>
            <div class="redco-card-content">
                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="custom_schema"><?php esc_html_e('Custom Schema JSON', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <textarea id="custom_schema" name="custom_schema" class="redco-textarea" rows="10" <?php echo !defined('REDCO_PREMIUM') ? 'disabled' : ''; ?>><?php echo esc_textarea(isset($settings['custom_schema']) ? $settings['custom_schema'] : ''); ?></textarea>
                        <p class="redco-form-help"><?php esc_html_e('Enter custom schema JSON to add to your website.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <?php if (!defined('REDCO_PREMIUM')) : ?>
                <div class="redco-premium-notice">
                    <p><?php esc_html_e('Custom Schema is a premium feature. Please upgrade to unlock this feature.', 'redco-optimizer'); ?></p>
                    <a href="#" class="redco-button redco-button-primary redco-upgrade-button"><?php esc_html_e('Upgrade to Premium', 'redco-optimizer'); ?></a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="redco-form-actions">
            <button type="submit" name="redco_schema_markup_generator_save_settings" class="redco-button redco-button-primary">
                <span class="dashicons dashicons-yes"></span>
                <?php esc_html_e('Save Settings', 'redco-optimizer'); ?>
            </button>
        </div>
    </form>
</div>
