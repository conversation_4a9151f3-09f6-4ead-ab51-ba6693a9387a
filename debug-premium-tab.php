<?php
/**
 * Debug Premium Tab Visibility
 */

// Load WordPress
require_once('../../../wp-load.php');

echo "=== PREMIUM TAB DEBUG ===\n\n";

// 1. Check if premium tab file exists
$premium_tab_file = __DIR__ . '/admin/partials/redco-optimizer-admin-premium-tab.php';
echo "1. PREMIUM TAB FILE CHECK:\n";
echo "File path: $premium_tab_file\n";
echo "File exists: " . (file_exists($premium_tab_file) ? 'YES' : 'NO') . "\n";
if (file_exists($premium_tab_file)) {
    echo "File size: " . filesize($premium_tab_file) . " bytes\n";
    echo "File readable: " . (is_readable($premium_tab_file) ? 'YES' : 'NO') . "\n";
}
echo "\n";

// 2. Check if premium tab content is being generated
echo "2. PREMIUM TAB CONTENT CHECK:\n";
ob_start();
if (file_exists($premium_tab_file)) {
    include($premium_tab_file);
}
$premium_content = ob_get_clean();
echo "Content length: " . strlen($premium_content) . " characters\n";
echo "Content preview (first 200 chars):\n";
echo substr($premium_content, 0, 200) . "...\n\n";

// 3. Check for any PHP errors in the premium tab file
echo "3. PREMIUM TAB ERROR CHECK:\n";
ob_start();
$error_reporting = error_reporting(E_ALL);
try {
    if (file_exists($premium_tab_file)) {
        include($premium_tab_file);
    }
    echo "✓ No PHP errors found\n";
} catch (Exception $e) {
    echo "❌ PHP Error: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ PHP Fatal Error: " . $e->getMessage() . "\n";
}
error_reporting($error_reporting);
ob_end_clean();
echo "\n";

// 4. Check if premium class exists
echo "4. PREMIUM CLASS CHECK:\n";
echo "Redco_Optimizer_Premium class exists: " . (class_exists('Redco_Optimizer_Premium') ? 'YES' : 'NO') . "\n";
if (class_exists('Redco_Optimizer_Premium')) {
    $premium = new Redco_Optimizer_Premium();
    echo "Premium instance created successfully\n";
    
    // Check if get_premium_features method exists
    if (method_exists($premium, 'get_premium_features')) {
        echo "get_premium_features method exists: YES\n";
        try {
            $features = $premium->get_premium_features();
            echo "Premium features count: " . count($features) . "\n";
        } catch (Exception $e) {
            echo "Error getting premium features: " . $e->getMessage() . "\n";
        }
    } else {
        echo "get_premium_features method exists: NO\n";
    }
} else {
    echo "Premium class not found - checking if file exists\n";
    $premium_class_file = __DIR__ . '/includes/class-redco-optimizer-premium.php';
    echo "Premium class file exists: " . (file_exists($premium_class_file) ? 'YES' : 'NO') . "\n";
}
echo "\n";

// 5. Check main display file for premium tab inclusion
echo "5. MAIN DISPLAY FILE CHECK:\n";
$main_display_file = __DIR__ . '/admin/partials/redco-optimizer-admin-display.php';
echo "Main display file exists: " . (file_exists($main_display_file) ? 'YES' : 'NO') . "\n";
if (file_exists($main_display_file)) {
    $content = file_get_contents($main_display_file);
    $premium_tab_found = strpos($content, 'redco-premium-tab') !== false;
    echo "Premium tab div found in main file: " . ($premium_tab_found ? 'YES' : 'NO') . "\n";
    
    // Check for inline style that might hide it
    $inline_style_found = strpos($content, 'redco-premium-tab" class="redco-tab-content" style="display: none;"') !== false;
    echo "Inline style display:none found: " . ($inline_style_found ? 'YES' : 'NO') . "\n";
    
    // Check for include statement
    $include_found = strpos($content, 'redco-optimizer-admin-premium-tab.php') !== false;
    echo "Premium tab include found: " . ($include_found ? 'YES' : 'NO') . "\n";
}
echo "\n";

echo "=== DEBUG COMPLETE ===\n";
