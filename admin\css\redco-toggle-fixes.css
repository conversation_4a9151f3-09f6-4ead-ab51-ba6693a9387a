/**
 * Redco Optimizer - Toggle Switch Fixes
 * Ensures all toggle switches are green when checked
 */

/* Global toggle switch styles */
.redco-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.3s;
    border-radius: 24px;
}

.redco-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

/* Force green color for all checked toggle switches */
input:checked + .redco-slider {
    background-color: #00A66B !important;
}

input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Modal toggle switches */
.redco-modal-content input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-modal-content input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Add-on toggle switches */
.redco-addon-settings-form input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-addon-settings-form input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Module toggle switches */
.redco-module-toggle-wrapper input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-module-toggle-wrapper input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Dashboard toggle switches */
.redco-dashboard-content input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-dashboard-content input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Settings toggle switches */
.redco-settings-content input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-settings-content input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Override any other toggle switch styles */
.redco-switch input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-switch input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}
