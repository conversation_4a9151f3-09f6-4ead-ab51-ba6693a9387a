/**
 * Enable free features in the File Optimization, Media, and Preload tabs
 *
 * This script ensures that the following features are available in the free version:
 * - Combine JavaScript Files
 * - Defer JavaScript
 * - Remove Unused CSS
 * - Delay JavaScript
 * - WebP Conversion (Media tab)
 * - Preload Fonts (Preload tab)
 *
 * Note: Async CSS and Self-host Google Fonts are now premium features
 */
jQuery(document).ready(function($) {
    // List of free features
    const freeFeatures = [
        // File Optimization tab
        'combine_js',
        'defer_js',
        'remove_unused_css',
        'delay_js',
        // Media tab
        'webp_conversion', // WebP Conversion is now a free feature
        // Preload tab
        'preload_fonts', // Preload Fonts is now a free feature
        // Heartbeat tab
        'disable_heartbeat_customizer', // Disable Heartbeat in Customizer is now a free feature
        // Database tab
        'schedule_cleanup' // Schedule Database Cleanup is now a free feature
        // 'async_css' and 'self_host_google_fonts' are now premium features
    ];

    // Function to enable free features
    function enableFreeFeatures() {
        // Process each free feature
        $.each(freeFeatures, function(index, featureId) {
            // Get the feature input
            const $input = $('#' + featureId);

            if ($input.length > 0) {
                // Enable the input
                $input.prop('disabled', false);

                // Remove disabled class from the switch
                $input.closest('.redco-switch').removeClass('redco-switch-disabled');

                // Find the heading and remove any premium badge
                const $row = $input.closest('.redco-toggle-row');
                const $heading = $row.find('.redco-toggle-info h4');
                $heading.find('.redco-premium-badge').remove();
            }
        });

        // Log to console for debugging
        console.log('Free features enabled: ' + freeFeatures.join(', '));
    }

    // Run on document ready
    enableFreeFeatures();

    // Also run when tabs are switched
    $(document).on('click', '.redco-nav-item', function() {
        setTimeout(enableFreeFeatures, 100);
    });

    // Run periodically to ensure free features stay enabled
    setInterval(enableFreeFeatures, 2000);
});
