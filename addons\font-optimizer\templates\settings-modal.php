<?php
/**
 * Font Optimizer Settings Modal Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/font-optimizer/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
$detected_fonts = isset($detected_fonts) ? $detected_fonts : (isset($this->detected_fonts) ? $this->detected_fonts : array());
?>

<form method="post" action="" class="redco-addon-settings-form" data-addon="font-optimizer">
    <?php wp_nonce_field('redco_font_optimizer_save_settings', 'redco_font_optimizer_nonce'); ?>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Font Optimization Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Enable Font Optimizer', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Enable font optimization.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="enabled" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Local Font Hosting', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Host fonts locally instead of loading from external sources.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="local_hosting" <?php checked(isset($settings['local_hosting']) ? $settings['local_hosting'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Fonts', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Add preload tags for fonts to improve loading performance.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_fonts" <?php checked(isset($settings['preload_fonts']) ? $settings['preload_fonts'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="font_display"><?php esc_html_e('Font Display', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="font_display" name="font_display" class="redco-select">
                    <option value="auto" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'auto'); ?>><?php esc_html_e('Auto', 'redco-optimizer'); ?></option>
                    <option value="block" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'block'); ?>><?php esc_html_e('Block', 'redco-optimizer'); ?></option>
                    <option value="swap" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'swap'); ?>><?php esc_html_e('Swap', 'redco-optimizer'); ?></option>
                    <option value="fallback" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'fallback'); ?>><?php esc_html_e('Fallback', 'redco-optimizer'); ?></option>
                    <option value="optional" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'optional'); ?>><?php esc_html_e('Optional', 'redco-optimizer'); ?></option>
                </select>
                <p class="redco-form-help"><?php esc_html_e('Controls how fonts are displayed while loading.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Font Sources', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Optimize Google Fonts', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Optimize Google Fonts loading.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="optimize_google_fonts" <?php checked(isset($settings['optimize_google_fonts']) ? $settings['optimize_google_fonts'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Optimize Typekit Fonts', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Optimize Adobe Typekit fonts loading.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="optimize_typekit_fonts" <?php checked(isset($settings['optimize_typekit_fonts']) ? $settings['optimize_typekit_fonts'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Optimize Custom Fonts', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Optimize custom font loading.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="optimize_custom_fonts" <?php checked(isset($settings['optimize_custom_fonts']) ? $settings['optimize_custom_fonts'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Advanced Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Font Face Observer', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Use Font Face Observer to detect when fonts are loaded.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="font_face_observer" <?php checked(isset($settings['font_face_observer']) ? $settings['font_face_observer'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Async CSS', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Load font CSS asynchronously.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="async_css" <?php checked(isset($settings['async_css']) ? $settings['async_css'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Remove Unused Variants', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Remove unused font variants to reduce file size.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="remove_unused_variants" <?php checked(isset($settings['remove_unused_variants']) ? $settings['remove_unused_variants'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="font_loading_mode"><?php esc_html_e('Font Loading Mode', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="font_loading_mode" name="font_loading_mode" class="redco-select">
                    <option value="default" <?php selected(isset($settings['font_loading_mode']) ? $settings['font_loading_mode'] : 'default', 'default'); ?>><?php esc_html_e('Default', 'redco-optimizer'); ?></option>
                    <option value="async" <?php selected(isset($settings['font_loading_mode']) ? $settings['font_loading_mode'] : 'default', 'async'); ?>><?php esc_html_e('Async', 'redco-optimizer'); ?></option>
                    <option value="critical" <?php selected(isset($settings['font_loading_mode']) ? $settings['font_loading_mode'] : 'default', 'critical'); ?>><?php esc_html_e('Critical', 'redco-optimizer'); ?></option>
                </select>
                <p class="redco-form-help"><?php esc_html_e('Controls how fonts are loaded.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Font Subsetting', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Reduce font file size by including only the characters you need.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="font_subsetting" <?php checked(isset($settings['font_subsetting']) ? $settings['font_subsetting'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('WOFF2 Conversion', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Convert fonts to WOFF2 format for better compression.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="woff2_conversion" <?php checked(isset($settings['woff2_conversion']) ? $settings['woff2_conversion'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Font Loading Optimization', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Critical Font Loading', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Load only critical fonts first for faster page rendering.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="critical_font_loading" <?php checked(isset($settings['critical_font_loading']) ? $settings['critical_font_loading'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="critical_fonts"><?php esc_html_e('Critical Fonts', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <textarea id="critical_fonts" name="critical_fonts" class="redco-textarea" rows="3" placeholder="Open Sans, Roboto"><?php echo isset($settings['critical_fonts']) ? esc_textarea($settings['critical_fonts']) : ''; ?></textarea>
                <p class="redco-form-help"><?php esc_html_e('Enter font names to load first, separated by commas.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Font Loading Timeout', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Set a timeout for font loading to prevent layout shifts.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="font_loading_timeout" <?php checked(isset($settings['font_loading_timeout']) ? $settings['font_loading_timeout'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="font_timeout_duration"><?php esc_html_e('Timeout Duration (ms)', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="number" id="font_timeout_duration" name="font_timeout_duration" class="redco-input" value="<?php echo intval(isset($settings['font_timeout_duration']) ? $settings['font_timeout_duration'] : 3000); ?>" min="0" max="10000" step="100">
                <p class="redco-form-help"><?php esc_html_e('Maximum time to wait for fonts to load before falling back.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e('Font Preloading', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Key Fonts', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload important fonts to improve page rendering.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_key_fonts" <?php checked(isset($settings['preload_key_fonts']) ? $settings['preload_key_fonts'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_fonts_list"><?php esc_html_e('Fonts to Preload', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <textarea id="preload_fonts_list" name="preload_fonts_list" class="redco-textarea" rows="3" placeholder="Open Sans:400, Roboto:700"><?php echo isset($settings['preload_fonts_list']) ? esc_textarea($settings['preload_fonts_list']) : ''; ?></textarea>
                <p class="redco-form-help"><?php esc_html_e('Enter font names and weights to preload, separated by commas.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="max_preloaded_fonts"><?php esc_html_e('Maximum Preloaded Fonts', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="number" id="max_preloaded_fonts" name="max_preloaded_fonts" class="redco-input" value="<?php echo intval(isset($settings['max_preloaded_fonts']) ? $settings['max_preloaded_fonts'] : 2); ?>" min="1" max="10" step="1">
                <p class="redco-form-help"><?php esc_html_e('Limit the number of fonts to preload to avoid performance issues.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-footer">
        <button type="submit" class="redco-button redco-button-primary">
            <span class="dashicons dashicons-yes"></span>
            <?php esc_html_e('Save Settings', 'redco-optimizer'); ?>
        </button>
        <button type="button" class="redco-button redco-modal-cancel">
            <?php esc_html_e('Cancel', 'redco-optimizer'); ?>
        </button>
    </div>
</form>
