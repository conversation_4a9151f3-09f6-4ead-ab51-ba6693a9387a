<?php
/**
 * The cache module functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/cache
 */

/**
 * The cache module functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for the cache module.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/cache
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Cache extends Redco_Optimizer_Module {

    /**
     * The cache directory.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $cache_dir    The cache directory.
     */
    private $cache_dir;

    /**
     * Initialize the module.
     *
     * @since    1.0.0
     * @param    string               $module_id       The ID of this module.
     * @param    Redco_Optimizer_Loader $loader          The loader object.
     */
    public function __construct( $module_id, $loader ) {
        parent::__construct( $module_id, $loader );

        // Set the cache directory
        $this->cache_dir = WP_CONTENT_DIR . '/cache/redco-optimizer/';

        // Create the cache directory if it doesn't exist
        if ( ! file_exists( $this->cache_dir ) ) {
            wp_mkdir_p( $this->cache_dir );
        }
    }

    /**
     * Register the hooks for this module.
     *
     * @since    1.0.0
     */
    protected function register_hooks() {
        // Add cache hooks
        $this->loader->add_action( 'init', $this, 'start_cache' );
        $this->loader->add_action( 'wp_footer', $this, 'end_cache' );

        // Add cache management hooks
        $this->loader->add_action( 'wp_ajax_redco_optimizer_clear_cache', $this, 'clear_cache' );
        $this->loader->add_action( 'wp_ajax_nopriv_redco_optimizer_clear_cache', $this, 'clear_cache' );

        // Add cache clearing hooks for various WordPress actions
        $this->loader->add_action( 'save_post', $this, 'clear_post_cache' );
        $this->loader->add_action( 'deleted_post', $this, 'clear_post_cache' );
        $this->loader->add_action( 'comment_post', $this, 'clear_post_cache' );
        $this->loader->add_action( 'wp_set_comment_status', $this, 'clear_post_cache' );
    }

    /**
     * Start the cache process.
     *
     * @since    1.0.0
     */
    public function start_cache() {
        // Don't cache admin pages, logged-in users, or AJAX requests
        if ( is_admin() || is_user_logged_in() || wp_doing_ajax() ) {
            return;
        }

        // Don't cache POST requests
        if ( $_SERVER['REQUEST_METHOD'] === 'POST' ) {
            return;
        }

        // Don't cache if there are specific cookies
        $exclude_cookies = array( 'wordpress_logged_in_', 'comment_author_', 'wp-postpass_' );
        foreach ( $_COOKIE as $key => $value ) {
            foreach ( $exclude_cookies as $exclude ) {
                if ( strpos( $key, $exclude ) === 0 ) {
                    return;
                }
            }
        }

        // Check if we have a cached version
        $cache_file = $this->get_cache_file();

        if ( file_exists( $cache_file ) ) {
            $cache_time = filemtime( $cache_file );
            $expiration = get_option( 'redco_optimizer_settings', array() )['cache_expiration'] ?? 3600;

            // If the cache is still valid, serve it
            if ( time() - $cache_time < $expiration ) {
                readfile( $cache_file );
                exit;
            }
        }

        // Start output buffering
        ob_start();
    }

    /**
     * End the cache process.
     *
     * @since    1.0.0
     */
    public function end_cache() {
        // Don't cache admin pages, logged-in users, or AJAX requests
        if ( is_admin() || is_user_logged_in() || wp_doing_ajax() ) {
            return;
        }

        // Get the content from the output buffer
        $content = ob_get_contents();

        if ( $content ) {
            // Save the content to the cache file
            $cache_file = $this->get_cache_file();
            file_put_contents( $cache_file, $content );
        }
    }

    /**
     * Clear the entire cache.
     *
     * @since    1.0.0
     */
    public function clear_cache() {
        $this->delete_cache_directory( $this->cache_dir );
        wp_mkdir_p( $this->cache_dir );

        if ( wp_doing_ajax() ) {
            wp_send_json_success( array( 'message' => __( 'Cache cleared successfully.', 'redco-optimizer' ) ) );
        }
    }

    /**
     * Clear the cache for a specific post.
     *
     * @since    1.0.0
     * @param    int    $post_id    The post ID.
     */
    public function clear_post_cache( $post_id ) {
        $post = get_post( $post_id );

        if ( ! $post ) {
            return;
        }

        // Clear the post page cache
        $post_url = get_permalink( $post_id );
        $cache_file = $this->get_cache_file( $post_url );

        if ( file_exists( $cache_file ) ) {
            unlink( $cache_file );
        }

        // Clear the home page cache
        $home_url = home_url();
        $home_cache_file = $this->get_cache_file( $home_url );

        if ( file_exists( $home_cache_file ) ) {
            unlink( $home_cache_file );
        }
    }

    /**
     * Get the cache file path for a URL.
     *
     * @since    1.0.0
     * @param    string    $url    The URL to get the cache file for. If not provided, uses the current URL.
     * @return   string    The cache file path.
     */
    private function get_cache_file( $url = '' ) {
        if ( empty( $url ) ) {
            $url = 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        }

        $url = parse_url( $url );
        $url_path = isset( $url['path'] ) ? $url['path'] : '';
        $url_query = isset( $url['query'] ) ? '?' . $url['query'] : '';

        $cache_key = md5( $url_path . $url_query );

        return $this->cache_dir . $cache_key . '.html';
    }

    /**
     * Delete a directory and its contents recursively.
     *
     * @since    1.0.0
     * @param    string    $dir    The directory to delete.
     * @return   bool    True on success, false on failure.
     */
    private function delete_cache_directory( $dir ) {
        if ( ! is_dir( $dir ) ) {
            return false;
        }

        $files = array_diff( scandir( $dir ), array( '.', '..' ) );

        foreach ( $files as $file ) {
            $path = $dir . '/' . $file;

            if ( is_dir( $path ) ) {
                $this->delete_cache_directory( $path );
            } else {
                unlink( $path );
            }
        }

        return rmdir( $dir );
    }

    /**
     * Get the module settings HTML.
     *
     * @since    1.0.0
     * @return   string    The module settings HTML.
     */
    public function get_settings_html() {
        $settings = get_option( 'redco_optimizer_caching_settings', array() );
        $cache_expiration = isset( $settings['cache_lifespan'] ) ? $settings['cache_lifespan'] * 3600 : 3600; // Convert hours to seconds

        ob_start();
        ?>
        <div class="redco-module-settings-section">
            <h3><?php esc_html_e( 'Cache Settings', 'redco-optimizer' ); ?></h3>

            <div class="redco-settings-field">
                <label for="cache_lifespan"><?php esc_html_e( 'Cache Lifespan (hours)', 'redco-optimizer' ); ?></label>
                <input type="number" id="cache_lifespan" name="cache_lifespan" value="<?php echo esc_attr( $cache_expiration / 3600 ); ?>" min="0">
                <p class="description"><?php esc_html_e( 'Set how long the cache should be kept before it expires (in hours). Set to 0 for no expiration.', 'redco-optimizer' ); ?></p>
            </div>

            <div class="redco-settings-actions">
                <button type="button" class="redco-button" id="redco-clear-cache"><?php esc_html_e( 'Clear Cache', 'redco-optimizer' ); ?></button>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
