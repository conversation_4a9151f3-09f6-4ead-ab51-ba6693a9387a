<?php
/**
 * Error Logs AJAX Handler
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Error Logs AJAX Handler
 *
 * This class handles AJAX requests for the error logs functionality.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Team
 */
class Redco_Optimizer_Error_Logs_Ajax {

    /**
     * The error logger instance.
     *
     * @since    1.0.0
     * @access   private
     * @var      Redco_Optimizer_Error_Logger    $error_logger    The error logger instance.
     */
    private $error_logger;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->error_logger = new Redco_Optimizer_Error_Logger();

        // Register AJAX handlers
        add_action('wp_ajax_redco_refresh_error_logs', array($this, 'refresh_error_logs'));
        add_action('wp_ajax_redco_clear_error_logs', array($this, 'clear_error_logs'));
        add_action('wp_ajax_redco_email_error_logs', array($this, 'email_error_logs'));
    }

    /**
     * Refresh error logs.
     *
     * @since    1.0.0
     */
    public function refresh_error_logs() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'redco-optimizer')));
        }

        // Check user capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get log content
        $log_content = $this->error_logger->get_log_content();

        // Get log info
        $log_file_path = $this->error_logger->get_log_file_path();
        $log_info = '';

        if (file_exists($log_file_path)) {
            $file_size = size_format(filesize($log_file_path), 2);
            $file_modified = date_i18n(get_option('date_format') . ' ' . get_option('time_format'), filemtime($log_file_path));

            $log_info = sprintf(
                __('Log file size: %1$s | Last modified: %2$s | Path: %3$s', 'redco-optimizer'),
                $file_size,
                $file_modified,
                $log_file_path
            );
        }

        // Send response
        wp_send_json_success(array(
            'message' => __('Error logs refreshed successfully.', 'redco-optimizer'),
            'log_content' => $log_content,
            'log_info' => $log_info
        ));
    }

    /**
     * Clear error logs.
     *
     * @since    1.0.0
     */
    public function clear_error_logs() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'redco-optimizer')));
        }

        // Check user capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Clear log file
        $result = $this->error_logger->clear_log();

        if ($result) {
            wp_send_json_success(array('message' => __('Error logs cleared successfully.', 'redco-optimizer')));
        } else {
            wp_send_json_error(array('message' => __('Failed to clear error logs.', 'redco-optimizer')));
        }
    }



    /**
     * Email error logs.
     *
     * @since    1.0.0
     */
    public function email_error_logs() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'redco-optimizer')));
        }

        // Check user capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get form data
        $recipient = isset($_POST['recipient']) ? sanitize_email($_POST['recipient']) : '';
        $subject = isset($_POST['subject']) ? sanitize_text_field($_POST['subject']) : '';
        $message = isset($_POST['message']) ? sanitize_textarea_field($_POST['message']) : '';

        // Validate email
        if (empty($recipient) || !is_email($recipient)) {
            wp_send_json_error(array('message' => __('Invalid email address.', 'redco-optimizer')));
        }

        // Get log content
        $log_content = $this->error_logger->get_log_content();

        if (empty($log_content)) {
            wp_send_json_error(array('message' => __('No error logs to send.', 'redco-optimizer')));
        }

        // Prepare email content
        $email_content = $message . "\n\n";
        $email_content .= "==========\n";
        $email_content .= __('ERROR LOGS', 'redco-optimizer') . "\n";
        $email_content .= "==========\n\n";
        $email_content .= $log_content;

        // Set headers
        $headers = array(
            'Content-Type: text/plain; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );

        // Send email
        $result = wp_mail($recipient, $subject, $email_content, $headers);

        if ($result) {
            wp_send_json_success(array('message' => __('Error logs sent successfully.', 'redco-optimizer')));
        } else {
            wp_send_json_error(array('message' => __('Failed to send error logs.', 'redco-optimizer')));
        }
    }
}
