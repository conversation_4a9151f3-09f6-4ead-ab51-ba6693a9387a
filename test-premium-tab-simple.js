// Simple Premium Tab Test
console.log('=== SIMPLE PREMIUM TAB TEST ===');

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM ready, testing premium tab...');
    
    // Test 1: Check if premium tab exists
    const premiumTab = document.getElementById('redco-premium-tab');
    console.log('Premium tab exists:', !!premiumTab);
    
    if (premiumTab) {
        console.log('Premium tab display:', window.getComputedStyle(premiumTab).display);
        console.log('Premium tab visibility:', window.getComputedStyle(premiumTab).visibility);
        console.log('Premium tab opacity:', window.getComputedStyle(premiumTab).opacity);
    }
    
    // Test 2: Check if premium nav item exists
    const premiumNavItem = document.querySelector('.redco-nav-item[data-tab="redco-premium-tab"]');
    console.log('Premium nav item exists:', !!premiumNavItem);
    
    // Test 3: Try to switch to premium tab
    if (typeof window.switchTab === 'function') {
        console.log('switchTab function available, testing...');
        
        // Add click handler to test
        if (premiumNavItem) {
            premiumNavItem.addEventListener('click', function() {
                console.log('Premium nav item clicked');
                setTimeout(function() {
                    const premiumTabAfter = document.getElementById('redco-premium-tab');
                    if (premiumTabAfter) {
                        console.log('After click - Display:', window.getComputedStyle(premiumTabAfter).display);
                        console.log('After click - Visibility:', window.getComputedStyle(premiumTabAfter).visibility);
                        console.log('After click - Opacity:', window.getComputedStyle(premiumTabAfter).opacity);
                    }
                }, 100);
            });
        }
    } else {
        console.log('switchTab function not available');
    }
    
    console.log('=== TEST COMPLETE ===');
});

// Also run immediately in case DOM is already ready
if (document.readyState === 'loading') {
    console.log('DOM still loading, waiting...');
} else {
    console.log('DOM already ready, running test immediately...');
    
    const premiumTab = document.getElementById('redco-premium-tab');
    console.log('Immediate test - Premium tab exists:', !!premiumTab);
    
    if (premiumTab) {
        console.log('Immediate test - Display:', window.getComputedStyle(premiumTab).display);
        console.log('Immediate test - Visibility:', window.getComputedStyle(premiumTab).visibility);
        console.log('Immediate test - Opacity:', window.getComputedStyle(premiumTab).opacity);
    }
}
