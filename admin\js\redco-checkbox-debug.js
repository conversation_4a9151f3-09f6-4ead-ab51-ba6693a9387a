/**
 * Redco Optimizer - Checkbox Debug Functions
 *
 * This file contains functions to help ensure checkbox values are properly saved.
 */

(function($) {
    'use strict';

    /**
     * Initialize debug fields for checkboxes
     */
    function initializeCheckboxDebugFields() {
        // Find all debug fields
        $('.redco-checkbox-debug-field').each(function() {
            const $debugField = $(this);
            const targetId = $debugField.data('target');

            if (targetId) {
                const $checkbox = $('#' + targetId);

                if ($checkbox.length > 0) {
                    // Set initial value based on checkbox state
                    const isChecked = $checkbox.prop('checked');
                    $debugField.val(isChecked ? '1' : '0');
                }
            }
        });

        // Also initialize debug fields for critical checkboxes
        const criticalCheckboxes = [
            'minify_js', 'combine_js', 'defer_js',
            'remove_unused_css', 'delay_js', 'delay_js_safe_mode',
            'webp_conversion', 'preload_fonts', 'disable_heartbeat_customizer',
            'schedule_cleanup'
        ];

        for (let checkboxId of criticalCheckboxes) {
            const $checkbox = $('#' + checkboxId);
            const $debugField = $('#' + checkboxId + '_debug');

            if ($checkbox.length > 0) {
                // If the checkbox exists but the debug field doesn't, create it
                if ($debugField.length === 0) {
                    const isChecked = $checkbox.prop('checked');
                    const $newDebugField = $('<input type="hidden" name="' + checkboxId + '_debug" id="' + checkboxId + '_debug" value="' + (isChecked ? '1' : '0') + '" data-target="' + checkboxId + '" class="redco-checkbox-debug-field">');
                    $checkbox.after($newDebugField);
                }
            }
        }
    }

    /**
     * Update debug field when checkbox changes
     */
    function updateCheckboxDebugField($checkbox) {
        const checkboxId = $checkbox.attr('id');
        const isChecked = $checkbox.prop('checked');
        let $debugField = $('#' + checkboxId + '_debug');

        // If debug field doesn't exist, create it
        if ($debugField.length === 0) {
            $debugField = $('<input type="hidden" name="' + checkboxId + '_debug" id="' + checkboxId + '_debug" value="' + (isChecked ? '1' : '0') + '" data-target="' + checkboxId + '" class="redco-checkbox-debug-field">');
            $checkbox.after($debugField);
        } else {
            // Update existing debug field
            $debugField.val(isChecked ? '1' : '0');
        }
    }

    // Run when document is ready
    $(document).ready(function() {
        // Initialize debug fields for checkboxes
        initializeCheckboxDebugFields();

        // Add event listeners to update debug fields when checkboxes change
        $('input[type="checkbox"]').on('change', function() {
            updateCheckboxDebugField($(this));
        });
    });

})(jQuery);
