<?php

/**
 * The caching functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The caching functionality of the plugin.
 *
 * Defines the caching functionality, including page caching, browser caching,
 * and mobile caching.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_Caching {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The caching settings.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The caching settings.
     */
    private $settings;

    /**
     * The cache directory.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $cache_dir    The cache directory.
     */
    private $cache_dir;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
        $this->cache_dir = WP_CONTENT_DIR . '/cache/redco-optimizer/';
    }

    /**
     * Get caching settings.
     *
     * @since    1.0.0
     * @return   array    The caching settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_caching_settings', array() );

        // Default settings
        $defaults = array(
            'enable_page_caching' => 1,
            'enable_browser_caching' => 1,
            'enable_mobile_caching' => 1,
            'separate_mobile_cache' => 1,
            'cache_lifespan' => 10, // In hours
            'cache_lifespan_unit' => 'hours', // hours or days
            'clear_on_post_edit' => 1,
            'clear_on_comment' => 1,
            'cache_logged_in_users' => 0,
            'cache_ssl' => 1,
            'cache_404' => 0,
            'cache_query_strings' => 0,
            'cache_exclusions' => "/cart/\n/checkout/\n/my-account/",
        );

        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize caching.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if caching is enabled
        if ( ! $this->settings['enable_page_caching'] ) {
            Redco_Optimizer::log_error('Page caching is disabled', 'info', 'caching');
            return;
        }

        // Create cache directory if it doesn't exist
        if ( ! file_exists( $this->cache_dir ) ) {
            $result = wp_mkdir_p( $this->cache_dir );
            if (!$result) {
                Redco_Optimizer::log_error('Failed to create cache directory: ' . $this->cache_dir, 'error', 'caching');
            } else {
                Redco_Optimizer::log_error('Cache directory created: ' . $this->cache_dir, 'info', 'caching');
            }
        }

        // Add browser caching rules to .htaccess
        if ( $this->settings['enable_browser_caching'] ) {
            add_action( 'admin_init', array( $this, 'add_browser_caching_rules' ) );
        }

        // Add hooks for cache clearing
        if ( $this->settings['clear_on_post_edit'] ) {
            add_action( 'save_post', array( $this, 'clear_post_cache' ) );
            add_action( 'edit_post', array( $this, 'clear_post_cache' ) );
            add_action( 'delete_post', array( $this, 'clear_post_cache' ) );
            add_action( 'wp_trash_post', array( $this, 'clear_post_cache' ) );
        }

        if ( $this->settings['clear_on_comment'] ) {
            add_action( 'comment_post', array( $this, 'clear_post_cache_on_comment' ) );
            add_action( 'edit_comment', array( $this, 'clear_post_cache_on_comment' ) );
            add_action( 'delete_comment', array( $this, 'clear_post_cache_on_comment' ) );
            add_action( 'wp_set_comment_status', array( $this, 'clear_post_cache_on_comment' ) );
        }

        // Add AJAX handler for clearing cache
        add_action( 'wp_ajax_redco_clear_cache', array( $this, 'ajax_clear_cache' ) );

        // Start output buffering for page caching
        add_action( 'template_redirect', array( $this, 'start_page_caching' ) );
    }

    /**
     * Start page caching.
     *
     * @since    1.0.0
     */
    public function start_page_caching() {
        // Don't cache if it's an admin page
        if ( is_admin() ) {
            return;
        }

        // Don't cache if user is logged in and we're not caching for logged-in users
        if ( is_user_logged_in() && ! $this->settings['cache_logged_in_users'] ) {
            return;
        }

        // Don't cache if it's a POST request
        if ( $_SERVER['REQUEST_METHOD'] === 'POST' ) {
            return;
        }

        // Don't cache if it's a 404 page and we're not caching 404 pages
        if ( is_404() && ! $this->settings['cache_404'] ) {
            return;
        }

        // Don't cache if it's an SSL page and we're not caching SSL pages
        if ( is_ssl() && ! $this->settings['cache_ssl'] ) {
            return;
        }

        // Don't cache if URL has query string and we're not caching query strings
        if ( ! empty( $_GET ) && ! $this->settings['cache_query_strings'] ) {
            return;
        }

        // Don't cache if URL is in exclusions list
        if ( $this->is_url_excluded() ) {
            return;
        }

        // Check if we have a cached version
        $cache_file = $this->get_cache_file_path();
        if ( file_exists( $cache_file ) ) {
            $cache_time = filemtime( $cache_file );
            $cache_lifespan = $this->get_cache_lifespan_in_seconds();

            // Check if cache is still valid
            if ( time() - $cache_time < $cache_lifespan ) {
                // Serve cached version
                $this->serve_cache_file( $cache_file );
                exit;
            }
        }

        // Start output buffering
        ob_start( array( $this, 'process_output' ) );
    }

    /**
     * Process output and save to cache.
     *
     * @since    1.0.0
     * @param    string    $buffer    The output buffer.
     * @return   string    The processed output buffer.
     */
    public function process_output( $buffer ) {
        // Don't cache if buffer is empty or contains an error
        if ( empty( $buffer ) || is_404() || is_search() || is_feed() || is_trackback() ) {
            return $buffer;
        }

        // Save buffer to cache file
        $cache_file = $this->get_cache_file_path();
        $cache_dir = dirname( $cache_file );

        if ( ! file_exists( $cache_dir ) ) {
            wp_mkdir_p( $cache_dir );
        }

        file_put_contents( $cache_file, $buffer );

        return $buffer;
    }

    /**
     * Serve cache file.
     *
     * @since    1.0.0
     * @param    string    $cache_file    The cache file path.
     */
    private function serve_cache_file( $cache_file ) {
        // Add cache header
        header( 'X-Redco-Cached: true' );

        // Output the cached file
        readfile( $cache_file );
    }

    /**
     * Get cache file path.
     *
     * @since    1.0.0
     * @return   string    The cache file path.
     */
    private function get_cache_file_path() {
        $url_parts = parse_url( $_SERVER['REQUEST_URI'] );
        $path = isset( $url_parts['path'] ) ? $url_parts['path'] : '';
        $query = isset( $url_parts['query'] ) ? $url_parts['query'] : '';

        // Create a unique filename based on the URL
        $filename = md5( $path . $query );

        // Add mobile prefix if needed
        if ( $this->settings['enable_mobile_caching'] && $this->settings['separate_mobile_cache'] && wp_is_mobile() ) {
            $filename = 'mobile-' . $filename;
        }

        // Create directory structure based on the URL
        $dir_path = $this->cache_dir . trailingslashit( $filename[0] . '/' . $filename[1] );

        return $dir_path . $filename . '.html';
    }

    /**
     * Check if URL is excluded from caching.
     *
     * @since    1.0.0
     * @return   bool    True if URL is excluded, false otherwise.
     */
    private function is_url_excluded() {
        $url = $_SERVER['REQUEST_URI'];
        $exclusions = explode( "\n", $this->settings['cache_exclusions'] );

        foreach ( $exclusions as $exclusion ) {
            $exclusion = trim( $exclusion );
            if ( empty( $exclusion ) ) {
                continue;
            }

            // Check if URL matches exclusion pattern
            if ( strpos( $url, $exclusion ) !== false ) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get cache lifespan in seconds.
     *
     * @since    1.0.0
     * @return   int    The cache lifespan in seconds.
     */
    private function get_cache_lifespan_in_seconds() {
        $lifespan = $this->settings['cache_lifespan'];
        $unit = $this->settings['cache_lifespan_unit'];

        if ( $unit === 'days' ) {
            return $lifespan * 24 * 60 * 60;
        }

        return $lifespan * 60 * 60; // hours
    }

    /**
     * Clear post cache.
     *
     * @since    1.0.0
     * @param    int    $post_id    The post ID.
     */
    public function clear_post_cache( $post_id ) {
        // Don't clear cache for revisions or auto-drafts
        if ( wp_is_post_revision( $post_id ) || wp_is_post_autosave( $post_id ) ) {
            return;
        }

        // Clear cache for this post
        $post_url = get_permalink( $post_id );
        $this->clear_url_cache( $post_url );

        // Clear home page cache
        $this->clear_url_cache( home_url() );
    }

    /**
     * Clear post cache on comment.
     *
     * @since    1.0.0
     * @param    int    $comment_id    The comment ID.
     */
    public function clear_post_cache_on_comment( $comment_id ) {
        $comment = get_comment( $comment_id );
        if ( $comment && isset( $comment->comment_post_ID ) ) {
            $this->clear_post_cache( $comment->comment_post_ID );
        }
    }

    /**
     * Clear URL cache.
     *
     * @since    1.0.0
     * @param    string    $url    The URL to clear cache for.
     */
    public function clear_url_cache( $url ) {
        $url_parts = parse_url( $url );
        $path = isset( $url_parts['path'] ) ? $url_parts['path'] : '';

        // Create a unique filename based on the URL
        $filename = md5( $path );

        // Clear both mobile and desktop cache
        $cache_files = array(
            $this->cache_dir . trailingslashit( $filename[0] . '/' . $filename[1] ) . $filename . '.html',
            $this->cache_dir . trailingslashit( $filename[0] . '/' . $filename[1] ) . 'mobile-' . $filename . '.html',
        );

        foreach ( $cache_files as $cache_file ) {
            if ( file_exists( $cache_file ) ) {
                unlink( $cache_file );
            }
        }
    }

    /**
     * Clear all cache.
     *
     * @since    1.0.0
     */
    public function clear_all_cache() {
        Redco_Optimizer::log_error('Clearing all cache', 'info', 'caching');

        try {
            $this->delete_directory_contents( $this->cache_dir );
            Redco_Optimizer::log_error('Cache cleared successfully', 'info', 'caching');
            return true;
        } catch (Exception $e) {
            Redco_Optimizer::log_error('Failed to clear cache: ' . $e->getMessage(), 'error', 'caching');
            return false;
        }
    }

    /**
     * Delete directory contents.
     *
     * @since    1.0.0
     * @param    string    $dir    The directory to delete contents from.
     */
    private function delete_directory_contents( $dir ) {
        if ( ! is_dir( $dir ) ) {
            return;
        }

        $files = scandir( $dir );
        foreach ( $files as $file ) {
            if ( $file === '.' || $file === '..' ) {
                continue;
            }

            $path = $dir . '/' . $file;
            if ( is_dir( $path ) ) {
                $this->delete_directory_contents( $path );
                rmdir( $path );
            } else {
                unlink( $path );
            }
        }
    }

    /**
     * AJAX handler for clearing cache.
     *
     * @since    1.0.0
     */
    public function ajax_clear_cache() {
        // Check nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'redco_optimizer_nonce' ) ) {
            Redco_Optimizer::log_error('Security check failed in AJAX clear cache', 'warning', 'caching');
            wp_send_json_error( array( 'message' => __( 'Security check failed.', 'redco-optimizer' ) ) );
        }

        // Check user capabilities
        if ( ! current_user_can( 'manage_options' ) ) {
            Redco_Optimizer::log_error('Permission denied in AJAX clear cache. User ID: ' . get_current_user_id(), 'warning', 'caching');
            wp_send_json_error( array( 'message' => __( 'You do not have permission to perform this action.', 'redco-optimizer' ) ) );
        }

        // Log user action
        Redco_Optimizer::log_error('User initiated cache clearing via AJAX. User ID: ' . get_current_user_id(), 'info', 'caching');

        // Clear cache
        $result = $this->clear_all_cache();

        if ( $result ) {
            wp_send_json_success( array( 'message' => __( 'Cache cleared successfully.', 'redco-optimizer' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to clear cache.', 'redco-optimizer' ) ) );
        }
    }

    /**
     * Add browser caching rules to .htaccess.
     *
     * @since    1.0.0
     */
    public function add_browser_caching_rules() {
        if ( ! function_exists( 'get_home_path' ) ) {
            require_once ABSPATH . 'wp-admin/includes/file.php';
        }

        $htaccess_file = get_home_path() . '.htaccess';

        if ( ! file_exists( $htaccess_file ) ) {
            return false;
        }

        if ( ! is_writable( $htaccess_file ) ) {
            return false;
        }

        $htaccess_content = file_get_contents( $htaccess_file );

        // Check if browser caching rules already exist
        if ( strpos( $htaccess_content, '# BEGIN Redco Optimizer Browser Caching' ) !== false ) {
            return true;
        }

        // Browser caching rules
        $rules = "# BEGIN Redco Optimizer Browser Caching\n";
        $rules .= "<IfModule mod_expires.c>\n";
        $rules .= "ExpiresActive On\n";
        $rules .= "ExpiresByType image/jpg \"access plus 1 year\"\n";
        $rules .= "ExpiresByType image/jpeg \"access plus 1 year\"\n";
        $rules .= "ExpiresByType image/gif \"access plus 1 year\"\n";
        $rules .= "ExpiresByType image/png \"access plus 1 year\"\n";
        $rules .= "ExpiresByType image/svg+xml \"access plus 1 year\"\n";
        $rules .= "ExpiresByType image/webp \"access plus 1 year\"\n";
        $rules .= "ExpiresByType text/css \"access plus 1 month\"\n";
        $rules .= "ExpiresByType text/javascript \"access plus 1 month\"\n";
        $rules .= "ExpiresByType application/javascript \"access plus 1 month\"\n";
        $rules .= "ExpiresByType application/x-javascript \"access plus 1 month\"\n";
        $rules .= "ExpiresByType application/x-font-ttf \"access plus 1 year\"\n";
        $rules .= "ExpiresByType application/x-font-woff \"access plus 1 year\"\n";
        $rules .= "ExpiresByType application/font-woff \"access plus 1 year\"\n";
        $rules .= "ExpiresByType application/font-woff2 \"access plus 1 year\"\n";
        $rules .= "ExpiresByType font/woff \"access plus 1 year\"\n";
        $rules .= "ExpiresByType font/woff2 \"access plus 1 year\"\n";
        $rules .= "</IfModule>\n";
        $rules .= "# END Redco Optimizer Browser Caching\n\n";

        // Add rules to .htaccess
        $htaccess_content = $rules . $htaccess_content;
        file_put_contents( $htaccess_file, $htaccess_content );

        return true;
    }

    /**
     * Get cache statistics.
     *
     * @since    1.0.0
     * @return   array    Cache statistics including file count and size.
     */
    public function get_cache_stats() {
        $stats = array(
            'count' => 0,
            'size' => 0,
        );

        if (!is_dir($this->cache_dir)) {
            return $stats;
        }

        $stats = $this->get_directory_stats($this->cache_dir);

        return $stats;
    }

    /**
     * Get directory statistics recursively.
     *
     * @since    1.0.0
     * @param    string    $dir    The directory to get statistics for.
     * @return   array     Directory statistics including file count and size.
     */
    private function get_directory_stats($dir) {
        $stats = array(
            'count' => 0,
            'size' => 0,
        );

        if (!is_dir($dir)) {
            return $stats;
        }

        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $sub_stats = $this->get_directory_stats($path);
                $stats['count'] += $sub_stats['count'];
                $stats['size'] += $sub_stats['size'];
            } else {
                $stats['count']++;
                $stats['size'] += filesize($path);
            }
        }

        return $stats;
    }

    /**
     * Remove browser caching rules from .htaccess.
     *
     * @since    1.0.0
     */
    public function remove_browser_caching_rules() {
        if ( ! function_exists( 'get_home_path' ) ) {
            require_once ABSPATH . 'wp-admin/includes/file.php';
        }

        $htaccess_file = get_home_path() . '.htaccess';

        if ( ! file_exists( $htaccess_file ) ) {
            return false;
        }

        if ( ! is_writable( $htaccess_file ) ) {
            return false;
        }

        $htaccess_content = file_get_contents( $htaccess_file );

        // Remove browser caching rules
        $pattern = '/# BEGIN Redco Optimizer Browser Caching.*?# END Redco Optimizer Browser Caching\n\n/s';
        $htaccess_content = preg_replace( $pattern, '', $htaccess_content );

        file_put_contents( $htaccess_file, $htaccess_content );

        return true;
    }
}
