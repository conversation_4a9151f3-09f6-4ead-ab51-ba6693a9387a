<?php
/**
 * Standalone Help Page
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get WordPress admin header
require_once(ABSPATH . 'wp-admin/admin-header.php');

// Enqueue the help tab fix script
wp_enqueue_script('redco-optimizer-help-tab-fix', plugin_dir_url(dirname(__FILE__)) . 'admin/js/redco-help-tab-fix.js', array('jquery'), REDCO_OPTIMIZER_VERSION, true);

// Enqueue the help page CSS
wp_enqueue_style('redco-optimizer-help-page', plugin_dir_url(dirname(__FILE__)) . 'admin/css/redco-help-page.css', array('dashicons'), REDCO_OPTIMIZER_VERSION);

// Localize the help tab fix script
wp_localize_script('redco-optimizer-help-tab-fix', 'redco_optimizer', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('redco_optimizer_nonce'),
    'plugin_url' => plugin_dir_url(dirname(__FILE__)),
));
?>

<div class="wrap redco-optimizer-wrap">
    <div class="redco-optimizer-header">
        <h1><?php esc_html_e('Redco Optimizer Help Documentation', 'redco-optimizer'); ?></h1>
        <span class="redco-version"><?php echo esc_html('v' . REDCO_OPTIMIZER_VERSION); ?></span>
    </div>

    <div class="redco-help-container">
        <div class="redco-help-sidebar">
            <div class="redco-help-search">
                <input type="text" id="redco-help-search-input" placeholder="<?php esc_attr_e('Search help topics...', 'redco-optimizer'); ?>">
            </div>
            <ul class="redco-help-topics">
                <li class="redco-help-topic active" data-topic="getting-started">
                    <span class="dashicons dashicons-admin-home"></span>
                    <?php esc_html_e('Getting Started', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="caching">
                    <span class="dashicons dashicons-performance"></span>
                    <?php esc_html_e('Caching', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="file-optimization">
                    <span class="dashicons dashicons-media-code"></span>
                    <?php esc_html_e('File Optimization', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="media">
                    <span class="dashicons dashicons-format-image"></span>
                    <?php esc_html_e('Media Optimization', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="database">
                    <span class="dashicons dashicons-database"></span>
                    <?php esc_html_e('Database Optimization', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="preload">
                    <span class="dashicons dashicons-download"></span>
                    <?php esc_html_e('Preload', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="heartbeat">
                    <span class="dashicons dashicons-heart"></span>
                    <?php esc_html_e('Heartbeat Control', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="cdn">
                    <span class="dashicons dashicons-networking"></span>
                    <?php esc_html_e('CDN Integration', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="site-health">
                    <span class="dashicons dashicons-shield"></span>
                    <?php esc_html_e('Site Health Inspector', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="faq">
                    <span class="dashicons dashicons-editor-help"></span>
                    <?php esc_html_e('FAQ', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="troubleshooting">
                    <span class="dashicons dashicons-warning"></span>
                    <?php esc_html_e('Troubleshooting', 'redco-optimizer'); ?>
                </li>
            </ul>
        </div>

        <div class="redco-help-content-container">
            <div id="redco-help-getting-started" class="redco-help-content active">
                <h2><?php esc_html_e('Getting Started with Redco Optimizer', 'redco-optimizer'); ?></h2>
                <div class="redco-help-section">
                    <p class="redco-help-intro"><?php esc_html_e('Welcome to Redco Optimizer! This comprehensive guide will help you optimize your WordPress site for maximum performance, improved user experience, and better search engine rankings.', 'redco-optimizer'); ?></p>

                    <div class="redco-help-tip">
                        <p><strong><?php esc_html_e('Pro Tip:', 'redco-optimizer'); ?></strong> <?php esc_html_e('For most websites, enabling Page Caching, Browser Caching, and GZIP Compression will provide an immediate and significant performance boost with minimal configuration.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-help-section">
                    <h3><?php esc_html_e('Quick Start Guide', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('Follow these steps to quickly optimize your WordPress site:', 'redco-optimizer'); ?></p>

                    <div class="redco-help-steps">
                        <div class="redco-help-step">
                            <div class="redco-help-step-number">1</div>
                            <div class="redco-help-step-content">
                                <h4><?php esc_html_e('Enable Caching', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Navigate to the Caching tab and enable Page Caching. This single step can improve your site speed by 2-5x.', 'redco-optimizer'); ?></p>
                                <div class="redco-help-note">
                                    <p><?php esc_html_e('Page caching creates static HTML files that are served to visitors, reducing server processing time and database queries.', 'redco-optimizer'); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="redco-help-step">
                            <div class="redco-help-step-number">2</div>
                            <div class="redco-help-step-content">
                                <h4><?php esc_html_e('Optimize Images', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Go to the Media tab and enable Image Optimization and Lazy Loading. Then click "Optimize All Images" to compress existing images.', 'redco-optimizer'); ?></p>
                                <div class="redco-help-note">
                                    <p><?php esc_html_e('Images often account for 50-80% of a webpage\'s size. Optimizing them can dramatically reduce page load times.', 'redco-optimizer'); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="redco-help-step">
                            <div class="redco-help-step-number">3</div>
                            <div class="redco-help-step-content">
                                <h4><?php esc_html_e('Minify Files', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Visit the File Optimization tab and enable HTML, CSS, and JavaScript minification to reduce file sizes.', 'redco-optimizer'); ?></p>
                                <div class="redco-help-note">
                                    <p><?php esc_html_e('Minification removes unnecessary characters from your code without changing functionality, resulting in smaller file sizes and faster loading.', 'redco-optimizer'); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="redco-help-step">
                            <div class="redco-help-step-number">4</div>
                            <div class="redco-help-step-content">
                                <h4><?php esc_html_e('Clean Database', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Go to the Database tab and run a database cleanup to remove unnecessary data.', 'redco-optimizer'); ?></p>
                                <div class="redco-help-note">
                                    <p><?php esc_html_e('Over time, your WordPress database accumulates post revisions, spam comments, and transients that slow down your site. Regular cleanup keeps your database lean and efficient.', 'redco-optimizer'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="redco-help-warning">
                        <p><strong><?php esc_html_e('Important:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Always make a backup of your site before making significant changes to your optimization settings.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
                <div class="redco-help-section">
                    <h3><?php esc_html_e('Understanding Your Dashboard', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('The Redco Optimizer dashboard provides a quick overview of your site\'s performance and optimization status:', 'redco-optimizer'); ?></p>

                    <ul class="redco-help-feature-list">
                        <li><strong><?php esc_html_e('Performance Score:', 'redco-optimizer'); ?></strong> <?php esc_html_e('A real-time assessment of your site\'s overall performance.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Active Optimizations:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Shows which optimization features are currently enabled.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Quick Actions:', 'redco-optimizer'); ?></strong> <?php esc_html_e('One-click access to common optimization tasks.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Optimization Tips:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Personalized recommendations to further improve your site.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Recent Activity:', 'redco-optimizer'); ?></strong> <?php esc_html_e('A log of recent optimization actions performed.', 'redco-optimizer'); ?></li>
                    </ul>
                </div>
            </div>

            <div id="redco-help-caching" class="redco-help-content">
                <h2><?php esc_html_e('Caching', 'redco-optimizer'); ?></h2>

                <div class="redco-help-section">
                    <p class="redco-help-intro"><?php esc_html_e('Caching is one of the most powerful ways to speed up your WordPress site. It creates static versions of your dynamic content, which significantly reduces the processing time needed to generate a page view.', 'redco-optimizer'); ?></p>

                    <div class="redco-help-tip">
                        <p><strong><?php esc_html_e('Did You Know:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Properly configured caching can reduce page load times by up to 80% and reduce server load by up to 65%.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-help-section">
                    <h3><?php esc_html_e('Page Caching', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('Page caching stores the full HTML of your pages, allowing them to be served instantly to visitors without having to be generated on each visit.', 'redco-optimizer'); ?></p>

                    <h4><?php esc_html_e('How Page Caching Works', 'redco-optimizer'); ?></h4>
                    <ol class="redco-help-numbered-list">
                        <li><?php esc_html_e('When a visitor first accesses a page, WordPress processes PHP, runs database queries, and generates HTML.', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Redco Optimizer captures this HTML output and saves it as a static file.', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('For subsequent visitors, this static file is served directly, bypassing PHP processing and database queries.', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('The cache is automatically refreshed when content is updated or based on your cache lifespan settings.', 'redco-optimizer'); ?></li>
                    </ol>
                </div>
            </div>

            <div id="redco-help-file-optimization" class="redco-help-content">
                <h2><?php esc_html_e('File Optimization', 'redco-optimizer'); ?></h2>

                <div class="redco-help-section">
                    <p class="redco-help-intro"><?php esc_html_e('File optimization reduces the size and number of CSS and JavaScript files loaded by your website, significantly improving page load times.', 'redco-optimizer'); ?></p>

                    <div class="redco-help-tip">
                        <p><strong><?php esc_html_e('Pro Tip:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Start with minification before enabling file combination. This allows you to identify any potential issues with minification alone before adding the complexity of file combination.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-help-section">
                    <h3><?php esc_html_e('HTML Optimization', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('HTML optimization reduces the size of your HTML files by removing unnecessary characters and whitespace.', 'redco-optimizer'); ?></p>

                    <h4><?php esc_html_e('Available Options', 'redco-optimizer'); ?></h4>
                    <ul class="redco-help-feature-list">
                        <li><strong><?php esc_html_e('Minify HTML:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Removes unnecessary whitespace, comments, and other characters from HTML files.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Minify HTML Comments:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Removes HTML comments from your pages.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Minify Inline CSS:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Minifies CSS code within style tags in your HTML.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Minify Inline JavaScript:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Minifies JavaScript code within script tags in your HTML.', 'redco-optimizer'); ?></li>
                    </ul>

                    <div class="redco-help-note">
                        <p><?php esc_html_e('If you experience any issues after enabling HTML minification, you can add specific pages to the exclusions list to prevent them from being minified.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-help-section">
                    <h3><?php esc_html_e('CSS Optimization', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('CSS optimization improves page load times by reducing the size and number of CSS files loaded by your website.', 'redco-optimizer'); ?></p>

                    <h4><?php esc_html_e('Available Options', 'redco-optimizer'); ?></h4>
                    <ul class="redco-help-feature-list">
                        <li><strong><?php esc_html_e('Minify CSS:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Removes unnecessary characters from CSS files without changing functionality.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Combine CSS:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Merges multiple CSS files into one to reduce HTTP requests.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Optimize CSS Delivery:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Inlines critical CSS and defers non-critical CSS to improve rendering speed.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Remove Unused CSS:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Identifies and removes CSS rules that aren\'t used on your pages.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Async CSS:', 'redco-optimizer'); ?></strong> <span class="redco-premium-badge"><?php esc_html_e('Premium', 'redco-optimizer'); ?></span> <?php esc_html_e('Loads CSS files asynchronously to prevent render-blocking.', 'redco-optimizer'); ?></li>
                    </ul>

                    <div class="redco-help-warning">
                        <p><strong><?php esc_html_e('Important:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Combining CSS files may cause styling issues on some themes. If you notice any problems, try adding the problematic CSS files to the exclusions list.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-help-section">
                    <h3><?php esc_html_e('JavaScript Optimization', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('JavaScript optimization improves page load times by reducing the size of JavaScript files and controlling how they load.', 'redco-optimizer'); ?></p>

                    <h4><?php esc_html_e('Available Options', 'redco-optimizer'); ?></h4>
                    <ul class="redco-help-feature-list">
                        <li><strong><?php esc_html_e('Minify JavaScript:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Removes unnecessary characters from JavaScript files.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Combine JavaScript:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Merges multiple JavaScript files into one to reduce HTTP requests.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Defer JavaScript:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Delays the loading of JavaScript until after the page has rendered.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Delay JavaScript Execution:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Delays the execution of JavaScript until user interaction.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Safe Mode:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Ensures compatibility with essential scripts by loading them normally.', 'redco-optimizer'); ?></li>
                    </ul>

                    <div class="redco-help-note">
                        <p><?php esc_html_e('JavaScript optimization can significantly improve page load times, but it may also cause functionality issues with some scripts. Always test your site thoroughly after enabling these features.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-help-section">
                    <h3><?php esc_html_e('Google Fonts Optimization', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('Google Fonts optimization improves page load times by optimizing how Google Fonts are loaded on your website.', 'redco-optimizer'); ?></p>

                    <h4><?php esc_html_e('Available Options', 'redco-optimizer'); ?></h4>
                    <ul class="redco-help-feature-list">
                        <li><strong><?php esc_html_e('Self-Host Google Fonts:', 'redco-optimizer'); ?></strong> <span class="redco-premium-badge"><?php esc_html_e('Premium', 'redco-optimizer'); ?></span> <?php esc_html_e('Downloads Google Fonts to your server and serves them locally, eliminating external requests.', 'redco-optimizer'); ?></li>
                    </ul>

                    <div class="redco-help-tip">
                        <p><strong><?php esc_html_e('Pro Tip:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Self-hosting Google Fonts not only improves performance but also helps with GDPR compliance by eliminating requests to Google\'s servers.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-help-section">
                    <h3><?php esc_html_e('Exclusions', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('You can exclude specific files, pages, or user agents from file optimization to ensure compatibility.', 'redco-optimizer'); ?></p>

                    <h4><?php esc_html_e('Exclusion Types', 'redco-optimizer'); ?></h4>
                    <ul class="redco-help-feature-list">
                        <li><strong><?php esc_html_e('CSS Exclusions:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Specify CSS files that should not be minified or combined.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('JavaScript Exclusions:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Specify JavaScript files that should not be minified, combined, or deferred.', 'redco-optimizer'); ?></li>
                        <li><strong><?php esc_html_e('Page Exclusions:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Specify pages that should not have any file optimization applied.', 'redco-optimizer'); ?></li>
                    </ul>

                    <div class="redco-help-note">
                        <p><?php esc_html_e('Use exclusions to resolve compatibility issues with specific plugins or themes. Common files to exclude include jQuery, theme customizer scripts, and payment gateway scripts.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
            </div>

            <div id="redco-help-faq" class="redco-help-content">
                <h2><?php esc_html_e('Frequently Asked Questions', 'redco-optimizer'); ?></h2>

                <div class="redco-help-section">
                    <p class="redco-help-intro"><?php esc_html_e('Find answers to the most common questions about Redco Optimizer.', 'redco-optimizer'); ?></p>

                    <div class="redco-help-faq-container">
                        <div class="redco-help-faq-item">
                            <h3><?php esc_html_e('Will Redco Optimizer slow down my admin dashboard?', 'redco-optimizer'); ?></h3>
                            <div class="redco-help-faq-answer">
                                <p><?php esc_html_e('No, Redco Optimizer is designed to have minimal impact on your admin experience. The plugin only applies optimizations to the frontend of your website by default, keeping your admin dashboard running smoothly.', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="redco-help-troubleshooting" class="redco-help-content">
                <h2><?php esc_html_e('Troubleshooting', 'redco-optimizer'); ?></h2>

                <div class="redco-help-section">
                    <p class="redco-help-intro"><?php esc_html_e('Encountering issues with Redco Optimizer? This troubleshooting guide will help you identify and resolve common problems.', 'redco-optimizer'); ?></p>

                    <div class="redco-help-warning">
                        <p><strong><?php esc_html_e('Before You Begin:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Always make a backup of your site before troubleshooting or making significant changes to your optimization settings.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    console.log('Redco Help Page Script Loaded');

    // Help topic navigation
    $(document).on('click', '.redco-help-topic', function() {
        console.log('Help topic clicked');
        var topic = $(this).data('topic');

        // Update active topic in sidebar
        $('.redco-help-topic').removeClass('active');
        $(this).addClass('active');

        // Show selected content
        $('.redco-help-content').hide().removeClass('active');
        $('#redco-help-' + topic).show().addClass('active');

        // Scroll to top
        $('.redco-help-content-container').scrollTop(0);

        // Initialize FAQ items in the newly shown content
        initializeFaqItems();
    });

    /**
     * Initialize FAQ items to be expandable/collapsible
     */
    function initializeFaqItems() {
        // Hide all FAQ answers initially
        $('.redco-help-content.active .redco-help-faq-answer').hide();

        // Open the first FAQ item by default if none is open
        if ($('.redco-help-content.active .redco-help-faq-item.active').length === 0) {
            $('.redco-help-content.active .redco-help-faq-item:first').addClass('active');
            $('.redco-help-content.active .redco-help-faq-item:first .redco-help-faq-answer').show();
        }
    }

    // Initialize FAQ items on page load
    initializeFaqItems();
});
</script>

<?php
// Get WordPress admin footer
require_once(ABSPATH . 'wp-admin/admin-footer.php');
?>
