/**
 * Redco Optimizer - Module Toggle Fix
 * Direct approach to fix module toggle switches UI
 */

(function($) {
    'use strict';
    
    // Run when DOM is ready
    $(document).ready(function() {
        // Direct fix for module toggle switches
        $(document).off('click', '.redco-module-toggle, .redco-module-card .redco-slider').on('click', '.redco-module-toggle, .redco-module-card .redco-slider', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Get the checkbox
            const $checkbox = $(this).hasClass('redco-module-toggle') ? $(this) : $(this).prev('input.redco-module-toggle');
            
            if ($checkbox.length > 0) {
                // Toggle the checkbox state programmatically
                const currentState = $checkbox.prop('checked');
                const newState = !currentState;
                
                // Update the checkbox state
                $checkbox.prop('checked', newState);
                
                // Update the UI directly
                const $slider = $checkbox.next('.redco-slider');
                if ($slider.length > 0) {
                    if (newState) {
                        $slider.css('background-color', '#00A66B');
                    } else {
                        $slider.css('background-color', '#cbd5e1');
                    }
                }
                
                // Add a visual feedback animation
                $slider.css('opacity', '0.7');
                setTimeout(function() {
                    $slider.css('opacity', '1');
                }, 200);
                
                // Get the module card
                const $moduleCard = $checkbox.closest('.redco-module-card');
                
                // Update module card class
                if (newState) {
                    $moduleCard.addClass('redco-module-active');
                } else {
                    $moduleCard.removeClass('redco-module-active');
                }
                
                // Get the module ID
                const moduleId = $checkbox.data('module');
                
                // Trigger AJAX request to save the module state
                if (moduleId) {
                    // Show loading state
                    $moduleCard.addClass('redco-loading');
                    
                    // Send AJAX request
                    $.ajax({
                        url: redco_optimizer.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'redco_optimizer_toggle_module',
                            module_id: moduleId,
                            status: newState ? 1 : 0,
                            nonce: redco_optimizer.nonce,
                            no_redirect: 1
                        },
                        success: function(response) {
                            // Remove loading state
                            $moduleCard.removeClass('redco-loading');
                            
                            if (response.success) {
                                // Show success notification
                                if (typeof showNotification === 'function') {
                                    showNotification('success', response.data.message, {
                                        title: newState ? 'Module Enabled' : 'Module Disabled',
                                        duration: 3000
                                    });
                                }
                            } else {
                                // Show error notification
                                if (typeof showNotification === 'function') {
                                    showNotification('error', response.data.message, {
                                        title: 'Error',
                                        duration: 5000
                                    });
                                }
                                
                                // Revert the toggle state
                                $checkbox.prop('checked', currentState);
                                
                                // Update the UI
                                if ($slider.length > 0) {
                                    if (currentState) {
                                        $slider.css('background-color', '#00A66B');
                                    } else {
                                        $slider.css('background-color', '#cbd5e1');
                                    }
                                }
                                
                                // Update module card class
                                if (currentState) {
                                    $moduleCard.addClass('redco-module-active');
                                } else {
                                    $moduleCard.removeClass('redco-module-active');
                                }
                            }
                        },
                        error: function() {
                            // Remove loading state
                            $moduleCard.removeClass('redco-loading');
                            
                            // Show error notification
                            if (typeof showNotification === 'function') {
                                showNotification('error', 'Connection error. Please try again.', {
                                    title: 'Error',
                                    duration: 5000
                                });
                            }
                            
                            // Revert the toggle state
                            $checkbox.prop('checked', currentState);
                            
                            // Update the UI
                            if ($slider.length > 0) {
                                if (currentState) {
                                    $slider.css('background-color', '#00A66B');
                                } else {
                                    $slider.css('background-color', '#cbd5e1');
                                }
                            }
                            
                            // Update module card class
                            if (currentState) {
                                $moduleCard.addClass('redco-module-active');
                            } else {
                                $moduleCard.removeClass('redco-module-active');
                            }
                        }
                    });
                }
                
                console.log('Module toggle clicked - Module:', moduleId, 'New state:', newState ? 'ON' : 'OFF');
            }
        });
    });

})(jQuery);
