<?php
/**
 * Provide a admin area view for the modules tab
 *
 * This file is used to markup the modules tab in the admin dashboard.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

// Get the modules from options - force a fresh read from the database
wp_cache_delete('redco_optimizer_modules', 'options');
$modules = get_option('redco_optimizer_modules', array());

// Get the admin class instance
global $redco_optimizer_admin;
?>



<div class="redco-modules-actions">
    <a href="#" class="redco-button redco-modules-toggle-all redco-button-primary"><?php esc_html_e('Disable All', 'redco-optimizer'); ?></a>
</div>

<div class="redco-modules-grid">
    <?php foreach ($modules as $module_id => $module) : ?>
        <div class="redco-module-card <?php echo $module['enabled'] ? 'redco-module-enabled' : 'redco-module-disabled'; ?> <?php echo isset($module['premium']) && $module['premium'] ? 'redco-module-premium' : ''; ?>"
             data-module="<?php echo esc_attr($module_id); ?>"
             data-enabled="<?php echo $module['enabled'] ? 'true' : 'false'; ?>"
             data-tab="<?php echo esc_attr(isset($module['tab']) ? $module['tab'] : 'redco-' . $module_id . '-tab'); ?>"
             title="<?php echo $module['enabled'] ? esc_attr__('Click to disable this module', 'redco-optimizer') : esc_attr__('Click to enable this module', 'redco-optimizer'); ?>">

            <!-- Module Icon -->
            <div class="redco-module-icon">
                <?php
                // Simple dashicon mapping
                $icon_class = 'dashicons-admin-generic';
                if ($module_id === 'caching') $icon_class = 'dashicons-superhero';
                elseif ($module_id === 'file-optimization') $icon_class = 'dashicons-media-code';
                elseif ($module_id === 'media') $icon_class = 'dashicons-format-image';
                elseif ($module_id === 'database') $icon_class = 'dashicons-database';
                elseif ($module_id === 'preload') $icon_class = 'dashicons-performance';
                elseif ($module_id === 'heartbeat') $icon_class = 'dashicons-heart';
                elseif ($module_id === 'cdn') $icon_class = 'dashicons-admin-site';
                elseif ($module_id === 'site-health-inspector') $icon_class = 'dashicons-shield';
                ?>
                <span class="dashicons <?php echo esc_attr($icon_class); ?>"></span>
            </div>

            <!-- Module Content -->
            <div class="redco-module-content">
                <h3 class="redco-module-title"><?php echo esc_html(isset($module['title']) ? $module['title'] : (isset($module['name']) ? $module['name'] : ucfirst(str_replace('-', ' ', $module_id)))); ?></h3>
                <p class="redco-module-description"><?php echo esc_html($module['description']); ?></p>

                <?php if ((isset($module['premium']) && $module['premium']) || $module_id === 'cdn') : ?>
                    <span class="redco-premium-badge"><?php esc_html_e('Premium', 'redco-optimizer'); ?></span>
                <?php endif; ?>
            </div>

            <!-- Module Status -->
            <div class="redco-module-status">
                <span class="redco-status-badge"></span>
            </div>

            <!-- Hidden input for form compatibility -->
            <input type="hidden" class="redco-module-toggle"
                   data-module="<?php echo esc_attr($module_id); ?>"
                   value="<?php echo $module['enabled'] ? '1' : '0'; ?>"
                   <?php disabled(($module_id === 'cdn' || (isset($module['premium']) && $module['premium'])) && !$redco_optimizer_admin->has_premium_access(), true); ?>>

        </div>
    <?php endforeach; ?>
</div>
