<?php
/**
 * Provide a admin area view for the modules tab
 *
 * This file is used to markup the modules tab in the admin dashboard.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

// Get the modules from options - force a fresh read from the database
wp_cache_delete('redco_optimizer_modules', 'options');
$modules = get_option('redco_optimizer_modules', array());

// Get the admin class instance
global $redco_optimizer_admin;
?>



<div class="redco-modules-actions" style="text-align: right; margin-bottom: 20px;">
    <a href="#" class="redco-button redco-modules-toggle-all" style="background-color: var(--primary-color); color: #fff;"><?php esc_html_e('Disable All', 'redco-optimizer'); ?></a>
</div>

<div class="redco-modules-grid">
    <?php foreach ($modules as $module_id => $module) : ?>
        <div class="redco-module-card <?php echo $module['enabled'] ? 'redco-module-enabled' : ''; ?> <?php echo isset($module['premium']) && $module['premium'] ? 'redco-module-premium' : ''; ?>"
             data-module="<?php echo esc_attr($module_id); ?>"
             data-tab="<?php echo esc_attr(isset($module['tab']) ? $module['tab'] : 'redco-' . $module_id . '-tab'); ?>">

            <!-- Module Header -->
            <div class="redco-module-header">
                <!-- Module Icon and Title -->
                <div class="redco-module-title-wrapper">
                    <div class="redco-module-title-container">
                        <div class="redco-module-title-row">
                            <div class="redco-module-icon">
                                <?php
                                // Module Icon
                                $icon_color = '#00A66B'; // Default green color for icons
                                $icon_svg = '';

                                if ($module_id === 'caching') {
                                    $icon_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="' . $icon_color . '" width="24" height="24"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"/><path d="M13 7h-2v5.414l3.293 3.293 1.414-1.414L13 11.586z"/></svg>';
                                } else if ($module_id === 'file-optimization') {
                                    $icon_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="' . $icon_color . '" width="24" height="24"><path d="M20.742 13.045a8.088 8.088 0 0 1-2.077.271c-2.135 0-4.14-.83-5.646-2.336a8.025 8.025 0 0 1-2.064-7.723A1 1 0 0 0 9.73 2.034a10.014 10.014 0 0 0-4.489 2.582c-3.898 3.898-3.898 10.243 0 14.143a9.937 9.937 0 0 0 7.072 2.93 9.93 9.93 0 0 0 7.07-2.929 10.007 10.007 0 0 0 2.583-4.491 1.001 1.001 0 0 0-1.224-1.224zm-2.772 4.301a7.947 7.947 0 0 1-5.656 2.343 7.953 7.953 0 0 1-5.658-2.344c-3.118-3.119-3.118-8.195 0-11.314a7.923 7.923 0 0 1 2.06-1.483 10.027 10.027 0 0 0 2.89 7.848 9.972 9.972 0 0 0 7.848 2.891 8.036 8.036 0 0 1-1.484 2.059z"/></svg>';
                                } elseif ($module_id === 'media') {
                                    $icon_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="' . $icon_color . '" width="24" height="24"><path d="M19 10V7c0-1.103-.897-2-2-2h-3V2h-4v3H7c-1.103 0-2 .897-2 2v3H2v4h3v3c0 1.103.897 2 2 2h3v3h4v-3h3c1.103 0 2-.897 2-2v-3h3v-4h-3zM15 15H9V9h6v6z"/></svg>';
                                } elseif ($module_id === 'database') {
                                    $icon_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="' . $icon_color . '" width="24" height="24"><path d="M20 6c0-2.168-3.663-4-8-4S4 3.832 4 6v2c0 2.168 3.663 4 8 4s8-1.832 8-4V6zm-8 13c-4.337 0-8-1.832-8-4v3c0 2.168 3.663 4 8 4s8-1.832 8-4v-3c0 2.168-3.663 4-8 4z"/><path d="M20 10c0 2.168-3.663 4-8 4s-8-1.832-8-4v3c0 2.168 3.663 4 8 4s8-1.832 8-4v-3z"/></svg>';
                                } elseif ($module_id === 'preload') {
                                    $icon_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="' . $icon_color . '" width="24" height="24"><path d="M12 6c-3.309 0-6 2.691-6 6s2.691 6 6 6 6-2.691 6-6-2.691-6-6-6zm0 10c-2.206 0-4-1.794-4-4s1.794-4 4-4 4 1.794 4 4-1.794 4-4 4z"/><path d="M12 2C6.579 2 2 6.579 2 12s4.579 10 10 10 10-4.579 10-10S17.421 2 12 2zm0 18c-4.337 0-8-3.663-8-8s3.663-8 8-8 8 3.663 8 8-3.663 8-8 8z"/><path d="M12 10c-1.081 0-2 .919-2 2s.919 2 2 2 2-.919 2-2-.919-2-2-2z"/></svg>';
                                } elseif ($module_id === 'heartbeat') {
                                    // Use the same green color for heartbeat as other icons
                                    $icon_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="' . $icon_color . '" width="24" height="24"><path d="M12.971 2.68a1 1 0 00-1.942 0l-1.529 5.2-5.2 1.53a1 1 0 000 1.942l5.2 1.53 1.53 5.2a1 1 0 001.942 0l1.529-5.2 5.2-1.53a1 1 0 000-1.942l-5.2-1.53-1.53-5.2z"/><path d="M20.314 16.686l-1.314-1.314 1.314-1.314a1 1 0 10-1.414-1.414l-1.314 1.314-1.314-1.314a1 1 0 10-1.414 1.414l1.314 1.314-1.314 1.314a1 1 0 101.414 1.414l1.314-1.314 1.314 1.314a1 1 0 101.414-1.414zM7.686 16.686l-1.314-1.314 1.314-1.314a1 1 0 00-1.414-1.414L5.958 13.958l-1.314-1.314a1 1 0 10-1.414 1.414l1.314 1.314-1.314 1.314a1 1 0 101.414 1.414l1.314-1.314 1.314 1.314a1 1 0 101.414-1.414z"/></svg>';
                                } elseif ($module_id === 'cdn') {
                                    $icon_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="' . $icon_color . '" width="24" height="24"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm7.931 9h-3.032A5.013 5.013 0 0 0 13 7.102V4.069A8.008 8.008 0 0 1 19.931 11zM12 4.069V7.1c0 .066.014.126.016.191a4.981 4.981 0 0 0-1.885 1.886C10.065 9.179 10 9.165 10 9.1V4.069c.659-.023 1.327.03 2 0zM4.069 11h3.032A5.01 5.01 0 0 0 10 15.898v3.033A8.008 8.008 0 0 1 4.069 13zm5.931 7.931v-3.033c0-.066-.014-.126-.016-.191a4.981 4.981 0 0 0 1.885-1.886c.066-.002.131.012.131.077v5.033c-.659.023-1.327-.03-2 0zm3-3.033v3.033A8.007 8.007 0 0 0 19.931 13h-3.032A5.013 5.013 0 0 1 13 15.898z"/></svg>';
                                } elseif ($module_id === 'site-health-inspector') {
                                    $icon_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="' . $icon_color . '" width="24" height="24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/></svg>';
                                }
                                echo $icon_svg;
                                ?>
                            </div>
                            <h3 class="redco-module-title"><?php echo esc_html(isset($module['title']) ? $module['title'] : (isset($module['name']) ? $module['name'] : ucfirst(str_replace('-', ' ', $module_id)))); ?></h3>
                        </div>
                        <?php if ((isset($module['premium']) && $module['premium']) || $module_id === 'cdn') : ?>
                            <div class="redco-premium-badge"><?php esc_html_e('Premium', 'redco-optimizer'); ?></div>
                        <?php endif; ?>
                    </div>
                    <?php if ($module_id === 'cdn') : ?>
                        <div class="redco-premium-badge-below"><?php esc_html_e('Premium', 'redco-optimizer'); ?></div>
                    <?php endif; ?>
                </div>

                <!-- Module Toggle Switch -->
                <div class="redco-module-toggle-wrapper">
                    <label class="redco-switch">
                        <input type="checkbox" value="1" class="redco-module-toggle"
                               data-module="<?php echo esc_attr($module_id); ?>"
                               <?php checked($module['enabled'], true); ?>
                               <?php disabled(($module_id === 'cdn' || (isset($module['premium']) && $module['premium'])) && !$redco_optimizer_admin->has_premium_access(), true); ?>>
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <!-- Module Content -->
            <div class="redco-module-content">
                <p class="redco-module-description"><?php echo esc_html($module['description']); ?></p>

                <!-- Module Features -->
                <div class="redco-module-features">
                    <?php if ($module_id === 'caching') : ?>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Page Caching', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Browser Caching', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Mobile Caching', 'redco-optimizer'); ?></span>
                        </div>
                    <?php elseif ($module_id === 'file-optimization') : ?>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('CSS Minification', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('JavaScript Minification', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('File Combination', 'redco-optimizer'); ?></span>
                        </div>
                    <?php elseif ($module_id === 'media') : ?>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Image Compression', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Lazy Loading', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Bulk Optimization', 'redco-optimizer'); ?></span>
                        </div>
                    <?php elseif ($module_id === 'database') : ?>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Database Cleanup', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Table Optimization', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Scheduled Cleanup', 'redco-optimizer'); ?></span>
                        </div>
                    <?php elseif ($module_id === 'preload') : ?>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Preload Cache', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Prefetch DNS', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Preload Links', 'redco-optimizer'); ?></span>
                        </div>
                    <?php elseif ($module_id === 'heartbeat') : ?>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Backend Control', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Post Editor Control', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Frontend Control', 'redco-optimizer'); ?></span>
                        </div>
                    <?php elseif ($module_id === 'cdn') : ?>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('CDN Integration', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Asset Delivery', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Global Distribution', 'redco-optimizer'); ?></span>
                        </div>
                    <?php elseif ($module_id === 'site-health-inspector') : ?>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Plugin & Theme Checks', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Security Scans', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="redco-module-feature">
                            <span class="redco-module-feature-icon"></span>
                            <span class="redco-module-feature-text"><?php esc_html_e('Performance Analysis', 'redco-optimizer'); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Module Footer -->
            <div class="redco-module-footer">
                <?php if (($module_id === 'cdn' || (isset($module['premium']) && $module['premium'])) && !$redco_optimizer_admin->has_premium_access()) : ?>
                    <a href="#" class="redco-button redco-button-premium redco-premium-tab-link" data-tab="redco-premium-tab">
                        <span class="dashicons dashicons-unlock"></span>
                        <?php esc_html_e('Upgrade to Unlock', 'redco-optimizer'); ?>
                    </a>
                <?php else : ?>
                    <a href="#" class="redco-button redco-button-secondary redco-configure-module" data-module="<?php echo esc_attr($module_id); ?>" data-tab="<?php echo esc_attr(isset($module['tab']) ? $module['tab'] : 'redco-' . $module_id . '-tab'); ?>">
                        <span class="dashicons dashicons-admin-generic"></span>
                        <?php esc_html_e('Configure', 'redco-optimizer'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>
</div>
