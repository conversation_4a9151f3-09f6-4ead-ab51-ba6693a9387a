/**
 * Redco Optimizer - Safe Mode Notice
 * Handles the confirmation notice for Safe Mode in Delay JavaScript Execution
 */

(function($) {
    'use strict';

    // Define global notification function if not already defined
    if (typeof showNotification !== 'function') {
        window.showNotification = function(type, message, options = {}) {
            // Use RedcoNotification if available
            if (typeof RedcoNotification !== 'undefined') {
                RedcoNotification.show(type, message, options.duration || 4000);
                return;
            }

            // Create notification container if it doesn't exist
            if ($('.redco-notifications-container').length === 0) {
                $('body').append('<div class="redco-notifications-container"></div>');
            }

            // Get notification title
            const title = options.title || (type === 'success' ? 'Success' : 'Notice');

            // Create notification HTML
            const icon = type === 'success'
                ? '<span class="dashicons dashicons-yes-alt"></span>'
                : (type === 'error' ? '<span class="dashicons dashicons-warning"></span>' : '<span class="dashicons dashicons-info"></span>');

            const notificationId = 'redco-notification-' + Date.now();
            const notificationHtml = `
                <div id="${notificationId}" class="redco-notification redco-notification-${type}">
                    <div class="redco-notification-header">
                        ${icon}
                        <h3>${title}</h3>
                        <button type="button" class="redco-notification-close">
                            <span class="dashicons dashicons-no-alt"></span>
                        </button>
                    </div>
                    <div class="redco-notification-content">
                        <p>${message}</p>
                    </div>
                </div>
            `;

            // Add notification to container
            $('.redco-notifications-container').append(notificationHtml);

            // Show notification with animation
            setTimeout(function() {
                $('#' + notificationId).addClass('redco-notification-show');
            }, 10);

            // Auto-hide notification after duration
            const duration = options.duration || 5000;
            if (duration > 0) {
                setTimeout(function() {
                    $('#' + notificationId).removeClass('redco-notification-show');
                    setTimeout(function() {
                        $('#' + notificationId).remove();
                    }, 300);
                }, duration);
            }

            // Close notification on click
            $('#' + notificationId + ' .redco-notification-close').on('click', function() {
                $('#' + notificationId).removeClass('redco-notification-show');
                setTimeout(function() {
                    $('#' + notificationId).remove();
                }, 300);
            });
        };
    }

    $(document).ready(function() {
        // Remove highlight from save button when form is submitted
        $('.redco-form').on('submit', function() {
            $('.redco-button-highlight').removeClass('redco-button-highlight').css({
                'animation': 'none'
            });
        });
        // Handle Safe Mode toggle
        const $safeModeToggle = $('#delay_js_safe_mode');
        const $safeModeRow = $('#delay-js-safe-mode-row');

        // Store original state
        let originalState = $safeModeToggle.prop('checked');

        // Create the performance impact notice
        const $performanceImpactNotice = $(`
            <div class="redco-performance-impact" style="display: none;">
                <div class="redco-performance-impact-icon">
                    <span class="dashicons dashicons-info"></span>
                </div>
                <div class="redco-performance-impact-content">
                    <p>By enabling Safe Mode, you significantly reduce your website performance improvements. We recommend using it only as a temporary solution. If you're experiencing issues with the Delay JavaScript feature, our support team can help you troubleshoot.</p>
                    <button class="redco-button redco-button-primary" id="activate-safe-mode">ACTIVATE SAFE MODE</button>
                </div>
            </div>
        `);

        // Insert the notice after the Safe Mode toggle row
        $performanceImpactNotice.insertAfter('#delay-js-safe-mode-row');

        // Handle Safe Mode toggle click
        $safeModeToggle.on('click', function(e) {
            // If trying to enable Safe Mode
            if (!originalState && $(this).prop('checked')) {
                // Prevent the default toggle action
                e.preventDefault();
                e.stopPropagation();

                // Show the performance impact notice
                $('.redco-performance-impact').slideDown(300);

                // Keep the toggle in its original state
                $(this).prop('checked', originalState);
                return false;
            }

            // If disabling Safe Mode, update the original state but don't auto-save
            originalState = $(this).prop('checked');

            // Add a visual indicator that the user needs to save settings
            const $saveButton = $('.redco-form-actions .redco-save-settings');
            if ($saveButton.length) {
                $saveButton.addClass('redco-button-highlight').css({
                    'animation': 'redco-pulse 1.5s infinite'
                });

                // Add CSS for the pulse animation if it doesn't exist
                if (!$('#redco-pulse-animation').length) {
                    $('head').append(`
                        <style id="redco-pulse-animation">
                            @keyframes redco-pulse {
                                0% { box-shadow: 0 0 0 0 rgba(0, 166, 107, 0.7); }
                                70% { box-shadow: 0 0 0 10px rgba(0, 166, 107, 0); }
                                100% { box-shadow: 0 0 0 0 rgba(0, 166, 107, 0); }
                            }
                            .redco-button-highlight {
                                animation: redco-pulse 1.5s infinite;
                            }
                        </style>
                    `);
                }
            }
        });

        // Handle Activate Safe Mode button click
        $(document).on('click', '#activate-safe-mode', function(e) {
            // Prevent any default action or event propagation
            e.preventDefault();
            e.stopPropagation();

            // Enable the toggle visually only (don't trigger change event)
            $safeModeToggle.prop('checked', true);

            // Update the original state
            originalState = true;

            // Hide the performance impact notice
            $('.redco-performance-impact').slideUp(300);

            // Add a visual indicator that the user needs to save settings
            const $saveButton = $('.redco-form-actions .redco-save-settings');
            if ($saveButton.length) {
                $saveButton.addClass('redco-button-highlight').css({
                    'animation': 'redco-pulse 1.5s infinite'
                });

                // Add CSS for the pulse animation if it doesn't exist
                if (!$('#redco-pulse-animation').length) {
                    $('head').append(`
                        <style id="redco-pulse-animation">
                            @keyframes redco-pulse {
                                0% { box-shadow: 0 0 0 0 rgba(0, 166, 107, 0.7); }
                                70% { box-shadow: 0 0 0 10px rgba(0, 166, 107, 0); }
                                100% { box-shadow: 0 0 0 0 rgba(0, 166, 107, 0); }
                            }
                            .redco-button-highlight {
                                animation: redco-pulse 1.5s infinite;
                            }
                        </style>
                    `);
                }
            }

            // Return false to prevent any form submission
            return false;
        });
    });

})(jQuery);
