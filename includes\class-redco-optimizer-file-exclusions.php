<?php
/**
 * File Exclusions Handler
 *
 * This class handles the exclusion of files from optimization processes.
 *
 * @link       https://redco.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * File Exclusions Handler
 *
 * This class handles the exclusion of files from optimization processes.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_File_Exclusions {

    /**
     * The file exclusion settings.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The file exclusion settings.
     */
    private $settings;

    /**
     * The exclusion patterns.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $exclusions    The exclusion patterns.
     */
    private $exclusions;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->settings = $this->get_settings();
        $this->exclusions = $this->get_exclusions();
        
        // Add filter to check if a file should be excluded
        add_filter('redco_optimizer_should_exclude_file', array($this, 'should_exclude_file'), 10, 2);
    }

    /**
     * Get file exclusion settings.
     *
     * @since    1.0.0
     * @return   array    The file exclusion settings.
     */
    private function get_settings() {
        $settings = get_option('redco_optimizer_file_optimization_settings', array());
        
        // Default settings
        $defaults = array(
            'file_exclusions' => '',
        );
        
        // Merge settings with defaults
        return wp_parse_args($settings, $defaults);
    }

    /**
     * Get exclusions for files.
     *
     * @since    1.0.0
     * @return   array    The exclusions for files.
     */
    private function get_exclusions() {
        $exclusions = array();
        
        // Add default exclusions
        $exclusions[] = 'wp-admin';
        $exclusions[] = 'wp-includes/js/jquery/jquery.js';
        
        // Add user exclusions
        if (!empty($this->settings['file_exclusions'])) {
            $user_exclusions = explode("\n", $this->settings['file_exclusions']);
            $exclusions = array_merge($exclusions, array_map('trim', $user_exclusions));
        }
        
        return array_unique(array_filter($exclusions));
    }

    /**
     * Check if a file should be excluded from optimization.
     *
     * @since    1.0.0
     * @param    bool     $should_exclude    Whether the file should be excluded.
     * @param    string   $file_path         The file path to check.
     * @return   bool                        Whether the file should be excluded.
     */
    public function should_exclude_file($should_exclude, $file_path) {
        // If already excluded, return true
        if ($should_exclude) {
            return true;
        }
        
        // Check against exclusion patterns
        foreach ($this->exclusions as $pattern) {
            // Convert wildcard pattern to regex
            if (strpos($pattern, '*') !== false) {
                $regex = '/' . str_replace('*', '.*', preg_quote($pattern, '/')) . '/i';
                if (preg_match($regex, $file_path)) {
                    return true;
                }
            } 
            // Direct match
            elseif (strpos($file_path, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
}

// Initialize the file exclusions handler
new Redco_Optimizer_File_Exclusions();
