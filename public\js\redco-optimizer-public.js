/**
 * All of the code for your public-facing JavaScript source
 * should reside in this file.
 */
(function( $ ) {
    'use strict';

    $(document).ready(function() {
        // Performance monitoring
        if (typeof redco_optimizer_public !== 'undefined' && redco_optimizer_public.show_performance_indicator) {
            // Record the page load time
            const loadTime = (window.performance.timing.domContentLoadedEventEnd - window.performance.timing.navigationStart) / 1000;

            // Create the performance indicator
            const $indicator = $('<div class="redco-performance-indicator"></div>');

            // Set the indicator text
            $indicator.text('Page Load: ' + loadTime.toFixed(2) + 's');

            // Set the indicator color based on the load time using thresholds from WP
            const goodThreshold = typeof redco_optimizer_public.good_threshold !== 'undefined' ? parseFloat(redco_optimizer_public.good_threshold) : 1.0;
            const warnThreshold = typeof redco_optimizer_public.warn_threshold !== 'undefined' ? parseFloat(redco_optimizer_public.warn_threshold) : 2.0;

            if (loadTime < goodThreshold) {
                $indicator.addClass('redco-performance-indicator-good');
            } else if (loadTime < warnThreshold) {
                $indicator.addClass('redco-performance-indicator-warning');
            } else {
                $indicator.addClass('redco-performance-indicator-bad');
            }

            // Add the indicator to the page
            $('body').append($indicator);

            // Hide the indicator after 5 seconds
            setTimeout(function() {
                $indicator.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }

        // Enhanced lazy loading
        if (typeof redco_optimizer_public !== 'undefined' && redco_optimizer_public.enhanced_lazy_loading) {
            // Find all images with loading="lazy"
            $('img[loading="lazy"]').each(function() {
                const $img = $(this);

                // Add lazy loading class
                $img.addClass('redco-lazy-loading');

                // When the image is loaded, add the loaded class
                $img.on('load', function() {
                    $(this).addClass('redco-lazy-loaded');
                });

                // If the image is already loaded, add the loaded class
                if ($img[0].complete) {
                    $img.addClass('redco-lazy-loaded');
                }
            });
        }
    });

})( jQuery );
