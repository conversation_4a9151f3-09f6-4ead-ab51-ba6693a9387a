<?php
/**
 * Extract screenshot from the shared image
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 */

// This script is meant to be run manually to extract the dashboard screenshot
// from the shared image and save it to the help documentation folder.

// Define the path to the help documentation images folder
$images_dir = __DIR__ . '/images/help/';

// Create the directory if it doesn't exist
if (!file_exists($images_dir)) {
    mkdir($images_dir, 0755, true);
}

// Define the path to save the screenshot
$screenshot_path = $images_dir . 'dashboard-screenshot.jpg';

// Create a simple HTML file with instructions
$html = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extract Screenshot</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #00A66B;
        }
        .instructions {
            background-color: #f8f9fa;
            border: 1px solid #e2e4e7;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        ol {
            margin-left: 20px;
        }
        .note {
            background-color: #e6f6ef;
            border-left: 4px solid #00A66B;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Extract Dashboard Screenshot</h1>
    
    <div class="instructions">
        <p>To extract the dashboard screenshot from the shared image:</p>
        <ol>
            <li>Take a screenshot of the dashboard area from the shared image</li>
            <li>Crop it to show just the dashboard content</li>
            <li>Save it as "dashboard-screenshot.jpg" in the "admin/images/help/" folder</li>
        </ol>
    </div>
    
    <div class="note">
        <p><strong>Note:</strong> The screenshot should be clear and show the main dashboard interface of Redco Optimizer.</p>
    </div>
    
    <p>Once you've saved the screenshot, the help documentation will automatically use it instead of the placeholder.</p>
</body>
</html>
HTML;

// Save the HTML file
file_put_contents(__DIR__ . '/extract-screenshot.html', $html);

echo "Instructions for extracting the screenshot have been saved to admin/extract-screenshot.html\n";
echo "Please follow the instructions to extract the dashboard screenshot.\n";
