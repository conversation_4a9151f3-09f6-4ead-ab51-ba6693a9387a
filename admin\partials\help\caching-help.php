<?php
/**
 * Caching Help Documentation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials/help
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="redco-help-section" id="caching-help">
    <div class="redco-help-header">
        <h2><?php esc_html_e('Caching', 'redco-optimizer'); ?></h2>
        <p class="redco-help-description"><?php esc_html_e('Speed up page loading by storing static versions of your pages.', 'redco-optimizer'); ?></p>
    </div>

    <div class="redco-help-content">
        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Understanding Caching', 'redco-optimizer'); ?></h3>
            <p><?php esc_html_e('Caching is one of the most effective ways to improve your website\'s performance. When a visitor accesses a page on your WordPress site, the server processes PHP code, runs database queries, and generates HTML. This process can be resource-intensive and time-consuming.', 'redco-optimizer'); ?></p>
            
            <p><?php esc_html_e('With caching enabled, Redco Optimizer creates static HTML versions of your pages and serves these to visitors, bypassing the need for PHP processing and database queries. This significantly reduces server load and improves page load times.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Page Caching', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Page caching creates static HTML copies of your pages to reduce server load and improve page load times.', 'redco-optimizer'); ?></p>
            
            <h5><?php esc_html_e('Enable Page Caching', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('When enabled, Redco Optimizer will create static HTML copies of your pages. This is the core caching feature and provides the most significant performance improvement.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Enable this option for all WordPress sites. It\'s safe and provides immediate performance benefits.', 'redco-optimizer'); ?></p>
            </div>
            
            <h5><?php esc_html_e('Enable Browser Caching', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('Browser caching instructs visitors\' browsers to store certain files locally. When a visitor returns to your site, their browser can use these cached files instead of downloading them again, resulting in faster page loads for returning visitors.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Enable this option to improve load times for returning visitors.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Mobile Caching', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Mobile caching creates separate cache files optimized for mobile devices.', 'redco-optimizer'); ?></p>
            
            <h5><?php esc_html_e('Enable Mobile Caching', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('When enabled, Redco Optimizer will create separate cache files for mobile devices. This is useful if your site displays differently on mobile devices compared to desktop.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Enable this option if your site has a responsive design that significantly changes layout for mobile devices.', 'redco-optimizer'); ?></p>
            </div>
            
            <h5><?php esc_html_e('Separate Mobile Cache', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('Creates separate cache files for mobile devices. This ensures mobile users get a version of your site optimized for their device.', 'redco-optimizer'); ?></p>
            <div class="redco-help-note">
                <p><strong><?php esc_html_e('Note:', 'redco-optimizer'); ?></strong> <?php esc_html_e('This option is only relevant if "Enable Mobile Caching" is activated.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Cache Lifespan', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Controls how long cached pages are stored before being regenerated.', 'redco-optimizer'); ?></p>
            
            <p><?php esc_html_e('The maximum time a cached page will be stored before being regenerated. Default is 10 hours.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong> <?php esc_html_e('For most sites, 10 hours is a good balance. For frequently updated sites, consider a shorter lifespan (4-6 hours). For static sites with infrequent updates, a longer lifespan (24 hours) is appropriate.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Cache Clearing', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Controls when cached files are automatically cleared.', 'redco-optimizer'); ?></p>
            
            <h5><?php esc_html_e('Clear Cache on Post Edit', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('Automatically clears cache when posts or pages are edited.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Keep this enabled to ensure visitors always see the latest version of your content.', 'redco-optimizer'); ?></p>
            </div>
            
            <h5><?php esc_html_e('Clear Cache on Comment', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('Automatically clears cache when comments are added or approved.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Enable this if your site has active comments and you want them to appear immediately.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Advanced Cache Settings', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Fine-tune caching behavior for specific scenarios.', 'redco-optimizer'); ?></p>
            
            <h5><?php esc_html_e('Cache for Logged-in Users', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('Enable caching for logged-in users. By default, logged-in users bypass the cache to ensure they see personalized content.', 'redco-optimizer'); ?></p>
            <div class="redco-help-warning">
                <p><strong><?php esc_html_e('Warning:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Only enable this if your site doesn\'t display user-specific content for logged-in users. This could cause users to see other users\' information if not configured properly.', 'redco-optimizer'); ?></p>
            </div>
            
            <h5><?php esc_html_e('Cache SSL Pages', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('Enable caching for SSL pages (https://). This creates separate cache files for secure pages.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Enable this if your site uses SSL (which most modern sites do).', 'redco-optimizer'); ?></p>
            </div>
            
            <h5><?php esc_html_e('Cache 404 Pages', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('Enable caching for 404 pages. This can reduce server load if your site receives many requests for non-existent pages.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong> <?php esc_html_e('Enable this if your site has a custom 404 page that requires significant resources to generate.', 'redco-optimizer'); ?></p>
            </div>
            
            <h5><?php esc_html_e('Cache Query Strings', 'redco-optimizer'); ?></h5>
            <p><?php esc_html_e('Enable caching for URLs with query strings. This creates separate cache files for URLs with different query parameters.', 'redco-optimizer'); ?></p>
            <div class="redco-help-note">
                <p><strong><?php esc_html_e('Note:', 'redco-optimizer'); ?></strong> <?php esc_html_e('This is useful for sites that use query strings to display different content (e.g., ?product=123). However, it can significantly increase the number of cache files.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Cache Exclusions', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Specify URLs or patterns that should not be cached.', 'redco-optimizer'); ?></p>
            <p><?php esc_html_e('Enter URLs or URL patterns (one per line) that should not be cached. This is useful for dynamic pages like checkout, cart, or user account pages.', 'redco-optimizer'); ?></p>
            <div class="redco-help-tip">
                <p><strong><?php esc_html_e('Common exclusions:', 'redco-optimizer'); ?></strong></p>
                <ul>
                    <li><code>/cart/</code> - <?php esc_html_e('WooCommerce cart page', 'redco-optimizer'); ?></li>
                    <li><code>/checkout/</code> - <?php esc_html_e('WooCommerce checkout page', 'redco-optimizer'); ?></li>
                    <li><code>/my-account/</code> - <?php esc_html_e('User account pages', 'redco-optimizer'); ?></li>
                    <li><code>/wp-admin/</code> - <?php esc_html_e('WordPress admin area', 'redco-optimizer'); ?></li>
                    <li><code>*cart*</code> - <?php esc_html_e('Any URL containing "cart"', 'redco-optimizer'); ?></li>
                </ul>
            </div>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Best Practices for Caching', 'redco-optimizer'); ?></h3>
            <ol>
                <li><?php esc_html_e('Always enable Page Caching and Browser Caching for optimal performance.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Clear the cache after making significant changes to your site.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Exclude dynamic pages like cart, checkout, and user account pages from caching.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('For e-commerce sites, be careful with caching product pages if inventory changes frequently.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Test your site thoroughly after enabling caching to ensure everything works correctly.', 'redco-optimizer'); ?></li>
            </ol>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Troubleshooting Caching Issues', 'redco-optimizer'); ?></h3>
            <h4><?php esc_html_e('Changes Not Appearing on the Site', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('If you\'ve made changes to your site but they\'re not appearing:', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Click the "CLEAR CACHE" button to manually clear the cache.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Check if "Clear Cache on Post Edit" is enabled.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Try viewing the page in an incognito/private browsing window to bypass browser cache.', 'redco-optimizer'); ?></li>
            </ol>
            
            <h4><?php esc_html_e('Dynamic Content Not Updating', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('If dynamic content (like personalized recommendations or user-specific information) isn\'t updating:', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Add the affected pages to the Cache Exclusions list.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Disable "Cache for Logged-in Users" if the content is user-specific.', 'redco-optimizer'); ?></li>
            </ol>
            
            <h4><?php esc_html_e('Server Error After Enabling Caching', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('If you encounter server errors after enabling caching:', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Access your site via FTP or hosting file manager.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Navigate to wp-content/cache/redco-optimizer/ and delete all files.', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Disable caching and contact your hosting provider for assistance.', 'redco-optimizer'); ?></li>
            </ol>
        </div>
    </div>
</div>
