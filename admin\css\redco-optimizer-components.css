/* ===================================
   REDCO OPTIMIZER - COMPONENT STYLES
   Modern Form Elements & Components
   =================================== */

/* Tab Content - Modern Layout */
.redco-tab-content {
    background: transparent;
    padding: 0;
    margin: 0;
    border: none;
    border-radius: 0;
    box-shadow: none;
}

/* Form Sections */
.redco-form-section {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    margin-bottom: 30px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.redco-form-section:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.redco-form-section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 24px 30px;
    border-bottom: 1px solid #e1e5e9;
}

.redco-form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
    letter-spacing: -0.3px;
}

.redco-form-section-description {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
}

.redco-form-section-content {
    padding: 30px;
}

/* Form Groups - Enhanced */
.redco-form-group {
    margin-bottom: 28px;
    position: relative;
}

.redco-form-group:last-child {
    margin-bottom: 0;
}

.redco-form-label {
    display: block;
    font-size: 15px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1.4;
}

.redco-form-description {
    font-size: 13px;
    color: #6c757d;
    margin: 6px 0 0 0;
    line-height: 1.5;
}

/* Input Fields - Modern Design */
.redco-form-input,
.redco-form-select,
.redco-form-textarea {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    color: #495057;
}

.redco-form-input:focus,
.redco-form-select:focus,
.redco-form-textarea:focus {
    outline: none;
    border-color: #00a66b;
    box-shadow: 0 0 0 4px rgba(0, 166, 107, 0.1);
    background: #ffffff;
}

.redco-form-input:disabled,
.redco-form-select:disabled,
.redco-form-textarea:disabled {
    background: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

.redco-form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* Select Dropdown */
.redco-form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
    appearance: none;
}

/* Checkbox Styling - Modern */
.redco-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.redco-checkbox-item {
    display: flex;
    align-items: flex-start;
    gap: 14px;
    padding: 16px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.redco-checkbox-item:hover {
    background: #ffffff;
    border-color: #00a66b;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.redco-checkbox-item.checked {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-color: #00a66b;
    box-shadow: 0 4px 16px rgba(0, 166, 107, 0.15);
}

.redco-checkbox {
    width: 22px;
    height: 22px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    margin-top: 2px;
    position: relative;
    appearance: none;
}

.redco-checkbox:checked {
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%);
    border-color: #00925f;
    box-shadow: 0 2px 8px rgba(0, 166, 107, 0.3);
}

.redco-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ffffff;
    font-size: 14px;
    font-weight: 700;
}

.redco-checkbox-content {
    flex: 1;
}

.redco-checkbox-label {
    font-size: 15px;
    font-weight: 500;
    color: #2c3e50;
    cursor: pointer;
    line-height: 1.4;
    margin-bottom: 4px;
}

.redco-checkbox-description {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.5;
    margin: 0;
}

/* Premium Features */
.redco-premium-feature {
    position: relative;
    opacity: 0.7;
}

/* REMOVED: Orange badge pseudo-element - using HTML badges only */
.redco-premium-feature::after {
    display: none !important;
    content: none !important;
}

.redco-premium-feature .redco-checkbox {
    pointer-events: none;
    background: #f8f9fa;
    border-color: #e9ecef;
}

/* Toggle Switches - Alternative Style */
.redco-toggle-switch {
    position: relative;
    display: inline-block;
    width: 52px;
    height: 28px;
}

.redco-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.redco-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #ccc;
    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 28px;
}

.redco-toggle-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 3px;
    bottom: 3px;
    background: white;
    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .redco-toggle-slider {
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%);
}

input:checked + .redco-toggle-slider:before {
    transform: translateX(24px);
}

/* Button Groups */
.redco-button-group {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    flex-wrap: wrap;
}

.redco-button-group .redco-button {
    flex: 1;
    min-width: 140px;
}

/* Info Boxes */
.redco-info-box {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #90caf9;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.redco-info-box.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
}

.redco-info-box.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
}

.redco-info-box.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
}

.redco-info-icon {
    font-size: 20px;
    flex-shrink: 0;
    margin-top: 2px;
}

.redco-info-content {
    flex: 1;
}

.redco-info-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #2c3e50;
}

.redco-info-text {
    font-size: 14px;
    color: #495057;
    margin: 0;
    line-height: 1.5;
}

/* Progress Bars */
.redco-progress {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 12px 0;
}

.redco-progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%);
    border-radius: 4px;
    transition: width 0.6s ease-in-out;
}

/* Loading States */
.redco-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 14px;
}

.redco-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #00a66b;
    border-radius: 50%;
    animation: redco-spin 1s linear infinite;
}

@keyframes redco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Form Elements */
@media (max-width: 768px) {
    .redco-form-section-content {
        padding: 20px;
    }

    .redco-form-section-header {
        padding: 20px;
    }

    .redco-button-group {
        flex-direction: column;
    }

    .redco-button-group .redco-button {
        width: 100%;
    }

    .redco-checkbox-item {
        padding: 12px;
    }
}
