/**
 * Redco Optimizer Heartbeat Control
 *
 * Controls the WordPress Heartbeat API to reduce server load
 */
(function($) {
    'use strict';

    // Store original wp.heartbeat settings
    var originalHeartbeatSettings = {
        interval: null,
        autostart: null
    };

    // Function to initialize heartbeat control
    function initHeartbeatControl() {
        // Only proceed if wp.heartbeat exists
        if (typeof wp !== 'undefined' && wp.heartbeat) {
            // Store original settings
            originalHeartbeatSettings.interval = wp.heartbeat.interval();
            originalHeartbeatSettings.autostart = wp.heartbeat.autostart;

            // Get current location (admin, editor, or frontend)
            var currentLocation = getHeartbeatLocation();

            // Get the behavior setting for this location
            var behavior = getHeartbeatBehavior(currentLocation);

            // Apply the behavior
            applyHeartbeatBehavior(behavior);
        }
    }

    // Function to determine current location
    function getHeartbeatLocation() {
        // Check if we're in the post editor
        if (typeof pagenow !== 'undefined' && (pagenow === 'post' || pagenow === 'page')) {
            return 'editor';
        }

        // Check if we're in the admin area
        if (typeof pagenow !== 'undefined') {
            return 'backend';
        }

        // Default to frontend
        return 'frontend';
    }

    // Function to get behavior setting for a location
    function getHeartbeatBehavior(location) {
        // Default behavior is to not limit
        var behavior = 'default';

        // Get behavior from redco_heartbeat_settings global variable
        if (typeof redco_heartbeat_settings !== 'undefined') {
            switch (location) {
                case 'backend':
                    behavior = redco_heartbeat_settings.backend || 'default';
                    break;
                case 'editor':
                    behavior = redco_heartbeat_settings.editor || 'default';
                    break;
                case 'frontend':
                    behavior = redco_heartbeat_settings.frontend || 'default';
                    break;
            }
        }

        return behavior;
    }

    // Function to apply heartbeat behavior
    function applyHeartbeatBehavior(behavior) {
        if (!wp.heartbeat) {
            return;
        }

        switch (behavior) {
            case 'disable':
                // Disable heartbeat completely
                wp.heartbeat.interval(0);
                break;

            case 'reduce':
                // Reduce heartbeat frequency to 120 seconds (2 minutes)
                wp.heartbeat.interval(120);
                break;

            case 'default':
            default:
                // Use default WordPress settings
                // No action needed as we're keeping the original settings
                break;
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize heartbeat control
        initHeartbeatControl();

        // Toggle heartbeat settings visibility based on control checkbox
        $('#control_heartbeat').on('change', function() {
            if ($(this).is(':checked')) {
                $('#redco-heartbeat-settings').slideDown(300).addClass('active');
                $('#redco-heartbeat-settings .redco-card-content').css({
                    'display': 'block',
                    'visibility': 'visible'
                });
            } else {
                $('#redco-heartbeat-settings').slideUp(300).removeClass('active');
            }
        });

        // Initialize visibility on page load
        if ($('#control_heartbeat').length) {
            if ($('#control_heartbeat').is(':checked')) {
                $('#redco-heartbeat-settings').show().addClass('active');
                $('#redco-heartbeat-settings .redco-card-content').css({
                    'display': 'block',
                    'visibility': 'visible'
                });
            } else {
                $('#redco-heartbeat-settings').hide().removeClass('active');
            }
        }
    });

})(jQuery);
