<?php
/**
 * Error Logs Download Handler
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Error Logs Download Handler
 *
 * This class handles direct downloads of error logs.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Team
 */
class Redco_Optimizer_Error_Logs_Download {

    /**
     * The error logger instance.
     *
     * @since    1.0.0
     * @access   private
     * @var      Redco_Optimizer_Error_Logger    $error_logger    The error logger instance.
     */
    private $error_logger;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->error_logger = new Redco_Optimizer_Error_Logger();

        // Register download handler
        add_action('admin_init', array($this, 'handle_download_request'));
    }

    /**
     * Handle download request.
     *
     * @since    1.0.0
     */
    public function handle_download_request() {
        // Check if this is a download request
        if (!isset($_GET['action']) || $_GET['action'] !== 'redco_download_error_logs') {
            return;
        }

        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'redco_optimizer_nonce')) {
            wp_die(__('Security check failed.', 'redco-optimizer'));
        }

        // Check user capability
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'redco-optimizer'));
        }

        // Get log content
        $log_content = $this->error_logger->get_log_content();

        if (empty($log_content)) {
            wp_die(__('No error logs to export.', 'redco-optimizer'));
        }

        // Generate filename
        $site_name = sanitize_title(get_bloginfo('name'));
        $filename = 'redco-optimizer-error-logs-' . $site_name . '-' . date('Y-m-d-H-i-s') . '.txt';

        // Clean any previous output
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Set headers for download
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($log_content));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Disable caching
        nocache_headers();

        // Output log content
        echo $log_content;

        // Exit to prevent WordPress from sending any other output
        exit;
    }
}
