<?php
/**
 * The JavaScript defer functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The JavaScript defer functionality of the plugin.
 *
 * Handles JavaScript defer to improve page load times.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_JS_Defer {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for JavaScript defer.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for JavaScript defer.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
    }

    /**
     * Get JavaScript defer settings.
     *
     * @since    1.0.0
     * @return   array    The JavaScript defer settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_file_optimization_settings', array() );
        
        // Default settings
        $defaults = array(
            'defer_js' => 0,
            'defer_js_exclusions' => 'jquery.js',
        );
        
        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize JavaScript defer.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if JavaScript defer is enabled
        if ( ! $this->settings['defer_js'] ) {
            return;
        }

        // Add filter to process HTML
        add_filter( 'redco_buffer', array( $this, 'process_html' ), 35 );
    }

    /**
     * Process HTML to add defer attribute to script tags.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    public function process_html( $html ) {
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }
        
        // Check if URL is excluded
        if ( $this->is_url_excluded() ) {
            return $html;
        }
        
        // Get exclusions
        $exclusions = $this->get_exclusions();
        
        // Process script tags
        $html = preg_replace_callback(
            '/<script\b([^>]*)>(.*?)<\/script>/is',
            function( $matches ) use ( $exclusions ) {
                $tag_attributes = $matches[1];
                $script_content = $matches[2];
                
                // Skip if script has defer or async attribute
                if ( strpos( $tag_attributes, 'defer' ) !== false || strpos( $tag_attributes, 'async' ) !== false ) {
                    return $matches[0];
                }
                
                // Skip if script has type other than text/javascript or no type
                if ( preg_match( '/type=["\'](?!text\/javascript)[^"\']+["\']/i', $tag_attributes ) ) {
                    return $matches[0];
                }
                
                // Skip if script is excluded
                foreach ( $exclusions as $exclusion ) {
                    if ( empty( $exclusion ) ) {
                        continue;
                    }
                    
                    if ( strpos( $tag_attributes, $exclusion ) !== false ) {
                        return $matches[0];
                    }
                }
                
                // Add defer attribute
                $tag_attributes = preg_replace( '/(\s*\/?>)$/', ' defer$1', $tag_attributes );
                
                return '<script' . $tag_attributes . '>' . $script_content . '</script>';
            },
            $html
        );
        
        return $html;
    }

    /**
     * Check if current URL is excluded from JavaScript defer.
     *
     * @since    1.0.0
     * @return   bool    True if URL is excluded, false otherwise.
     */
    private function is_url_excluded() {
        // Get current URL
        $url = $_SERVER['REQUEST_URI'];
        
        // Common exclusions
        $common_exclusions = array(
            '/wp-admin/',
            '/wp-login.php',
            '/cart/',
            '/checkout/',
            '/my-account/',
        );
        
        // Check if URL matches any common exclusion
        foreach ( $common_exclusions as $exclusion ) {
            if ( strpos( $url, $exclusion ) !== false ) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Get JavaScript defer exclusions.
     *
     * @since    1.0.0
     * @return   array    The JavaScript defer exclusions.
     */
    private function get_exclusions() {
        $exclusions = array();
        
        if ( ! empty( $this->settings['defer_js_exclusions'] ) ) {
            $exclusions = explode( "\n", $this->settings['defer_js_exclusions'] );
            $exclusions = array_map( 'trim', $exclusions );
        }
        
        // Add common exclusions
        $common_exclusions = array(
            'jquery.js',
            'jquery.min.js',
            'wp-includes/js/jquery/jquery',
            'googletagmanager.com/gtm.js',
            'google-analytics.com/analytics.js',
            'googletagmanager.com/gtag/js',
            'fb-pixel',
            'fbevents.js',
            'recaptcha',
            'data-cfasync',
            'data-no-defer',
            'data-no-optimize',
            'data-noptimize',
        );
        
        return array_merge( $exclusions, $common_exclusions );
    }
}
