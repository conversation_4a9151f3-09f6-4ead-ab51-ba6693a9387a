/**
 * Advanced Cache Preloader Modal Fix
 * Ensures all sections in the Advanced Cache Preloader modal are visible
 * and settings are properly saved
 */

(function($) {
    'use strict';

    // Store the current settings to avoid overriding with test values
    var currentSettings = {};

    // Run when document is ready
    $(document).ready(function() {
        // Add event listener for the Advanced Cache Preloader settings button
        $(document).on('click', '.redco-addon-card:contains("Advanced Cache Preloader") .redco-addon-settings', function() {
            console.log('Advanced Cache Preloader settings button clicked');

            // Wait for the modal to open
            setTimeout(fixAdvancedCachePreloaderModal, 300);
            setTimeout(fixAdvancedCachePreloaderModal, 600);
            setTimeout(fixAdvancedCachePreloaderModal, 1200);
        });

        // Also listen for AJAX success events
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('admin-ajax.php') !== -1) {
                if (settings.data && (
                    settings.data.indexOf('advanced-cache-preloader') !== -1 ||
                    settings.data.indexOf('redco_optimizer_load_addon_settings') !== -1
                )) {
                    console.log('Advanced Cache Preloader AJAX detected');

                    // Store the current settings from the response if available
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response && response.success && response.data && response.data.content) {
                            console.log('Settings data found in AJAX response');
                            // Wait for the modal to be populated
                            setTimeout(function() {
                                fixAdvancedCachePreloaderModal(true);
                            }, 300);
                            setTimeout(function() {
                                fixAdvancedCachePreloaderModal(true);
                            }, 600);
                            setTimeout(function() {
                                fixAdvancedCachePreloaderModal(true);
                            }, 1200);
                        } else {
                            // Regular fix without preserving settings
                            setTimeout(fixAdvancedCachePreloaderModal, 300);
                            setTimeout(fixAdvancedCachePreloaderModal, 600);
                            setTimeout(fixAdvancedCachePreloaderModal, 1200);
                        }
                    } catch (e) {
                        console.error('Error parsing AJAX response:', e);
                        // Regular fix without preserving settings
                        setTimeout(fixAdvancedCachePreloaderModal, 300);
                        setTimeout(fixAdvancedCachePreloaderModal, 600);
                        setTimeout(fixAdvancedCachePreloaderModal, 1200);
                    }
                }
            }
        });

        // Handle form submission
        $(document).on('submit', '.redco-addon-settings-form[data-addon="advanced-cache-preloader"]', function(e) {
            console.log('Advanced Cache Preloader form submitted');

            // Ensure all form fields are included in the submission
            var $form = $(this);
            var formData = $form.serialize();
            console.log('Form data:', formData);

            // Don't prevent default - let the regular submission handler work
        });
    });

    /**
     * Fix the Advanced Cache Preloader modal
     *
     * @param {boolean} preserveSettings - Whether to preserve existing settings
     */
    function fixAdvancedCachePreloaderModal(preserveSettings) {
        console.log('Fixing Advanced Cache Preloader modal, preserveSettings:', preserveSettings);

        // Check if we're in the Advanced Cache Preloader modal
        if ($('.redco-modal:visible').length > 0 &&
            ($('.redco-modal-header h2:contains("Advanced Cache Preloader")').length > 0 ||
             $('.redco-modal-content:contains("Cache Preloader Settings")').length > 0)) {

            console.log('Advanced Cache Preloader modal confirmed');

            // Capture existing settings before making changes if preserveSettings is true
            if (preserveSettings) {
                captureExistingSettings();
            }

            // Make the modal taller
            $('.redco-modal').css({
                'max-height': '95vh',
                'height': 'auto',
                'width': '800px',
                'max-width': '95%',
                'display': 'flex',
                'flex-direction': 'column',
                'overflow': 'hidden'
            });

            // Make the content area scrollable
            $('.redco-modal-content').css({
                'max-height': 'calc(95vh - 140px)',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
                'flex': '1 1 auto',
                'padding': '25px',
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Check for missing sections
            var expectedSections = [
                'Advanced Settings',
                'Content Type Settings',
                'Priority Settings'
            ];

            var missingSections = [];

            expectedSections.forEach(function(section) {
                if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                    missingSections.push(section);
                }
            });

            if (missingSections.length > 0) {
                console.log('Missing sections detected: ' + missingSections.join(', '));

                // Add missing sections
                injectMissingSections(missingSections);
            } else {
                console.log('All sections found');
            }

            // Force all sections to be visible
            $('.redco-modal-section').show().css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'margin-bottom': '25px !important'
            });

            // Restore settings if we captured them
            if (preserveSettings && Object.keys(currentSettings).length > 0) {
                restoreSettings();
            }

            // Add a footer with save button if it doesn't exist
            if ($('.redco-modal-footer').length === 0) {
                console.log('Adding modal footer with save button');

                var $footer = $('<div class="redco-modal-footer"></div>');
                $footer.css({
                    'display': 'flex',
                    'justify-content': 'flex-end',
                    'gap': '10px',
                    'padding': '20px',
                    'border-top': '1px solid var(--border-color)',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                var $saveButton = $('<button type="button" class="redco-button redco-button-primary redco-modal-save">Save Settings</button>');
                var $cancelButton = $('<button type="button" class="redco-button redco-modal-cancel">Cancel</button>');

                $footer.append($cancelButton).append($saveButton);
                $('.redco-modal').append($footer);
            }

            // Ensure the form has the correct data-addon attribute
            $('.redco-addon-settings-form').attr('data-addon', 'advanced-cache-preloader');
        }
    }

    /**
     * Capture existing settings from the form
     */
    function captureExistingSettings() {
        console.log('Capturing existing settings');

        // Reset current settings
        currentSettings = {};

        // Get all form inputs
        $('.redco-addon-settings-form input, .redco-addon-settings-form select, .redco-addon-settings-form textarea').each(function() {
            var $input = $(this);
            var name = $input.attr('name');

            if (name) {
                if ($input.is(':checkbox')) {
                    currentSettings[name] = $input.is(':checked');
                } else {
                    currentSettings[name] = $input.val();
                }
            }
        });

        console.log('Captured settings:', currentSettings);
    }

    /**
     * Restore settings to the form
     */
    function restoreSettings() {
        console.log('Restoring settings:', currentSettings);

        // Apply settings to form inputs
        for (var name in currentSettings) {
            var value = currentSettings[name];
            var $input = $('.redco-addon-settings-form [name="' + name + '"]');

            if ($input.length > 0) {
                if ($input.is(':checkbox')) {
                    $input.prop('checked', value);
                } else {
                    $input.val(value);
                }
            }
        }
    }

    /**
     * Inject missing sections into the modal
     */
    function injectMissingSections(missingSections) {
        console.log('Injecting missing sections: ' + missingSections.join(', '));

        // Get the modal content and footer
        var $modalContent = $('.redco-modal-content');
        var $footer = $('.redco-modal-footer');

        if ($modalContent.length === 0) {
            console.log('Modal content not found');
            return;
        }

        // Add each missing section
        missingSections.forEach(function(section) {
            console.log('Adding section: ' + section);

            var $section = $('<div class="redco-modal-section"></div>');
            $section.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'margin-bottom': '25px',
                'position': 'relative'
            });

            var $heading = $('<h3>' + section + '</h3>');
            $heading.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            $section.append($heading);

            // Add content based on the section
            if (section === 'Advanced Settings') {
                addAdvancedSettingsContent($section);
            } else if (section === 'Content Type Settings') {
                addContentTypeSettingsContent($section);
            } else if (section === 'Priority Settings') {
                addPrioritySettingsContent($section);
            }

            // Add the section to the modal
            if ($footer.length > 0) {
                $footer.before($section);
            } else {
                $modalContent.append($section);
            }
        });
    }

    /**
     * Add Advanced Settings content
     */
    function addAdvancedSettingsContent($section) {
        // Get values from current settings or use defaults
        var throttleValue = currentSettings.preload_throttle || 3;
        var batchSizeValue = currentSettings.preload_batch_size || 20;
        var mobilePreloadChecked = currentSettings.mobile_preload ? 'checked' : '';
        var desktopPreloadChecked = currentSettings.desktop_preload !== false ? 'checked' : '';

        $section.append(
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="preload_throttle">Preload Throttle</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="number" id="preload_throttle" name="preload_throttle" class="redco-input" value="' + throttleValue + '" min="0" max="60">' +
                    '<p class="redco-form-help">Delay in seconds between preloading each URL. Set to 0 for no delay.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="preload_batch_size">Batch Size</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="number" id="preload_batch_size" name="preload_batch_size" class="redco-input" value="' + batchSizeValue + '" min="1" max="100">' +
                    '<p class="redco-form-help">Number of URLs to preload in each batch. Lower values reduce server load.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Mobile Preload</h4>' +
                    '<p>Preload cache for mobile devices.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="mobile_preload" value="1" ' + mobilePreloadChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Desktop Preload</h4>' +
                    '<p>Preload cache for desktop devices.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="desktop_preload" value="1" ' + desktopPreloadChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Content Type Settings content
     */
    function addContentTypeSettingsContent($section) {
        // Get values from current settings or use defaults
        var preloadPostsChecked = currentSettings.preload_posts !== false ? 'checked' : '';
        var preloadPagesChecked = currentSettings.preload_pages !== false ? 'checked' : '';
        var preloadCategoriesChecked = currentSettings.preload_categories !== false ? 'checked' : '';

        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Preload Posts</h4>' +
                    '<p>Preload cache for posts.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="preload_posts" value="1" ' + preloadPostsChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Preload Pages</h4>' +
                    '<p>Preload cache for pages.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="preload_pages" value="1" ' + preloadPagesChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Preload Categories</h4>' +
                    '<p>Preload cache for category archives.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="preload_categories" value="1" ' + preloadCategoriesChecked + '>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Priority Settings content
     */
    function addPrioritySettingsContent($section) {
        // Get values from current settings or use defaults
        var priorityValue = currentSettings.preload_priority || 'homepage_first';
        var depthValue = currentSettings.preload_depth || '2';

        // Create option selections based on current values
        var priorityOptions = {
            'homepage_first': priorityValue === 'homepage_first' ? 'selected' : '',
            'recent_content': priorityValue === 'recent_content' ? 'selected' : '',
            'popular_content': priorityValue === 'popular_content' ? 'selected' : '',
            'sequential': priorityValue === 'sequential' ? 'selected' : ''
        };

        var depthOptions = {
            '1': depthValue === '1' ? 'selected' : '',
            '2': depthValue === '2' ? 'selected' : '',
            '3': depthValue === '3' ? 'selected' : '',
            'all': depthValue === 'all' ? 'selected' : ''
        };

        $section.append(
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="preload_priority">Preload Priority</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<select id="preload_priority" name="preload_priority" class="redco-select">' +
                        '<option value="homepage_first" ' + priorityOptions.homepage_first + '>Homepage First</option>' +
                        '<option value="recent_content" ' + priorityOptions.recent_content + '>Recent Content First</option>' +
                        '<option value="popular_content" ' + priorityOptions.popular_content + '>Popular Content First</option>' +
                        '<option value="sequential" ' + priorityOptions.sequential + '>Sequential (No Priority)</option>' +
                    '</select>' +
                    '<p class="redco-form-help">Determine which content should be preloaded first.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="preload_depth">Preload Depth</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<select id="preload_depth" name="preload_depth" class="redco-select">' +
                        '<option value="1" ' + depthOptions['1'] + '>Level 1 (Homepage Only)</option>' +
                        '<option value="2" ' + depthOptions['2'] + '>Level 2 (Homepage + Main Pages)</option>' +
                        '<option value="3" ' + depthOptions['3'] + '>Level 3 (Deep Preload)</option>' +
                        '<option value="all" ' + depthOptions.all + '>All Pages (Complete Preload)</option>' +
                    '</select>' +
                    '<p class="redco-form-help">How deep to follow links when preloading. Higher values may increase server load.</p>' +
                '</div>' +
            '</div>'
        );
    }

})(jQuery);
