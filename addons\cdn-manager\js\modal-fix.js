/**
 * CDN Manager Modal Fix
 * Ensures all sections in the CDN Manager modal are visible
 */

(function($) {
    'use strict';

    // Run when document is ready
    $(document).ready(function() {
        // Add event listener for the CDN Manager settings button
        $(document).on('click', '.redco-addon-card:contains("CDN Manager") .redco-addon-settings', function() {
            console.log('CDN Manager settings button clicked');
            
            // Wait for the modal to open
            setTimeout(fixCdnManagerModal, 300);
            setTimeout(fixCdnManagerModal, 600);
            setTimeout(fixCdnManagerModal, 1200);
        });
        
        // Also listen for AJAX success events
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('admin-ajax.php') !== -1) {
                if (settings.data && settings.data.indexOf('cdn-manager') !== -1) {
                    console.log('CDN Manager AJAX detected');
                    
                    // Wait for the modal to be populated
                    setTimeout(fixCdnManagerModal, 300);
                    setTimeout(fixCdnManagerModal, 600);
                    setTimeout(fixCdnManagerModal, 1200);
                }
            }
        });
    });
    
    /**
     * Fix the CDN Manager modal
     */
    function fixCdnManagerModal() {
        console.log('Fixing CDN Manager modal');
        
        // Check if we're in the CDN Manager modal
        if ($('.redco-modal:visible').length > 0 && 
            ($('.redco-modal-header h2:contains("CDN Manager")').length > 0 ||
             $('.redco-modal-content:contains("CDN Settings")').length > 0)) {
            
            console.log('CDN Manager modal confirmed');
            
            // Make the modal taller
            $('.redco-modal').css({
                'max-height': '95vh',
                'height': 'auto',
                'width': '800px',
                'max-width': '95%',
                'display': 'flex',
                'flex-direction': 'column',
                'overflow': 'hidden'
            });
            
            // Make the content area scrollable
            $('.redco-modal-content').css({
                'max-height': 'calc(95vh - 140px)',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
                'flex': '1 1 auto',
                'padding': '25px',
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });
            
            // Check for missing sections
            var expectedSections = [
                'CDN Settings',
                'Content Settings',
                'Exclusion Settings'
            ];
            
            var missingSections = [];
            
            expectedSections.forEach(function(section) {
                if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                    missingSections.push(section);
                }
            });
            
            if (missingSections.length > 0) {
                console.log('Missing sections detected: ' + missingSections.join(', '));
                
                // Add a debug message to the modal
                if ($('.redco-modal-debug-message').length === 0) {
                    $('<div>')
                        .addClass('redco-modal-debug-message')
                        .html('<div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; text-align: center;">' +
                              '<p style="font-weight: bold; margin: 0;">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                              '<div style="margin-top: 10px; font-size: 24px; animation: bounce 1s infinite;">↓</div>' +
                              '</div>' +
                              '<style>@keyframes bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }</style>')
                        .prependTo('.redco-modal-content');
                }
                
                // Add missing sections
                injectMissingSections(missingSections);
            } else {
                console.log('All sections found');
            }
            
            // Force all sections to be visible
            $('.redco-modal-section').show().css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'margin-bottom': '25px !important'
            });
            
            // Add a scroll indicator at the bottom
            if ($('.redco-scroll-indicator-bottom').length === 0) {
                $('<div>')
                    .addClass('redco-scroll-indicator-bottom')
                    .html('<div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-top: 20px; text-align: center;">' +
                          '<p style="font-weight: bold; margin: 0;">End of settings</p>' +
                          '<div style="margin-top: 5px;">Don\'t forget to save your changes!</div>' +
                          '</div>')
                    .appendTo('.redco-modal-content');
            }
        }
    }
    
    /**
     * Inject missing sections into the modal
     */
    function injectMissingSections(missingSections) {
        console.log('Injecting missing sections: ' + missingSections.join(', '));
        
        // Get the modal content and footer
        var $modalContent = $('.redco-modal-content');
        var $footer = $('.redco-modal-footer');
        
        if ($modalContent.length === 0) {
            console.log('Modal content not found');
            return;
        }
        
        // Add each missing section
        missingSections.forEach(function(section) {
            console.log('Adding section: ' + section);
            
            var $section = $('<div class="redco-modal-section"></div>');
            $section.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'margin-bottom': '25px',
                'position': 'relative'
            });
            
            var $heading = $('<h3>' + section + '</h3>');
            $heading.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });
            
            $section.append($heading);
            
            // Add content based on the section
            if (section === 'CDN Settings') {
                addCdnSettingsContent($section);
            } else if (section === 'Content Settings') {
                addContentSettingsContent($section);
            } else if (section === 'Exclusion Settings') {
                addExclusionSettingsContent($section);
            }
            
            // Add the section to the modal
            if ($footer.length > 0) {
                $footer.before($section);
            } else {
                $modalContent.append($section);
            }
        });
    }
    
    /**
     * Add CDN Settings content
     */
    function addCdnSettingsContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Enable CDN</h4>' +
                    '<p>Enable content delivery network integration.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="enable_cdn" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +
            
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="cdn_url">CDN URL</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="text" id="cdn_url" name="cdn_url" class="redco-input" value="https://cdn.example.com">' +
                    '<p class="redco-form-help">Enter your CDN URL without trailing slash.</p>' +
                '</div>' +
            '</div>'
        );
    }
    
    /**
     * Add Content Settings content
     */
    function addContentSettingsContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>CDN for Images</h4>' +
                    '<p>Serve images from CDN.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="cdn_images" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +
            
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>CDN for JavaScript</h4>' +
                    '<p>Serve JavaScript files from CDN.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="cdn_js" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +
            
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>CDN for CSS</h4>' +
                    '<p>Serve CSS files from CDN.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="cdn_css" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }
    
    /**
     * Add Exclusion Settings content
     */
    function addExclusionSettingsContent($section) {
        $section.append(
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="cdn_exclusions">CDN Exclusions</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<textarea id="cdn_exclusions" name="cdn_exclusions" class="redco-textarea" rows="5"></textarea>' +
                    '<p class="redco-form-help">Enter URLs or file patterns to exclude from CDN, one per line.</p>' +
                '</div>' +
            '</div>'
        );
    }

})(jQuery);
