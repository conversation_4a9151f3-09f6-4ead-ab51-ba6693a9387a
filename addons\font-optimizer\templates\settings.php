<?php
/**
 * Font Optimizer Settings Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/font-optimizer/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings and detected fonts from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
$detected_fonts = isset($detected_fonts) ? $detected_fonts : (isset($this->detected_fonts) ? $this->detected_fonts : array());
?>

<div class="redco-addon-settings-form">
    <form method="post" action="">
        <?php wp_nonce_field('redco_font_optimizer_save_settings', 'redco_font_optimizer_nonce'); ?>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Font Optimizer Settings', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Font Optimizer', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Enable font optimization for better performance.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="enabled" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Font Sources', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Optimize Google Fonts', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Optimize Google Fonts loading.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="optimize_google_fonts" <?php checked(isset($settings['optimize_google_fonts']) ? $settings['optimize_google_fonts'] : 1, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Optimize Typekit Fonts', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Optimize Adobe Typekit fonts loading.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="optimize_typekit_fonts" <?php checked(isset($settings['optimize_typekit_fonts']) ? $settings['optimize_typekit_fonts'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Optimize Custom Fonts', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Optimize custom font loading.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="optimize_custom_fonts" <?php checked(isset($settings['optimize_custom_fonts']) ? $settings['optimize_custom_fonts'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="custom_fonts"><?php esc_html_e('Custom Fonts', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <textarea id="custom_fonts" name="custom_fonts" class="redco-textarea" rows="5"><?php echo esc_textarea(isset($settings['custom_fonts']) ? $settings['custom_fonts'] : ''); ?></textarea>
                        <p class="redco-form-help"><?php esc_html_e('Enter custom font URLs, one per line.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="excluded_fonts"><?php esc_html_e('Excluded Fonts', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <textarea id="excluded_fonts" name="excluded_fonts" class="redco-textarea" rows="5"><?php echo esc_textarea(isset($settings['excluded_fonts']) ? $settings['excluded_fonts'] : ''); ?></textarea>
                        <p class="redco-form-help"><?php esc_html_e('Enter font families to exclude from optimization, one per line.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Optimization Options', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Local Font Hosting', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Download and host fonts locally for better performance.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="local_hosting" <?php checked(isset($settings['local_hosting']) ? $settings['local_hosting'] : 1, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Preload Fonts', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Add preload tags for critical fonts.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="preload_fonts" <?php checked(isset($settings['preload_fonts']) ? $settings['preload_fonts'] : 1, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="font_display"><?php esc_html_e('Font Display', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <select id="font_display" name="font_display" class="redco-select">
                            <option value="auto" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'auto'); ?>><?php esc_html_e('Auto', 'redco-optimizer'); ?></option>
                            <option value="block" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'block'); ?>><?php esc_html_e('Block', 'redco-optimizer'); ?></option>
                            <option value="swap" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'swap'); ?>><?php esc_html_e('Swap', 'redco-optimizer'); ?></option>
                            <option value="fallback" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'fallback'); ?>><?php esc_html_e('Fallback', 'redco-optimizer'); ?></option>
                            <option value="optional" <?php selected(isset($settings['font_display']) ? $settings['font_display'] : 'swap', 'optional'); ?>><?php esc_html_e('Optional', 'redco-optimizer'); ?></option>
                        </select>
                        <p class="redco-form-help"><?php esc_html_e('Set the font-display property for all fonts.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Subset Fonts', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Load only the character sets you need.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="subset_fonts" <?php checked(isset($settings['subset_fonts']) ? $settings['subset_fonts'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row" id="subset-options">
                    <div class="redco-form-label">
                        <label><?php esc_html_e('Character Sets', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <div class="redco-checkbox-group">
                            <label>
                                <input type="checkbox" name="subset_latin" <?php checked(isset($settings['subset_latin']) ? $settings['subset_latin'] : 1, 1); ?> value="1">
                                <?php esc_html_e('Latin', 'redco-optimizer'); ?>
                            </label>

                            <label>
                                <input type="checkbox" name="subset_latin_ext" <?php checked(isset($settings['subset_latin_ext']) ? $settings['subset_latin_ext'] : 0, 1); ?> value="1">
                                <?php esc_html_e('Latin Extended', 'redco-optimizer'); ?>
                            </label>

                            <label>
                                <input type="checkbox" name="subset_cyrillic" <?php checked(isset($settings['subset_cyrillic']) ? $settings['subset_cyrillic'] : 0, 1); ?> value="1">
                                <?php esc_html_e('Cyrillic', 'redco-optimizer'); ?>
                            </label>

                            <label>
                                <input type="checkbox" name="subset_cyrillic_ext" <?php checked(isset($settings['subset_cyrillic_ext']) ? $settings['subset_cyrillic_ext'] : 0, 1); ?> value="1">
                                <?php esc_html_e('Cyrillic Extended', 'redco-optimizer'); ?>
                            </label>

                            <label>
                                <input type="checkbox" name="subset_greek" <?php checked(isset($settings['subset_greek']) ? $settings['subset_greek'] : 0, 1); ?> value="1">
                                <?php esc_html_e('Greek', 'redco-optimizer'); ?>
                            </label>

                            <label>
                                <input type="checkbox" name="subset_greek_ext" <?php checked(isset($settings['subset_greek_ext']) ? $settings['subset_greek_ext'] : 0, 1); ?> value="1">
                                <?php esc_html_e('Greek Extended', 'redco-optimizer'); ?>
                            </label>

                            <label>
                                <input type="checkbox" name="subset_vietnamese" <?php checked(isset($settings['subset_vietnamese']) ? $settings['subset_vietnamese'] : 0, 1); ?> value="1">
                                <?php esc_html_e('Vietnamese', 'redco-optimizer'); ?>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Remove Unused Variants', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Remove font variants that are not used on your site.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="remove_unused_variants" <?php checked(isset($settings['remove_unused_variants']) ? $settings['remove_unused_variants'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Advanced Options', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Font Face Observer', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Use Font Face Observer to detect when fonts are loaded.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="font_face_observer" <?php checked(isset($settings['font_face_observer']) ? $settings['font_face_observer'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Async CSS', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Load font CSS asynchronously.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="async_css" <?php checked(isset($settings['async_css']) ? $settings['async_css'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="font_loading_mode"><?php esc_html_e('Font Loading Mode', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <select id="font_loading_mode" name="font_loading_mode" class="redco-select">
                            <option value="default" <?php selected(isset($settings['font_loading_mode']) ? $settings['font_loading_mode'] : 'default', 'default'); ?>><?php esc_html_e('Default', 'redco-optimizer'); ?></option>
                            <option value="async" <?php selected(isset($settings['font_loading_mode']) ? $settings['font_loading_mode'] : 'default', 'async'); ?>><?php esc_html_e('Asynchronous', 'redco-optimizer'); ?></option>
                            <option value="critical" <?php selected(isset($settings['font_loading_mode']) ? $settings['font_loading_mode'] : 'default', 'critical'); ?>><?php esc_html_e('Critical', 'redco-optimizer'); ?></option>
                            <option value="foft" <?php selected(isset($settings['font_loading_mode']) ? $settings['font_loading_mode'] : 'default', 'foft'); ?>><?php esc_html_e('FOFT (Flash of Faux Text)', 'redco-optimizer'); ?></option>
                        </select>
                        <p class="redco-form-help"><?php esc_html_e('Select the font loading strategy.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Detected Fonts', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-detected-fonts">
                    <?php if (empty($detected_fonts)) : ?>
                        <p><?php esc_html_e('No fonts detected yet. Click the button below to detect fonts on your site.', 'redco-optimizer'); ?></p>
                    <?php else : ?>
                        <div class="redco-font-groups">
                            <?php if (!empty($detected_fonts['google'])) : ?>
                                <div class="redco-font-group">
                                    <h4><?php esc_html_e('Google Fonts', 'redco-optimizer'); ?></h4>
                                    <ul class="redco-font-list">
                                        <?php foreach ($detected_fonts['google'] as $font) : ?>
                                            <li>
                                                <div class="redco-font-info">
                                                    <span class="redco-font-family"><?php echo esc_html($font['family']); ?></span>
                                                    <span class="redco-font-variants"><?php echo esc_html(implode(', ', $font['variants'])); ?></span>
                                                </div>
                                                <div class="redco-font-actions">
                                                    <?php if (isset($settings['local_hosting']) && $settings['local_hosting']) : ?>
                                                        <button type="button" class="redco-button redco-button-secondary redco-download-font" data-url="<?php echo esc_url($font['url']); ?>">
                                                            <span class="dashicons dashicons-download"></span>
                                                            <?php esc_html_e('Download', 'redco-optimizer'); ?>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($detected_fonts['typekit'])) : ?>
                                <div class="redco-font-group">
                                    <h4><?php esc_html_e('Typekit Fonts', 'redco-optimizer'); ?></h4>
                                    <ul class="redco-font-list">
                                        <?php foreach ($detected_fonts['typekit'] as $font) : ?>
                                            <li>
                                                <div class="redco-font-info">
                                                    <span class="redco-font-family"><?php echo esc_html($font['family']); ?></span>
                                                    <span class="redco-font-variants"><?php echo esc_html(implode(', ', $font['variants'])); ?></span>
                                                </div>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($detected_fonts['custom'])) : ?>
                                <div class="redco-font-group">
                                    <h4><?php esc_html_e('Custom Fonts', 'redco-optimizer'); ?></h4>
                                    <ul class="redco-font-list">
                                        <?php foreach ($detected_fonts['custom'] as $font) : ?>
                                            <li>
                                                <div class="redco-font-info">
                                                    <span class="redco-font-family"><?php echo esc_html($font['family']); ?></span>
                                                    <span class="redco-font-variants"><?php echo esc_html(implode(', ', $font['variants'])); ?></span>
                                                </div>
                                                <div class="redco-font-actions">
                                                    <?php if (isset($settings['local_hosting']) && $settings['local_hosting']) : ?>
                                                        <button type="button" class="redco-button redco-button-secondary redco-download-font" data-url="<?php echo esc_url($font['url']); ?>">
                                                            <span class="dashicons dashicons-download"></span>
                                                            <?php esc_html_e('Download', 'redco-optimizer'); ?>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <div class="redco-font-actions">
                        <button type="button" id="detect-fonts" class="redco-button redco-button-secondary">
                            <span class="dashicons dashicons-search"></span>
                            <?php esc_html_e('Detect Fonts', 'redco-optimizer'); ?>
                        </button>

                        <?php if (!empty($detected_fonts)) : ?>
                            <button type="button" id="reset-fonts" class="redco-button redco-button-secondary">
                                <span class="dashicons dashicons-update"></span>
                                <?php esc_html_e('Reset Fonts', 'redco-optimizer'); ?>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-form-actions">
            <button type="submit" name="redco_font_optimizer_save_settings" class="redco-button redco-button-primary">
                <span class="dashicons dashicons-yes"></span>
                <?php esc_html_e('Save Settings', 'redco-optimizer'); ?>
            </button>
        </div>
    </form>
</div>
