<?php
/**
 * The plugin bootstrap file
 *
 * @link              https://redco-optimizer.com
 * @since             1.0.0
 * @package           Redco_Optimizer
 *
 * @wordpress-plugin
 * Plugin Name:       Redco Optimizer
 * Plugin URI:        https://redco-optimizer.com
 * Description:       A powerful WordPress plugin for site optimization and performance monitoring.
 * Version:           1.0.0
 * Author:            Redco
 * Author URI:        https://redco-optimizer.com
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       redco-optimizer
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

/**
 * Currently plugin version.
 * Start at version 1.0.0 and use SemVer - https://semver.org
 */
define( 'REDCO_OPTIMIZER_VERSION', '1.0.0' );
define( 'REDCO_OPTIMIZER_PLUGIN_NAME', 'redco-optimizer' );
define( 'REDCO_OPTIMIZER_PLUGIN_PATH', plugin_dir_path( __FILE__ ) );
define( 'REDCO_OPTIMIZER_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'REDCO_OPTIMIZER_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

/**
 * The code that runs during plugin activation.
 * This action is documented in includes/class-redco-optimizer-activator.php
 */
function activate_redco_optimizer() {
    require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-activator.php';
    Redco_Optimizer_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 * This action is documented in includes/class-redco-optimizer-deactivator.php
 */
function deactivate_redco_optimizer() {
    require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-deactivator.php';
    Redco_Optimizer_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_redco_optimizer' );
register_deactivation_hook( __FILE__, 'deactivate_redco_optimizer' );

/**
 * The helper functions file.
 */
require REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/redco-optimizer-functions.php';

/**
 * The configuration class that centralizes all plugin settings.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-redco-optimizer-config.php';

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer.php';

/**
 * Begins execution of the plugin.
 *
 * @since    1.0.0
 */
function run_redco_optimizer() {
    $plugin = new Redco_Optimizer();
    $plugin->run();
}

run_redco_optimizer();
