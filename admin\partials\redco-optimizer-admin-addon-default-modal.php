<?php
/**
 * Default Add-on Settings Modal Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get the add-on slug
$addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';

// Get the add-on data
global $redco_optimizer_addons;
if (!isset($redco_optimizer_addons)) {
    $redco_optimizer_addons = new Redco_Optimizer_Addons();
}

$addons = $redco_optimizer_addons->get_installed_addons();
$addon = isset($addons[$addon_slug]) ? $addons[$addon_slug] : array();

// Get the add-on name
$addon_name = isset($addon['name']) ? $addon['name'] : ucwords(str_replace('-', ' ', $addon_slug));

// Get the add-on description
$addon_description = isset($addon['description']) ? $addon['description'] : '';

// Get the add-on settings
$option_name = 'redco_' . str_replace('-', '_', $addon_slug) . '_settings';
$settings = get_option($option_name, array('enabled' => false));
?>

<form method="post" action="" class="redco-addon-settings-form" data-addon="<?php echo esc_attr($addon_slug); ?>">
    <?php wp_nonce_field('redco_' . $addon_slug . '_save_settings', 'redco_' . $addon_slug . '_nonce'); ?>

    <div class="redco-modal-section">
        <h3><?php echo esc_html($addon_name . ' ' . __('Settings', 'redco-optimizer')); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php echo esc_html(__('Enable', 'redco-optimizer') . ' ' . $addon_name); ?></h4>
                <p><?php echo esc_html($addon_description); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="enabled" v value="1"alue="1" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : false, true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>

    <?php if ($addon_slug === 'advanced-cache-preloader'): ?>
    <div class="redco-modal-section">
        <h3><?php esc_html_e('Cache Preloader Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Homepage', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload your homepage to ensure it loads quickly for visitors.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_homepage" v value="1"alue="1" <?php checked(isset($settings['preload_homepage']) ? $settings['preload_homepage'] : true, true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_schedule"><?php esc_html_e('Preload Schedule', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_schedule" name="preload_schedule" class="redco-select">
                    <option value="hourly" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'hourly'); ?>><?php esc_html_e('Hourly', 'redco-optimizer'); ?></option>
                    <option value="twicedaily" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'twicedaily'); ?>><?php esc_html_e('Twice Daily', 'redco-optimizer'); ?></option>
                    <option value="daily" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'daily'); ?>><?php esc_html_e('Daily', 'redco-optimizer'); ?></option>
                    <option value="weekly" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'weekly'); ?>><?php esc_html_e('Weekly', 'redco-optimizer'); ?></option>
                </select>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($addon_slug === 'font-optimizer'): ?>
    <div class="redco-modal-section">
        <h3><?php esc_html_e('Font Optimization Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Local Font Hosting', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Host Google Fonts locally for better performance and privacy.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="local_hosting" v value="1"alue="1" <?php checked(isset($settings['local_hosting']) ? $settings['local_hosting'] : false, true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Fonts', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Add preload hints for critical fonts.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_fonts" v value="1"alue="1" <?php checked(isset($settings['preload_fonts']) ? $settings['preload_fonts'] : false, true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($addon_slug === 'advanced-lazy-load'): ?>
    <div class="redco-modal-section">
        <h3><?php esc_html_e('Media Types', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Lazy Load Images', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Apply lazy loading to images in your content.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="lazy_load_images" v value="1"alue="1" <?php checked(isset($settings['lazy_load_images']) ? $settings['lazy_load_images'] : true, true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Lazy Load iFrames', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Apply lazy loading to iframes in your content.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="lazy_load_iframes" v value="1"alue="1" <?php checked(isset($settings['lazy_load_iframes']) ? $settings['lazy_load_iframes'] : true, true); ?>>
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="redco-modal-footer">
        <button type="submit" class="redco-button redco-button-primary">
            <span class="dashicons dashicons-yes"></span>
            <?php esc_html_e('Save Settings', 'redco-optimizer'); ?>
        </button>
        <button type="button" class="redco-button redco-button-secondary redco-modal-cancel">
            <?php esc_html_e('Cancel', 'redco-optimizer'); ?>
        </button>
    </div>
</form>
