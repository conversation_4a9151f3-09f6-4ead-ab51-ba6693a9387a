/**
 * Redco Optimizer - Modules Tab Styles
 * This file contains styles specific to the modules tab
 *
 * IMPORTANT: This file should be loaded AFTER the main admin CSS
 * to ensure these styles take precedence.
 *
 * Version: 1.0.1 - Updated to fix caching issues
 */

/* Section Intro */
.redco-section-intro {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.redco-section-intro h2 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 22px;
    color: #333;
    position: relative;
    display: inline-block;
}

.redco-section-intro h2::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0; /* Changed from 40px to 0 to remove the green line */
    height: 0; /* Changed from 3px to 0 */
    background: transparent; /* Changed from gradient to transparent */
    opacity: 0;
}

.redco-section-intro p {
    margin: 0;
    color: #666;
    font-size: 15px;
    line-height: 1.5;
    max-width: 700px;
}

/* Module Actions */
.redco-modules-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Module Cards */
.redco-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.redco-module-card {
    background: #fff !important;
    border-radius: 10px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    border: 1px solid var(--border-color) !important;
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    margin-bottom: 15px !important;
}

.redco-module-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-3px);
}

/* Module States */
.redco-module-enabled {
    border-color: var(--success-color) !important;
    box-shadow: 0 2px 10px rgba(0, 166, 107, 0.15) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%) !important;
}

.redco-module-enabled:hover {
    box-shadow: 0 8px 20px rgba(0, 166, 107, 0.25) !important;
    transform: translateY(-3px);
}

.redco-module-disabled {
    border-color: #e0e0e0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%) !important;
    opacity: 0.8;
}

.redco-module-disabled:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px);
    opacity: 1;
}

/* Clickable Module Cards */
.redco-module-clickable {
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.redco-module-clickable:hover {
    transform: translateY(-3px) !important;
}

.redco-module-clickable:active {
    transform: translateY(-1px) !important;
}

/* Click Overlay */
.redco-module-click-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    cursor: pointer;
    background: transparent;
}

.redco-module-footer {
    position: relative;
    z-index: 2;
}

.redco-module-footer-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Status Indicator */
.redco-module-status-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-module-status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-status-enabled {
    background: rgba(0, 166, 107, 0.1);
    color: #00A66B;
    border: 1px solid rgba(0, 166, 107, 0.2);
}

.redco-status-disabled {
    background: rgba(128, 128, 128, 0.1);
    color: #666;
    border: 1px solid rgba(128, 128, 128, 0.2);
}

.redco-status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
}

.redco-status-enabled .redco-status-dot {
    background: #00A66B;
    box-shadow: 0 0 4px rgba(0, 166, 107, 0.4);
}

.redco-status-disabled .redco-status-dot {
    background: #999;
}

/* Button Styles */
.redco-button-toggle {
    transition: all 0.3s ease !important;
}

.redco-button-success {
    background: #00A66B !important;
    color: white !important;
    border: 1px solid #00A66B !important;
}

.redco-button-success:hover {
    background: #008a5a !important;
    border-color: #008a5a !important;
}

.redco-button-danger {
    background: #dc3545 !important;
    color: white !important;
    border: 1px solid #dc3545 !important;
}

.redco-button-danger:hover {
    background: #c82333 !important;
    border-color: #c82333 !important;
}

.redco-module-enabled::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 4px !important;
    height: 100% !important;
    background-color: var(--success-color) !important;
    display: block !important;
}

/* Module Header */
.redco-module-header {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    background: #f8f9fa;
}

/* Module Title and Icon */
.redco-module-title-wrapper {
    display: flex;
    flex-direction: column;
}

.redco-module-title-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 3px;
}

.redco-module-title-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 3px;
    flex-wrap: wrap;
}

.redco-module-title-row .redco-premium-badge {
    margin-left: 8px !important;
    margin-top: 0 !important;
    font-size: 9px !important;
    padding: 2px 8px !important;
}





/* Ensure toggle wrapper has proper positioning for the badge */
.redco-module-toggle-wrapper {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
}

.redco-module-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-module-icon svg {
    width: 20px;
    height: 20px;
    opacity: 1;
}

.redco-module-title {
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    font-size: 16px;
    line-height: 1.3;
}

/* Premium Badge - Unified System */
.redco-premium-badge {
    display: inline-flex !important;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #fff !important;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    width: fit-content;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Removed pseudo-element content - using HTML badges only */

.redco-title-wrapper {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
}

.redco-module-title-container .redco-premium-badge {
    margin-top: 4px;
    font-size: 10px;
    padding: 3px 10px;
    display: inline-flex !important;
    align-self: flex-start !important;
    margin-left: 0 !important;
}

/* Removed duplicate badge styling - using unified system above */

/* Special styling for CDN module premium badge */
.redco-premium-badge-cdn {
    margin-top: 4px !important;
    margin-left: 0 !important;
    align-self: flex-start !important;
}

/* New style for premium badge below title */
.redco-premium-badge-below {
    display: inline-flex !important;
    align-items: center;
    background: var(--premium-gradient) !important;
    color: #fff !important;
    font-size: 10px;
    font-weight: 600;
    padding: 3px 10px;
    border-radius: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    width: fit-content;
    margin-top: 3px !important;
    margin-left: 0 !important;
    align-self: flex-start !important;
}

/* Removed pseudo-element content - using HTML badges only */

/* Module Content */
.redco-module-content {
    padding: 12px;
    flex: 1;
}

.redco-module-description {
    color: var(--text-light);
    margin-bottom: 10px;
    font-size: 13px;
    line-height: 1.4;
}

.redco-module-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
    gap: 8px;
    margin-bottom: 12px;
}

.redco-module-feature {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--text-color);
}

.redco-module-feature-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-right: 6px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300A66B"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

.redco-module-premium .redco-module-feature-icon {
    opacity: 0.5;
}

/* Module Footer */
.redco-module-footer {
    padding: 10px 12px;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #f8f9fa;
    margin-top: auto;
}

/* Toggle Switch */
.redco-switch {
    position: relative;
    display: inline-block;
    width: 46px;
    height: 24px;
}

.redco-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.redco-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e2e4e7;
    transition: .4s;
    border-radius: 24px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.redco-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

input:checked + .redco-slider {
    background-color: #00A66B !important;
}

input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

input:checked + .redco-slider:before {
    transform: translateX(22px) !important;
}

/* Disabled toggle switch */
.redco-switch-disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
}

.redco-switch-disabled .redco-slider {
    cursor: not-allowed !important;
    background-color: #e2e4e7 !important;
}

input:disabled + .redco-slider {
    background-color: #e2e4e7 !important;
    cursor: not-allowed !important;
}

/* Buttons */
.redco-module-footer .redco-button-secondary {
    background: #f8f9fa !important;
    border: 1px solid #d0d0d0 !important;
    color: #3c434a !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08) !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.redco-module-footer .redco-button-secondary:hover,
.redco-module-footer .redco-button-secondary:focus {
    background: #ffffff !important;
    border-color: #bcbcbc !important;
    color: #2c3338 !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px) !important;
    outline: none !important;
}

.redco-module-footer .redco-button-secondary .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 6px;
    vertical-align: text-bottom;
}

.redco-module-footer .redco-button-premium {
    background: var(--premium-gradient) !important;
    color: #fff !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    transition: all 0.2s ease !important;
}

.redco-module-footer .redco-button-premium:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px) !important;
    color: #fff !important;
}

/* Dashicons styling */
.redco-module-footer .dashicons {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
    margin-right: 5px !important;
    line-height: 1.3 !important;
}

/* Fix for any remaining issues - more specific selector to avoid affecting header icons */
.redco-module-card .dashicons:not(.redco-module-footer .dashicons):not(#redco-header-icon-inner):not(.redco-page-header-icon .dashicons):not(.redco-addon-icon .dashicons):not(.redco-module-icon .dashicons) {
    display: none !important; /* Hide any dashicons in the module card except in buttons, header, addon icons, and module icons */
}

.redco-module-footer .dashicons {
    display: inline-block !important; /* Show dashicons in buttons */
}

/* Ensure module icon dashicons are always visible */
.redco-module-icon .dashicons {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
}

/* Ensure header icon is always visible */
#redco-header-icon-inner,
.redco-page-header-icon .dashicons {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 9999 !important;
}

/* Notification styling override - Complete styles to match main CSS */
.redco-notification-container {
    position: fixed !important;
    top: 50px !important;
    right: 20px !important;
    width: 350px !important;
    max-width: 90% !important;
    z-index: 9999 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
}

.redco-notification {
    padding: 18px 20px !important;
    border-radius: 4px !important;
    display: flex !important;
    align-items: flex-start !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
    animation: slideIn 0.3s ease-out forwards !important;
    background: #fff !important;
    border-left: 4px solid !important;
    margin-bottom: 0 !important;
}

.redco-notification.redco-notification-inline {
    margin-bottom: 25px !important;
    position: relative !important;
    top: auto !important;
    right: auto !important;
    width: 100% !important;
}

@keyframes slideIn {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(30px);
    }
}

.redco-notification-icon {
    flex-shrink: 0 !important;
    width: 24px !important;
    height: 24px !important;
    margin-right: 15px !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
}

.redco-notification-content {
    flex: 1 !important;
}

.redco-notification-title {
    font-weight: 600 !important;
    margin-bottom: 5px !important;
    font-size: 14px !important;
}

.redco-notification-message {
    font-size: 13px !important;
    line-height: 1.5 !important;
    margin: 0 !important;
}

.redco-notification-close {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    width: 16px !important;
    height: 16px !important;
    cursor: pointer !important;
    opacity: 0.5 !important;
    transition: opacity 0.2s !important;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23333"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>') !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
}

.redco-notification-close:hover {
    opacity: 1 !important;
}

.redco-notification-progress {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    height: 3px !important;
    background: rgba(0, 0, 0, 0.1) !important;
    width: 100% !important;
}

.redco-notification-progress-bar {
    height: 100% !important;
    width: 100% !important;
    transform-origin: left !important;
    animation: progress 5s linear forwards !important;
}

@keyframes progress {
    0% {
        transform: scaleX(1);
    }
    100% {
        transform: scaleX(0);
    }
}

/* Notification Types */
.redco-notification-success {
    border-color: #2ecc71 !important;
}

.redco-notification-success .redco-notification-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232ecc71"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z"/></svg>') !important;
}

.redco-notification-success .redco-notification-title {
    color: #27ae60 !important;
}

.redco-notification-success .redco-notification-progress-bar {
    background-color: #2ecc71 !important;
}

.redco-notification-error {
    border-color: #e74c3c !important;
}

.redco-notification-error .redco-notification-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e74c3c"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm4.207 12.793-1.414 1.414L12 13.414l-2.793 2.793-1.414-1.414L10.586 12 7.793 9.207l1.414-1.414L12 10.586l2.793-2.793 1.414 1.414L13.414 12l2.793 2.793z"/></svg>') !important;
}

.redco-notification-error .redco-notification-title {
    color: #c0392b !important;
}

.redco-notification-error .redco-notification-progress-bar {
    background-color: #e74c3c !important;
}

.redco-notification-warning {
    border-color: #f39c12 !important;
}

.redco-notification-warning .redco-notification-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23f39c12"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 15c-.744 0-1.35-.595-1.35-1.33s.606-1.33 1.35-1.33c.745 0 1.35.595 1.35 1.33S12.745 17 12 17zm1.5-5h-3c-.413 0-.75-.337-.75-.75v-5.5c0-.413.337-.75.75-.75h3c.413 0 .75.337.75.75v5.5c0 .413-.337.75-.75.75z"/></svg>') !important;
}

.redco-notification-warning .redco-notification-title {
    color: #d35400 !important;
}

.redco-notification-warning .redco-notification-progress-bar {
    background-color: #f39c12 !important;
}

.redco-notification-info {
    border-color: #3498db !important;
}

.redco-notification-info .redco-notification-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233498db"><path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 15c-.744 0-1.35-.595-1.35-1.33s.606-1.33 1.35-1.33c.745 0 1.35.595 1.35 1.33S12.745 17 12 17zm1.5-5h-3c-.413 0-.75-.337-.75-.75v-5.5c0-.413.337-.75.75-.75h3c.413 0 .75.337.75.75v5.5c0 .413-.337.75-.75.75z"/></svg>') !important;
}

.redco-notification-info .redco-notification-title {
    color: #2980b9 !important;
}

.redco-notification-info .redco-notification-progress-bar {
    background-color: #3498db !important;
}


