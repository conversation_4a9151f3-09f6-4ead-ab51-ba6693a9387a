/**
 * Schema Markup Generator Modal Fix
 * Ensures all sections in the Schema Markup Generator modal are visible
 */

(function($) {
    'use strict';

    // Run when document is ready
    $(document).ready(function() {
        // Add event listener for the Schema Markup Generator settings button
        $(document).on('click', '.redco-addon-card:contains("Schema Markup Generator") .redco-addon-settings', function() {
            console.log('Schema Markup Generator settings button clicked');

            // Wait for the modal to open
            setTimeout(fixSchemaMarkupGeneratorModal, 300);
            setTimeout(fixSchemaMarkupGeneratorModal, 600);
            setTimeout(fixSchemaMarkupGeneratorModal, 1200);
        });

        // Also listen for AJAX success events
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('admin-ajax.php') !== -1) {
                if (settings.data && settings.data.indexOf('schema-markup-generator') !== -1) {
                    console.log('Schema Markup Generator AJAX detected');

                    // Wait for the modal to be populated
                    setTimeout(fixSchemaMarkupGeneratorModal, 300);
                    setTimeout(fixSchemaMarkupGeneratorModal, 600);
                    setTimeout(fixSchemaMarkupGeneratorModal, 1200);
                }
            }
        });
    });

    /**
     * Fix the Schema Markup Generator modal
     */
    function fixSchemaMarkupGeneratorModal() {
        console.log('Fixing Schema Markup Generator modal');

        // Check if we're in the Schema Markup Generator modal
        if ($('.redco-modal:visible').length > 0 &&
            ($('.redco-modal-header h2:contains("Schema Markup Generator")').length > 0 ||
             $('.redco-modal-content:contains("Schema Settings")').length > 0)) {

            console.log('Schema Markup Generator modal confirmed');

            // Make the modal taller
            $('.redco-modal').css({
                'max-height': '95vh',
                'height': 'auto',
                'width': '800px',
                'max-width': '95%',
                'display': 'flex',
                'flex-direction': 'column',
                'overflow': 'hidden'
            });

            // Make the content area scrollable
            $('.redco-modal-content').css({
                'max-height': 'calc(95vh - 140px)',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
                'flex': '1 1 auto',
                'padding': '25px',
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Check for missing sections
            var expectedSections = [
                'Schema Settings',
                'Organization Schema',
                'Website Schema',
                'Article Schema'
            ];

            var missingSections = [];

            expectedSections.forEach(function(section) {
                if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                    missingSections.push(section);
                }
            });

            if (missingSections.length > 0) {
                console.log('Missing sections detected: ' + missingSections.join(', '));

                // Add a debug message to the modal
                if ($('.redco-modal-debug-message').length === 0) {
                    $('<div>')
                        .addClass('redco-modal-debug-message')
                        .html('<div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; text-align: center;">' +
                              '<p style="font-weight: bold; margin: 0;">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                              '<div style="margin-top: 10px; font-size: 24px; animation: bounce 1s infinite;">↓</div>' +
                              '</div>' +
                              '<style>@keyframes bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }</style>')
                        .prependTo('.redco-modal-content');
                }

                // Add missing sections
                injectMissingSections(missingSections);
            } else {
                console.log('All sections found');
            }

            // Force all sections to be visible
            $('.redco-modal-section').show().css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'margin-bottom': '25px !important'
            });

            // Add a scroll indicator at the bottom
            if ($('.redco-scroll-indicator-bottom').length === 0) {
                $('<div>')
                    .addClass('redco-scroll-indicator-bottom')
                    .html('<div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-top: 20px; text-align: center;">' +
                          '<p style="font-weight: bold; margin: 0;">End of settings</p>' +
                          '<div style="margin-top: 5px;">Don\'t forget to save your changes!</div>' +
                          '</div>')
                    .appendTo('.redco-modal-content');
            }
        }
    }

    /**
     * Inject missing sections into the modal
     */
    function injectMissingSections(missingSections) {
        console.log('Injecting missing sections: ' + missingSections.join(', '));

        // Get the modal content and footer
        var $modalContent = $('.redco-modal-content');
        var $footer = $('.redco-modal-footer');

        if ($modalContent.length === 0) {
            console.log('Modal content not found');
            return;
        }

        // Add each missing section
        missingSections.forEach(function(section) {
            console.log('Adding section: ' + section);

            var $section = $('<div class="redco-modal-section"></div>');
            $section.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'margin-bottom': '25px',
                'position': 'relative'
            });

            var $heading = $('<h3>' + section + '</h3>');
            $heading.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            $section.append($heading);

            // Add content based on the section
            if (section === 'Schema Settings') {
                addSchemaSettingsContent($section);
            } else if (section === 'Organization Schema') {
                addOrganizationSchemaContent($section);
            } else if (section === 'Website Schema') {
                addWebsiteSchemaContent($section);
            } else if (section === 'Article Schema') {
                addArticleSchemaContent($section);
            }

            // Add the section to the modal
            if ($footer.length > 0) {
                $footer.before($section);
            } else {
                $modalContent.append($section);
            }
        });
    }

    /**
     * Add Schema Settings content
     */
    function addSchemaSettingsContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Enable Schema Markup</h4>' +
                    '<p>Add structured data to your website.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label>' +
                        '<input type="checkbox" name="enable_schema" value="1" checked>' +
                        ' Enable' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Organization Schema</h4>' +
                    '<p>Add organization schema to your website.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label>' +
                        '<input type="checkbox" name="organization_schema" value="1" checked>' +
                        ' Enable' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Website Schema</h4>' +
                    '<p>Add website schema to your website.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label>' +
                        '<input type="checkbox" name="website_schema" value="1" checked>' +
                        ' Enable' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Article Schema</h4>' +
                    '<p>Add article schema to your posts.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label>' +
                        '<input type="checkbox" name="article_schema" value="1" checked>' +
                        ' Enable' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Organization Schema content
     */
    function addOrganizationSchemaContent($section) {
        $section.append(
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="organization_name">Organization Name</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="text" id="organization_name" name="organization_name" class="redco-input" value="">' +
                    '<p class="redco-form-help">Enter your organization name.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="organization_logo">Organization Logo URL</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="text" id="organization_logo" name="organization_logo" class="redco-input" value="">' +
                    '<p class="redco-form-help">Enter the URL of your organization logo.</p>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Website Schema content
     */
    function addWebsiteSchemaContent($section) {
        $section.append(
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="website_name">Website Name</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="text" id="website_name" name="website_name" class="redco-input" value="">' +
                    '<p class="redco-form-help">Enter your website name.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="website_description">Website Description</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<textarea id="website_description" name="website_description" class="redco-textarea" rows="3"></textarea>' +
                    '<p class="redco-form-help">Enter a description of your website.</p>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Article Schema content
     */
    function addArticleSchemaContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Enable for Posts</h4>' +
                    '<p>Add article schema to posts.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label>' +
                        '<input type="checkbox" name="article_schema_posts" value="1" checked>' +
                        ' Enable' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Enable for Pages</h4>' +
                    '<p>Add article schema to pages.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label>' +
                        '<input type="checkbox" name="article_schema_pages" value="1">' +
                        ' Enable' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

})(jQuery);
