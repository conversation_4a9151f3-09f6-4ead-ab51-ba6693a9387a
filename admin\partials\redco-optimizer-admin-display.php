<?php
/**
 * Provide a admin area view for the plugin
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */



// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

// Get site health status from WordPress core
$site_health_data = get_transient('health-check-site-status-result');
if (!$site_health_data) {
    // If transient doesn't exist, use default values but schedule a refresh
    $site_health = array(
        'score' => 0,
        'status' => 'needs_refresh',
        'issues' => 0,
    );

    // Schedule a refresh of site health data
    if (!wp_next_scheduled('redco_refresh_site_health')) {
        wp_schedule_single_event(time(), 'redco_refresh_site_health');
    }
} else {
    // Calculate score based on WordPress site health
    $site_health_score = 0;

    if (isset($site_health_data['good']) && isset($site_health_data['recommended'])) {
        $total_tests = $site_health_data['good'] + $site_health_data['recommended'];
        if ($total_tests > 0) {
            $site_health_score = ($site_health_data['good'] / $total_tests) * 100;
        }
    }

    $site_health = array(
        'score' => round($site_health_score),
        'status' => $site_health_score >= 80 ? 'good' : ($site_health_score >= 50 ? 'needs_improvement' : 'critical'),
        'issues' => isset($site_health_data['recommended']) ? $site_health_data['recommended'] : 0,
    );
}

// Get optimization stats from options
$optimization_stats = get_option('redco_optimization_stats', array(
    'cache_hits' => 0,
    'images_optimized' => 0,
    'db_size_reduced' => '0 KB',
    'total_saved' => '0 KB',
));

// Get server resources using our admin class method
global $redco_optimizer_admin;
$server_resources = $redco_optimizer_admin->get_server_resources();

// Get recent optimizations from logs
$recent_optimizations = get_option('redco_recent_optimizations', array());
if (empty($recent_optimizations)) {
    // Add initial log entry if no logs exist
    $recent_optimizations = array(
        array(
            'type' => 'system',
            'message' => __('Redco Optimizer activated', 'redco-optimizer'),
            'time' => __('just now', 'redco-optimizer'),
            'timestamp' => current_time('timestamp'),
        ),
    );

    // Save this initial log
    update_option('redco_recent_optimizations', $recent_optimizations);
}

// Get optimization tips based on site analysis
$optimization_tips = array();

// Add a default tip to ensure the section is not empty
$optimization_tips[] = array(
    'title' => __('Enable Browser Caching', 'redco-optimizer'),
    'description' => __('Browser caching stores website resources locally in the user\'s browser, reducing load times for returning visitors.', 'redco-optimizer'),
    'priority' => 'high',
);

// Add another default tip
$optimization_tips[] = array(
    'title' => __('Optimize Images', 'redco-optimizer'),
    'description' => __('Optimize your images to reduce their file size without losing quality, improving page load times.', 'redco-optimizer'),
    'priority' => 'medium',
);

// Check if browser caching is enabled
$caching_settings = $redco_optimizer_admin->get_settings('caching');

// Check if image optimization is enabled
$media_settings = $redco_optimizer_admin->get_settings('media');
if (!isset($media_settings['enable_image_optimization']) || !$media_settings['enable_image_optimization']) {
    $optimization_tips[] = array(
        'title' => __('Enable Image Optimization', 'redco-optimizer'),
        'description' => __('Optimize your images to reduce their file size without losing quality, improving page load times.', 'redco-optimizer'),
        'priority' => 'medium',
    );
}

// Check if lazy loading is enabled
if (!isset($media_settings['lazy_load_images']) || !$media_settings['lazy_load_images']) {
    $optimization_tips[] = array(
        'title' => __('Enable Lazy Loading', 'redco-optimizer'),
        'description' => __('Lazy loading defers the loading of images until they are needed, improving initial page load times.', 'redco-optimizer'),
        'priority' => 'medium',
    );
}

// Check database cleanup settings
$db_settings = $redco_optimizer_admin->get_settings('database');
$db_cleanup_enabled = false;
foreach ($db_settings as $key => $value) {
    if (strpos($key, 'clean_') === 0 && $value) {
        $db_cleanup_enabled = true;
        break;
    }
}
if (!$db_cleanup_enabled) {
    $optimization_tips[] = array(
        'title' => __('Enable Database Cleanup', 'redco-optimizer'),
        'description' => __('Regular database cleanup removes unnecessary data and optimizes your database tables, improving site performance.', 'redco-optimizer'),
        'priority' => 'medium',
    );
}

// Check if site health inspector is enabled
$modules = $redco_optimizer_admin->get_active_modules();
if (!in_array('site-health-inspector', $modules)) {
    $optimization_tips[] = array(
        'title' => __('Enable Site Health Inspector', 'redco-optimizer'),
        'description' => __('The Site Health Inspector module helps identify and fix issues with your WordPress site, improving performance and security.', 'redco-optimizer'),
        'priority' => 'high',
    );
}

// Add more tips based on site analysis
$file_settings = $redco_optimizer_admin->get_settings('file-optimization');
if (!isset($file_settings['minify_css']) || !$file_settings['minify_css']) {
    $optimization_tips[] = array(
        'title' => __('Minify CSS Files', 'redco-optimizer'),
        'description' => __('Minifying CSS files reduces their size and improves loading times.', 'redco-optimizer'),
        'priority' => 'medium',
    );
}

if (!isset($file_settings['minify_js']) || !$file_settings['minify_js']) {
    $optimization_tips[] = array(
        'title' => __('Minify JavaScript Files', 'redco-optimizer'),
        'description' => __('Minifying JavaScript files reduces their size and improves loading times.', 'redco-optimizer'),
        'priority' => 'medium',
    );
}
?>

<script>
// Immediate script to ensure header icon is visible
document.addEventListener('DOMContentLoaded', function() {
    // Function to ensure header icon visibility
    function fixHeaderIcon() {
        var headerIcon = document.getElementById('redco-header-icon');
        var headerIconInner = document.getElementById('redco-header-icon-inner');

        if (headerIcon) {
            headerIcon.style.display = 'flex';
            headerIcon.style.visibility = 'visible';
            headerIcon.style.opacity = '1';
            headerIcon.style.zIndex = '9999';
        }

        if (headerIconInner) {
            headerIconInner.style.display = 'inline-block';
            headerIconInner.style.visibility = 'visible';
            headerIconInner.style.opacity = '1';
            headerIconInner.style.zIndex = '9999';
        }
    }

    // Run header icon fix
    fixHeaderIcon();

    // Also run after a short delay
    setTimeout(function() {
        fixHeaderIcon();
    }, 100);

    // Protect the plugin notices container from being hidden by tab switching
    function protectNoticesContainer() {
        var noticesContainer = document.querySelector('.redco-plugin-notices-container');
        if (noticesContainer) {
            noticesContainer.style.display = 'block';
            noticesContainer.style.visibility = 'visible';
            noticesContainer.style.opacity = '1';
            noticesContainer.style.position = 'relative';
            noticesContainer.style.zIndex = '1000';
        }
    }

    // Run protection function initially
    protectNoticesContainer();

    // Run protection function periodically to ensure notices stay visible
    setInterval(protectNoticesContainer, 500);

    // Also run protection when any tab switching occurs
    document.addEventListener('click', function(e) {
        if (e.target.closest('.redco-nav-item')) {
            setTimeout(protectNoticesContainer, 100);
            setTimeout(protectNoticesContainer, 300);
            setTimeout(protectNoticesContainer, 500);
        }
    });
});
</script>

<style>
/* Standard checkbox styling - no special toggle styles needed */

/* Plugin notices container - ensure it stays visible and separate from tab content */
.redco-plugin-notices-container {
    position: relative !important;
    z-index: 1000 !important;
    margin: 0 0 20px 0 !important;
    padding: 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure notices inside the container are visible */
.redco-plugin-notices-container .notice,
.redco-plugin-notices-container .redco-notice {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Collapsible sections for File Optimization */
.redco-collapsible-section {
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    margin-bottom: 15px;
    overflow: hidden;
}

.redco-collapsible-header {
    background-color: #f8f9fa;
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e4e7;
}

.redco-collapsible-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.redco-collapsible-header .dashicons {
    transition: transform 0.3s ease;
}

.redco-collapsible-header.active .dashicons {
    transform: rotate(180deg);
}

.redco-collapsible-content {
    padding: 15px;
    display: none;
}

.redco-collapsible-content.active {
    display: block;
}

.redco-section-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #23282d;
}
</style>

<div class="wrap">
    <!-- WordPress standard page header -->
    <h1><?php esc_html_e('Redco Optimizer', 'redco-optimizer'); ?></h1>

    <!-- Site Health Inspector notice - only show on dashboard (no tab parameter) -->
    <?php if (!isset($_GET['tab']) || empty($_GET['tab'])): ?>
    <div class="redco-plugin-notices-container" style="position: relative; z-index: 1000; margin-bottom: 20px;">
        <?php
        // Display plugin notices at the top of the plugin page when on dashboard
        do_action('redco_optimizer_plugin_notices');
        ?>
    </div>
    <?php endif; ?>

    <!-- Global notification area for AJAX responses -->
    <div id="redco-global-notification-area" style="margin-bottom: 20px;"></div>

    <div class="redco-layout">
        <div class="redco-sidebar">
            <div class="redco-logo">
                <div class="redco-logo-text">
                    <span class="redco-logo-part-1">Redco</span>
                    <span class="redco-logo-part-2">Optimizer</span>
                    <span class="redco-version"><?php echo esc_html('v' . REDCO_OPTIMIZER_VERSION); ?></span>
                </div>
            </div>

            <ul class="redco-nav">
                <li class="redco-nav-item active" data-tab="redco-dashboard-tab">
                    <div class="redco-nav-text"><?php esc_html_e('Dashboard', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-description"><?php esc_html_e('Performance overview', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-content">
                        <span class="redco-nav-icon dashicons dashicons-dashboard"></span>
                    </div>
                </li>

                <?php
                // Get all modules - force a fresh read from the database
                wp_cache_delete('redco_optimizer_modules', 'options');
                $modules = get_option('redco_optimizer_modules', array());

                // Define module to tab mapping with descriptions
                $module_tabs = array(
                    'caching' => array(
                        'tab' => 'redco-caching-tab',
                        'icon' => 'dashicons-superhero',
                        'text' => __('Caching', 'redco-optimizer'),
                        'description' => __('Speed up page loading', 'redco-optimizer')
                    ),
                    'file-optimization' => array(
                        'tab' => 'redco-file-optimization-tab',
                        'icon' => 'dashicons-media-code',
                        'text' => __('File Optimization', 'redco-optimizer'),
                        'description' => __('Minify & combine assets', 'redco-optimizer')
                    ),
                    'media' => array(
                        'tab' => 'redco-media-tab',
                        'icon' => 'dashicons-format-image',
                        'text' => __('Media', 'redco-optimizer'),
                        'description' => __('Optimize images & lazy load', 'redco-optimizer')
                    ),
                    'preload' => array(
                        'tab' => 'redco-preload-tab',
                        'icon' => 'dashicons-performance',
                        'text' => __('Preload', 'redco-optimizer'),
                        'description' => __('Boost initial page speed', 'redco-optimizer')
                    ),
                    'database' => array(
                        'tab' => 'redco-database-tab',
                        'icon' => 'dashicons-database',
                        'text' => __('Database', 'redco-optimizer'),
                        'description' => __('Clean & optimize DB', 'redco-optimizer')
                    ),
                    'cdn' => array(
                        'tab' => 'redco-cdn-tab',
                        'icon' => 'dashicons-admin-site',
                        'text' => __('CDN', 'redco-optimizer'),
                        'description' => __('Global content delivery', 'redco-optimizer')
                    ),
                    'heartbeat' => array(
                        'tab' => 'redco-heartbeat-tab',
                        'icon' => 'dashicons-heart',
                        'text' => __('Heartbeat', 'redco-optimizer'),
                        'description' => __('Reduce admin CPU usage', 'redco-optimizer')
                    ),
                    'site-health-inspector' => array(
                        'tab' => 'redco-site-health-inspector-tab',
                        'icon' => 'dashicons-shield',
                        'text' => __('Site Health', 'redco-optimizer'),
                        'description' => __('Fix performance issues', 'redco-optimizer')
                    ),
                );

                // Display tabs for all modules, but add a class to hide disabled ones
                foreach ($module_tabs as $module_id => $tab_info) {
                    // Skip if module doesn't exist
                    if (!isset($modules[$module_id])) {
                        continue;
                    }

                    // Check if module is enabled
                    $is_enabled = isset($modules[$module_id]['enabled']) && $modules[$module_id]['enabled'];

                    // Always create all tabs, but add a class to hide disabled ones
                    // We'll use CSS to control visibility
                    $tab_class = $is_enabled ? '' : 'redco-module-tab-disabled';
                    // Use inline style for immediate hiding
                    $tab_style = $is_enabled ? '' : 'style="display:none;"';
                    ?>
                    <li class="redco-nav-item <?php echo $tab_class; ?>" <?php echo $tab_style; ?> data-tab="<?php echo esc_attr($tab_info['tab']); ?>" data-module="<?php echo esc_attr($module_id); ?>">
                        <div class="redco-nav-text"><?php echo esc_html($tab_info['text']); ?></div>
                        <div class="redco-nav-description"><?php echo esc_html($tab_info['description']); ?></div>
                        <div class="redco-nav-content">
                            <span class="redco-nav-icon dashicons <?php echo esc_attr($tab_info['icon']); ?>"></span>
                        </div>
                    </li>
                    <?php
                }
                ?>

                <li class="redco-nav-item" data-tab="redco-modules-tab">
                    <div class="redco-nav-text"><?php esc_html_e('Modules', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-description"><?php esc_html_e('Enable/disable features', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-content">
                        <span class="redco-nav-icon dashicons dashicons-admin-plugins"></span>
                    </div>
                </li>

                <li class="redco-nav-item" data-tab="redco-tools-tab">
                    <div class="redco-nav-text"><?php esc_html_e('Tools', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-description"><?php esc_html_e('Backup & restore settings', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-content">
                        <span class="redco-nav-icon dashicons dashicons-admin-tools"></span>
                    </div>
                </li>

                <li class="redco-nav-item" data-tab="redco-addons-tab">
                    <div class="redco-nav-text"><?php esc_html_e('Add-Ons', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-description"><?php esc_html_e('Enhance optimization', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-content">
                        <span class="redco-nav-icon dashicons dashicons-admin-plugins"></span>
                    </div>
                </li>

                <li class="redco-nav-item" data-tab="redco-help-tab" data-url="<?php echo esc_url(admin_url('admin.php?page=redco-optimizer-help')); ?>">
                    <div class="redco-nav-text"><?php esc_html_e('Help', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-description"><?php esc_html_e('Documentation & support', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-content">
                        <span class="redco-nav-icon dashicons dashicons-editor-help"></span>
                    </div>
                </li>

                <li class="redco-nav-item" data-tab="redco-coming-soon-tab">
                    <div class="redco-nav-text"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-description"><?php esc_html_e('Exciting new features', 'redco-optimizer'); ?></div>
                    <div class="redco-nav-content">
                        <span class="redco-nav-icon dashicons dashicons-clock"></span>
                    </div>
                </li>
            </ul>

            <div class="redco-sidebar-footer">
                <a href="#" class="redco-upgrade-button" data-tab="redco-coming-soon-tab">
                    <?php esc_html_e('View Coming Soon', 'redco-optimizer'); ?>
                </a>
            </div>
        </div>

        <div class="redco-main-content">

            <div class="redco-page-header">
                <div class="redco-page-header-left">
                    <div class="redco-page-header-icon" id="redco-header-icon" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                        <span class="dashicons dashicons-dashboard" id="redco-header-icon-inner" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important;"></span>
                    </div>
                    <div class="redco-page-header-content">
                        <h1><?php esc_html_e('Dashboard', 'redco-optimizer'); ?></h1>
                        <p class="redco-page-header-description"><?php esc_html_e('Performance overview', 'redco-optimizer'); ?></p>
                    </div>
                </div>
                <div class="redco-page-header-right">
                    <a href="<?php echo esc_url(admin_url('admin.php?page=redco-optimizer-help')); ?>" class="redco-header-action-button redco-help-direct-link" id="redco-help-header-button" target="_blank" style="background-color: #00A66B; color: white; margin-right: 10px; text-decoration: none;">
                        <span class="dashicons dashicons-editor-help"></span>
                        <?php esc_html_e('Help', 'redco-optimizer'); ?>
                    </a>
                    <a href="#" class="redco-header-action-button" data-tab="redco-coming-soon-tab">
                        <span class="dashicons dashicons-clock"></span>
                        <?php esc_html_e('Coming Soon', 'redco-optimizer'); ?>
                    </a>
                </div>
            </div>

            <div id="redco-dashboard-tab" class="redco-tab-content">
                <?php
                // Get settings for all tabs
                global $redco_optimizer_admin;
                $caching_settings = $redco_optimizer_admin->get_settings('caching');
                $file_settings = $redco_optimizer_admin->get_settings('file-optimization');
                $media_settings = $redco_optimizer_admin->get_settings('media');
                $preload_settings = $redco_optimizer_admin->get_settings('preload');
                $db_settings = $redco_optimizer_admin->get_settings('database');
                ?>
                <div class="redco-success-message">
                    <span class="redco-success-icon dashicons dashicons-yes-alt"></span>
                    <div class="redco-success-content">
                        <h2><?php esc_html_e('Congratulations!', 'redco-optimizer'); ?></h2>
                        <p><?php esc_html_e('Redco Optimizer is now activated and already working for you. Your website should be loading faster now!', 'redco-optimizer'); ?></p>
                        <p class="redco-success-details"><?php esc_html_e('To guarantee fast websites, Redco Optimizer automatically applies 80% of web performance best practices. We also enable options that provide immediate benefits to your website.', 'redco-optimizer'); ?></p>
                        <p class="redco-success-cta"><?php esc_html_e('Continue to the options to further optimize your site!', 'redco-optimizer'); ?></p>
                    </div>
                    <button class="redco-close-message dashicons dashicons-no-alt"></button>
                </div>

                <div class="redco-account-section">
                    <div class="redco-section-title">
                        <h2><?php esc_html_e('My Account', 'redco-optimizer'); ?></h2>
                        <div class="redco-refresh-info">
                            <span class="dashicons dashicons-update"></span>
                            <?php esc_html_e('Refresh Info', 'redco-optimizer'); ?>
                        </div>
                    </div>

                    <div class="redco-account-info">
                        <div class="redco-info-row">
                            <div class="redco-info-label"><?php esc_html_e('License', 'redco-optimizer'); ?></div>
                            <div class="redco-info-value redco-license-free"><?php esc_html_e('Free', 'redco-optimizer'); ?></div>
                        </div>

                        <div class="redco-view-account">
                            <a href="<?php echo esc_url(admin_url('admin.php?page=redco-optimizer-premium')); ?>" class="redco-button redco-button-primary">
                                <span class="dashicons dashicons-admin-users"></span>
                                <?php esc_html_e('View My Account', 'redco-optimizer'); ?>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="redco-dashboard-columns">
                    <div class="redco-column-main">
                        <div class="redco-card redco-quick-actions-card">
                            <div class="redco-card-header">
                                <h3><?php esc_html_e('Quick Actions', 'redco-optimizer'); ?></h3>
                            </div>
                            <div class="redco-card-content">
                                <?php
                                // Check if caching module is enabled
                                $caching_module_enabled = isset($modules['caching']) && $modules['caching']['enabled'];
                                ?>
                                <div class="redco-action-item">
                                    <div class="redco-action-info">
                                        <h4><?php esc_html_e('Cache files', 'redco-optimizer'); ?></h4>
                                        <p><?php esc_html_e('This action will clear all the cache files.', 'redco-optimizer'); ?></p>
                                    </div>
                                    <div class="redco-action-button">
                                        <button class="redco-button redco-button-secondary redco-clear-cache" <?php echo !$caching_module_enabled ? 'disabled' : ''; ?>>
                                            <span class="dashicons dashicons-trash"></span>
                                            <?php esc_html_e('Clear', 'redco-optimizer'); ?>
                                        </button>
                                    </div>
                                </div>

                                <?php
                                // Check if media module is enabled
                                $media_module_enabled = isset($modules['media']) && $modules['media']['enabled'];
                                ?>
                                <div class="redco-action-item">
                                    <div class="redco-action-info">
                                        <h4><?php esc_html_e('Optimize Images', 'redco-optimizer'); ?></h4>
                                        <p><?php esc_html_e('Compress all unoptimized images.', 'redco-optimizer'); ?></p>
                                    </div>
                                    <div class="redco-action-button">
                                        <button class="redco-button redco-button-secondary redco-optimize-images" <?php echo !$media_module_enabled ? 'disabled' : ''; ?>>
                                            <span class="dashicons dashicons-format-image"></span>
                                            <?php esc_html_e('Optimize', 'redco-optimizer'); ?>
                                        </button>
                                    </div>
                                </div>

                                <?php
                                // Check if database module is enabled
                                $database_module_enabled = isset($modules['database']) && $modules['database']['enabled'];
                                ?>
                                <div class="redco-action-item">
                                    <div class="redco-action-info">
                                        <h4><?php esc_html_e('Database Cleanup', 'redco-optimizer'); ?></h4>
                                        <p><?php esc_html_e('Clean up your database to improve performance.', 'redco-optimizer'); ?></p>
                                    </div>
                                    <div class="redco-action-button">
                                        <button class="redco-button redco-button-secondary redco-clean-database" <?php echo !$database_module_enabled ? 'disabled' : ''; ?>>
                                            <span class="dashicons dashicons-database"></span>
                                            <?php esc_html_e('Clean', 'redco-optimizer'); ?>
                                        </button>
                                    </div>
                                </div>

                                <?php
                                // Check if file-optimization module is enabled
                                $file_optimization_enabled = isset($modules['file-optimization']) && $modules['file-optimization']['enabled'];

                                if ($file_optimization_enabled && isset($file_settings['remove_unused_css']) && $file_settings['remove_unused_css']) :
                                ?>
                                <div class="redco-action-item">
                                    <div class="redco-action-info">
                                        <h4><?php esc_html_e('Used CSS Cache', 'redco-optimizer'); ?></h4>
                                        <p><?php esc_html_e('Clear the used CSS cache to regenerate optimized CSS.', 'redco-optimizer'); ?></p>
                                    </div>
                                    <div class="redco-action-button">
                                        <button class="redco-button redco-button-secondary redco-clear-used-css">
                                            <span class="dashicons dashicons-editor-code"></span>
                                            <?php esc_html_e('Clear', 'redco-optimizer'); ?>
                                        </button>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php
                                if ($file_optimization_enabled && isset($file_settings['optimize_google_fonts']) && $file_settings['optimize_google_fonts'] &&
                                    isset($file_settings['self_host_google_fonts']) && $file_settings['self_host_google_fonts']) :
                                ?>
                                <div class="redco-action-item">
                                    <div class="redco-action-info">
                                        <h4><?php esc_html_e('Google Fonts Cache', 'redco-optimizer'); ?></h4>
                                        <p><?php esc_html_e('Clear the Google Fonts cache to download fresh font files.', 'redco-optimizer'); ?></p>
                                    </div>
                                    <div class="redco-action-button">
                                        <button class="redco-button redco-button-secondary redco-clear-fonts-cache">
                                            <span class="dashicons dashicons-editor-textcolor"></span>
                                            <?php esc_html_e('Clear', 'redco-optimizer'); ?>
                                        </button>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="redco-card">
                            <div class="redco-card-header">
                                <h3><?php esc_html_e('Bulk Optimization', 'redco-optimizer'); ?></h3>
                            </div>
                            <div class="redco-card-content">
                                <p><?php esc_html_e('Optimize all images in your media library at once.', 'redco-optimizer'); ?></p>

                                <div class="redco-stats-row">
                                    <?php
                                    // Get real image counts from the media library
                                    $image_counts = $redco_optimizer_admin->get_image_optimization_stats();
                                    $total_images = $image_counts['total'];
                                    $optimized_images = $image_counts['optimized'];
                                    $unoptimized_images = $image_counts['unoptimized'];

                                    // Calculate optimization percentage
                                    $optimization_percentage = $total_images > 0 ? round(($optimized_images / $total_images) * 100) : 0;

                                    // Calculate saved space
                                    $saved_per_image = 100 * 1024; // Assume average 100KB saved per image
                                    $total_saved = $redco_optimizer_admin->format_file_size($optimized_images * $saved_per_image);
                                    ?>
                                    <div class="redco-stat">
                                        <span class="redco-stat-value"><?php echo esc_html($total_images); ?></span>
                                        <span class="redco-stat-label"><?php esc_html_e('Total Images', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="redco-stat">
                                        <span class="redco-stat-value"><?php echo esc_html($optimized_images); ?></span>
                                        <span class="redco-stat-label"><?php esc_html_e('Optimized', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="redco-stat">
                                        <span class="redco-stat-value"><?php echo esc_html($unoptimized_images); ?></span>
                                        <span class="redco-stat-label"><?php esc_html_e('Unoptimized', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="redco-stat">
                                        <span class="redco-stat-value"><?php echo esc_html($total_saved); ?></span>
                                        <span class="redco-stat-label"><?php esc_html_e('Space Saved', 'redco-optimizer'); ?></span>
                                    </div>
                                </div>

                                <button class="redco-button redco-button-primary redco-optimize-images" <?php echo !$media_module_enabled ? 'disabled' : ''; ?>><?php esc_html_e('Optimize All Images', 'redco-optimizer'); ?></button>
                            </div>
                        </div>

                        <div class="redco-card redco-optimization-status">
                            <div class="redco-card-header">
                                <h3><?php esc_html_e('Optimization Status', 'redco-optimizer'); ?></h3>
                            </div>
                            <div class="redco-card-content">
                                <div class="redco-status-grid">
                                    <?php
                                    // Get all modules - force a fresh read from the database
                                    wp_cache_delete('redco_optimizer_modules', 'options');
                                    $modules = get_option('redco_optimizer_modules', array());

                                    // Get lazyload settings
                                    $lazyload_settings = $redco_optimizer_admin->get_settings('lazyload');

                                    // Define module features to display in the dashboard
                                    $module_features = array(
                                        'caching' => array(
                                            array(
                                                'name' => 'Page Caching',
                                                'enabled' => $caching_settings['enable_page_caching'] ?? true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Browser Caching',
                                                'enabled' => $caching_settings['enable_browser_caching'] ?? true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Mobile Caching',
                                                'enabled' => $caching_settings['enable_mobile_caching'] ?? true,
                                                'premium' => true
                                            ),
                                        ),
                                        'file-optimization' => array(
                                            array(
                                                'name' => 'HTML Minification',
                                                'enabled' => $file_settings['minify_html'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'CSS Minification',
                                                'enabled' => $file_settings['minify_css'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'JavaScript Minification',
                                                'enabled' => $file_settings['minify_js'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'JavaScript Defer',
                                                'enabled' => $file_settings['defer_js'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'CSS File Combination',
                                                'enabled' => $file_settings['combine_css'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'CSS Delivery Optimization',
                                                'enabled' => $file_settings['optimize_css_delivery'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'JS File Combination',
                                                'enabled' => $file_settings['combine_js'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Remove Unused CSS',
                                                'enabled' => $file_settings['remove_unused_css'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Delay JavaScript',
                                                'enabled' => $file_settings['delay_js'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Async CSS',
                                                'enabled' => $file_settings['async_css'] ?? false,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Google Fonts Optimization',
                                                'enabled' => $file_settings['optimize_google_fonts'] ?? false,
                                                'premium' => false
                                            ),
                                        ),
                                        'media' => array(
                                            array(
                                                'name' => 'Image Compression',
                                                'enabled' => $media_settings['enable_image_optimization'] ?? true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Lazy Loading Images',
                                                'enabled' => $media_settings['lazy_load_images'] ?? true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'WebP Conversion',
                                                'enabled' => $media_settings['webp_conversion'] ?? false,
                                                'premium' => true
                                            ),
                                        ),

                                        'database' => array(
                                            array(
                                                'name' => 'Database Cleanup',
                                                'enabled' => true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Table Optimization',
                                                'enabled' => true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Scheduled Cleanup',
                                                'enabled' => $db_settings['schedule_cleanup'] ?? false,
                                                'premium' => true
                                            ),
                                        ),
                                        'preload' => array(
                                            array(
                                                'name' => 'Preload Cache',
                                                'enabled' => $preload_settings['preload_cache'] ?? true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Prefetch DNS',
                                                'enabled' => $preload_settings['prefetch_dns'] ?? false,
                                                'premium' => true
                                            ),
                                            array(
                                                'name' => 'Preload Fonts',
                                                'enabled' => $preload_settings['preload_fonts'] ?? false,
                                                'premium' => true
                                            ),
                                            array(
                                                'name' => 'Preload Links',
                                                'enabled' => $preload_settings['preload_links'] ?? false,
                                                'premium' => true
                                            ),
                                        ),

                                        'heartbeat' => array(
                                            array(
                                                'name' => 'Control Heartbeat',
                                                'enabled' => true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Reduce Backend Activity',
                                                'enabled' => true,
                                                'premium' => true
                                            ),
                                        ),
                                        'cdn' => array(
                                            array(
                                                'name' => 'CDN Integration',
                                                'enabled' => false,
                                                'premium' => true
                                            ),
                                        ),
                                        'site-health-inspector' => array(
                                            array(
                                                'name' => 'Site Health Scanning',
                                                'enabled' => true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Security Checks',
                                                'enabled' => true,
                                                'premium' => false
                                            ),
                                            array(
                                                'name' => 'Performance Analysis',
                                                'enabled' => true,
                                                'premium' => false
                                            ),
                                        ),
                                    );

                                    // Display features for enabled modules
                                    foreach ($module_features as $module_id => $features) {
                                        // Skip if module doesn't exist or is disabled
                                        if (!isset($modules[$module_id]) || !$modules[$module_id]['enabled']) {
                                            continue;
                                        }

                                        // Display features for this module
                                        foreach ($features as $feature) {
                                            ?>
                                            <div class="redco-status-item">
                                                <?php if ($feature['premium'] && !$redco_optimizer_admin->has_premium_access()) : ?>
                                                    <div class="redco-status-icon premium">
                                                        <span class="dashicons dashicons-lock"></span>
                                                    </div>
                                                    <div class="redco-status-info">
                                                        <h4><?php echo esc_html($feature['name']); ?></h4>
                                                        <p><?php esc_html_e('Premium Feature', 'redco-optimizer'); ?></p>
                                                    </div>
                                                <?php elseif ($feature['enabled']) : ?>
                                                    <div class="redco-status-icon active">
                                                        <span class="dashicons dashicons-yes-alt"></span>
                                                    </div>
                                                    <div class="redco-status-info">
                                                        <h4><?php echo esc_html($feature['name']); ?></h4>
                                                        <p><?php esc_html_e('Enabled', 'redco-optimizer'); ?></p>
                                                    </div>
                                                <?php else : ?>
                                                    <div class="redco-status-icon inactive">
                                                        <span class="dashicons dashicons-no-alt"></span>
                                                    </div>
                                                    <div class="redco-status-info">
                                                        <h4><?php echo esc_html($feature['name']); ?></h4>
                                                        <p><?php esc_html_e('Disabled', 'redco-optimizer'); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="redco-column-side">
                        <div class="redco-card redco-performance-card">
                            <div class="redco-card-header">
                                <h3><?php esc_html_e('Performance Score', 'redco-optimizer'); ?></h3>
                            </div>
                            <div class="redco-card-content">
                                <?php
                                // Get site health data from WordPress
                                $site_health_data = get_transient('health-check-site-status-result');

                                if (!$site_health_data) {
                                    // If no data exists, refresh it
                                    $redco_optimizer_admin->refresh_site_health();
                                    $site_health_data = get_transient('health-check-site-status-result');
                                }

                                // Calculate score
                                $site_health_score = 0;
                                if ($site_health_data && is_array($site_health_data) &&
                                    isset($site_health_data['good']) && isset($site_health_data['recommended']) && isset($site_health_data['critical'])) {
                                    $total_tests = $site_health_data['good'] + $site_health_data['recommended'] + $site_health_data['critical'];
                                    if ($total_tests > 0) {
                                        $site_health_score = ($site_health_data['good'] / $total_tests) * 100;
                                    }
                                } else {
                                    // Fallback to default values if data is not available or not in expected format
                                    $site_health_score = 70; // Default score
                                }

                                // Round to nearest whole number
                                $site_health_score = round($site_health_score);

                                // Determine score color class
                                $score_class = 'poor';
                                if ($site_health_score >= 80) {
                                    $score_class = 'good';
                                } elseif ($site_health_score >= 50) {
                                    $score_class = 'ok';
                                }
                                ?>
                                <div class="redco-score-circle redco-score-<?php echo esc_attr($score_class); ?>">
                                    <svg viewBox="0 0 36 36" class="redco-score-svg">
                                        <path class="redco-score-circle-bg"
                                            d="M18 2.0845
                                            a 15.9155 15.9155 0 0 1 0 31.831
                                            a 15.9155 15.9155 0 0 1 0 -31.831"
                                        />
                                        <path class="redco-score-circle-fill"
                                            stroke-dasharray="<?php echo esc_attr($site_health_score); ?>, 100"
                                            d="M18 2.0845
                                            a 15.9155 15.9155 0 0 1 0 31.831
                                            a 15.9155 15.9155 0 0 1 0 -31.831"
                                            transform="rotate(-90, 18, 18)"
                                        />
                                    </svg>
                                    <div class="redco-score-text-container">
                                        <span class="redco-score-text"><?php echo esc_html($site_health_score); ?></span>
                                        <span class="redco-score-percentage">%</span>
                                    </div>
                                </div>
                                <div class="redco-score-info">
                                    <?php if ($site_health_score >= 80) : ?>
                                        <p><?php esc_html_e('Your site is performing well! There are still some optimizations you can make to improve your score.', 'redco-optimizer'); ?></p>
                                    <?php elseif ($site_health_score >= 50) : ?>
                                        <p><?php esc_html_e('Your site needs some improvements. Check the optimization tips below.', 'redco-optimizer'); ?></p>
                                    <?php else : ?>
                                        <p><?php esc_html_e('Your site needs significant improvements. Follow the optimization tips below to improve performance.', 'redco-optimizer'); ?></p>
                                    <?php endif; ?>
                                    <a href="<?php echo esc_url(admin_url('site-health.php')); ?>" class="redco-text-link"><?php esc_html_e('View detailed report', 'redco-optimizer'); ?></a>
                                </div>
                            </div>
                        </div>

                        <div class="redco-card redco-tips-card">
                            <div class="redco-card-header">
                                <h3><?php esc_html_e('Optimization Tips', 'redco-optimizer'); ?></h3>
                            </div>
                            <div class="redco-card-content">
                                <div class="redco-tips-list">
                                    <?php foreach ($optimization_tips as $tip) : ?>
                                        <div class="redco-tip-item priority-<?php echo esc_attr($tip['priority']); ?>">
                                            <div class="redco-tip-header">
                                                <div class="redco-tip-icon">
                                                    <span class="dashicons dashicons-lightbulb"></span>
                                                </div>
                                                <div class="redco-tip-title"><?php echo esc_html($tip['title']); ?></div>
                                            </div>
                                            <div class="redco-tip-content">
                                                <p><?php echo esc_html($tip['description']); ?></p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <div class="redco-card redco-help-card">
                            <div class="redco-card-header">
                                <h3><?php esc_html_e('Need Help?', 'redco-optimizer'); ?></h3>
                            </div>
                            <div class="redco-card-content">
                                <p><?php esc_html_e('Check our documentation for help with setting up and using Redco Optimizer.', 'redco-optimizer'); ?></p>
                                <a href="#" data-tab="redco-help-tab" class="redco-button redco-button-secondary redco-help-button">
                                    <span class="dashicons dashicons-book"></span>
                                    <?php esc_html_e('Documentation', 'redco-optimizer'); ?>
                                </a>
                            </div>
                        </div>

                        <div class="redco-card">
                            <div class="redco-card-header">
                                <h3><?php esc_html_e('Frequently Asked Questions', 'redco-optimizer'); ?></h3>
                            </div>
                            <div class="redco-card-content">
                                <div class="redco-faq-list">
                                    <div class="redco-faq-item">
                                        <div class="redco-faq-question">
                                            <?php esc_html_e('What is page caching and how does it work?', 'redco-optimizer'); ?>
                                            <span class="redco-faq-toggle dashicons dashicons-arrow-down-alt2"></span>
                                        </div>
                                        <div class="redco-faq-answer">
                                            <p><?php esc_html_e('Page caching creates static HTML copies of your dynamic WordPress pages. When a visitor requests a page, the plugin serves the cached copy instead of processing the PHP scripts and database queries each time. This significantly reduces server load and improves page load times.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('Redco Optimizer automatically manages the cache, creating new cached versions when content changes and clearing outdated cache files. You can also manually clear the cache at any time using the "Clear Cache" button.', 'redco-optimizer'); ?></p>
                                        </div>
                                    </div>

                                    <div class="redco-faq-item">
                                        <div class="redco-faq-question">
                                            <?php esc_html_e('Will optimization break my site?', 'redco-optimizer'); ?>
                                            <span class="redco-faq-toggle dashicons dashicons-arrow-down-alt2"></span>
                                        </div>
                                        <div class="redco-faq-answer">
                                            <p><?php esc_html_e('Redco Optimizer is designed to be safe and compatible with most WordPress themes and plugins. However, in some rare cases, certain optimization features might conflict with specific themes or plugins.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('If you notice any issues after enabling an optimization feature, you can easily disable it. We recommend enabling one feature at a time and testing your site after each change to identify any potential conflicts.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('The plugin also includes exclusion options for each optimization feature, allowing you to exclude specific files or pages that might be causing issues.', 'redco-optimizer'); ?></p>
                                        </div>
                                    </div>

                                    <div class="redco-faq-item">
                                        <div class="redco-faq-question">
                                            <?php esc_html_e('How do I know if the optimizations are working?', 'redco-optimizer'); ?>
                                            <span class="redco-faq-toggle dashicons dashicons-arrow-down-alt2"></span>
                                        </div>
                                        <div class="redco-faq-answer">
                                            <p><?php esc_html_e('You can verify that the optimizations are working by:', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('1. Checking your site\'s performance score using tools like Google PageSpeed Insights, GTmetrix, or Pingdom.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('2. Monitoring the Performance Score in the Redco Optimizer dashboard, which shows your site\'s health and performance metrics.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('3. Observing faster page load times when browsing your site.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('4. Looking for specific optimization indicators in your page source, such as minified CSS/JS, deferred JavaScript, or lazy-loaded images.', 'redco-optimizer'); ?></p>
                                        </div>
                                    </div>

                                    <div class="redco-faq-item">
                                        <div class="redco-faq-question">
                                            <?php esc_html_e('What\'s the difference between the free and premium versions?', 'redco-optimizer'); ?>
                                            <span class="redco-faq-toggle dashicons dashicons-arrow-down-alt2"></span>
                                        </div>
                                        <div class="redco-faq-answer">
                                            <p><?php esc_html_e('The free version of Redco Optimizer includes essential optimization features like page caching, basic file optimization, and image optimization.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('The premium version adds advanced features such as:', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('- CDN integration for global content delivery', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('- Advanced lazy loading features', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('- Advanced font optimization', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('- Scheduled database cleanup', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('- Priority support', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('Premium users also get access to regular updates with new features and improvements.', 'redco-optimizer'); ?></p>
                                        </div>
                                    </div>

                                    <div class="redco-faq-item">
                                        <div class="redco-faq-question">
                                            <?php esc_html_e('How do I clear the cache?', 'redco-optimizer'); ?>
                                            <span class="redco-faq-toggle dashicons dashicons-arrow-down-alt2"></span>
                                        </div>
                                        <div class="redco-faq-answer">
                                            <p><?php esc_html_e('You can clear the cache in several ways:', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('1. From the Dashboard tab, click the "Clear" button in the Quick Actions section.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('2. From any tab with cache settings, use the "Clear Cache" button at the bottom of the page.', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('3. From the WordPress admin bar, click on "Redco Optimizer" and then "Clear Cache".', 'redco-optimizer'); ?></p>
                                            <p><?php esc_html_e('The cache is also automatically cleared in certain situations, such as when you publish or update content, change theme settings, or activate/deactivate plugins.', 'redco-optimizer'); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- JavaScript for FAQ accordion functionality -->
                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const faqItems = document.querySelectorAll('.redco-faq-item');

                    faqItems.forEach(item => {
                        const question = item.querySelector('.redco-faq-question');

                        question.addEventListener('click', function() {
                            // Close all other items
                            faqItems.forEach(otherItem => {
                                if (otherItem !== item && otherItem.classList.contains('active')) {
                                    otherItem.classList.remove('active');
                                }
                            });

                            // Toggle current item
                            item.classList.toggle('active');
                        });
                    });
                });
                </script>
        </div>

        <div id="redco-file-optimization-tab" class="redco-tab-content" style="display: none;">
            <?php
            // Get file optimization settings
            $file_settings = $redco_optimizer_admin->get_settings('file-optimization');
            ?>

            <form class="redco-form" method="post">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('CSS Files', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Minify CSS', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Remove unnecessary characters from CSS files to reduce their size.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="minify_css" id="minify_css" <?php checked($file_settings['minify_css'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Combine CSS Files', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Combine multiple CSS files into one to reduce HTTP requests.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="combine_css" id="combine_css" <?php checked($file_settings['combine_css'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Optimize CSS Delivery', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Optimize the delivery of CSS files to improve page rendering.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="optimize_css_delivery" id="optimize_css_delivery" <?php checked($file_settings['optimize_css_delivery'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('HTML Files', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Minify HTML', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Remove unnecessary characters from HTML files to reduce their size.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="minify_html" id="minify_html" value="1" <?php checked($file_settings['minify_html'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Remove HTML Comments', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Remove HTML comments to reduce file size.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="minify_html_comments" id="minify_html_comments" value="1" <?php checked($file_settings['minify_html_comments'] ?? 1, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Minify Inline CSS', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Minify CSS within style tags in HTML.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="minify_html_inline_css" id="minify_html_inline_css" value="1" <?php checked($file_settings['minify_html_inline_css'] ?? 1, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Minify Inline JavaScript', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Minify JavaScript within script tags in HTML.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="minify_html_inline_js" id="minify_html_inline_js" value="1" <?php checked($file_settings['minify_html_inline_js'] ?? 1, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('JavaScript Files', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Minify JavaScript', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Remove unnecessary characters from JavaScript files to reduce their size.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="minify_js" id="minify_js" value="1" <?php checked($file_settings['minify_js'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Combine JavaScript Files', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Combine multiple JavaScript files into one to reduce HTTP requests.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="combine_js" id="combine_js" value="1" <?php checked($file_settings['combine_js'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Defer JavaScript', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Defer the loading of JavaScript files to improve page rendering.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="defer_js" id="defer_js" value="1" <?php checked($file_settings['defer_js'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('JavaScript Defer Exclusions', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Enter JavaScript files or patterns to exclude from defer (one per line).', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <button type="button" class="redco-button redco-button-secondary redco-js-defer-exclusions-btn"><?php esc_html_e('Configure', 'redco-optimizer'); ?></button>
                                <input type="hidden" name="defer_js_exclusions" id="defer_js_exclusions_field" value="<?php echo esc_attr($file_settings['defer_js_exclusions'] ?? 'jquery.js'); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Remove Unused CSS', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Remove Unused CSS', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Analyze pages to identify and remove unused CSS, reducing file size and improving load times.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="remove_unused_css" id="remove_unused_css" value="1" <?php checked($file_settings['remove_unused_css'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>

                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="css_safelist"><?php esc_html_e('CSS Safelist', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="css_safelist" id="css_safelist" class="redco-textarea" placeholder=".my-class { color: red; }&#10;.another-class { font-size: 16px; }"><?php echo esc_textarea($file_settings['css_safelist'] ?? ''); ?></textarea>
                                <p class="redco-form-help"><?php esc_html_e('Add CSS rules that should always be included, even if they appear unused.', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Delay JavaScript Execution', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Delay JavaScript', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Postpone JavaScript loading until user interaction, improving initial page load speed.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="delay_js" id="delay_js" value="1" <?php checked($file_settings['delay_js'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>



                        <!-- Expandable sections for different script types -->
                        <div class="redco-expandable-sections<?php echo !$file_settings['delay_js'] ? ' hidden' : ''; ?>">
                            <!-- jQuery & Related -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-admin-tools" style="color: #72777c;"></span> <?php esc_html_e('jQuery & Related', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_jquery" id="delay_js_exclude_jquery" <?php checked($file_settings['delay_js_exclude_jquery'] ?? 1, 1); ?>>
                                            <span class="redco-checkbox-text">jquery.js</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_jquery_migrate" id="delay_js_exclude_jquery_migrate" <?php checked($file_settings['delay_js_exclude_jquery_migrate'] ?? 1, 1); ?>>
                                            <span class="redco-checkbox-text">jquery-migrate.min.js</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_jquery_core" id="delay_js_exclude_jquery_core" <?php checked($file_settings['delay_js_exclude_jquery_core'] ?? 1, 1); ?>>
                                            <span class="redco-checkbox-text">jquery.min.js</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- WP Core Scripts -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-wordpress" style="color: #72777c;"></span> <?php esc_html_e('WP Core Scripts', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_wp_embed" id="delay_js_exclude_wp_embed" <?php checked($file_settings['delay_js_exclude_wp_embed'] ?? 1, 1); ?>>
                                            <span class="redco-checkbox-text">wp-embed.min.js</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_wp_core" id="delay_js_exclude_wp_core" <?php checked($file_settings['delay_js_exclude_wp_core'] ?? 1, 1); ?>>
                                            <span class="redco-checkbox-text">wp-includes</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_comment_reply" id="delay_js_exclude_comment_reply" <?php checked($file_settings['delay_js_exclude_comment_reply'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">comment-reply.min.js</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Analytics & Trackers -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-chart-line" style="color: #72777c;"></span> <?php esc_html_e('Analytics & Trackers', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_analytics" id="delay_js_exclude_analytics" <?php checked($file_settings['delay_js_exclude_analytics'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Google Analytics (analytics)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_gtag" id="delay_js_exclude_gtag" <?php checked($file_settings['delay_js_exclude_gtag'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Google Tag (gtag)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_gtm" id="delay_js_exclude_gtm" <?php checked($file_settings['delay_js_exclude_gtm'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Google Tag Manager (gtm)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_fbevents" id="delay_js_exclude_fbevents" <?php checked($file_settings['delay_js_exclude_fbevents'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Facebook Pixel (fbevents.js)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_hotjar" id="delay_js_exclude_hotjar" <?php checked($file_settings['delay_js_exclude_hotjar'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Hotjar Analytics (hotjar)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_clarity" id="delay_js_exclude_clarity" <?php checked($file_settings['delay_js_exclude_clarity'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Microsoft Clarity (clarity)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Ad Networks -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-megaphone" style="color: #72777c;"></span> <?php esc_html_e('Ad Networks', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_adsense" id="delay_js_exclude_adsense" <?php checked($file_settings['delay_js_exclude_adsense'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Google AdSense (adsbygoogle)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_admanager" id="delay_js_exclude_admanager" <?php checked($file_settings['delay_js_exclude_admanager'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Google Ad Manager (googletag)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_amazon" id="delay_js_exclude_amazon" <?php checked($file_settings['delay_js_exclude_amazon'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Amazon Ads (amazon-adsystem)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Processors -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-money-alt" style="color: #72777c;"></span> <?php esc_html_e('Payment Processors', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <?php
                                        // Get active payment gateways
                                        $payment_gateways = array();

                                        // Check if WooCommerce is active
                                        if (class_exists('WooCommerce')) {
                                            $wc_payment_gateways = WC()->payment_gateways()->payment_gateways();
                                            foreach ($wc_payment_gateways as $gateway) {
                                                if ($gateway->enabled === 'yes') {
                                                    $payment_gateways[] = array(
                                                        'id' => 'delay_js_exclude_' . sanitize_title($gateway->id),
                                                        'name' => $gateway->title,
                                                        'script' => strtolower($gateway->id)
                                                    );
                                                }
                                            }
                                        }

                                        // Add common payment processors if none found
                                        if (empty($payment_gateways)) {
                                            $payment_gateways = array(
                                                array('id' => 'delay_js_exclude_stripe', 'name' => 'Stripe', 'script' => 'stripe'),
                                                array('id' => 'delay_js_exclude_paypal', 'name' => 'PayPal', 'script' => 'paypal'),
                                                array('id' => 'delay_js_exclude_square', 'name' => 'Square', 'script' => 'square'),
                                                array('id' => 'delay_js_exclude_authorize', 'name' => 'Authorize.net', 'script' => 'authorize')
                                            );
                                        }

                                        // Output payment gateways
                                        foreach ($payment_gateways as $gateway) {
                                            ?>
                                            <label class="redco-checkbox-item">
                                                <input type="checkbox" value="1" name="<?php echo esc_attr($gateway['id']); ?>" id="<?php echo esc_attr($gateway['id']); ?>" <?php checked($file_settings[$gateway['id']] ?? 0, 1); ?>>
                                                <span class="redco-checkbox-text"><?php echo esc_html($gateway['name']); ?> (<?php echo esc_html($gateway['script']); ?>)</span>
                                            </label>
                                            <?php
                                        }
                                        ?>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_recaptcha" id="delay_js_exclude_recaptcha" <?php checked($file_settings['delay_js_exclude_recaptcha'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Google reCAPTCHA (recaptcha)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Other Services -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-admin-generic" style="color: #72777c;"></span> <?php esc_html_e('Other Services', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_livechat" id="delay_js_exclude_livechat" <?php checked($file_settings['delay_js_exclude_livechat'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">LiveChat (livechat)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_intercom" id="delay_js_exclude_intercom" <?php checked($file_settings['delay_js_exclude_intercom'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Intercom Chat (intercom)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_zopim" id="delay_js_exclude_zopim" <?php checked($file_settings['delay_js_exclude_zopim'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Zopim Chat (zopim)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_zendesk" id="delay_js_exclude_zendesk" <?php checked($file_settings['delay_js_exclude_zendesk'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Zendesk Support (zendesk)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Handlers -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-feedback" style="color: #72777c;"></span> <?php esc_html_e('Form Handlers', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_cf7" id="delay_js_exclude_cf7" <?php checked($file_settings['delay_js_exclude_cf7'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Contact Form 7 (contact-form-7)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_wpcf7" id="delay_js_exclude_wpcf7" <?php checked($file_settings['delay_js_exclude_wpcf7'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Contact Form 7 API (wpcf7)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_formidable" id="delay_js_exclude_formidable" <?php checked($file_settings['delay_js_exclude_formidable'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Formidable Forms (formidable)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Media Players -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-format-video" style="color: #72777c;"></span> <?php esc_html_e('Media Players', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_mediaelement" id="delay_js_exclude_mediaelement" <?php checked($file_settings['delay_js_exclude_mediaelement'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">WordPress Media Player (mediaelement)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_vimeo" id="delay_js_exclude_vimeo" <?php checked($file_settings['delay_js_exclude_vimeo'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Vimeo Player (player.vimeo.com)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_youtube" id="delay_js_exclude_youtube" <?php checked($file_settings['delay_js_exclude_youtube'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">YouTube Player (youtube.com/iframe_api)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Sliders & Carousels -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-images-alt2" style="color: #72777c;"></span> <?php esc_html_e('Sliders & Carousels', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_swiper" id="delay_js_exclude_swiper" <?php checked($file_settings['delay_js_exclude_swiper'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Swiper Slider (swiper)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_slider" id="delay_js_exclude_slider" <?php checked($file_settings['delay_js_exclude_slider'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Generic Sliders (slider)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_carousel" id="delay_js_exclude_carousel" <?php checked($file_settings['delay_js_exclude_carousel'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Carousel Sliders (carousel)</span>
                                        </label>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_revolution" id="delay_js_exclude_revolution" <?php checked($file_settings['delay_js_exclude_revolution'] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text">Revolution Slider (revslider)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Themes -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-admin-appearance" style="color: #72777c;"></span> <?php esc_html_e('Themes', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <?php
                                        // Get current theme
                                        $current_theme = wp_get_theme();
                                        $theme_name = $current_theme->get('Name');
                                        $theme_slug = sanitize_title($theme_name);
                                        ?>
                                        <label class="redco-checkbox-item">
                                            <input type="checkbox" value="1" name="delay_js_exclude_theme_<?php echo esc_attr($theme_slug); ?>" id="delay_js_exclude_theme_<?php echo esc_attr($theme_slug); ?>" <?php checked($file_settings['delay_js_exclude_theme_' . $theme_slug] ?? 0, 1); ?>>
                                            <span class="redco-checkbox-text"><?php echo esc_html($theme_name); ?></span>
                                        </label>

                                        <?php
                                        // Check for parent theme
                                        if ($current_theme->parent()) {
                                            $parent_theme = $current_theme->parent();
                                            $parent_name = $parent_theme->get('Name');
                                            $parent_slug = sanitize_title($parent_name);
                                            ?>
                                            <label class="redco-checkbox-item">
                                                <input type="checkbox" value="1" name="delay_js_exclude_theme_<?php echo esc_attr($parent_slug); ?>" id="delay_js_exclude_theme_<?php echo esc_attr($parent_slug); ?>" <?php checked($file_settings['delay_js_exclude_theme_' . $parent_slug] ?? 0, 1); ?>>
                                                <span class="redco-checkbox-text"><?php echo esc_html($parent_name); ?> (Parent)</span>
                                            </label>
                                            <?php
                                        }

                                        // Common theme frameworks
                                        $theme_frameworks = array(
                                            'elementor' => 'Elementor',
                                            'avada' => 'Avada',
                                            'divi' => 'Divi',
                                            'astra' => 'Astra',
                                            'generatepress' => 'GeneratePress',
                                            'oceanwp' => 'OceanWP'
                                        );

                                        foreach ($theme_frameworks as $framework_slug => $framework_name) {
                                            // Check if the framework is likely being used
                                            $is_used = false;

                                            // Check if it's a plugin that's active
                                            if (function_exists('is_plugin_active')) {
                                                if (is_plugin_active($framework_slug . '/' . $framework_slug . '.php') ||
                                                    is_plugin_active($framework_slug . '-pro/' . $framework_slug . '-pro.php')) {
                                                    $is_used = true;
                                                }
                                            }

                                            // Check if it's the current theme or parent theme
                                            if (strpos(strtolower($theme_name), strtolower($framework_slug)) !== false ||
                                                (isset($parent_name) && strpos(strtolower($parent_name), strtolower($framework_slug)) !== false)) {
                                                $is_used = true;
                                            }

                                            if ($is_used) {
                                                ?>
                                                <label class="redco-checkbox-item">
                                                    <input type="checkbox" value="1" name="delay_js_exclude_<?php echo esc_attr($framework_slug); ?>" id="delay_js_exclude_<?php echo esc_attr($framework_slug); ?>" <?php checked($file_settings['delay_js_exclude_' . $framework_slug] ?? 0, 1); ?>>
                                                    <span class="redco-checkbox-text"><?php echo esc_html($framework_name); ?></span>
                                                </label>
                                                <?php
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Plugins -->
                            <div class="redco-expandable-section">
                                <div class="redco-expandable-header">
                                    <h4><span class="dashicons dashicons-admin-plugins" style="color: #72777c;"></span> <?php esc_html_e('Plugins', 'redco-optimizer'); ?></h4>
                                    <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                                </div>
                                <div class="redco-expandable-content">
                                    <div class="redco-checkbox-list">
                                        <?php
                                        // Common plugins that might need JS exclusions
                                        $common_plugins = array(
                                            'woocommerce' => 'WooCommerce',
                                            'elementor' => 'Elementor',
                                            'contact-form-7' => 'Contact Form 7',
                                            'wp-rocket' => 'WP Rocket',
                                            'wpforms' => 'WPForms',
                                            'gravity-forms' => 'Gravity Forms',
                                            'ninja-forms' => 'Ninja Forms',
                                            'formidable' => 'Formidable Forms',
                                            'yoast-seo' => 'Yoast SEO',
                                            'rank-math' => 'Rank Math SEO',
                                            'all-in-one-seo-pack' => 'All in One SEO',
                                            'jetpack' => 'Jetpack',
                                            'wordfence' => 'Wordfence Security',
                                            'wp-super-cache' => 'WP Super Cache',
                                            'w3-total-cache' => 'W3 Total Cache',
                                            'autoptimize' => 'Autoptimize',
                                            'litespeed-cache' => 'LiteSpeed Cache'
                                        );

                                        // Get active plugins
                                        $active_plugins = array();
                                        if (function_exists('get_option')) {
                                            $plugins = get_option('active_plugins', array());
                                            foreach ($plugins as $plugin) {
                                                $plugin_data = get_plugin_data(WP_PLUGIN_DIR . '/' . $plugin);
                                                if (!empty($plugin_data['Name'])) {
                                                    $plugin_slug = sanitize_title($plugin_data['Name']);
                                                    $active_plugins[$plugin_slug] = $plugin_data['Name'];
                                                }
                                            }
                                        }

                                        // If we couldn't get active plugins, use common ones
                                        if (empty($active_plugins)) {
                                            $active_plugins = $common_plugins;
                                        }

                                        // Output plugin checkboxes
                                        foreach ($active_plugins as $plugin_slug => $plugin_name) {
                                            ?>
                                            <label class="redco-checkbox-item">
                                                <input type="checkbox" value="1" name="delay_js_exclude_plugin_<?php echo esc_attr($plugin_slug); ?>" id="delay_js_exclude_plugin_<?php echo esc_attr($plugin_slug); ?>" <?php checked($file_settings['delay_js_exclude_plugin_' . $plugin_slug] ?? 0, 1); ?>>
                                                <span class="redco-checkbox-text"><?php echo esc_html($plugin_name); ?></span>
                                            </label>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="redco-form-row" id="delay-js-exclusions-row" style="<?php echo !$file_settings['delay_js'] ? 'display: none;' : ''; ?>">
                            <div class="redco-form-label">
                                <label for="delay_js_exclusions"><?php esc_html_e('Custom Exclusions', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="delay_js_exclusions" id="delay_js_exclusions" class="redco-textarea" placeholder="jquery&#10;analytics&#10;recaptcha"><?php echo esc_textarea($file_settings['delay_js_exclusions'] ?? ''); ?></textarea>
                                <p class="redco-form-help"><?php esc_html_e('Add custom scripts that should not be delayed (one per line).', 'redco-optimizer'); ?></p>
                            </div>
                        </div>

                        <div class="redco-toggle-row" id="delay-js-safe-mode-row" style="<?php echo !$file_settings['delay_js'] ? 'display: none;' : ''; ?>">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Safe Mode', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Exclude WordPress core scripts and jQuery from being delayed.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" name="delay_js_safe_mode" id="delay_js_safe_mode" value="1" <?php checked($file_settings['delay_js_safe_mode'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card redco-expandable-section">
                    <div class="redco-card-header redco-expandable-header">
                        <h3><?php esc_html_e('Load CSS Asynchronously', 'redco-optimizer'); ?> <span class="redco-premium-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span></h3>
                        <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                    </div>
                    <div class="redco-card-content redco-expandable-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Async CSS', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Load CSS files asynchronously with critical CSS inline, preventing render-blocking.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="async_css" id="async_css_premium" <?php checked($file_settings['async_css'] ?? 0, 1); ?> disabled>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="async_css_exclusions"><?php esc_html_e('Exclusions', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="async_css_exclusions" id="async_css_exclusions" class="redco-textarea" placeholder="admin-bar.min.css&#10;dashicons.min.css" disabled><?php echo esc_textarea($file_settings['async_css_exclusions'] ?? ''); ?></textarea>
                                <p class="redco-form-help"><?php esc_html_e('Add CSS files that should not be loaded asynchronously (one per line).', 'redco-optimizer'); ?></p>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="critical_css"><?php esc_html_e('Critical CSS', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="critical_css" id="critical_css" class="redco-textarea" placeholder="body { font-family: sans-serif; }&#10;header { background-color: #fff; }" disabled><?php echo esc_textarea($file_settings['critical_css'] ?? ''); ?></textarea>
                                <p class="redco-form-help"><?php esc_html_e('Add critical CSS that will be inlined in the head of the page.', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card redco-expandable-section">
                    <div class="redco-card-header redco-expandable-header">
                        <h3><?php esc_html_e('Google Fonts Optimization', 'redco-optimizer'); ?></h3>
                        <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                    </div>
                    <div class="redco-card-content redco-expandable-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Optimize Google Fonts', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Optimize Google Fonts loading with preloading and display swap.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="optimize_google_fonts" id="optimize_google_fonts" <?php checked($file_settings['optimize_google_fonts'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Self-host Google Fonts', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Download and serve Google Fonts from your own server to improve privacy and performance.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="self_host_google_fonts" id="self_host_google_fonts_premium" <?php checked($file_settings['self_host_google_fonts'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card redco-expandable-section">
                    <div class="redco-card-header redco-expandable-header">
                        <h3><?php esc_html_e('Advanced Settings', 'redco-optimizer'); ?> <span class="redco-premium-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span></h3>
                        <span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>
                    </div>
                    <div class="redco-card-content redco-expandable-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Exclude Files', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Exclude specific files from optimization.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <button type="button" class="redco-button redco-button-secondary redco-exclude-files-btn" disabled><?php esc_html_e('Configure', 'redco-optimizer'); ?></button>
                                <input type="hidden" name="file_exclusions" id="file_exclusions" value="<?php echo esc_attr($file_settings['file_exclusions'] ?? ''); ?>">
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="cache_lifetime"><?php esc_html_e('Cache Lifetime', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <select name="cache_lifetime" id="cache_lifetime" class="redco-select" disabled>
                                    <option value="3600" <?php selected($file_settings['cache_lifetime'], 3600); ?>><?php esc_html_e('1 hour', 'redco-optimizer'); ?></option>
                                    <option value="86400" <?php selected($file_settings['cache_lifetime'], 86400); ?>><?php esc_html_e('1 day', 'redco-optimizer'); ?></option>
                                    <option value="604800" <?php selected($file_settings['cache_lifetime'], 604800); ?>><?php esc_html_e('1 week', 'redco-optimizer'); ?></option>
                                    <option value="2592000" <?php selected($file_settings['cache_lifetime'], 2592000); ?>><?php esc_html_e('1 month', 'redco-optimizer'); ?></option>
                                </select>
                                <p class="redco-form-help"><?php esc_html_e('Set how long cached files should be stored before being regenerated.', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>




                <div class="redco-form-actions">
                    <button type="submit" class="redco-button redco-button-primary"><?php esc_html_e('Save Changes', 'redco-optimizer'); ?></button>
                    <button type="button" class="redco-button redco-button-secondary redco-clear-cache"><?php esc_html_e('Clear Cache', 'redco-optimizer'); ?></button>
                </div>
            </form>
        </div>

        <div id="redco-media-tab" class="redco-tab-content" style="display: none;">
            <?php
            // Get media settings
            $media_settings = $redco_optimizer_admin->get_settings('media');
            ?>

            <form class="redco-form" method="post">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Image Optimization', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Enable Image Optimization', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Automatically optimize images when uploaded to the media library.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="enable_image_optimization" id="enable_image_optimization" <?php checked($media_settings['enable_image_optimization'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Lazy Load Images', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Load images only when they enter the viewport to improve page load times.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="lazy_load_images" id="lazy_load_images" <?php checked($media_settings['lazy_load_images'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="lazy_load_threshold"><?php esc_html_e('Lazy Load Threshold', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <input type="number" name="lazy_load_threshold" id="lazy_load_threshold" min="0" max="10" value="<?php echo esc_attr($media_settings['lazy_load_threshold'] ?? 3); ?>" class="redco-input-small">
                                <p class="redco-form-help"><?php esc_html_e('Number of viewport heights to start loading images before they become visible (0-10). Higher values load earlier.', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Image Quality & Format', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Add Missing Image Dimensions', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Add width and height attributes to images to prevent layout shifts during page load.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="image_dimensions" id="image_dimensions" <?php checked($media_settings['image_dimensions'] ?? 1, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="image_quality"><?php esc_html_e('Image Quality', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <input type="range" name="image_quality" id="image_quality" min="50" max="100" value="<?php echo esc_attr($media_settings['image_quality'] ?? 82); ?>" class="redco-range">
                                <span class="redco-range-value"><?php echo esc_html($media_settings['image_quality'] ?? 82); ?>%</span>
                            </div>
                            <p class="redco-form-help"><?php esc_html_e('Set the quality of optimized images. Lower values mean smaller file sizes but lower quality.', 'redco-optimizer'); ?></p>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('WebP Conversion', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Convert images to WebP format for better compression and quality.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="webp_conversion" id="webp_conversion" <?php checked($media_settings['webp_conversion'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('WordPress Embeds', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Disable Emojis', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Remove WordPress emoji scripts and styles to reduce HTTP requests.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="disable_emojis" id="disable_emojis" <?php checked($media_settings['disable_emojis'] ?? 1, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Disable Embeds', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Disable WordPress embed scripts to reduce HTTP requests.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="disable_embeds" id="disable_embeds" <?php checked($media_settings['disable_embeds'] ?? 1, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-form-actions">
                    <button type="submit" class="redco-button redco-button-primary"><?php esc_html_e('Save Changes', 'redco-optimizer'); ?></button>
                </div>
            </form>
        </div>

        <div id="redco-lazyload-tab" class="redco-tab-content" style="display: none;">
            <?php
            // Get lazyload settings
            $lazyload_settings = $redco_optimizer_admin->get_settings('lazyload');

            // Display admin notices for this tab
            do_action('redco_optimizer_admin_notices');
            ?>

            <form class="redco-form" method="post">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Images', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('LazyLoad Images', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Load images only when they enter the viewport to improve page load times.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="lazyload_images" id="lazyload_images" <?php checked($lazyload_settings['lazyload_images'] ?? 1, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('LazyLoad CSS Background Images', 'redco-optimizer'); ?>
                                    <span class="redco-premium-badge"><?php esc_html_e('Premium', 'redco-optimizer'); ?></span>
                                </h4>
                                <p><?php esc_html_e('Load CSS background images only when they enter the viewport.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="lazyload_css_bg" id="lazyload_css_bg" <?php checked($lazyload_settings['lazyload_css_bg'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="lazyload_css_bg_exclusions"><?php esc_html_e('CSS Background Exclusions', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="lazyload_css_bg_exclusions" id="lazyload_css_bg_exclusions" class="redco-textarea" placeholder=".hero-section&#10;.banner&#10;header"><?php echo esc_textarea($lazyload_settings['lazyload_css_bg_exclusions'] ?? ''); ?></textarea>
                            </div>
                            <p class="redco-form-help"><?php esc_html_e('Add CSS selectors or image URLs that should not be lazy loaded (one per line).', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('iFrames & Videos', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('LazyLoad iFrames', 'redco-optimizer'); ?>
                                    <span class="redco-premium-badge"><?php esc_html_e('Premium', 'redco-optimizer'); ?></span>
                                </h4>
                                <p><?php esc_html_e('Load iframes only when they enter the viewport.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="lazyload_iframes" id="lazyload_iframes" <?php checked($lazyload_settings['lazyload_iframes'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('LazyLoad Videos', 'redco-optimizer'); ?>
                                    <span class="redco-premium-badge"><?php esc_html_e('Premium', 'redco-optimizer'); ?></span>
                                </h4>
                                <p><?php esc_html_e('Replace videos with a lightweight preview image until played.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="lazyload_videos" id="lazyload_videos" <?php checked($lazyload_settings['lazyload_videos'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="youtube_preview_quality"><?php esc_html_e('YouTube Preview Quality', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <select name="youtube_preview_quality" id="youtube_preview_quality" class="redco-select">
                                    <option value="default" <?php selected($lazyload_settings['youtube_preview_quality'] ?? 'hqdefault', 'default'); ?>><?php esc_html_e('Default', 'redco-optimizer'); ?></option>
                                    <option value="hqdefault" <?php selected($lazyload_settings['youtube_preview_quality'] ?? 'hqdefault', 'hqdefault'); ?>><?php esc_html_e('High Quality', 'redco-optimizer'); ?></option>
                                    <option value="mqdefault" <?php selected($lazyload_settings['youtube_preview_quality'] ?? 'hqdefault', 'mqdefault'); ?>><?php esc_html_e('Medium Quality', 'redco-optimizer'); ?></option>
                                    <option value="sddefault" <?php selected($lazyload_settings['youtube_preview_quality'] ?? 'hqdefault', 'sddefault'); ?>><?php esc_html_e('Standard Quality', 'redco-optimizer'); ?></option>
                                </select>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="lazyload_iframe_exclusions"><?php esc_html_e('iFrame & Video Exclusions', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="lazyload_iframe_exclusions" id="lazyload_iframe_exclusions" class="redco-textarea" placeholder="recaptcha&#10;maps&#10;analytics"><?php echo esc_textarea($lazyload_settings['lazyload_iframe_exclusions'] ?? ''); ?></textarea>
                            </div>
                            <p class="redco-form-help"><?php esc_html_e('Add keywords to exclude specific iframes or videos from lazy loading (one per line).', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Advanced Settings', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="lazyload_threshold"><?php esc_html_e('LazyLoad Threshold', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <input type="number" name="lazyload_threshold" id="lazyload_threshold" min="0" max="10" value="<?php echo esc_attr($lazyload_settings['lazyload_threshold'] ?? 3); ?>" class="redco-input-small">
                            </div>
                            <p class="redco-form-help"><?php esc_html_e('Number of viewport heights to start loading elements before they become visible (0-10). Higher values load earlier.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="redco-form-actions">
                    <button type="submit" class="redco-button redco-button-primary redco-save-settings" data-module="lazyload">
                        <span class="dashicons dashicons-yes"></span>
                        <?php esc_html_e('Save Changes', 'redco-optimizer'); ?>
                    </button>
                </div>
            </form>
        </div>

        <div id="redco-preload-tab" class="redco-tab-content" style="display: none;">
            <?php
            // Get preload settings
            $preload_settings = $redco_optimizer_admin->get_settings('preload');
            ?>


            <form class="redco-form" method="post">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Preload Cache', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Enable Preload Cache', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Automatically generate cache files for your homepage and frequently visited pages.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="preload_cache" id="preload_cache" <?php checked($preload_settings['preload_cache'] ?? 1, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Sitemap-based Preloading', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Automatically preload pages from your XML sitemap to ensure they are cached.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="preload_sitemap" id="preload_sitemap" <?php checked($preload_settings['preload_sitemap'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="sitemap_urls"><?php esc_html_e('Sitemap URLs', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="sitemap_urls" id="sitemap_urls" class="redco-textarea" placeholder="https://example.com/sitemap.xml&#10;https://example.com/sitemap_index.xml"><?php echo esc_textarea($preload_settings['sitemap_urls'] ?? ''); ?></textarea>
                            </div>
                            <p class="redco-form-help"><?php esc_html_e('Enter sitemap URLs (one per line). Leave empty to auto-detect from popular SEO plugins.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Preload Fonts', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Preload Fonts', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Preload critical font files to improve rendering speed.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="preload_fonts" id="preload_fonts" <?php checked($preload_settings['preload_fonts'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>

                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="preload_fonts_list"><?php esc_html_e('Font Files to Preload', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="preload_fonts_list" id="preload_fonts_list" class="redco-textarea" placeholder="/wp-content/themes/your-theme/fonts/font.woff2&#10;https://example.com/fonts/font.woff2"><?php echo esc_textarea($preload_settings['preload_fonts_list'] ?? ''); ?></textarea>
                            </div>
                            <p class="redco-form-help"><?php esc_html_e('Add URLs of font files to preload (one per line). Use relative or absolute URLs.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Prefetch DNS Requests', 'redco-optimizer'); ?> <span class="redco-premium-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Enable DNS Prefetching', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Resolve DNS for external domains in advance to speed up resource loading.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="prefetch_dns" id="prefetch_dns" <?php checked($preload_settings['prefetch_dns'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="prefetch_dns_list"><?php esc_html_e('Domains to Prefetch', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="prefetch_dns_list" id="prefetch_dns_list" class="redco-textarea" placeholder="//example.com&#10;//cdnjs.cloudflare.com&#10;//google-analytics.com"><?php echo esc_textarea($preload_settings['prefetch_dns_list'] ?? $preload_settings['prefetch_dns'] ?? ''); ?></textarea>
                                <p class="redco-form-help"><?php esc_html_e('Specify external domains to prefetch DNS (one per line). This can improve performance for resources loaded from external domains.', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Preload Links', 'redco-optimizer'); ?> <span class="redco-premium-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Enable Link Preloading', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Preload pages that users are likely to visit next for instant navigation.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="preload_links" id="preload_links" <?php checked($preload_settings['preload_links'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="preload_links_exclusions"><?php esc_html_e('Link Preloading Exclusions', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="preload_links_exclusions" id="preload_links_exclusions" class="redco-textarea" placeholder="/cart/&#10;/checkout/&#10;/my-account/"><?php echo esc_textarea($preload_settings['preload_links_exclusions'] ?? ''); ?></textarea>
                                <p class="redco-form-help"><?php esc_html_e('Add URLs or URL patterns that should not be preloaded (one per line).', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-form-actions">
                    <button type="submit" class="redco-button redco-button-primary"><?php esc_html_e('Save Changes', 'redco-optimizer'); ?></button>
                </div>
            </form>
        </div>

        <div id="redco-database-tab" class="redco-tab-content" style="display: none;">
            <?php
            // Get database settings
            $db_settings = $redco_optimizer_admin->get_settings('database');
            ?>


            <form class="redco-form" method="post">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Database Cleanup', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-checkbox-group">
                            <?php
                            // Get real database counts
                            global $wpdb;

                            // Count post revisions
                            $revisions_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->posts WHERE post_type = 'revision'");

                            // Count auto drafts
                            $auto_drafts_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->posts WHERE post_status = 'auto-draft'");

                            // Count trashed posts
                            $trashed_posts_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->posts WHERE post_status = 'trash'");

                            // Count spam comments
                            $spam_comments_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->comments WHERE comment_approved = 'spam'");

                            // Count trashed comments
                            $trashed_comments_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->comments WHERE comment_approved = 'trash'");

                            // Count expired transients (this is an approximation)
                            $expired_transients_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->options WHERE option_name LIKE '%_transient_timeout_%' AND option_value < " . time());

                            // Count all transients
                            $all_transients_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->options WHERE option_name LIKE '%_transient_%'");
                            ?>
                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="clean_post_revisions" <?php checked($db_settings['clean_post_revisions'], 1); ?>>
                                <span class="redco-checkbox-label"><?php esc_html_e('Post Revisions', 'redco-optimizer'); ?></span>
                                <span class="redco-checkbox-count"><?php echo esc_html($revisions_count); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="clean_auto_drafts" <?php checked($db_settings['clean_auto_drafts'], 1); ?>>
                                <span class="redco-checkbox-label"><?php esc_html_e('Auto Drafts', 'redco-optimizer'); ?></span>
                                <span class="redco-checkbox-count"><?php echo esc_html($auto_drafts_count); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="clean_trashed_posts" <?php checked($db_settings['clean_trashed_posts'], 1); ?>>
                                <span class="redco-checkbox-label"><?php esc_html_e('Trashed Posts', 'redco-optimizer'); ?></span>
                                <span class="redco-checkbox-count"><?php echo esc_html($trashed_posts_count); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="clean_spam_comments" <?php checked($db_settings['clean_spam_comments'], 1); ?>>
                                <span class="redco-checkbox-label"><?php esc_html_e('Spam Comments', 'redco-optimizer'); ?></span>
                                <span class="redco-checkbox-count"><?php echo esc_html($spam_comments_count); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="clean_trashed_comments" <?php checked($db_settings['clean_trashed_comments'], 1); ?>>
                                <span class="redco-checkbox-label"><?php esc_html_e('Trashed Comments', 'redco-optimizer'); ?></span>
                                <span class="redco-checkbox-count"><?php echo esc_html($trashed_comments_count); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="clean_expired_transients" <?php checked($db_settings['clean_expired_transients'], 1); ?>>
                                <span class="redco-checkbox-label"><?php esc_html_e('Expired Transients', 'redco-optimizer'); ?></span>
                                <span class="redco-checkbox-count"><?php echo esc_html($expired_transients_count); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="clean_all_transients" <?php checked($db_settings['clean_all_transients'], 1); ?>>
                                <span class="redco-checkbox-label"><?php esc_html_e('All Transients', 'redco-optimizer'); ?></span>
                                <span class="redco-checkbox-count"><?php echo esc_html($all_transients_count); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="clean_optimize_tables" <?php checked($db_settings['clean_optimize_tables'], 1); ?>>
                                <span class="redco-checkbox-label"><?php esc_html_e('Optimize Database Tables', 'redco-optimizer'); ?></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Scheduled Cleanup', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Enable Scheduled Cleanup', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Automatically clean your database on a schedule.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="schedule_cleanup" id="schedule_cleanup" <?php checked($db_settings['schedule_cleanup'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>

                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="schedule_frequency"><?php esc_html_e('Cleanup Frequency', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <select name="schedule_frequency" id="schedule_frequency" class="redco-select">
                                    <option value="daily" <?php selected($db_settings['schedule_frequency'] ?? 'weekly', 'daily'); ?>><?php esc_html_e('Daily', 'redco-optimizer'); ?></option>
                                    <option value="weekly" <?php selected($db_settings['schedule_frequency'] ?? 'weekly', 'weekly'); ?>><?php esc_html_e('Weekly', 'redco-optimizer'); ?></option>
                                    <option value="monthly" <?php selected($db_settings['schedule_frequency'] ?? 'weekly', 'monthly'); ?>><?php esc_html_e('Monthly', 'redco-optimizer'); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Advanced Database Cleanup', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <p class="redco-warning"><?php esc_html_e('Warning: These options can remove data that might be needed by some plugins. Use with caution.', 'redco-optimizer'); ?></p>

                        <div class="redco-checkbox-group">
                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="cleanup_postmeta" id="cleanup_postmeta" <?php checked($db_settings['cleanup_postmeta'] ?? 0, 1); ?> disabled>
                                <span class="redco-checkbox-label"><?php esc_html_e('Orphaned Post Meta', 'redco-optimizer'); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="cleanup_commentmeta" id="cleanup_commentmeta" <?php checked($db_settings['cleanup_commentmeta'] ?? 0, 1); ?> disabled>
                                <span class="redco-checkbox-label"><?php esc_html_e('Orphaned Comment Meta', 'redco-optimizer'); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="cleanup_orphaned_term_relationships" id="cleanup_orphaned_term_relationships" <?php checked($db_settings['cleanup_orphaned_term_relationships'] ?? 0, 1); ?> disabled>
                                <span class="redco-checkbox-label"><?php esc_html_e('Orphaned Term Relationships', 'redco-optimizer'); ?></span>
                            </label>

                            <label class="redco-checkbox">
                                <input type="checkbox" value="1" name="cleanup_wp_options" id="cleanup_wp_options" <?php checked($db_settings['cleanup_wp_options'] ?? 0, 1); ?> disabled>
                                <span class="redco-checkbox-label"><?php esc_html_e('Unused Options', 'redco-optimizer'); ?></span>
                            </label>
                        </div>

                        <div class="redco-premium-cta">
                            <p><?php esc_html_e('Advanced Database Cleanup is a premium feature. Upgrade to Redco Optimizer Premium to access these powerful database optimization tools.', 'redco-optimizer'); ?></p>
                            <a href="#" class="redco-button redco-button-primary redco-premium-tab-link" data-tab="redco-premium-tab"><?php esc_html_e('Upgrade to Premium', 'redco-optimizer'); ?></a>
                        </div>
                    </div>
                </div>

                <div class="redco-form-actions">
                    <button type="submit" class="redco-button redco-button-primary"><?php esc_html_e('Save Settings', 'redco-optimizer'); ?></button>
                    <button type="button" class="redco-button redco-button-secondary redco-clean-database"><?php esc_html_e('Clean Database Now', 'redco-optimizer'); ?></button>
                </div>
            </form>
        </div>

        <div id="redco-cdn-tab" class="redco-tab-content" style="display: none;">
            <?php
            // Get CDN settings
            $cdn_settings = $redco_optimizer_admin->get_settings('cdn');
            ?>

            <form class="redco-form" method="post">
                <div class="redco-card">
                <div class="redco-card-header">
                    <h3><?php esc_html_e('CDN Settings', 'redco-optimizer'); ?></h3>
                </div>
                <div class="redco-card-content">
                    <div class="redco-toggle-row">
                        <div class="redco-toggle-info">
                            <h4><?php esc_html_e('Enable CDN', 'redco-optimizer'); ?></h4>
                            <p><?php esc_html_e('Enable CDN integration to serve your static files from a global network.', 'redco-optimizer'); ?></p>
                        </div>
                        <div class="redco-toggle-control">
                            <label>
                                <input type="checkbox" value="1" name="enable_cdn" id="enable_cdn" <?php checked(isset($cdn_settings['enable_cdn']) ? $cdn_settings['enable_cdn'] : 0, 1); ?>
                                <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                            </label>
                        </div>
                    </div>

                    <div class="redco-form-row">
                        <div class="redco-form-label">
                            <label for="cdn_url"><?php esc_html_e('CDN URL', 'redco-optimizer'); ?></label>
                        </div>
                        <div class="redco-form-field">
                            <input type="text" name="cdn_url" id="cdn_url" class="redco-input" placeholder="https://cdn.example.com" value="<?php echo esc_attr(isset($cdn_settings['cdn_url']) ? $cdn_settings['cdn_url'] : ''); ?>">
                        </div>
                        <p class="redco-form-help"><?php esc_html_e('Enter your CDN URL without trailing slash.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
            </div>

            <div class="redco-card">
                <div class="redco-card-header">
                    <h3><?php esc_html_e('CDN Inclusions', 'redco-optimizer'); ?></h3>
                </div>
                <div class="redco-card-content">
                    <p><?php esc_html_e('Select which file types to serve from your CDN.', 'redco-optimizer'); ?></p>

                    <div class="redco-checkbox-group">
                        <label class="redco-checkbox">
                            <input type="checkbox" value="1" name="cdn_include_images" <?php checked(isset($cdn_settings['cdn_include_images']) ? $cdn_settings['cdn_include_images'] : 1, 1); ?>>
                            <span class="redco-checkbox-label"><?php esc_html_e('Images (jpg, jpeg, png, gif, webp, svg)', 'redco-optimizer'); ?></span>
                        </label>

                        <label class="redco-checkbox">
                            <input type="checkbox" value="1" name="cdn_include_js" <?php checked(isset($cdn_settings['cdn_include_js']) ? $cdn_settings['cdn_include_js'] : 1, 1); ?>>
                            <span class="redco-checkbox-label"><?php esc_html_e('JavaScript (js)', 'redco-optimizer'); ?></span>
                        </label>

                        <label class="redco-checkbox">
                            <input type="checkbox" value="1" name="cdn_include_css" <?php checked(isset($cdn_settings['cdn_include_css']) ? $cdn_settings['cdn_include_css'] : 1, 1); ?>>
                            <span class="redco-checkbox-label"><?php esc_html_e('CSS (css)', 'redco-optimizer'); ?></span>
                        </label>

                        <label class="redco-checkbox">
                            <input type="checkbox" value="1" name="cdn_include_fonts" <?php checked(isset($cdn_settings['cdn_include_fonts']) ? $cdn_settings['cdn_include_fonts'] : 1, 1); ?>>
                            <span class="redco-checkbox-label"><?php esc_html_e('Fonts (eot, ttf, otf, woff, woff2)', 'redco-optimizer'); ?></span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="redco-card">
                <div class="redco-card-header">
                    <h3><?php esc_html_e('CDN Exclusions', 'redco-optimizer'); ?></h3>
                </div>
                <div class="redco-card-content">
                    <p><?php esc_html_e('Exclude specific files or directories from being served through the CDN.', 'redco-optimizer'); ?></p>

                    <div class="redco-form-row">
                        <div class="redco-form-field">
                            <textarea name="cdn_exclusions" id="cdn_exclusions" class="redco-textarea" placeholder=".php
wp-admin/
wp-includes/
wp-login.php"><?php echo esc_textarea(isset($cdn_settings['cdn_exclusions']) ? $cdn_settings['cdn_exclusions'] : ''); ?></textarea>
                            <p class="redco-form-help"><?php esc_html_e('Enter one pattern per line. Use * as a wildcard.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="redco-form-actions">
                <button type="submit" class="redco-button redco-button-primary redco-save-settings" data-module="cdn">
                    <span class="dashicons dashicons-yes"></span>
                    <?php esc_html_e('Save Changes', 'redco-optimizer'); ?>
                </button>
            </div>
            </form>
        </div>



        <!-- Caching Tab -->
        <div id="redco-caching-tab" class="redco-tab-content" style="display: none;">
            <?php
            // Get caching settings
            $caching_settings = $redco_optimizer_admin->get_settings('caching');
            ?>


            <form class="redco-form" method="post">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Page Caching', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Enable Page Caching', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Store static HTML copies of your pages to reduce server load and improve page load times.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="enable_page_caching" id="enable_page_caching" <?php checked($caching_settings['enable_page_caching'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Enable Browser Caching', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Add browser caching rules to your .htaccess file to improve page load times for returning visitors.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="enable_browser_caching" id="enable_browser_caching" <?php checked($caching_settings['enable_browser_caching'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Mobile Caching', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Enable Mobile Caching', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Enable caching for mobile devices.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="enable_mobile_caching" id="enable_mobile_caching" <?php checked($caching_settings['enable_mobile_caching'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Separate Mobile Cache', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Create separate cache files for mobile devices.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="separate_mobile_cache" id="separate_mobile_cache" <?php checked($caching_settings['separate_mobile_cache'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Cache Lifespan', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="cache_lifespan"><?php esc_html_e('Cache Lifespan', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field redco-form-field-inline">
                                <input type="number" name="cache_lifespan" id="cache_lifespan" min="1" max="720" value="<?php echo esc_attr($caching_settings['cache_lifespan']); ?>" class="redco-input-small">
                                <select name="cache_lifespan_unit" id="cache_lifespan_unit" class="redco-select-small">
                                    <option value="hours" <?php selected($caching_settings['cache_lifespan_unit'], 'hours'); ?>><?php esc_html_e('Hours', 'redco-optimizer'); ?></option>
                                    <option value="days" <?php selected($caching_settings['cache_lifespan_unit'], 'days'); ?>><?php esc_html_e('Days', 'redco-optimizer'); ?></option>
                                </select>
                                <p class="redco-form-help"><?php esc_html_e('The maximum time a cached page will be stored before being regenerated. Default is 10 hours.', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Cache Clearing', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Clear Cache on Post Edit', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Automatically clear cache when posts or pages are edited.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="clear_on_post_edit" id="clear_on_post_edit" <?php checked($caching_settings['clear_on_post_edit'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Clear Cache on Comment', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Automatically clear cache when comments are added or approved.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="clear_on_comment" id="clear_on_comment" <?php checked($caching_settings['clear_on_comment'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Advanced Cache Settings', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Cache for Logged-in Users', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Enable caching for logged-in users.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="cache_logged_in_users" id="cache_logged_in_users" <?php checked($caching_settings['cache_logged_in_users'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Cache SSL Pages', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Enable caching for SSL pages.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="cache_ssl" id="cache_ssl" <?php checked($caching_settings['cache_ssl'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Cache 404 Pages', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Enable caching for 404 pages.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="cache_404" id="cache_404" <?php checked($caching_settings['cache_404'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Cache Query Strings', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Enable caching for URLs with query strings.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="cache_query_strings" id="cache_query_strings" <?php checked($caching_settings['cache_query_strings'], 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="cache_exclusions"><?php esc_html_e('Cache Exclusions', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <textarea name="cache_exclusions" id="cache_exclusions" class="redco-textarea" placeholder="/example-page/&#10;/another-page/"><?php echo esc_textarea($caching_settings['cache_exclusions']); ?></textarea>
                                <p class="redco-form-help"><?php esc_html_e('Enter URLs or URL patterns (one per line) that should not be cached.', 'redco-optimizer'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-form-actions">
                    <button type="submit" class="redco-button redco-button-primary redco-save-settings" data-module="caching">
                        <span class="dashicons dashicons-yes"></span>
                        <?php esc_html_e('Save Changes', 'redco-optimizer'); ?>
                    </button>
                    <button type="button" class="redco-button redco-button-secondary redco-clear-cache">
                        <span class="dashicons dashicons-trash"></span>
                        <?php esc_html_e('Clear Cache', 'redco-optimizer'); ?>
                    </button>
                </div>
            </form>
        </div>

        <!-- Modules Tab -->
        <div id="redco-modules-tab" class="redco-tab-content" style="display: none;">
            <?php include_once plugin_dir_path(dirname(__FILE__)) . 'partials/redco-optimizer-admin-modules-tab.php'; ?>
        </div>

        <!-- Tools Tab -->
        <div id="redco-tools-tab" class="redco-tab-content" style="display: none;">
            <div class="redco-tools-section">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Import / Export Settings', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <p class="redco-settings-description">
                            <?php esc_html_e('Export your Redco Optimizer settings to use on another site or to create a backup.', 'redco-optimizer'); ?>
                        </p>

                        <div class="redco-tools-row">
                            <div class="redco-tools-col">
                                <h4><?php esc_html_e('Export Settings', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Export your current settings as a JSON file.', 'redco-optimizer'); ?></p>
                                <button type="button" class="redco-button redco-button-primary redco-export-settings">
                                    <span class="dashicons dashicons-download"></span>
                                    <?php esc_html_e('Export Settings', 'redco-optimizer'); ?>
                                </button>
                            </div>

                            <div class="redco-tools-col">
                                <h4><?php esc_html_e('Import Settings', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Import settings from a JSON file.', 'redco-optimizer'); ?></p>
                                <div class="redco-import-container">
                                    <input type="file" id="redco-import-file" accept=".json" class="redco-import-file">
                                    <label for="redco-import-file" class="redco-button redco-button-secondary">
                                        <span class="dashicons dashicons-upload"></span>
                                        <?php esc_html_e('Choose File', 'redco-optimizer'); ?>
                                    </label>
                                    <button type="button" class="redco-button redco-button-primary redco-import-settings" disabled>
                                        <?php esc_html_e('Import Settings', 'redco-optimizer'); ?>
                                    </button>
                                </div>
                                <div id="redco-import-file-name" class="redco-import-file-name"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Rollback to Previous Version', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <p class="redco-settings-description">
                            <?php esc_html_e('If you\'re experiencing issues with the current version, you can rollback to a previous version.', 'redco-optimizer'); ?>
                        </p>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="redco-rollback-version"><?php esc_html_e('Select Version', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <select id="redco-rollback-version" class="redco-select">
                                    <option value=""><?php esc_html_e('Select a version', 'redco-optimizer'); ?></option>
                                    <?php
                                    // Get current version
                                    $current_version = REDCO_OPTIMIZER_VERSION;

                                    // Define previous versions (for demonstration)
                                    $previous_versions = array(
                                        '1.0.0',
                                        '0.9.5',
                                        '0.9.0',
                                    );

                                    foreach ($previous_versions as $version) {
                                        if (version_compare($version, $current_version, '<')) {
                                            echo '<option value="' . esc_attr($version) . '">' . esc_html($version) . '</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <div class="redco-rollback-warning">
                            <p>
                                <span class="dashicons dashicons-warning"></span>
                                <?php esc_html_e('Warning: Please backup your site before rolling back to a previous version.', 'redco-optimizer'); ?>
                            </p>
                        </div>

                        <button type="button" class="redco-button redco-button-danger redco-rollback-button" disabled>
                            <span class="dashicons dashicons-backup"></span>
                            <?php esc_html_e('Rollback to Selected Version', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </div>

                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Reset Settings', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content">
                        <p class="redco-settings-description">
                            <?php esc_html_e('Reset all Redco Optimizer settings to their default values.', 'redco-optimizer'); ?>
                        </p>

                        <div class="redco-reset-warning">
                            <p>
                                <span class="dashicons dashicons-warning"></span>
                                <?php esc_html_e('Warning: This action cannot be undone. All your settings will be reset to default values.', 'redco-optimizer'); ?>
                            </p>
                        </div>

                        <button type="button" class="redco-button redco-button-danger redco-reset-settings">
                            <span class="dashicons dashicons-trash"></span>
                            <?php esc_html_e('Reset All Settings', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </div>

                <?php
                // Include the error logs section
                include_once plugin_dir_path(dirname(__FILE__)) . 'partials/redco-optimizer-admin-error-logs.php';
                ?>
            </div>
        </div>

        <!-- Heartbeat Tab -->
        <div id="redco-heartbeat-tab" class="redco-tab-content" style="display: none;">
            <?php
            // Get heartbeat settings
            $heartbeat_settings = $redco_optimizer_admin->get_settings('heartbeat');
            ?>


            <form class="redco-form" method="post">
                <div class="redco-card active">
                    <div class="redco-card-header">
                        <h3><?php esc_html_e('Heartbeat', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="redco-card-content" style="display: block; visibility: visible;">
                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4><?php esc_html_e('Control Heartbeat', 'redco-optimizer'); ?></h4>
                                <p><?php esc_html_e('Control WordPress Heartbeat API to reduce server load and improve performance.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="control_heartbeat" id="control_heartbeat" <?php checked($heartbeat_settings['control_heartbeat'] ?? false, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-card redco-heartbeat-settings active" id="redco-heartbeat-settings">
                    <div class="redco-card-header">
                        <h3>
                            <?php esc_html_e('Reduce or disable Heartbeat activity', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="redco-card-content" style="display: block; visibility: visible;">
                        <p class="redco-settings-description">
                            <?php esc_html_e('Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes.', 'redco-optimizer'); ?><br>
                            <?php esc_html_e('Disabling Heartbeat entirely may break plugins and themes using this API.', 'redco-optimizer'); ?>
                        </p>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="heartbeat_behavior_backend"><?php esc_html_e('Behavior in backend', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <select name="heartbeat_behavior_backend" id="heartbeat_behavior_backend">
                                    <option value="default" <?php selected($heartbeat_settings['heartbeat_behavior_backend'] ?? 'default', 'default'); ?>><?php esc_html_e('Do not limit', 'redco-optimizer'); ?></option>
                                    <option value="reduce" <?php selected($heartbeat_settings['heartbeat_behavior_backend'] ?? 'default', 'reduce'); ?>><?php esc_html_e('Reduce activity', 'redco-optimizer'); ?></option>
                                    <option value="disable" <?php selected($heartbeat_settings['heartbeat_behavior_backend'] ?? 'default', 'disable'); ?>><?php esc_html_e('Disable', 'redco-optimizer'); ?></option>
                                </select>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="heartbeat_behavior_editor"><?php esc_html_e('Behavior in post editor', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <select name="heartbeat_behavior_editor" id="heartbeat_behavior_editor">
                                    <option value="default" <?php selected($heartbeat_settings['heartbeat_behavior_editor'] ?? 'default', 'default'); ?>><?php esc_html_e('Do not limit', 'redco-optimizer'); ?></option>
                                    <option value="reduce" <?php selected($heartbeat_settings['heartbeat_behavior_editor'] ?? 'default', 'reduce'); ?>><?php esc_html_e('Reduce activity', 'redco-optimizer'); ?></option>
                                    <option value="disable" <?php selected($heartbeat_settings['heartbeat_behavior_editor'] ?? 'default', 'disable'); ?>><?php esc_html_e('Disable', 'redco-optimizer'); ?></option>
                                </select>
                            </div>
                        </div>

                        <div class="redco-form-row">
                            <div class="redco-form-label">
                                <label for="heartbeat_behavior_frontend"><?php esc_html_e('Behavior in frontend', 'redco-optimizer'); ?></label>
                            </div>
                            <div class="redco-form-field">
                                <select name="heartbeat_behavior_frontend" id="heartbeat_behavior_frontend">
                                    <option value="default" <?php selected($heartbeat_settings['heartbeat_behavior_frontend'] ?? 'default', 'default'); ?>><?php esc_html_e('Do not limit', 'redco-optimizer'); ?></option>
                                    <option value="reduce" <?php selected($heartbeat_settings['heartbeat_behavior_frontend'] ?? 'default', 'reduce'); ?>><?php esc_html_e('Reduce activity', 'redco-optimizer'); ?></option>
                                    <option value="disable" <?php selected($heartbeat_settings['heartbeat_behavior_frontend'] ?? 'default', 'disable'); ?>><?php esc_html_e('Disable', 'redco-optimizer'); ?></option>
                                </select>
                            </div>
                        </div>

                        <div class="redco-toggle-row">
                            <div class="redco-toggle-info">
                                <h4>
                                    <?php esc_html_e('Disable Heartbeat in Customizer', 'redco-optimizer'); ?>
                                </h4>
                                <p><?php esc_html_e('Disable WordPress Heartbeat API in the Customizer to reduce server load.', 'redco-optimizer'); ?></p>
                            </div>
                            <div class="redco-toggle-control">
                                <label>
                                    <input type="checkbox" value="1" name="disable_heartbeat_customizer" id="disable_heartbeat_customizer" <?php checked($heartbeat_settings['disable_heartbeat_customizer'] ?? 0, 1); ?>>
                                    <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                                </label>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="redco-form-actions">
                    <button type="submit" class="redco-button redco-button-primary redco-save-settings" data-module="heartbeat">
                        <span class="dashicons dashicons-yes"></span>
                        <?php esc_html_e('Save Changes', 'redco-optimizer'); ?>
                    </button>
                </div>
            </form>
        </div>

        <!-- Site Health Inspector Tab -->
        <div id="redco-site-health-inspector-tab" class="redco-tab-content" style="display: none;">
            <?php
            $site_health_tab_path = plugin_dir_path(dirname(dirname(__FILE__))) . 'modules/site-health-inspector/admin/partials/site-health-inspector-tab.php';
            if (file_exists($site_health_tab_path)) {
                include($site_health_tab_path);
            } else {
                echo '<div class="redco-notice redco-notice-error">';
                echo '<p>' . esc_html__('Error: Site Health Inspector tab template not found.', 'redco-optimizer') . '</p>';
                echo '<p>' . esc_html__('Path: ', 'redco-optimizer') . esc_html($site_health_tab_path) . '</p>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- Add-Ons Tab -->
        <div id="redco-addons-tab" class="redco-tab-content" style="display: none;">
            <div class="redco-addons-container">
                <div class="redco-addons-header">
                    <div class="redco-addons-header-content">
                        <h2><?php esc_html_e('Add-Ons', 'redco-optimizer'); ?></h2>
                        <p><?php esc_html_e('Extend Redco Optimizer with these powerful add-ons to enhance your website performance even further.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
                <div class="redco-addons-section">
                    <h3><?php esc_html_e('Available Add-Ons', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('No add-ons are currently available.', 'redco-optimizer'); ?></p>
                </div>
            </div>
        </div>

        <!-- BRAND NEW Coming Soon Tab -->
        <div id="redco-coming-soon-tab" class="redco-tab-content" style="display: none;">
            <div class="redco-coming-soon-container" style="padding: 40px; text-align: center;">
                <h1 style="font-size: 2.5em; color: #00A66B; margin-bottom: 20px;">
                    🚀 <?php esc_html_e('Coming Soon', 'redco-optimizer'); ?>
                </h1>
                <p style="font-size: 1.2em; color: #666; margin-bottom: 40px;">
                    <?php esc_html_e('Exciting new features are in development!', 'redco-optimizer'); ?>
                </p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 40px;">
                    <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3 style="color: #00A66B;"><?php esc_html_e('Advanced Caching', 'redco-optimizer'); ?></h3>
                        <p><?php esc_html_e('Enhanced caching mechanisms for better performance.', 'redco-optimizer'); ?></p>
                        <span style="background: #e7f3ff; color: #0073aa; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                            <?php esc_html_e('In Development', 'redco-optimizer'); ?>
                        </span>
                    </div>

                    <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3 style="color: #00A66B;"><?php esc_html_e('Smart Optimization', 'redco-optimizer'); ?></h3>
                        <p><?php esc_html_e('AI-powered optimization recommendations.', 'redco-optimizer'); ?></p>
                        <span style="background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                            <?php esc_html_e('Planning', 'redco-optimizer'); ?>
                        </span>
                    </div>

                    <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3 style="color: #00A66B;"><?php esc_html_e('Performance Analytics', 'redco-optimizer'); ?></h3>
                        <p><?php esc_html_e('Detailed performance tracking and insights.', 'redco-optimizer'); ?></p>
                        <span style="background: #f8d7da; color: #721c24; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                            <?php esc_html_e('Research', 'redco-optimizer'); ?>
                        </span>
                    </div>
                </div>

                <div style="margin-top: 40px;">
                    <p style="font-size: 1.1em; margin-bottom: 20px;">
                        <?php esc_html_e('Stay tuned for updates!', 'redco-optimizer'); ?>
                    </p>
                    <button class="redco-button redco-button-primary" style="background: #00A66B; color: white; padding: 12px 24px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">
                        <?php esc_html_e('Get Notified', 'redco-optimizer'); ?>
                    </button>
                </div>
            </div>


        </div>

        <script>
        // EMERGENCY DEBUG - Check if Coming Soon tab exists and force it to show
        setTimeout(function() {
            console.log('🔍 EMERGENCY DEBUG: Checking Coming Soon tab...');

            var comingSoonTab = document.getElementById('redco-coming-soon-tab');
            console.log('Coming Soon tab element:', comingSoonTab);

            if (comingSoonTab) {
                console.log('✅ Coming Soon tab found in DOM');
                console.log('Current display:', comingSoonTab.style.display);
                console.log('Current visibility:', comingSoonTab.style.visibility);
                console.log('Inner HTML length:', comingSoonTab.innerHTML.length);
                console.log('First 200 chars:', comingSoonTab.innerHTML.substring(0, 200));

                // Force it to be visible
                comingSoonTab.style.display = 'block';
                comingSoonTab.style.visibility = 'visible';
                comingSoonTab.style.opacity = '1';
                comingSoonTab.style.position = 'relative';
                comingSoonTab.style.zIndex = '1';

                console.log('🔧 Forced Coming Soon tab to be visible');
            } else {
                console.log('❌ Coming Soon tab NOT found in DOM');
            }

            // Also check if switchTab function exists
            console.log('switchTab function exists:', typeof window.switchTab);

            // Try to manually switch to coming soon tab
            if (typeof window.switchTab === 'function') {
                console.log('🔄 Attempting to switch to Coming Soon tab...');
                window.switchTab('redco-coming-soon-tab');
            }
        }, 2000);
        </script>

        <!-- Help Tab -->
        <div id="redco-help-tab" class="redco-tab-content" style="display: none;">
            <div class="redco-section">
                <div class="redco-section-header">
                    <h2><?php esc_html_e('Help & Documentation', 'redco-optimizer'); ?></h2>
                    <p class="redco-section-description"><?php esc_html_e('Find answers to common questions and learn how to use Redco Optimizer effectively.', 'redco-optimizer'); ?></p>
                </div>

                <div class="redco-section-content">
                    <div class="redco-help-buttons" style="display: flex; justify-content: center; margin: 30px 0;">
                        <a href="<?php echo esc_url(Redco_Optimizer_Help_Page::get_help_page_url()); ?>" class="redco-button redco-button-primary redco-help-direct-link" target="_blank" style="font-size: 16px; padding: 15px 30px; background-color: #00A66B; color: white; text-decoration: none; border-radius: 4px; display: inline-flex; align-items: center;">
                            <span class="dashicons dashicons-book-alt" style="margin-right: 10px; font-size: 20px;"></span>
                            <?php esc_html_e('Open Help Documentation', 'redco-optimizer'); ?>
                        </a>
                    </div>

                    <script>
                    jQuery(document).ready(function($) {
                        // Prevent help links from triggering add-on settings modals
                        $('.redco-help-direct-link').on('click', function(e) {
                            e.stopPropagation();
                            // Let the default link behavior happen
                            return true;
                        });
                    });
                    </script>

                    <div class="redco-help-topics" style="display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 40px;">
                        <div class="redco-help-topic-card" style="width: 48%; background: #fff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); padding: 20px; margin-bottom: 20px;">
                            <h3 style="margin-top: 0; color: #00A66B;">
                                <span class="dashicons dashicons-admin-home" style="margin-right: 10px; color: #00A66B;"></span>
                                <?php esc_html_e('Getting Started', 'redco-optimizer'); ?>
                            </h3>
                            <p><?php esc_html_e('Learn the basics of Redco Optimizer and how to quickly set up your site for optimal performance.', 'redco-optimizer'); ?></p>
                            <a href="<?php echo esc_url(Redco_Optimizer_Help_Page::get_help_page_url()); ?>" class="redco-help-direct-link" target="_blank" style="color: #00A66B; text-decoration: none; font-weight: bold; display: inline-flex; align-items: center;">
                                <?php esc_html_e('Read More', 'redco-optimizer'); ?>
                                <span class="dashicons dashicons-arrow-right-alt" style="margin-left: 5px;"></span>
                            </a>
                        </div>

                        <div class="redco-help-topic-card" style="width: 48%; background: #fff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); padding: 20px; margin-bottom: 20px;">
                            <h3 style="margin-top: 0; color: #00A66B;">
                                <span class="dashicons dashicons-superhero" style="margin-right: 10px; color: #00A66B;"></span>
                                <?php esc_html_e('Caching', 'redco-optimizer'); ?>
                            </h3>
                            <p><?php esc_html_e('Understand how caching works and how to configure it for maximum performance.', 'redco-optimizer'); ?></p>
                            <a href="<?php echo esc_url(Redco_Optimizer_Help_Page::get_help_page_url()); ?>" class="redco-help-direct-link" target="_blank" style="color: #00A66B; text-decoration: none; font-weight: bold; display: inline-flex; align-items: center;">
                                <?php esc_html_e('Read More', 'redco-optimizer'); ?>
                                <span class="dashicons dashicons-arrow-right-alt" style="margin-left: 5px;"></span>
                            </a>
                        </div>

                        <div class="redco-help-topic-card" style="width: 48%; background: #fff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); padding: 20px; margin-bottom: 20px;">
                            <h3 style="margin-top: 0; color: #00A66B;">
                                <span class="dashicons dashicons-editor-help" style="margin-right: 10px; color: #00A66B;"></span>
                                <?php esc_html_e('FAQ', 'redco-optimizer'); ?>
                            </h3>
                            <p><?php esc_html_e('Find answers to frequently asked questions about Redco Optimizer.', 'redco-optimizer'); ?></p>
                            <a href="<?php echo esc_url(Redco_Optimizer_Help_Page::get_help_page_url()); ?>" class="redco-help-direct-link" target="_blank" style="color: #00A66B; text-decoration: none; font-weight: bold; display: inline-flex; align-items: center;">
                                <?php esc_html_e('Read More', 'redco-optimizer'); ?>
                                <span class="dashicons dashicons-arrow-right-alt" style="margin-left: 5px;"></span>
                            </a>
                        </div>

                        <div class="redco-help-topic-card" style="width: 48%; background: #fff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); padding: 20px; margin-bottom: 20px;">
                            <h3 style="margin-top: 0; color: #00A66B;">
                                <span class="dashicons dashicons-warning" style="margin-right: 10px; color: #00A66B;"></span>
                                <?php esc_html_e('Troubleshooting', 'redco-optimizer'); ?>
                            </h3>
                            <p><?php esc_html_e('Find solutions to common issues and learn how to troubleshoot problems.', 'redco-optimizer'); ?></p>
                            <a href="<?php echo esc_url(Redco_Optimizer_Help_Page::get_help_page_url()); ?>" class="redco-help-direct-link" target="_blank" style="color: #00A66B; text-decoration: none; font-weight: bold; display: inline-flex; align-items: center;">
                                <?php esc_html_e('Read More', 'redco-optimizer'); ?>
                                <span class="dashicons dashicons-arrow-right-alt" style="margin-left: 5px;"></span>
                            </a>
                        </div>
                    </div>

                    <div class="redco-help-support" style="background: #f8f9fa; border-radius: 4px; padding: 30px; margin-top: 20px; text-align: center;">
                        <h3 style="margin-top: 0;"><?php esc_html_e('Need More Help?', 'redco-optimizer'); ?></h3>
                        <p><?php esc_html_e('If you can\'t find the answer in our documentation, you can contact our support team.', 'redco-optimizer'); ?></p>
                        <a href="<?php echo esc_url(Redco_Optimizer_Help_Page::get_help_page_url()); ?>" class="redco-button redco-help-direct-link" target="_blank" style="background-color: #00A66B; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block; margin-top: 10px;">
                            <?php esc_html_e('View Full Documentation', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Debug Script -->
<script>
jQuery(document).ready(function($) {
    console.log('Redco Optimizer admin loaded');

    // Force help tab to be visible after a delay
    setTimeout(function() {
        console.log('Forcing help tab visibility');

        // Log the current state
        console.log('Help tab display:', $('#redco-help-tab').css('display'));
        console.log('Help tab visibility:', $('#redco-help-tab').css('visibility'));
        console.log('Help tab opacity:', $('#redco-help-tab').css('opacity'));

        // Force display
        $('#redco-help-tab').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
        $('#redco-help-tab .redco-section').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
        $('#redco-help-tab .redco-section-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
        $('#redco-help-tab .redco-help-direct-container').attr('style', 'display: flex !important; visibility: visible !important; opacity: 1 !important;');

        // Log the new state
        console.log('After forcing - Help tab display:', $('#redco-help-tab').css('display'));
        console.log('After forcing - Help tab visibility:', $('#redco-help-tab').css('visibility'));
        console.log('After forcing - Help tab opacity:', $('#redco-help-tab').css('opacity'));

        // Add click handler to help tab navigation item
        $('.redco-nav-item[data-tab="redco-help-tab"]').on('click', function() {
            console.log('Help tab clicked');

            // Force display after a short delay
            setTimeout(function() {
                $('#redco-help-tab').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-help-tab .redco-section').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-help-tab .redco-section-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
                $('#redco-help-tab .redco-help-direct-container').attr('style', 'display: flex !important; visibility: visible !important; opacity: 1 !important;');
            }, 100);
        });
    }, 1000);
});
</script>

<!-- File Exclusions Modal -->
<div id="redco-exclude-files-modal" class="redco-modal-overlay" style="display: none; visibility: hidden; opacity: 0;">
    <div class="redco-modal">
        <div class="redco-modal-header">
            <h2><?php esc_html_e('Exclude Files from Optimization', 'redco-optimizer'); ?></h2>
            <button type="button" class="redco-modal-close">&times;</button>
        </div>
        <div class="redco-modal-content">
            <p><?php esc_html_e('Enter the filenames or patterns of files you want to exclude from optimization. One per line.', 'redco-optimizer'); ?></p>
            <p><?php esc_html_e('Examples:', 'redco-optimizer'); ?></p>
            <ul>
                <li><code>jquery.js</code> - <?php esc_html_e('Excludes any file named jquery.js', 'redco-optimizer'); ?></li>
                <li><code>/wp-content/plugins/plugin-name/</code> - <?php esc_html_e('Excludes all files in this directory', 'redco-optimizer'); ?></li>
                <li><code>*.min.js</code> - <?php esc_html_e('Excludes all minified JS files', 'redco-optimizer'); ?></li>
            </ul>
            <textarea id="redco-exclusions-textarea" class="redco-textarea" placeholder="jquery.js
/wp-content/plugins/plugin-name/
*.min.js"></textarea>
        </div>
        <div class="redco-modal-footer">
            <button type="button" class="redco-button redco-button-secondary redco-modal-cancel"><?php esc_html_e('Cancel', 'redco-optimizer'); ?></button>
            <button type="button" class="redco-button redco-button-primary redco-modal-save"><?php esc_html_e('Save Exclusions', 'redco-optimizer'); ?></button>
        </div>
    </div>
</div>

<!-- JavaScript Defer Exclusions Modal -->
<div id="redco-js-defer-exclusions-modal" class="redco-modal-overlay" style="display: none; visibility: hidden; opacity: 0;">
    <div class="redco-modal">
        <div class="redco-modal-header">
            <h2><?php esc_html_e('JavaScript Defer Exclusions', 'redco-optimizer'); ?></h2>
            <button type="button" class="redco-modal-close">&times;</button>
        </div>
        <div class="redco-modal-content">
            <p><?php esc_html_e('Enter JavaScript files or patterns to exclude from defer. One per line.', 'redco-optimizer'); ?></p>
            <p><?php esc_html_e('Examples:', 'redco-optimizer'); ?></p>
            <ul>
                <li><code>jquery.js</code> - <?php esc_html_e('Excludes any file named jquery.js', 'redco-optimizer'); ?></li>
                <li><code>analytics.js</code> - <?php esc_html_e('Excludes any file named analytics.js', 'redco-optimizer'); ?></li>
                <li><code>tracking.js</code> - <?php esc_html_e('Excludes any file named tracking.js', 'redco-optimizer'); ?></li>
            </ul>
            <textarea id="redco-js-defer-exclusions-textarea" class="redco-textarea" placeholder="jquery.js
analytics.js
tracking.js"></textarea>
        </div>
        <div class="redco-modal-footer">
            <button type="button" class="redco-button redco-button-secondary redco-modal-cancel"><?php esc_html_e('Cancel', 'redco-optimizer'); ?></button>
            <button type="button" class="redco-button redco-button-primary redco-modal-save"><?php esc_html_e('Save Exclusions', 'redco-optimizer'); ?></button>
        </div>
    </div>
</div>

<?php
// Include the add-on settings modal template
include_once plugin_dir_path(dirname(__FILE__)) . 'partials/redco-optimizer-admin-addon-modal.php';
?>

