<?php
/**
 * The help documentation functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The help documentation functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for
 * the help documentation functionality.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Help {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version    The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Register hooks
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_styles' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
        add_action( 'redco_optimizer_admin_tabs', array( $this, 'add_help_tab' ), 90 );
        add_action( 'admin_menu', array( $this, 'register_help_page' ), 20 );
    }

    /**
     * Register the stylesheets for the help documentation.
     *
     * @since    1.0.0
     */
    public function enqueue_styles( $hook ) {
        // Only load on plugin pages
        if ( strpos( $hook, 'redco-optimizer' ) === false ) {
            return;
        }

        wp_enqueue_style( $this->plugin_name . '-help', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/css/redco-optimizer-admin-help.css', array(), $this->version, 'all' );
    }

    /**
     * Register the JavaScript for the help documentation.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts( $hook ) {
        // Only load on plugin pages
        if ( strpos( $hook, 'redco-optimizer' ) === false ) {
            return;
        }

        wp_enqueue_script( $this->plugin_name . '-help', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/redco-optimizer-admin-help.js', array( 'jquery' ), $this->version, false );

        // Enqueue the help tab fix script
        wp_enqueue_script( $this->plugin_name . '-help-tab-fix', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/redco-help-tab-fix.js', array( 'jquery', $this->plugin_name . '-help' ), $this->version, true );
    }

    /**
     * Add help tab to the admin tabs.
     *
     * @since    1.0.0
     * @param    array    $tabs    The admin tabs.
     * @return   array    The updated admin tabs.
     */
    public function add_help_tab( $tabs ) {
        $tabs['help'] = array(
            'tab' => 'redco-help-tab',
            'icon' => 'dashicons-editor-help',
            'text' => __( 'Help', 'redco-optimizer' ),
            'description' => __( 'Documentation and help resources', 'redco-optimizer' ),
            'url' => admin_url( 'admin.php?page=redco-optimizer-help' )
        );

        return $tabs;
    }

    /**
     * Register the help page.
     *
     * @since    1.0.0
     */
    public function register_help_page() {
        add_submenu_page(
            'redco-optimizer',
            __( 'Redco Optimizer Help', 'redco-optimizer' ),
            __( 'Help', 'redco-optimizer' ),
            'manage_options',
            'redco-optimizer-help',
            array( $this, 'display_help_page' )
        );
    }

    /**
     * Display the help page.
     *
     * @since    1.0.0
     */
    public function display_help_page() {
        include plugin_dir_path( dirname( __FILE__ ) ) . 'admin/redco-help-page.php';
    }

    /**
     * Display the help tab content.
     *
     * @since    1.0.0
     */
    public static function display_help_tab() {
        include plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/redco-optimizer-admin-help.php';
    }

    /**
     * Check if help documentation files exist.
     *
     * @since    1.0.0
     * @return   bool    True if help files exist, false otherwise.
     */
    public static function help_files_exist() {
        $help_dir = plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/help';
        return file_exists( $help_dir ) && is_dir( $help_dir );
    }

    /**
     * Create help documentation directory if it doesn't exist.
     *
     * @since    1.0.0
     */
    public static function create_help_directory() {
        $help_dir = plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/help';
        if ( ! file_exists( $help_dir ) ) {
            wp_mkdir_p( $help_dir );
        }
    }
}
