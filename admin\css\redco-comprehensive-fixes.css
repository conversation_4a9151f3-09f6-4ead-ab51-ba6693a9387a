/**
 * Redco Optimizer - Comprehensive UI Fixes
 * Fixes all consistency issues across the plugin
 */

/* ===================================
   FORCE GRID LAYOUT - HIGHEST PRIORITY
   =================================== */

/* Force the exclusions grid to display properly */
.redco-exclusions-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 20px !important;
    margin: 20px 0 !important;
    padding: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
}

.redco-exclusions-grid.hidden {
    display: none !important;
}

.redco-exclusion-section {
    background: #f8f9fa !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 16px !important;
    margin: 0 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.redco-exclusion-section h4 {
    margin: 0 0 12px 0 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #374151 !important;
}

.redco-checkbox-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
}

.redco-checkbox-item {
    display: flex !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

.redco-checkbox-item input[type="checkbox"] {
    margin: 0 8px 0 0 !important;
    flex-shrink: 0 !important;
}

.redco-checkbox-text {
    font-size: 13px !important;
    color: #6b7280 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
}

/* ===================================
   REMOVE ALL BADGES COMPLETELY
   =================================== */

/* Hide ALL badge types across the entire plugin */
.redco-premium-badge,
.redco-coming-soon-badge,
.redco-premium-only-badge,
.redco-addon-badge,
.redco-addon-badge-coming-soon,
span[class*="badge"],
span[class*="premium"],
span[class*="coming-soon"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* ===================================
   CHECKBOX CONSISTENCY
   =================================== */

/* Universal checkbox styling */
input[type="checkbox"] {
    width: 18px !important;
    height: 18px !important;
    border: 2px solid #d1d5db !important;
    border-radius: 3px !important;
    background: #ffffff !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin: 0 8px 0 0 !important;
    position: relative !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    vertical-align: middle !important;
    display: inline-block !important;
    flex-shrink: 0 !important;
}

input[type="checkbox"]:checked {
    background: #00a66b !important;
    border-color: #00a66b !important;
    box-shadow: 0 0 0 2px rgba(0, 166, 107, 0.1) !important;
}

input[type="checkbox"]:checked::before {
    content: '✓' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    color: #ffffff !important;
    font-size: 11px !important;
    font-weight: 700 !important;
    line-height: 1 !important;
}

/* ===================================
   FORM FIELD CONSISTENCY
   =================================== */

.redco-form-field {
    width: 100% !important;
    max-width: 100% !important;
}

.redco-input,
.redco-textarea,
.redco-select {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    padding: 10px 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    font-size: 14px !important;
}

/* ===================================
   SPACING AND MARGINS
   =================================== */

/* Page bottom margin */
.redco-layout .wrap {
    padding-bottom: 40px !important;
}

/* Form actions spacing */
.redco-form-actions {
    margin: 30px 0 40px 0 !important;
    padding: 20px 0 !important;
    border-top: 1px solid #e5e7eb !important;
}

/* Tab content spacing */
.redco-tab-content {
    padding-bottom: 40px !important;
    min-height: calc(100vh - 200px) !important;
}

/* Card spacing */
.redco-card {
    margin-bottom: 20px !important;
}

.redco-card:last-child {
    margin-bottom: 40px !important;
}

/* ===================================
   LABEL ALIGNMENT
   =================================== */

/* Consistent label alignment */
label {
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    margin-bottom: 8px !important;
}

.redco-checkbox-label,
.redco-checkbox-text {
    margin: 0 !important;
    line-height: 1.4 !important;
    font-size: 14px !important;
    color: #374151 !important;
}

/* Toggle row alignment */
.redco-toggle-row {
    display: flex !important;
    align-items: flex-start !important;
    justify-content: space-between !important;
    margin-bottom: 16px !important;
    gap: 20px !important;
}

.redco-toggle-info {
    flex: 1 !important;
}

.redco-toggle-control {
    flex-shrink: 0 !important;
    display: flex !important;
    align-items: center !important;
}

/* ===================================
   RESPONSIVE GRID FIXES
   =================================== */

@media (max-width: 768px) {
    .redco-exclusions-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
    
    .redco-toggle-row {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }
    
    .redco-toggle-control {
        width: 100% !important;
        justify-content: flex-start !important;
    }
}

/* ===================================
   FORCE VISIBILITY
   =================================== */

/* Ensure grid is always visible when not hidden */
.redco-exclusions-grid:not(.hidden) {
    display: grid !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Override any conflicting display rules */
.redco-layout * {
    box-sizing: border-box !important;
}
