<?php

/**
 * The Delay JavaScript functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The Delay JavaScript functionality of the plugin.
 *
 * Defines the functionality for delaying JavaScript execution.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_Delay_JS {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for delay JS.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for delay JS.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
    }

    /**
     * Get delay JS settings.
     *
     * @since    1.0.0
     * @return   array    The delay JS settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_file_optimization_settings', array() );
        
        // Default settings
        $defaults = array(
            'delay_js' => 0,
            'delay_js_exclusions' => '',
            'delay_js_safe_mode' => 0,
        );
        
        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize delay JS.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if delay JS is enabled
        if ( ! $this->settings['delay_js'] ) {
            return;
        }

        // Add filter to process HTML
        add_filter( 'redco_buffer', array( $this, 'process_html' ), 30 );
    }

    /**
     * Process HTML to delay JavaScript execution.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    public function process_html( $html ) {
        // Don't process if user is logged in
        if ( is_user_logged_in() ) {
            return $html;
        }
        
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }
        
        // Check for IE11
        if ( $this->is_ie11() ) {
            return $html;
        }
        
        // Process script tags
        $html = $this->process_scripts( $html );
        
        // Add delay script
        $html = $this->add_delay_script( $html );
        
        return $html;
    }

    /**
     * Check if the browser is IE11.
     *
     * @since    1.0.0
     * @return   bool    True if the browser is IE11, false otherwise.
     */
    private function is_ie11() {
        if ( ! isset( $_SERVER['HTTP_USER_AGENT'] ) ) {
            return false;
        }
        
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        
        return ( strpos( $user_agent, 'Trident/7.0' ) !== false && strpos( $user_agent, 'rv:11.0' ) !== false );
    }

    /**
     * Process script tags to delay execution.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    private function process_scripts( $html ) {
        // Get exclusions
        $exclusions = $this->get_exclusions();
        
        // Process script tags
        $html = preg_replace_callback(
            '/<script\b([^>]*)>(.*?)<\/script>/is',
            function( $matches ) use ( $exclusions ) {
                $tag_attributes = $matches[1];
                $script_content = $matches[2];
                
                // Skip if script has nowprocket attribute
                if ( strpos( $tag_attributes, 'nowprocket' ) !== false ) {
                    return $matches[0];
                }
                
                // Skip if script is excluded
                foreach ( $exclusions as $exclusion ) {
                    if ( strpos( $tag_attributes, $exclusion ) !== false || strpos( $script_content, $exclusion ) !== false ) {
                        return $matches[0];
                    }
                }
                
                // Get src attribute
                $src = '';
                if ( preg_match( '/src=[\'"](.*?)[\'"]/i', $tag_attributes, $src_matches ) ) {
                    $src = $src_matches[1];
                }
                
                // Get type attribute
                $type = '';
                if ( preg_match( '/type=[\'"](.*?)[\'"]/i', $tag_attributes, $type_matches ) ) {
                    $type = $type_matches[1];
                    
                    // Skip if type is not JavaScript
                    if ( $type !== 'text/javascript' && $type !== 'application/javascript' ) {
                        return $matches[0];
                    }
                }
                
                // Create new tag attributes
                $new_attributes = str_replace( 'src=', 'data-rocket-src=', $tag_attributes );
                $new_attributes = preg_replace( '/type=[\'"](.*?)[\'"]/i', 'data-rocket-type="$1"', $new_attributes );
                $new_attributes = ' type="rocketlazyloadscript"' . $new_attributes;
                
                // Add preload for external scripts
                if ( ! empty( $src ) ) {
                    $preload = '<link rel="preload" as="script" href="' . esc_url( $src ) . '" data-rocket-async="true">';
                    $html = $preload . '<script' . $new_attributes . '>' . $script_content . '</script>';
                } else {
                    $html = '<script' . $new_attributes . '>' . $script_content . '</script>';
                }
                
                return $html;
            },
            $html
        );
        
        return $html;
    }

    /**
     * Get exclusions for delay JS.
     *
     * @since    1.0.0
     * @return   array    The exclusions for delay JS.
     */
    private function get_exclusions() {
        $exclusions = array();
        
        // Add default exclusions
        $exclusions[] = 'wp-embed.min.js';
        
        // Add user exclusions
        if ( ! empty( $this->settings['delay_js_exclusions'] ) ) {
            $user_exclusions = explode( "\n", $this->settings['delay_js_exclusions'] );
            $exclusions = array_merge( $exclusions, array_map( 'trim', $user_exclusions ) );
        }
        
        // Add safe mode exclusions
        if ( $this->settings['delay_js_safe_mode'] ) {
            $safe_mode_exclusions = array(
                '/wp-content/',
                '/wp-includes/',
                'jquery',
                'jquery-migrate',
                'js-before',
                'js-after',
            );
            
            $exclusions = array_merge( $exclusions, $safe_mode_exclusions );
        }
        
        return array_unique( $exclusions );
    }

    /**
     * Add delay script to HTML.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The HTML content with delay script.
     */
    private function add_delay_script( $html ) {
        $delay_script = $this->get_delay_script();
        
        // Add delay script before </body>
        $html = str_replace( '</body>', $delay_script . '</body>', $html );
        
        return $html;
    }

    /**
     * Get delay script.
     *
     * @since    1.0.0
     * @return   string    The delay script.
     */
    private function get_delay_script() {
        $script = <<<EOT
<script>
(function() {
    'use strict';
    var redcoDelayEventListeners = [];
    var redcoLoadEvents = ['keydown', 'mouseover', 'touchmove', 'touchstart', 'wheel'];
    var redcoDelayedScripts = {
        normal: [],
        defer: [],
        async: []
    };

    function redcoTriggerDOMContentLoaded() {
        document.dispatchEvent(new Event('redco-DOMContentLoaded'));
    }

    function redcoTriggerWindowLoad() {
        window.dispatchEvent(new Event('redco-load'));
    }

    function redcoAddEventListener(element, eventName, listener) {
        element.addEventListener(eventName, listener);
        redcoDelayEventListeners.push({
            element: element,
            eventName: eventName,
            listener: listener
        });
    }

    function redcoLoadDelayedScripts() {
        document.querySelectorAll('script[type="rocketlazyloadscript"]').forEach(function(script) {
            script.setAttribute('data-rocket-status', 'loading');
            var src = script.getAttribute('data-rocket-src');
            var scriptType = script.getAttribute('data-rocket-type');
            
            if (src) {
                var tag = document.createElement('script');
                tag.src = src;
                if (scriptType) {
                    tag.type = scriptType;
                }
                tag.onload = function() {
                    script.setAttribute('data-rocket-status', 'executed');
                };
                tag.onerror = function() {
                    script.setAttribute('data-rocket-status', 'failed');
                };
                script.parentNode.replaceChild(tag, script);
            } else {
                var textContent = script.textContent;
                var tag = document.createElement('script');
                if (scriptType) {
                    tag.type = scriptType;
                }
                tag.textContent = textContent;
                tag.onload = function() {
                    script.setAttribute('data-rocket-status', 'executed');
                };
                tag.onerror = function() {
                    script.setAttribute('data-rocket-status', 'failed');
                };
                script.parentNode.replaceChild(tag, script);
            }
        });
        
        redcoTriggerDOMContentLoaded();
        redcoTriggerWindowLoad();
    }

    function redcoRemoveEventListeners() {
        redcoDelayEventListeners.forEach(function(eventInfo) {
            eventInfo.element.removeEventListener(eventInfo.eventName, eventInfo.listener);
        });
        redcoDelayEventListeners = [];
    }

    function redcoUserInteraction(e) {
        redcoRemoveEventListeners();
        redcoLoadDelayedScripts();
    }

    redcoLoadEvents.forEach(function(eventName) {
        redcoAddEventListener(document, eventName, redcoUserInteraction);
    });

    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            redcoUserInteraction();
        }
    });
})();
</script>
EOT;

        return $script;
    }
}
