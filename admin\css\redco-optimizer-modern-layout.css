/* ===================================
   REDCO OPTIMIZER - MODERN LAYOUT
   Professional, Clean & Accessible Design
   =================================== */

/* Reset and Base Styles */
.redco-layout * {
    box-sizing: border-box;
}

.redco-layout {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: #1e1e1e;
    background: #f8f9fa;
}

/* Top Navigation - Modern Design */
.redco-top-nav {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e1e5e9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 32px;
    z-index: 1000;
    margin: 0 0 30px 0;
}

.redco-nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
    max-width: 1600px;
    margin: 0 auto;
    min-height: 70px;
}

/* Logo - Enhanced */
.redco-nav-logo {
    flex-shrink: 0;
    margin-right: 40px;
}

.redco-logo-text {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.redco-logo-part-1 {
    color: #00a66b;
    text-shadow: 0 1px 2px rgba(0, 166, 107, 0.1);
}

.redco-logo-part-2 {
    color: #2c3e50;
}

.redco-version {
    font-size: 11px;
    color: #6c757d;
    background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    border: 1px solid #dee2e6;
}

/* Navigation Items - Modern Style */
.redco-nav-items {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.redco-nav-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 18px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    border: 1px solid transparent;
}

.redco-nav-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #00a66b;
    border-color: #e1e5e9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.redco-nav-item.active {
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%);
    color: #ffffff;
    border-color: #00925f;
    box-shadow: 0 4px 16px rgba(0, 166, 107, 0.3);
}

.redco-nav-item .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Dropdown - Enhanced */
.redco-nav-dropdown {
    position: relative;
}

.redco-nav-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 18px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    white-space: nowrap;
    border: 1px solid transparent;
}

.redco-nav-dropdown-toggle:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #00a66b;
    border-color: #e1e5e9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.redco-nav-dropdown-toggle .dashicons-arrow-down-alt2 {
    font-size: 16px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.redco-nav-dropdown:hover .redco-nav-dropdown-toggle .dashicons-arrow-down-alt2 {
    transform: rotate(180deg);
}

.redco-nav-dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    min-width: 220px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
    padding: 8px;
    backdrop-filter: blur(10px);
}

.redco-nav-dropdown:hover .redco-nav-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.redco-nav-dropdown-menu .redco-nav-item {
    padding: 12px 16px;
    margin: 2px 0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    border: none;
}

.redco-nav-dropdown-menu .redco-nav-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #00a66b;
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Save Button - Premium Style */
.redco-nav-save {
    flex-shrink: 0;
    margin-left: 30px;
}

.redco-nav-save .redco-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-nav-save .redco-button-primary {
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%);
    color: #ffffff;
    border: 1px solid #00925f;
    box-shadow: 0 4px 16px rgba(0, 166, 107, 0.3);
}

.redco-nav-save .redco-button-primary:hover {
    background: linear-gradient(135deg, #00925f 0%, #007a51 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 166, 107, 0.4);
}

.redco-nav-save .redco-button-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 166, 107, 0.3);
}

/* Main Content - Modern Layout */
.redco-main-content-full {
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Page Header - Enhanced */
.redco-page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 30px 40px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.redco-page-header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.redco-page-header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(0, 166, 107, 0.3);
}

.redco-page-header-icon .dashicons {
    color: #ffffff;
    font-size: 28px;
    width: 28px;
    height: 28px;
}

.redco-page-header-content h1 {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
    letter-spacing: -0.5px;
}

.redco-page-header-description {
    font-size: 16px;
    color: #6c757d;
    margin: 0;
    font-weight: 400;
}

.redco-page-header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Header Account Info */
.redco-header-account-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    min-width: 280px;
}

.redco-account-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.redco-account-section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.redco-refresh-info {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6c757d;
    cursor: pointer;
    font-size: 12px;
    padding: 6px 10px;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.redco-refresh-info:hover {
    background: rgba(0, 166, 107, 0.1);
    color: #00a66b;
}

.redco-refresh-info .dashicons {
    font-size: 14px;
}

.redco-refresh-info.loading {
    pointer-events: none;
    opacity: 0.7;
}

.redco-spin {
    animation: redco-spin 1s linear infinite;
}

@keyframes redco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.redco-account-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.redco-account-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.redco-account-label {
    font-weight: 500;
    color: #6c757d;
    min-width: 50px;
}

.redco-account-value {
    font-weight: 600;
    color: #2c3e50;
}

.redco-license-free {
    color: #6c757d;
    background: #f8f9fa;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.redco-status-active {
    color: #28a745;
    font-weight: 600;
}

.redco-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 8px;
}

.redco-view-account-button {
    font-size: 12px;
    padding: 8px 14px;
    font-weight: 500;
    flex: 1;
}

.redco-save-button {
    font-size: 12px;
    padding: 8px 14px;
    font-weight: 600;
    flex: 1;
}

.redco-header-action-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 18px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;
}

.redco-header-action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Cards - Modern Design */
.redco-card {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.redco-card:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.redco-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 24px 30px;
    border-bottom: 1px solid #e1e5e9;
}

.redco-card-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    letter-spacing: -0.3px;
}

.redco-card-content {
    padding: 30px;
}

/* Buttons - Enhanced */
.redco-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;
    text-align: center;
    justify-content: center;
}

.redco-button-primary {
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%);
    color: #ffffff;
    border-color: #00925f;
    box-shadow: 0 2px 8px rgba(0, 166, 107, 0.2);
}

.redco-button-primary:hover {
    background: linear-gradient(135deg, #00925f 0%, #007a51 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 166, 107, 0.3);
}

.redco-button-secondary {
    background: #ffffff;
    color: #495057;
    border-color: #dee2e6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.redco-button-secondary:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Form Elements - Modern */
.redco-form-group {
    margin-bottom: 24px;
}

.redco-form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.redco-form-input,
.redco-form-select,
.redco-form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
}

.redco-form-input:focus,
.redco-form-select:focus,
.redco-form-textarea:focus {
    outline: none;
    border-color: #00a66b;
    box-shadow: 0 0 0 3px rgba(0, 166, 107, 0.1);
}

/* Checkboxes - Modern Style */
.redco-checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
}

.redco-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    margin-top: 2px;
}

.redco-checkbox:checked {
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%);
    border-color: #00925f;
}

.redco-checkbox-label {
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1400px) {
    .redco-nav-wrapper,
    .redco-main-content-full {
        padding: 0 30px;
    }
}

@media (max-width: 1200px) {
    .redco-nav-wrapper,
    .redco-main-content-full {
        padding: 0 25px;
    }

    .redco-page-header {
        padding: 25px 30px;
    }
}

@media (max-width: 768px) {
    .redco-nav-wrapper {
        flex-direction: column;
        padding: 15px 20px;
        min-height: auto;
        gap: 15px;
    }

    .redco-nav-logo {
        margin-right: 0;
        margin-bottom: 0;
    }

    .redco-nav-items {
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
    }

    .redco-nav-save {
        margin-left: 0;
        width: 100%;
    }

    .redco-nav-save .redco-button {
        width: 100%;
    }

    .redco-main-content-full {
        padding: 0 20px;
    }

    .redco-page-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 20px;
    }

    .redco-page-header-right {
        width: 100%;
        justify-content: center;
        flex-direction: column;
        gap: 16px;
    }

    .redco-header-account-info {
        min-width: auto;
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
    }

    .redco-account-section-header {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .redco-account-details {
        align-items: center;
        text-align: center;
    }

    .redco-header-actions {
        flex-direction: column;
        gap: 8px;
    }

    .redco-view-account-button,
    .redco-save-button {
        width: 100%;
        justify-content: center;
        flex: none;
    }

    .redco-card-content {
        padding: 20px;
    }

    /* Performance Score Responsive */
    .redco-performance-card .redco-card-content {
        padding: 30px 20px;
    }

    .redco-score-circle {
        width: 120px;
        height: 120px;
        margin-bottom: 20px;
    }

    .redco-score-svg {
        width: 100px;
        height: 100px;
    }

    .redco-score-text {
        font-size: 28px;
    }

    .redco-score-percentage {
        font-size: 14px;
    }
}

/* Dashboard Specific Styles */
.redco-dashboard-columns {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-top: 30px;
}

.redco-column-main,
.redco-column-side {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Success Message - Modern */
.redco-success-message {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #b8dacc;
    border-radius: 12px;
    padding: 24px 30px;
    margin-bottom: 30px;
    position: relative;
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.1);
}

.redco-success-icon {
    color: #155724;
    font-size: 24px;
    margin-right: 16px;
    float: left;
}

.redco-success-content h2 {
    color: #155724;
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.redco-success-content p {
    color: #155724;
    margin: 0 0 8px 0;
    line-height: 1.6;
}

.redco-close-message {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    color: #155724;
    cursor: pointer;
    font-size: 18px;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.redco-close-message:hover {
    background: rgba(21, 87, 36, 0.1);
}

/* Account Section */
.redco-account-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.redco-section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.redco-section-title h2 {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.redco-refresh-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.redco-refresh-info:hover {
    background: #f8f9fa;
    color: #00a66b;
}

.redco-account-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.redco-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.redco-info-label {
    font-weight: 500;
    color: #495057;
}

.redco-info-value {
    font-weight: 600;
}

.redco-license-free {
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Quick Actions */
.redco-quick-actions-card .redco-card-content {
    padding: 0;
}

.redco-action-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 30px;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
}

.redco-action-item:last-child {
    border-bottom: none;
}

.redco-action-item:hover {
    background: #f8f9fa;
}

.redco-action-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.redco-action-info p {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
}

/* Performance Score Circle - Enhanced */
.redco-performance-card .redco-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 40px 30px;
}

.redco-score-circle {
    position: relative;
    width: 140px;
    height: 140px;
    margin: 0 auto 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-score-svg {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.redco-score-circle-bg {
    fill: none;
    stroke: #f0f0f1;
    stroke-width: 3.5;
}

.redco-score-circle-fill {
    fill: none;
    stroke-width: 3.5;
    stroke-linecap: round;
    transition: stroke-dasharray 1s ease-in-out;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
}

.redco-score-good .redco-score-circle-fill {
    stroke: #28a745;
}

.redco-score-ok .redco-score-circle-fill {
    stroke: #ffc107;
}

.redco-score-poor .redco-score-circle-fill {
    stroke: #dc3545;
}

.redco-score-text-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-90deg);
    text-align: center;
    z-index: 10;
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 3px;
}

.redco-score-text {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.redco-score-percentage {
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    line-height: 1;
}

.redco-score-info {
    text-align: center;
    max-width: 280px;
}

.redco-score-info p {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
}

.redco-text-link {
    color: #00a66b;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.redco-text-link:hover {
    color: #00925f;
    text-decoration: underline;
}

/* Stats Grid */
.redco-stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.redco-stat {
    text-align: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.redco-stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #00a66b;
    margin-bottom: 4px;
}

.redco-stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Status Grid */
.redco-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

.redco-status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.redco-status-item:hover {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.redco-status-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.redco-status-icon.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.redco-status-icon.inactive {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.redco-status-icon.premium {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.redco-status-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.redco-status-info p {
    font-size: 12px;
    color: #6c757d;
    margin: 0;
}

/* Responsive Dashboard */
@media (max-width: 1200px) {
    .redco-dashboard-columns {
        grid-template-columns: 1fr;
    }

    .redco-stats-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .redco-action-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        text-align: left;
    }

    .redco-action-button {
        width: 100%;
    }

    .redco-action-button .redco-button {
        width: 100%;
    }

    .redco-stats-row {
        grid-template-columns: 1fr;
    }

    .redco-status-grid {
        grid-template-columns: 1fr;
    }
}
