/**
 * CSS to hide premium elements in Redco Optimizer
 *
 * This file contains styles to hide the "Upgrade to Premium" buttons
 * and related premium elements from the Redco Optimizer interface.
 */

/* Hide Premium tab in sidebar navigation */
.redco-nav-item[data-tab="redco-premium-tab"] {
    display: none !important;
}

/* Hide Upgrade to Premium button in sidebar footer */
.redco-sidebar-footer {
    display: none !important;
}

/* Hide Upgrade to Premium button in page header */
.redco-header-action-button[data-tab="redco-premium-tab"] {
    display: none !important;
}

/* Hide premium-related buttons in modules */
.redco-button-premium.redco-premium-tab-link,
.redco-button-premium[data-tab="redco-premium-tab"] {
    display: none !important;
}

/* Hide premium notices in add-ons */
.redco-premium-notice {
    display: none !important;
}

/* Hide premium badges */
.redco-premium-badge,
.redco-premium-badge-below,
.redco-premium-tag {
    display: none !important;
}

/* Hide premium status items in the Optimization Status section */
.redco-status-item .redco-status-icon.premium {
    display: none !important;
}

/* Hide any status item that has a premium class */
.redco-status-item.premium-feature,
.redco-status-item.premium-only {
    display: none !important;
}

/* Target specific premium features by their IDs or classes */
#premium-feature-item,
.redco-premium-feature-status {
    display: none !important;
}

/* Hide any status item that mentions premium in its text */
.redco-status-item .redco-status-info p.premium-text {
    display: none !important;
}

/* Hide premium-related content in the dashboard */
#redco-premium-tab {
    display: none !important;
}

/* Hide premium-related CTA sections */
.redco-premium-cta,
.redco-premium-hero {
    display: none !important;
}

/* Hide View My Account button that links to premium page */
.redco-view-account {
    display: none !important;
}
