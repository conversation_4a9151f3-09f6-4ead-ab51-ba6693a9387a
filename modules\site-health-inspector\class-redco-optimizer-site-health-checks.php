<?php
/**
 * Site Health Inspector Checks
 *
 * @link       https://redco.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/site-health-inspector
 */

/**
 * Site Health Inspector Checks
 *
 * This class contains all the diagnostic checks for the Site Health Inspector module.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/site-health-inspector
 * <AUTHOR> <<EMAIL>>
 */
trait Redco_Optimizer_Site_Health_Checks {

    /**
     * Add an issue to the scan results.
     *
     * @since    1.0.0
     * @param    string    $category     The issue category.
     * @param    string    $id           The issue ID.
     * @param    string    $title        The issue title.
     * @param    string    $description  The issue description.
     * @param    string    $severity     The issue severity (critical, warning, info).
     * @param    array     $data         Additional data about the issue.
     */
    private function add_issue($category, $id, $title, $description, $severity, $data = array()) {
        $issue = array(
            'id' => $id,
            'title' => $title,
            'description' => $description,
            'severity' => $severity,
            'data' => $data,
            'timestamp' => current_time('timestamp')
        );

        $this->scan_results['categories'][$category][] = $issue;
        $this->issues[] = $issue;
    }

    /**
     * Read the last n lines of a file.
     *
     * @since    1.0.0
     * @param    string    $file_path    The file path.
     * @param    int       $lines        The number of lines to read.
     * @return   array     The last n lines of the file.
     */
    private function tail_file($file_path, $lines = 10) {
        $result = array();

        if (!file_exists($file_path)) {
            return $result;
        }

        $file = new SplFileObject($file_path, 'r');
        $file->seek(PHP_INT_MAX);
        $last_line = $file->key();

        $lines = min($lines, $last_line);
        $start = $last_line - $lines;

        if ($start < 0) {
            $start = 0;
        }

        $file->seek($start);

        while (!$file->eof()) {
            $line = $file->current();
            $file->next();

            if (trim($line) !== '') {
                $result[] = trim($line);
            }
        }

        return $result;
    }

    /**
     * Check plugins for issues.
     *
     * @since    1.0.0
     */
    private function check_plugins() {
        // Get all plugins
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $plugins = get_plugins();
        $active_plugins = get_option('active_plugins', array());

        // Check for inactive plugins
        $inactive_plugins = array_diff(array_keys($plugins), $active_plugins);
        if (count($inactive_plugins) > 5) {
            $this->add_issue(
                'plugins',
                'inactive_plugins',
                __('Too many inactive plugins', 'redco-optimizer'),
                __('You have ' . count($inactive_plugins) . ' inactive plugins. Consider removing plugins you don\'t use to improve security and reduce clutter.', 'redco-optimizer'),
                'warning',
                array(
                    'count' => count($inactive_plugins),
                    'plugins' => $inactive_plugins
                )
            );
        }

        // Check for outdated plugins
        $update_plugins = get_site_transient('update_plugins');
        if ($update_plugins && !empty($update_plugins->response)) {
            $outdated_plugins = array();

            foreach ($update_plugins->response as $plugin_file => $plugin_data) {
                if (in_array($plugin_file, $active_plugins)) {
                    $outdated_plugins[] = array(
                        'file' => $plugin_file,
                        'name' => $plugins[$plugin_file]['Name'],
                        'current_version' => $plugins[$plugin_file]['Version'],
                        'new_version' => $plugin_data->new_version
                    );
                }
            }

            if (!empty($outdated_plugins)) {
                $this->add_issue(
                    'plugins',
                    'outdated_plugins',
                    __('Outdated plugins detected', 'redco-optimizer'),
                    __('You have ' . count($outdated_plugins) . ' outdated plugins. Outdated plugins can contain security vulnerabilities.', 'redco-optimizer'),
                    'critical',
                    array(
                        'count' => count($outdated_plugins),
                        'plugins' => $outdated_plugins
                    )
                );
            }
        }

        // Check for plugin errors
        $this->check_plugin_errors();
    }

    /**
     * Check for plugin errors in error logs.
     *
     * @since    1.0.0
     */
    private function check_plugin_errors() {
        // Check for PHP error log
        $error_log_path = ini_get('error_log');

        if (!empty($error_log_path) && file_exists($error_log_path)) {
            // Read the last 100 lines of the error log
            $errors = $this->tail_file($error_log_path, 100);

            if (!empty($errors)) {
                $plugin_errors = array();
                $plugin_dirs = wp_normalize_path(WP_PLUGIN_DIR);

                foreach ($errors as $error) {
                    // Check if the error is related to a plugin
                    if (strpos($error, $plugin_dirs) !== false) {
                        // Extract plugin name from path
                        preg_match('/wp-content\/plugins\/([^\/]+)/', $error, $matches);

                        if (!empty($matches[1])) {
                            $plugin_name = $matches[1];

                            if (!isset($plugin_errors[$plugin_name])) {
                                $plugin_errors[$plugin_name] = array(
                                    'name' => $plugin_name,
                                    'errors' => array()
                                );
                            }

                            $plugin_errors[$plugin_name]['errors'][] = $error;
                        }
                    }
                }

                if (!empty($plugin_errors)) {
                    foreach ($plugin_errors as $plugin_name => $data) {
                        $this->add_issue(
                            'plugins',
                            'plugin_error_' . $plugin_name,
                            sprintf(__('Errors detected in plugin: %s', 'redco-optimizer'), $plugin_name),
                            sprintf(__('The plugin "%s" is generating PHP errors. This can affect site performance and functionality.', 'redco-optimizer'), $plugin_name),
                            'critical',
                            array(
                                'plugin' => $plugin_name,
                                'errors' => array_slice($data['errors'], 0, 5) // Limit to 5 errors
                            )
                        );
                    }
                }
            }
        }

        // Check for WordPress debug.log
        $wp_debug_log = WP_CONTENT_DIR . '/debug.log';

        if (file_exists($wp_debug_log)) {
            // Read the last 100 lines of the debug log
            $debug_errors = $this->tail_file($wp_debug_log, 100);

            if (!empty($debug_errors)) {
                $plugin_errors = array();
                $plugin_dirs = wp_normalize_path(WP_PLUGIN_DIR);

                foreach ($debug_errors as $error) {
                    // Check if the error is related to a plugin
                    if (strpos($error, $plugin_dirs) !== false) {
                        // Extract plugin name from path
                        preg_match('/wp-content\/plugins\/([^\/]+)/', $error, $matches);

                        if (!empty($matches[1])) {
                            $plugin_name = $matches[1];

                            if (!isset($plugin_errors[$plugin_name])) {
                                $plugin_errors[$plugin_name] = array(
                                    'name' => $plugin_name,
                                    'errors' => array()
                                );
                            }

                            $plugin_errors[$plugin_name]['errors'][] = $error;
                        }
                    }
                }

                if (!empty($plugin_errors)) {
                    foreach ($plugin_errors as $plugin_name => $data) {
                        $this->add_issue(
                            'plugins',
                            'plugin_debug_error_' . $plugin_name,
                            sprintf(__('Debug errors detected in plugin: %s', 'redco-optimizer'), $plugin_name),
                            sprintf(__('The plugin "%s" is generating debug errors. This can affect site performance and functionality.', 'redco-optimizer'), $plugin_name),
                            'warning',
                            array(
                                'plugin' => $plugin_name,
                                'errors' => array_slice($data['errors'], 0, 5) // Limit to 5 errors
                            )
                        );
                    }
                }
            }
        }
    }

    /**
     * Check themes for issues.
     *
     * @since    1.0.0
     */
    private function check_themes() {
        // Get all themes
        $themes = wp_get_themes();
        $current_theme = wp_get_theme();

        // Check for outdated themes
        $update_themes = get_site_transient('update_themes');
        if ($update_themes && !empty($update_themes->response)) {
            $outdated_themes = array();

            foreach ($update_themes->response as $theme_dir => $theme_data) {
                $theme = $themes[$theme_dir];
                $outdated_themes[] = array(
                    'dir' => $theme_dir,
                    'name' => $theme->get('Name'),
                    'current_version' => $theme->get('Version'),
                    'new_version' => $theme_data['new_version']
                );
            }

            if (!empty($outdated_themes)) {
                $this->add_issue(
                    'themes',
                    'outdated_themes',
                    __('Outdated themes detected', 'redco-optimizer'),
                    __('You have ' . count($outdated_themes) . ' outdated themes. Outdated themes can contain security vulnerabilities.', 'redco-optimizer'),
                    'warning',
                    array(
                        'count' => count($outdated_themes),
                        'themes' => $outdated_themes
                    )
                );
            }
        }

        // Check for inactive themes
        $inactive_themes = array();
        foreach ($themes as $theme_dir => $theme) {
            if ($theme->get_stylesheet() != $current_theme->get_stylesheet() && $theme->get_template() != $current_theme->get_template()) {
                $inactive_themes[] = array(
                    'dir' => $theme_dir,
                    'name' => $theme->get('Name')
                );
            }
        }

        if (count($inactive_themes) > 3) {
            $this->add_issue(
                'themes',
                'inactive_themes',
                __('Too many inactive themes', 'redco-optimizer'),
                __('You have ' . count($inactive_themes) . ' inactive themes. Consider removing themes you don\'t use to improve security and reduce clutter.', 'redco-optimizer'),
                'info',
                array(
                    'count' => count($inactive_themes),
                    'themes' => $inactive_themes
                )
            );
        }
    }

    /**
     * Check WordPress for issues.
     *
     * @since    1.0.0
     */
    private function check_wordpress() {
        global $wp_version;

        // Check for WordPress updates
        $update_core = get_site_transient('update_core');

        if ($update_core && !empty($update_core->updates)) {
            $latest_version = $update_core->updates[0]->version;

            if (version_compare($wp_version, $latest_version, '<')) {
                $this->add_issue(
                    'wordpress',
                    'outdated_wordpress',
                    __('WordPress is outdated', 'redco-optimizer'),
                    sprintf(__('You are running WordPress %s. The latest version is %s. Outdated WordPress installations can contain security vulnerabilities.', 'redco-optimizer'), $wp_version, $latest_version),
                    'critical',
                    array(
                        'current_version' => $wp_version,
                        'latest_version' => $latest_version
                    )
                );
            }
        }

        // Check for debug mode
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $this->add_issue(
                'wordpress',
                'debug_mode_enabled',
                __('Debug mode is enabled', 'redco-optimizer'),
                __('WordPress debug mode is enabled. This should be disabled on production sites.', 'redco-optimizer'),
                'warning',
                array()
            );
        }

        // Check for default content
        $sample_page = get_page_by_path('sample-page');
        $hello_world_post = get_page_by_title('Hello World!', OBJECT, 'post');

        if ($sample_page || $hello_world_post) {
            $this->add_issue(
                'wordpress',
                'default_content',
                __('Default WordPress content detected', 'redco-optimizer'),
                __('Your site still contains default WordPress content (sample page or hello world post). Consider removing these for a more professional site.', 'redco-optimizer'),
                'info',
                array(
                    'sample_page' => !empty($sample_page),
                    'hello_world_post' => !empty($hello_world_post)
                )
            );
        }
    }

    /**
     * Check server for issues.
     *
     * @since    1.0.0
     */
    private function check_server() {
        // Check PHP version
        $php_version = phpversion();
        $recommended_version = '7.4';

        if (version_compare($php_version, $recommended_version, '<')) {
            $this->add_issue(
                'server',
                'outdated_php',
                __('PHP version is outdated', 'redco-optimizer'),
                sprintf(__('You are running PHP %s. We recommend using PHP %s or higher for better performance and security.', 'redco-optimizer'), $php_version, $recommended_version),
                'warning',
                array(
                    'current_version' => $php_version,
                    'recommended_version' => $recommended_version
                )
            );
        }

        // Check PHP memory limit
        $memory_limit = ini_get('memory_limit');
        $memory_limit_bytes = wp_convert_hr_to_bytes($memory_limit);

        if ($memory_limit_bytes < 128 * 1024 * 1024) { // 128MB
            $this->add_issue(
                'server',
                'low_memory_limit',
                __('PHP memory limit is low', 'redco-optimizer'),
                sprintf(__('Your PHP memory limit is set to %s. We recommend at least 128MB for WordPress sites.', 'redco-optimizer'), $memory_limit),
                'warning',
                array(
                    'current_limit' => $memory_limit,
                    'recommended_limit' => '128M'
                )
            );
        }

        // Check PHP max execution time
        $max_execution_time = ini_get('max_execution_time');

        if ($max_execution_time < 30 && $max_execution_time != 0) {
            $this->add_issue(
                'server',
                'low_execution_time',
                __('PHP max execution time is low', 'redco-optimizer'),
                sprintf(__('Your PHP max execution time is set to %s seconds. We recommend at least 30 seconds for WordPress sites.', 'redco-optimizer'), $max_execution_time),
                'warning',
                array(
                    'current_time' => $max_execution_time,
                    'recommended_time' => 30
                )
            );
        }

        // Check for required PHP extensions
        $required_extensions = array(
            'curl' => __('Required for making HTTP requests', 'redco-optimizer'),
            'json' => __('Required for JSON encoding/decoding', 'redco-optimizer'),
            'mbstring' => __('Required for multibyte string handling', 'redco-optimizer'),
            'xml' => __('Required for XML processing', 'redco-optimizer'),
            'zip' => __('Required for plugin/theme updates', 'redco-optimizer')
        );

        $missing_extensions = array();

        foreach ($required_extensions as $extension => $description) {
            if (!extension_loaded($extension)) {
                $missing_extensions[$extension] = $description;
            }
        }

        if (!empty($missing_extensions)) {
            $this->add_issue(
                'server',
                'missing_php_extensions',
                __('Missing required PHP extensions', 'redco-optimizer'),
                __('Your server is missing some PHP extensions that are recommended for WordPress.', 'redco-optimizer'),
                'critical',
                array(
                    'missing_extensions' => $missing_extensions
                )
            );
        }
    }

    /**
     * Check database for issues.
     *
     * @since    1.0.0
     */
    private function check_database() {
        global $wpdb;

        // Check for database errors
        $wpdb->hide_errors();
        $result = $wpdb->query("SELECT 1");
        $last_error = $wpdb->last_error;

        if (!empty($last_error)) {
            $this->add_issue(
                'database',
                'database_error',
                __('Database connection error', 'redco-optimizer'),
                sprintf(__('There was an error connecting to the database: %s', 'redco-optimizer'), $last_error),
                'critical',
                array(
                    'error' => $last_error
                )
            );
        }

        // Check database tables
        $tables = $wpdb->get_results("SHOW TABLE STATUS", ARRAY_A);
        $total_size = 0;
        $tables_needing_repair = array();

        foreach ($tables as $table) {
            $table_name = $table['Name'];

            // Skip non-WordPress tables
            if (strpos($table_name, $wpdb->prefix) !== 0) {
                continue;
            }

            // Calculate table size
            $data_length = $table['Data_length'];
            $index_length = $table['Index_length'];
            $total_size += $data_length + $index_length;

            // Check for tables needing repair
            if ($table['Engine'] == 'MyISAM' && ($table['Comment'] == 'Table is marked as crashed' || $table['Comment'] == 'Table is marked as crashed and should be repaired')) {
                $tables_needing_repair[] = $table_name;
            }
        }

        // Report tables needing repair
        if (!empty($tables_needing_repair)) {
            $this->add_issue(
                'database',
                'tables_need_repair',
                __('Database tables need repair', 'redco-optimizer'),
                __('Some database tables are marked as crashed and need to be repaired.', 'redco-optimizer'),
                'critical',
                array(
                    'tables' => $tables_needing_repair
                )
            );
        }

        // Check database size
        $total_size_mb = round($total_size / (1024 * 1024), 2);

        if ($total_size_mb > 100) {
            $this->add_issue(
                'database',
                'large_database',
                __('Database is large', 'redco-optimizer'),
                sprintf(__('Your WordPress database is %s MB. Large databases can slow down your site.', 'redco-optimizer'), $total_size_mb),
                'warning',
                array(
                    'size_mb' => $total_size_mb
                )
            );
        }

        // Check for post revisions
        $revision_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->posts WHERE post_type = 'revision'");

        if ($revision_count > 1000) {
            $this->add_issue(
                'database',
                'many_revisions',
                __('Too many post revisions', 'redco-optimizer'),
                sprintf(__('Your database contains %s post revisions. Consider cleaning up old revisions to improve performance.', 'redco-optimizer'), $revision_count),
                'warning',
                array(
                    'count' => $revision_count
                )
            );
        }
    }

    /**
     * Check security issues.
     *
     * @since    1.0.0
     */
    private function check_security() {
        // Check for admin username
        $admin_user = get_user_by('login', 'admin');

        if ($admin_user) {
            $this->add_issue(
                'security',
                'admin_username',
                __('Default admin username detected', 'redco-optimizer'),
                __('Your site has a user with the username "admin". This is a security risk as it makes brute force attacks easier.', 'redco-optimizer'),
                'critical',
                array()
            );
        }

        // Check for file editing
        if (defined('DISALLOW_FILE_EDIT') && !DISALLOW_FILE_EDIT) {
            $this->add_issue(
                'security',
                'file_editing_enabled',
                __('File editing is enabled', 'redco-optimizer'),
                __('WordPress file editing is enabled. This can be a security risk if your admin account is compromised.', 'redco-optimizer'),
                'warning',
                array()
            );
        }

        // Check for SSL
        if (!is_ssl()) {
            $this->add_issue(
                'security',
                'no_ssl',
                __('SSL is not enabled', 'redco-optimizer'),
                __('Your site is not using SSL (HTTPS). SSL encrypts data between your server and visitors, improving security.', 'redco-optimizer'),
                'critical',
                array()
            );
        }

        // Check for vulnerable plugins
        $this->check_vulnerable_plugins();
    }

    /**
     * Check for vulnerable plugins.
     *
     * @since    1.0.0
     */
    private function check_vulnerable_plugins() {
        // This would ideally connect to a vulnerability database API
        // For now, we'll just check for some commonly vulnerable plugins

        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $plugins = get_plugins();
        $active_plugins = get_option('active_plugins', array());

        // Get vulnerable plugins data from the database or API
        $vulnerable_plugins = get_option('redco_vulnerable_plugins', array());

        // If no data exists, use a default empty array
        if (empty($vulnerable_plugins)) {
            $vulnerable_plugins = array();

            // Schedule a refresh of vulnerability data
            if (!wp_next_scheduled('redco_refresh_vulnerability_data')) {
                wp_schedule_single_event(time(), 'redco_refresh_vulnerability_data');
            }
        }

        foreach ($plugins as $plugin_file => $plugin_data) {
            $plugin_dir = dirname($plugin_file);

            if (array_key_exists($plugin_dir, $vulnerable_plugins) && in_array($plugin_file, $active_plugins)) {
                $vulnerable_info = $vulnerable_plugins[$plugin_dir];
                $current_version = $plugin_data['Version'];

                foreach ($vulnerable_info['versions'] as $version_check) {
                    if (strpos($version_check, '< ') === 0) {
                        $check_version = substr($version_check, 2);

                        if (version_compare($current_version, $check_version, '<')) {
                            $this->add_issue(
                                'security',
                                'vulnerable_plugin_' . $plugin_dir,
                                sprintf(__('Vulnerable plugin: %s', 'redco-optimizer'), $plugin_data['Name']),
                                sprintf(__('You are running %s version %s which has known security vulnerabilities. %s', 'redco-optimizer'),
                                    $plugin_data['Name'],
                                    $current_version,
                                    $vulnerable_info['description']
                                ),
                                'critical',
                                array(
                                    'plugin' => $plugin_data['Name'],
                                    'version' => $current_version,
                                    'vulnerability' => $vulnerable_info['description']
                                )
                            );
                        }
                    }
                }
            }
        }
    }

    /**
     * Check performance issues.
     *
     * @since    1.0.0
     */
    private function check_performance() {
        // Check if Redco Optimizer modules are enabled
        $redco_modules = get_option('redco_optimizer_modules', array());

        // Check for caching
        $caching_enabled = isset($redco_modules['page_cache']) && $redco_modules['page_cache']['enabled'];

        if (!$caching_enabled) {
            $this->add_issue(
                'performance',
                'redco_caching_disabled',
                __('Redco Optimizer Page Cache is disabled', 'redco-optimizer'),
                __('The Redco Optimizer Page Cache module is currently disabled. Enabling page caching can significantly improve your site\'s performance.', 'redco-optimizer'),
                'warning',
                array(
                    'recommendation' => __('Enable the Page Cache module in Redco Optimizer settings.', 'redco-optimizer'),
                    'module' => 'page_cache'
                )
            );
        }

        // Check for image optimization
        $image_optimization_enabled = isset($redco_modules['image_optimization']) && $redco_modules['image_optimization']['enabled'];

        if (!$image_optimization_enabled) {
            $this->add_issue(
                'performance',
                'redco_image_optimization_disabled',
                __('Redco Optimizer Image Optimization is disabled', 'redco-optimizer'),
                __('The Redco Optimizer Image Optimization module is currently disabled. Optimizing images can significantly improve page load times.', 'redco-optimizer'),
                'info',
                array(
                    'recommendation' => __('Enable the Image Optimization module in Redco Optimizer settings.', 'redco-optimizer'),
                    'module' => 'image_optimization'
                )
            );
        }

        // Check for browser caching
        $browser_cache_enabled = isset($redco_modules['browser_cache']) && $redco_modules['browser_cache']['enabled'];

        if (!$browser_cache_enabled) {
            $this->add_issue(
                'performance',
                'redco_browser_cache_disabled',
                __('Redco Optimizer Browser Cache is disabled', 'redco-optimizer'),
                __('The Redco Optimizer Browser Cache module is currently disabled. Browser caching can reduce page load times for returning visitors.', 'redco-optimizer'),
                'info',
                array(
                    'recommendation' => __('Enable the Browser Cache module in Redco Optimizer settings.', 'redco-optimizer'),
                    'module' => 'browser_cache'
                )
            );
        }

        // Check for minification
        $minification_enabled = isset($redco_modules['minification']) && $redco_modules['minification']['enabled'];

        if (!$minification_enabled) {
            $this->add_issue(
                'performance',
                'redco_minification_disabled',
                __('Redco Optimizer Minification is disabled', 'redco-optimizer'),
                __('The Redco Optimizer Minification module is currently disabled. Minifying CSS and JavaScript can improve page load times.', 'redco-optimizer'),
                'info',
                array(
                    'recommendation' => __('Enable the Minification module in Redco Optimizer settings.', 'redco-optimizer'),
                    'module' => 'minification'
                )
            );
        }

        // Check for database optimization
        $database_optimization_enabled = isset($redco_modules['database_optimization']) && $redco_modules['database_optimization']['enabled'];

        if (!$database_optimization_enabled) {
            $this->add_issue(
                'performance',
                'redco_database_optimization_disabled',
                __('Redco Optimizer Database Optimization is disabled', 'redco-optimizer'),
                __('The Redco Optimizer Database Optimization module is currently disabled. Regular database optimization can improve site performance.', 'redco-optimizer'),
                'info',
                array(
                    'recommendation' => __('Enable the Database Optimization module in Redco Optimizer settings.', 'redco-optimizer'),
                    'module' => 'database_optimization'
                )
            );
        }

        // Check for CDN integration
        $cdn_enabled = isset($redco_modules['cdn_integration']) && $redco_modules['cdn_integration']['enabled'];

        if (!$cdn_enabled) {
            $this->add_issue(
                'performance',
                'redco_cdn_disabled',
                __('Redco Optimizer CDN Integration is disabled', 'redco-optimizer'),
                __('The Redco Optimizer CDN Integration module is currently disabled. Using a CDN can significantly improve global site performance.', 'redco-optimizer'),
                'info',
                array(
                    'recommendation' => __('Enable the CDN Integration module in Redco Optimizer settings.', 'redco-optimizer'),
                    'module' => 'cdn_integration'
                )
            );
        }
    }

    /**
     * Check for JavaScript errors and issues.
     *
     * @since    1.0.0
     */
    private function check_javascript() {
        try {
            // Get theme directory
            $theme_dir = get_template_directory();
            $theme_uri = get_template_directory_uri();
            $child_theme_dir = get_stylesheet_directory();
            $child_theme_uri = get_stylesheet_directory_uri();

            // Arrays to store issues
            $minification_issues = array();
            $console_errors = array();
            $large_files = array();
            $render_blocking = array();

            // Check for JavaScript console errors if we can access the browser console
            if (function_exists('wp_get_recent_js_errors')) {
                $js_errors = wp_get_recent_js_errors();
                if (!empty($js_errors)) {
                    foreach ($js_errors as $error) {
                        $console_errors[] = array(
                            'message' => $error['message'],
                            'url' => $error['url'],
                            'line' => $error['line'],
                            'timestamp' => $error['timestamp']
                        );
                    }

                    if (!empty($console_errors)) {
                        $this->add_issue(
                            'javascript',
                            'js_console_errors',
                            __('JavaScript console errors detected', 'redco-optimizer'),
                            __('Your site has JavaScript errors in the browser console. These can cause functionality issues for your visitors.', 'redco-optimizer'),
                            'critical',
                            array(
                                'errors' => $console_errors,
                                'recommendation' => __('Review and fix the JavaScript errors. Consider using the Redco Optimizer JavaScript optimization features.', 'redco-optimizer')
                            )
                        );
                    }
                }
            }

            // Scan theme JS files - with error handling
            $js_files = array();
            try {
                $js_files = $this->find_js_files($theme_dir);
                if (is_child_theme()) {
                    $js_files = array_merge($js_files, $this->find_js_files($child_theme_dir));
                }
            } catch (Exception $e) {
                // Log the error but continue with the scan
                error_log('Redco Optimizer - Error scanning JS files: ' . $e->getMessage());
            }

            // Check for unminified JS files
            foreach ($js_files as $file) {
                try {
                    if (!file_exists($file) || !is_readable($file)) {
                        continue;
                    }

                    $file_size = filesize($file);
                    $file_content = file_get_contents($file);

                    if ($file_content === false) {
                        continue;
                    }

                    $relative_path = str_replace(array($theme_dir, $child_theme_dir), array($theme_uri, $child_theme_uri), $file);

                    // Check if file is minified
                    if ($file_size > 10000 && !$this->is_minified($file_content)) {
                        $minification_issues[] = array(
                            'file' => basename($file),
                            'path' => $relative_path,
                            'size' => round($file_size / 1024, 2) . ' KB'
                        );
                    }

                    // Check for large JS files
                    if ($file_size > 100000) { // 100KB
                        $large_files[] = array(
                            'file' => basename($file),
                            'path' => $relative_path,
                            'size' => round($file_size / 1024, 2) . ' KB'
                        );
                    }

                    // Check for render-blocking scripts in header
                    if ($this->is_render_blocking($file)) {
                        $render_blocking[] = array(
                            'file' => basename($file),
                            'path' => $relative_path
                        );
                    }
                } catch (Exception $e) {
                    // Skip this file and continue with others
                    error_log('Redco Optimizer - Error processing JS file ' . $file . ': ' . $e->getMessage());
                    continue;
                }
            }
        } catch (Exception $e) {
            // Log the error but don't stop the scan
            error_log('Redco Optimizer - Error in check_javascript: ' . $e->getMessage());
        }

        try {
            // Report unminified JS files
            if (!empty($minification_issues)) {
                $this->add_issue(
                    'javascript',
                    'unminified_js',
                    __('Unminified JavaScript files detected', 'redco-optimizer'),
                    __('Your theme contains JavaScript files that are not minified. Minifying these files can improve page load times.', 'redco-optimizer'),
                    'warning',
                    array(
                        'files' => $minification_issues,
                        'recommendation' => __('Enable JavaScript minification in the Redco Optimizer File Optimization settings.', 'redco-optimizer'),
                        'module' => 'minification'
                    )
                );
            }

            // Report large JS files
            if (!empty($large_files)) {
                $this->add_issue(
                    'javascript',
                    'large_js_files',
                    __('Large JavaScript files detected', 'redco-optimizer'),
                    __('Your theme contains large JavaScript files that may slow down page loading.', 'redco-optimizer'),
                    'warning',
                    array(
                        'files' => $large_files,
                        'recommendation' => __('Consider splitting large JavaScript files into smaller modules or loading them asynchronously. Enable JavaScript minification and combination in Redco Optimizer.', 'redco-optimizer'),
                        'module' => 'minification'
                    )
                );
            }

            // Report render-blocking scripts
            if (!empty($render_blocking)) {
                $this->add_issue(
                    'javascript',
                    'render_blocking_js',
                    __('Render-blocking JavaScript detected', 'redco-optimizer'),
                    __('Your site has JavaScript files that block rendering. This can delay how quickly your pages load.', 'redco-optimizer'),
                    'warning',
                    array(
                        'files' => $render_blocking,
                        'recommendation' => __('Enable the Redco Optimizer File Optimization module and set JavaScript files to load with defer or async attributes.', 'redco-optimizer'),
                        'module' => 'minification'
                    )
                );
            }

            // Check if jQuery is loaded multiple times - safely
            try {
                if ($this->has_multiple_jquery()) {
                    $this->add_issue(
                        'javascript',
                        'multiple_jquery',
                        __('Multiple jQuery versions detected', 'redco-optimizer'),
                        __('Your site is loading jQuery multiple times. This can cause conflicts and performance issues.', 'redco-optimizer'),
                        'critical',
                        array(
                            'recommendation' => __('Enable the Redco Optimizer File Optimization module to combine and optimize JavaScript files.', 'redco-optimizer'),
                            'module' => 'minification'
                        )
                    );
                }
            } catch (Exception $e) {
                error_log('Redco Optimizer - Error checking jQuery: ' . $e->getMessage());
            }
        } catch (Exception $e) {
            error_log('Redco Optimizer - Error reporting JavaScript issues: ' . $e->getMessage());
        }
    }

    /**
     * Find all JavaScript files in a directory recursively.
     *
     * @since    1.0.0
     * @param    string    $dir    The directory to scan.
     * @return   array     Array of JavaScript file paths.
     */
    private function find_js_files($dir) {
        $js_files = array();

        if (!is_dir($dir) || !is_readable($dir)) {
            return $js_files;
        }

        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CATCH_GET_CHILD
            );

            foreach ($iterator as $file) {
                try {
                    if ($file->isFile() && $file->getExtension() === 'js' && $file->isReadable()) {
                        $js_files[] = $file->getPathname();
                    }
                } catch (Exception $e) {
                    // Skip this file and continue with others
                    error_log('Redco Optimizer - Error processing file in directory scan: ' . $e->getMessage());
                    continue;
                }
            }
        } catch (UnexpectedValueException $e) {
            // This can happen if a directory can't be read
            error_log('Redco Optimizer - Error scanning directory: ' . $e->getMessage());
        } catch (Exception $e) {
            // Catch any other exceptions
            error_log('Redco Optimizer - Error in find_js_files: ' . $e->getMessage());
        }

        return $js_files;
    }

    /**
     * Check if a JavaScript file is minified.
     *
     * @since    1.0.0
     * @param    string    $content    The file content.
     * @return   boolean   True if minified, false otherwise.
     */
    private function is_minified($content) {
        // Count newlines
        $newlines = substr_count($content, "\n");
        $content_length = strlen($content);

        // If the file has very few newlines relative to its size, it's likely minified
        if ($content_length > 0 && ($newlines / $content_length) < 0.01) {
            return true;
        }

        // Check for common minification patterns
        if (strpos($content, '}\n') === false && strpos($content, '{\n') === false) {
            return true;
        }

        return false;
    }

    /**
     * Check if a JavaScript file is render-blocking.
     *
     * @since    1.0.0
     * @param    string    $file    The file path.
     * @return   boolean   True if render-blocking, false otherwise.
     */
    private function is_render_blocking($file) {
        // This is a simplified check - in a real implementation, you would need to
        // check if the script is loaded in the head without defer/async attributes
        $filename = basename($file);

        // Common render-blocking scripts
        $render_blocking_patterns = array(
            'jquery', 'main', 'script', 'global', 'common'
        );

        foreach ($render_blocking_patterns as $pattern) {
            if (stripos($filename, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if jQuery is loaded multiple times.
     *
     * @since    1.0.0
     * @return   boolean   True if multiple jQuery instances detected, false otherwise.
     */
    private function has_multiple_jquery() {
        // In a real implementation, you would need to check the actual page HTML
        // This is a placeholder that would need to be implemented with actual page scanning

        // For demonstration purposes, we'll return false
        return false;
    }

    /**
     * Send email notification for critical issues.
     *
     * @since    1.0.0
     * @param    array    $results    The scan results.
     */
    private function maybe_send_notification($results) {
        // Only send notification if there are critical issues
        if (isset($results['critical_issues']) && $results['critical_issues'] > 0) {
            $admin_email = get_option('admin_email');
            $site_name = get_bloginfo('name');

            $subject = sprintf(__('[%s] Critical Site Health Issues Detected', 'redco-optimizer'), $site_name);

            $message = sprintf(__('The Site Health Inspector has detected %d critical issues on your WordPress site.', 'redco-optimizer'), $results['critical_issues']) . "\n\n";

            // Add critical issues to the email
            $message .= __('Critical Issues:', 'redco-optimizer') . "\n";

            foreach ($results['categories'] as $category => $issues) {
                foreach ($issues as $issue) {
                    if ($issue['severity'] === 'critical') {
                        $message .= '- ' . $issue['title'] . ': ' . $issue['description'] . "\n";
                    }
                }
            }

            $message .= "\n" . __('Please log in to your WordPress admin panel and go to Redco Optimizer > Site Health to view and fix these issues.', 'redco-optimizer') . "\n\n";
            $message .= admin_url('admin.php?page=redco-optimizer&tab=site-health-inspector');

            wp_mail($admin_email, $subject, $message);
        }
    }
}
