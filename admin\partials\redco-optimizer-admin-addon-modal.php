<?php
/**
 * Template for the add-on settings modal
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}
?>

<!-- Add-on Settings Modal Template -->
<div id="redco-addon-settings-modal" class="redco-modal-overlay" style="display: none; visibility: hidden; opacity: 0;">
    <div class="redco-modal redco-addon-settings-modal">
        <div class="redco-modal-header">
            <h2 id="redco-addon-settings-title"><?php esc_html_e('Add-on Settings', 'redco-optimizer'); ?></h2>
            <button type="button" class="redco-modal-close" aria-label="<?php esc_attr_e('Close', 'redco-optimizer'); ?>">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"></path></svg>
            </button>
        </div>
        <div class="redco-modal-content" id="redco-addon-settings-content">
            <!-- Content will be loaded via AJAX -->
            <div class="redco-modal-loading">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="redco-spin"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg>
                <p><?php esc_html_e('Loading settings...', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>
</div>
