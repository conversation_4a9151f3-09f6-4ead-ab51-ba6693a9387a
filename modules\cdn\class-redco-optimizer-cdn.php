<?php
/**
 * The CDN module functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/cdn
 */

/**
 * The CDN module functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for the CDN module.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/cdn
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Cdn extends Redco_Optimizer_Module {

    /**
     * Register the hooks for this module.
     *
     * @since    1.0.0
     */
    protected function register_hooks() {
        // Add AJAX handlers
        $this->loader->add_action('wp_ajax_redco_cdn_save_settings', $this, 'ajax_save_settings');
        $this->loader->add_action('wp_ajax_redco_cdn_test_connection', $this, 'ajax_test_connection');

        // Add content filters if CDN is enabled
        if ($this->is_cdn_enabled()) {
            $this->loader->add_filter('the_content', $this, 'rewrite_urls');
            $this->loader->add_filter('wp_get_attachment_url', $this, 'rewrite_attachment_url');
            $this->loader->add_filter('style_loader_src', $this, 'rewrite_asset_url');
            $this->loader->add_filter('script_loader_src', $this, 'rewrite_asset_url');
        }
    }

    /**
     * Check if CDN is enabled.
     *
     * @since    1.0.0
     * @return   bool    True if CDN is enabled, false otherwise.
     */
    private function is_cdn_enabled() {
        $settings = $this->get_settings();
        return isset($settings['enabled']) && $settings['enabled'];
    }

    /**
     * Get CDN settings.
     *
     * @since    1.0.0
     * @return   array    The CDN settings.
     */
    public function get_settings() {
        $default_settings = array(
            'enabled' => false,
            'cdn_url' => '',
            'cdn_zone' => '',
            'cdn_key' => '',
            'cdn_secret' => '',
            'include_images' => true,
            'include_js' => true,
            'include_css' => true,
            'include_media' => true,
            'exclude_files' => '',
            'relative_path' => true,
            'cdn_https' => true,
        );

        $settings = get_option('redco_optimizer_cdn_settings', array());
        return wp_parse_args($settings, $default_settings);
    }

    /**
     * Handle AJAX request to save CDN settings.
     *
     * @since    1.0.0
     */
    public function ajax_save_settings() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to save settings.', 'redco-optimizer')));
        }

        // Get the settings from POST
        $settings = isset($_POST['settings']) ? $_POST['settings'] : array();

        // Sanitize settings
        $sanitized_settings = $this->sanitize_settings($settings);

        // Save the settings
        update_option('redco_optimizer_cdn_settings', $sanitized_settings);

        wp_send_json_success(array(
            'message' => __('CDN settings saved successfully.', 'redco-optimizer')
        ));
    }

    /**
     * Handle AJAX request to test CDN connection.
     *
     * @since    1.0.0
     */
    public function ajax_test_connection() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to test CDN connection.', 'redco-optimizer')));
        }

        // Get the settings
        $settings = $this->get_settings();

        // Test the connection
        $result = $this->test_cdn_connection($settings);

        if ($result['success']) {
            wp_send_json_success(array(
                'message' => __('CDN connection successful.', 'redco-optimizer')
            ));
        } else {
            wp_send_json_error(array(
                'message' => $result['message']
            ));
        }
    }

    /**
     * Test CDN connection.
     *
     * @since    1.0.0
     * @param    array    $settings    The CDN settings.
     * @return   array    The test result.
     */
    private function test_cdn_connection($settings) {
        // This is a placeholder for actual CDN connection testing
        // In a real implementation, you would test the connection to the CDN provider

        if (empty($settings['cdn_url'])) {
            return array(
                'success' => false,
                'message' => __('CDN URL is required.', 'redco-optimizer')
            );
        }

        // For demonstration, we'll just check if the CDN URL is valid
        if (filter_var($settings['cdn_url'], FILTER_VALIDATE_URL)) {
            return array(
                'success' => true,
                'message' => __('CDN connection successful.', 'redco-optimizer')
            );
        } else {
            return array(
                'success' => false,
                'message' => __('Invalid CDN URL.', 'redco-optimizer')
            );
        }
    }

    /**
     * Sanitize CDN settings.
     *
     * @since    1.0.0
     * @param    array    $settings    The settings to sanitize.
     * @return   array    The sanitized settings.
     */
    private function sanitize_settings($settings) {
        $sanitized = array();

        // Sanitize boolean values
        // Check for debug field first (most reliable)
        if (isset($settings['enabled_debug'])) {
            $sanitized['enabled'] = (bool) $settings['enabled_debug'];
        }
        // If no debug field, check regular field
        else if (isset($settings['enabled'])) {
            $sanitized['enabled'] = (bool) $settings['enabled'];
        } else {
            // Default to false if not set
            $sanitized['enabled'] = false;
        }
        $sanitized['include_images'] = isset($settings['include_images']) ? (bool) $settings['include_images'] : true;
        $sanitized['include_js'] = isset($settings['include_js']) ? (bool) $settings['include_js'] : true;
        $sanitized['include_css'] = isset($settings['include_css']) ? (bool) $settings['include_css'] : true;
        $sanitized['include_media'] = isset($settings['include_media']) ? (bool) $settings['include_media'] : true;
        $sanitized['relative_path'] = isset($settings['relative_path']) ? (bool) $settings['relative_path'] : true;
        $sanitized['cdn_https'] = isset($settings['cdn_https']) ? (bool) $settings['cdn_https'] : true;

        // Sanitize string values
        $sanitized['cdn_url'] = isset($settings['cdn_url']) ? esc_url_raw(trim($settings['cdn_url'])) : '';
        $sanitized['cdn_zone'] = isset($settings['cdn_zone']) ? sanitize_text_field($settings['cdn_zone']) : '';
        $sanitized['cdn_key'] = isset($settings['cdn_key']) ? sanitize_text_field($settings['cdn_key']) : '';
        $sanitized['cdn_secret'] = isset($settings['cdn_secret']) ? sanitize_text_field($settings['cdn_secret']) : '';
        $sanitized['exclude_files'] = isset($settings['exclude_files']) ? sanitize_textarea_field($settings['exclude_files']) : '';

        return $sanitized;
    }

    /**
     * Rewrite URLs in content to use CDN.
     *
     * @since    1.0.0
     * @param    string    $content    The content to rewrite URLs in.
     * @return   string    The content with rewritten URLs.
     */
    public function rewrite_urls($content) {
        $settings = $this->get_settings();

        if (!$settings['enabled'] || empty($settings['cdn_url'])) {
            return $content;
        }

        $site_url = site_url();
        $cdn_url = rtrim($settings['cdn_url'], '/');

        // Get the upload directory
        $upload_dir = wp_upload_dir();
        $upload_url = $upload_dir['baseurl'];

        // Define patterns to search for
        $patterns = array();

        // Add images
        if ($settings['include_images']) {
            $patterns[] = '/(https?:\/\/[^\/]+)?(\/[^\/]+)?\/wp-content\/uploads\/.*\.(jpg|jpeg|png|gif|webp|svg)/i';
        }

        // Add JavaScript
        if ($settings['include_js']) {
            $patterns[] = '/(https?:\/\/[^\/]+)?(\/[^\/]+)?\/wp-content\/.*\.js/i';
            $patterns[] = '/(https?:\/\/[^\/]+)?(\/[^\/]+)?\/wp-includes\/.*\.js/i';
        }

        // Add CSS
        if ($settings['include_css']) {
            $patterns[] = '/(https?:\/\/[^\/]+)?(\/[^\/]+)?\/wp-content\/.*\.css/i';
            $patterns[] = '/(https?:\/\/[^\/]+)?(\/[^\/]+)?\/wp-includes\/.*\.css/i';
        }

        // Process each pattern
        foreach ($patterns as $pattern) {
            $content = preg_replace_callback($pattern, function($matches) use ($site_url, $cdn_url, $settings) {
                $url = $matches[0];

                // Skip excluded files
                if ($this->is_excluded($url, $settings['exclude_files'])) {
                    return $url;
                }

                // Handle relative URLs
                if (strpos($url, 'http') !== 0) {
                    if ($settings['relative_path']) {
                        // For relative URLs, just prepend the CDN URL
                        return $cdn_url . $url;
                    } else {
                        // For relative URLs, convert to absolute first
                        $url = $site_url . $url;
                    }
                }

                // Replace the site URL with the CDN URL
                return str_replace($site_url, $cdn_url, $url);
            }, $content);
        }

        return $content;
    }

    /**
     * Rewrite attachment URL to use CDN.
     *
     * @since    1.0.0
     * @param    string    $url    The attachment URL.
     * @return   string    The rewritten URL.
     */
    public function rewrite_attachment_url($url) {
        $settings = $this->get_settings();

        if (!$settings['enabled'] || empty($settings['cdn_url']) || !$settings['include_media']) {
            return $url;
        }

        // Skip excluded files
        if ($this->is_excluded($url, $settings['exclude_files'])) {
            return $url;
        }

        $site_url = site_url();
        $cdn_url = rtrim($settings['cdn_url'], '/');

        return str_replace($site_url, $cdn_url, $url);
    }

    /**
     * Rewrite asset URL to use CDN.
     *
     * @since    1.0.0
     * @param    string    $url    The asset URL.
     * @return   string    The rewritten URL.
     */
    public function rewrite_asset_url($url) {
        $settings = $this->get_settings();

        if (!$settings['enabled'] || empty($settings['cdn_url'])) {
            return $url;
        }

        // Skip excluded files
        if ($this->is_excluded($url, $settings['exclude_files'])) {
            return $url;
        }

        // Check if it's a JavaScript or CSS file
        $is_js = (strpos($url, '.js') !== false);
        $is_css = (strpos($url, '.css') !== false);

        if (($is_js && !$settings['include_js']) || ($is_css && !$settings['include_css'])) {
            return $url;
        }

        $site_url = site_url();
        $cdn_url = rtrim($settings['cdn_url'], '/');

        return str_replace($site_url, $cdn_url, $url);
    }

    /**
     * Check if a file is excluded from CDN.
     *
     * @since    1.0.0
     * @param    string    $url           The file URL.
     * @param    string    $exclude_files The list of excluded files.
     * @return   bool      True if the file is excluded, false otherwise.
     */
    private function is_excluded($url, $exclude_files) {
        if (empty($exclude_files)) {
            return false;
        }

        $excluded = explode("\n", $exclude_files);

        foreach ($excluded as $exclude) {
            $exclude = trim($exclude);

            if (empty($exclude)) {
                continue;
            }

            if (strpos($url, $exclude) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the module settings HTML.
     *
     * @since    1.0.0
     * @return   string    The module settings HTML.
     */
    public function get_settings_html() {
        $settings = $this->get_settings();
        $has_premium_access = true; // Always allow access to CDN features

        ob_start();
        ?>
        <div class="redco-module-settings-section">
            <h3><?php esc_html_e('CDN Integration Settings', 'redco-optimizer'); ?></h3>

            <div class="redco-settings-field">
                <label for="cdn_enabled">
                    <input type="checkbox" id="cdn_enabled" name="enabled" <?php checked($settings['enabled'], true); ?>>
                    <?php esc_html_e('Enable CDN Integration', 'redco-optimizer'); ?>
                </label>
                <!-- Add hidden debug field to track checkbox state -->
                <input type="hidden" name="enabled_debug" id="enabled_debug" value="<?php echo $settings['enabled'] ? '1' : '0'; ?>"
                       data-target="cdn_enabled" class="redco-checkbox-debug-field">
                <p class="description"><?php esc_html_e('Enable content delivery network integration.', 'redco-optimizer'); ?></p>
            </div>

            <div class="redco-settings-field">
                <label for="cdn_url"><?php esc_html_e('CDN URL', 'redco-optimizer'); ?></label>
                <input type="url" id="cdn_url" name="cdn_url" value="<?php echo esc_attr($settings['cdn_url']); ?>" placeholder="https://cdn.example.com">
                <p class="description"><?php esc_html_e('Enter your CDN URL without trailing slash.', 'redco-optimizer'); ?></p>
            </div>

            <div class="redco-settings-field">
                <label for="cdn_zone"><?php esc_html_e('CDN Zone', 'redco-optimizer'); ?></label>
                <input type="text" id="cdn_zone" name="cdn_zone" value="<?php echo esc_attr($settings['cdn_zone']); ?>">
                <p class="description">
                    <?php esc_html_e('Enter your CDN zone identifier.', 'redco-optimizer'); ?>
                </p>
            </div>

            <div class="redco-settings-field">
                <label for="cdn_key"><?php esc_html_e('API Key', 'redco-optimizer'); ?></label>
                <input type="text" id="cdn_key" name="cdn_key" value="<?php echo esc_attr($settings['cdn_key']); ?>">
                <p class="description">
                    <?php esc_html_e('Enter your CDN API key.', 'redco-optimizer'); ?>
                </p>
            </div>

            <div class="redco-settings-field">
                <label for="cdn_secret"><?php esc_html_e('API Secret', 'redco-optimizer'); ?></label>
                <input type="password" id="cdn_secret" name="cdn_secret" value="<?php echo esc_attr($settings['cdn_secret']); ?>">
                <p class="description">
                    <?php esc_html_e('Enter your CDN API secret.', 'redco-optimizer'); ?>
                </p>
            </div>

            <h4><?php esc_html_e('CDN File Types', 'redco-optimizer'); ?></h4>

            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="include_images" <?php checked($settings['include_images'], true); ?>>
                    <?php esc_html_e('Include Images', 'redco-optimizer'); ?>
                </label>
                <p class="description"><?php esc_html_e('Serve images through the CDN.', 'redco-optimizer'); ?></p>
            </div>

            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="include_js" <?php checked($settings['include_js'], true); ?>>
                    <?php esc_html_e('Include JavaScript', 'redco-optimizer'); ?>
                </label>
                <p class="description"><?php esc_html_e('Serve JavaScript files through the CDN.', 'redco-optimizer'); ?></p>
            </div>

            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="include_css" <?php checked($settings['include_css'], true); ?>>
                    <?php esc_html_e('Include CSS', 'redco-optimizer'); ?>
                </label>
                <p class="description"><?php esc_html_e('Serve CSS files through the CDN.', 'redco-optimizer'); ?></p>
            </div>

            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="include_media" <?php checked($settings['include_media'], true); ?>>
                    <?php esc_html_e('Include Media', 'redco-optimizer'); ?>
                </label>
                <p class="description"><?php esc_html_e('Serve media files through the CDN.', 'redco-optimizer'); ?></p>
            </div>

            <h4><?php esc_html_e('Advanced Settings', 'redco-optimizer'); ?></h4>

            <div class="redco-settings-field">
                <label for="exclude_files"><?php esc_html_e('Exclude Files', 'redco-optimizer'); ?></label>
                <textarea id="exclude_files" name="exclude_files" rows="4"><?php echo esc_textarea($settings['exclude_files']); ?></textarea>
                <p class="description"><?php esc_html_e('Enter file paths or patterns to exclude from CDN, one per line.', 'redco-optimizer'); ?></p>
            </div>

            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="relative_path" <?php checked($settings['relative_path'], true); ?>>
                    <?php esc_html_e('Use Relative Path', 'redco-optimizer'); ?>
                </label>
                <p class="description"><?php esc_html_e('Use relative paths for CDN URLs.', 'redco-optimizer'); ?></p>
            </div>

            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="cdn_https" <?php checked($settings['cdn_https'], true); ?>>
                    <?php esc_html_e('Force HTTPS', 'redco-optimizer'); ?>
                </label>
                <p class="description"><?php esc_html_e('Force HTTPS for CDN URLs.', 'redco-optimizer'); ?></p>
            </div>

            <div class="redco-settings-actions">
                <button type="button" class="redco-button" id="redco-test-cdn"><?php esc_html_e('Test CDN Connection', 'redco-optimizer'); ?></button>
                <button type="button" class="redco-button redco-button-primary" id="redco-save-cdn"><?php esc_html_e('Save Settings', 'redco-optimizer'); ?></button>
            </div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Update debug field when checkbox changes
                $('#cdn_enabled').on('change', function() {
                    var isChecked = $(this).is(':checked');
                    $('#enabled_debug').val(isChecked ? '1' : '0');
                });

                // Test CDN connection
                $('#redco-test-cdn').on('click', function() {
                    var $button = $(this);
                    var originalText = $button.text();

                    $button.prop('disabled', true).text('<?php esc_html_e('Testing...', 'redco-optimizer'); ?>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'redco_cdn_test_connection',
                            nonce: redco_optimizer.nonce,
                            settings: {
                                cdn_url: $('#cdn_url').val(),
                                cdn_zone: $('#cdn_zone').val(),
                                cdn_key: $('#cdn_key').val(),
                                cdn_secret: $('#cdn_secret').val()
                            }
                        },
                        success: function(response) {
                            if (response.success) {
                                alert(response.data.message);
                            } else {
                                alert(response.data.message);
                            }

                            $button.prop('disabled', false).text(originalText);
                        },
                        error: function() {
                            alert('<?php esc_html_e('An error occurred while testing the CDN connection.', 'redco-optimizer'); ?>');
                            $button.prop('disabled', false).text(originalText);
                        }
                    });
                });

                // Save CDN settings
                $('#redco-save-cdn').on('click', function() {
                    var $button = $(this);
                    var originalText = $button.text();

                    $button.prop('disabled', true).text('<?php esc_html_e('Saving...', 'redco-optimizer'); ?>');

                    var isEnabled = $('#cdn_enabled').is(':checked');

                    // Update the debug field
                    $('#enabled_debug').val(isEnabled ? '1' : '0');

                    var settings = {
                        enabled: isEnabled,
                        enabled_debug: isEnabled ? '1' : '0', // Include debug field
                        cdn_url: $('#cdn_url').val(),
                        cdn_zone: $('#cdn_zone').val(),
                        cdn_key: $('#cdn_key').val(),
                        cdn_secret: $('#cdn_secret').val(),
                        include_images: $('input[name="include_images"]').is(':checked'),
                        include_js: $('input[name="include_js"]').is(':checked'),
                        include_css: $('input[name="include_css"]').is(':checked'),
                        include_media: $('input[name="include_media"]').is(':checked'),
                        exclude_files: $('#exclude_files').val(),
                        relative_path: $('input[name="relative_path"]').is(':checked'),
                        cdn_https: $('input[name="cdn_https"]').is(':checked')
                    };

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'redco_cdn_save_settings',
                            nonce: redco_optimizer.nonce,
                            settings: settings
                        },
                        success: function(response) {
                            if (response.success) {
                                alert(response.data.message);
                            } else {
                                alert(response.data.message);
                            }

                            $button.prop('disabled', false).text(originalText);
                        },
                        error: function() {
                            alert('<?php esc_html_e('An error occurred while saving the settings.', 'redco-optimizer'); ?>');
                            $button.prop('disabled', false).text(originalText);
                        }
                    });
                });
            });
        </script>
        <?php
        return ob_get_clean();
    }

    /**
     * Check if the user has premium access.
     *
     * @since    1.0.0
     * @return   bool    True if the user has premium access, false otherwise.
     */
    protected function has_premium_access() {
        // CDN features are now completely free
        return true;
    }
}