/**
 * Help Tab Fix JavaScript
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/js
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Help Tab Fix loaded');

        // Force help tab content to be visible when the tab is clicked
        $('.redco-nav-item[data-tab="redco-help-tab"]').on('click', function() {
            console.log('Help tab clicked, applying fix');

            // Apply fix after a short delay to ensure the tab is loaded
            setTimeout(function() {
                applyHelpTabFix();
            }, 100);
        });

        // Also apply the fix on page load if the help tab is active or we're on the help page
        if ($('#redco-help-tab').is(':visible') ||
            window.location.href.indexOf('page=redco-optimizer-help') > -1 ||
            window.location.href.indexOf('redco-help-page.php') > -1) {

            console.log('Help tab is visible on page load, applying fix');
            applyHelpTabFix();

            // Also apply after a short delay to ensure everything is loaded
            setTimeout(applyHelpTabFix, 500);
            setTimeout(applyHelpTabFix, 1000);
        }

        // Apply the fix when window is fully loaded
        $(window).on('load', function() {
            if ($('#redco-help-tab').is(':visible') ||
                window.location.href.indexOf('page=redco-optimizer-help') > -1 ||
                window.location.href.indexOf('redco-help-page.php') > -1) {

                console.log('Window loaded, applying help tab fix');
                applyHelpTabFix();
            }
        });
    });

    /**
     * Apply all fixes to make the help tab content visible
     */
    function applyHelpTabFix() {
        console.log('Applying help tab fix');

        // Only apply fixes if we're actually on the help tab or help page
        if ($('#redco-help-tab').is(':visible') ||
            window.location.href.indexOf('page=redco-optimizer-help') > -1 ||
            window.location.href.indexOf('redco-help-page.php') > -1) {

            // Force the help tab to be visible
            $('#redco-help-tab').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
            $('#redco-help-tab .redco-section').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
            $('#redco-help-tab .redco-section-content').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');

            // Force the help content container to be visible
            $('.redco-help-container').attr('style', 'display: flex !important; visibility: visible !important; opacity: 1 !important;');
            $('.redco-help-content-container').attr('style', 'display: block !important; flex: 1 !important; visibility: visible !important; opacity: 1 !important;');

            // Force the active help content to be visible
            $('.redco-help-content.active').attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
        }

        // Create placeholder images if they don't exist
        createPlaceholderImages();

        // Ensure the first help topic is active if none is selected
        if ($('.redco-help-topic.active').length === 0) {
            $('.redco-help-topic:first').addClass('active');
            var firstTopicId = $('.redco-help-topic:first').data('topic');
            $('#redco-help-' + firstTopicId).addClass('active').show();
        }

        // Make sure FAQ items are properly initialized
        initializeFaqItems();

        console.log('Help tab fix applied');
    }

    /**
     * Initialize FAQ items to be expandable/collapsible
     */
    function initializeFaqItems() {
        // Hide all FAQ answers initially
        $('.redco-help-faq-answer').hide();

        // Open the first FAQ item by default if none is open
        if ($('.redco-help-faq-item.active').length === 0) {
            $('.redco-help-faq-item:first').addClass('active');
            $('.redco-help-faq-item:first .redco-help-faq-answer').show();
        }
    }

    /**
     * Create placeholder images for the help documentation if they don't exist.
     * This is a temporary solution until real screenshots are added.
     */
    function createPlaceholderImages() {
        console.log('Creating placeholder images');

        // List of image paths that should exist
        var imagePaths = [
            'dashboard-overview.jpg',
            'caching-settings.jpg',
            'media-optimization.jpg',
            'file-optimization.jpg',
            'database-optimization.jpg',
            'dashboard-explained.jpg',
            'caching-overview.jpg',
            'page-caching.jpg',
            'browser-caching.jpg',
            'gzip-compression.jpg',
            'cache-exclusions.jpg',
            'performance-score.jpg',
            'database-schedule.jpg',
            'cdn-integration.jpg',
            'export-settings.jpg',
            'diagnostic-tools.jpg',
            'optimization-overview.jpg',
            'file-optimization-settings.jpg',
            'media-optimization-settings.jpg',
            'preload-settings.jpg',
            'heartbeat-settings.jpg'
        ];

        // Get appropriate dashicon for each image type
        function getDashiconForImage(imageName) {
            var iconMap = {
                'dashboard': 'dashicons-dashboard',
                'caching': 'dashicons-performance',
                'media': 'dashicons-format-image',
                'file': 'dashicons-media-code',
                'database': 'dashicons-database',
                'cdn': 'dashicons-networking',
                'performance': 'dashicons-chart-line',
                'export': 'dashicons-download',
                'diagnostic': 'dashicons-admin-tools',
                'preload': 'dashicons-upload',
                'heartbeat': 'dashicons-heart',
                'optimization': 'dashicons-admin-generic',
                'gzip': 'dashicons-archive',
                'browser': 'dashicons-admin-site',
                'page': 'dashicons-admin-page',
                'exclusions': 'dashicons-filter'
            };

            // Find the matching key in the image name
            for (var key in iconMap) {
                if (imageName.indexOf(key) !== -1) {
                    return iconMap[key];
                }
            }

            // Default icon
            return 'dashicons-format-image';
        }

        // Process each image container
        $('.redco-help-image-container').each(function() {
            var $container = $(this);

            // Skip containers that already have actual screenshots
            if ($container.find('.redco-help-actual-screenshot').length > 0) {
                console.log('Skipping container with actual screenshot');
                return;
            }

            var $img = $container.find('img');

            if ($img.length > 0) {
                var imgSrc = $img.attr('src');
                var imgAlt = $img.attr('alt') || 'Redco Optimizer';

                // Check if the image exists
                var img = new Image();
                img.onload = function() {
                    // Image exists, do nothing
                    console.log('Image exists: ' + imgSrc);
                };
                img.onerror = function() {
                    // Image doesn't exist, create a placeholder
                    console.log('Creating placeholder for: ' + imgSrc);

                    // Extract image name from src
                    var imageName = imgSrc.split('/').pop().replace('.jpg', '');

                    // Get appropriate dashicon
                    var dashicon = getDashiconForImage(imageName);

                    // Create a placeholder div with better structure
                    var $placeholder = $('<div class="redco-help-placeholder"></div>');
                    var $content = $('<div class="redco-help-placeholder-content"></div>');

                    // Add icon
                    $content.append('<span class="dashicons ' + dashicon + '"></span>');

                    // Add title and subtitle
                    $content.append('<div class="redco-help-placeholder-title">' + imgAlt + '</div>');
                    $content.append('<div class="redco-help-placeholder-subtitle">Placeholder for ' + imageName + '</div>');

                    // Add content to placeholder
                    $placeholder.append($content);

                    // Replace the image with the placeholder
                    $img.replaceWith($placeholder);

                    // Add caption if it doesn't exist
                    if ($container.find('.redco-help-image-caption').length === 0) {
                        $container.append('<p class="redco-help-image-caption">' + imgAlt + '</p>');
                    }
                };
                img.src = imgSrc;
            }
        });
    }

})(jQuery);
