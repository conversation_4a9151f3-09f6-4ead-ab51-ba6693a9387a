/**
 * Site Health Inspector Admin JavaScript
 *
 * @link       https://redco.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/site-health-inspector/admin/js
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Always show the plugin notices container on the plugin page
        $('.redco-plugin-notices-container').css('display', 'block');

        // Protect Site Health notices from being hidden
        function protectSiteHealthNotices() {
            $('.redco-site-health-notice').css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'position': 'relative',
                'z-index': '1000'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; position: relative; z-index: 1000;';
            });
        }

        // Run protection initially
        protectSiteHealthNotices();

        // Run protection periodically
        setInterval(protectSiteHealthNotices, 1000);

        // Show global notification area when not on the main dashboard page
        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
            window.location.href.indexOf('tab=') > -1) {
            $('#redco-global-notification-area').css('display', 'block');
        }

        // Run site scan
        $('#redco-run-site-scan').on('click', function() {
            var $button = $(this);
            var originalText = $button.html();

            // Disable button and show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-update-alt redco-spin"></span> ' + redcoSiteHealth.scanning_text);

            // Show scanning notification
            showNotification('info', redcoSiteHealth.scanning_message, {
                title: redcoSiteHealth.scanning_title,
                autoClose: false,
                id: 'redco-scanning-notification'
            });

            // Run the scan via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_run_site_scan',
                    nonce: redcoSiteHealth.nonce
                },
                success: function(response) {
                    // Close scanning notification
                    closeNotification('redco-scanning-notification');

                    if (response.success) {
                        // Show success notification
                        showNotification('success', response.data.message, {
                            title: redcoSiteHealth.scan_complete_title
                        });

                        // Reload the page to show new results
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        // Show error notification
                        showNotification('error', response.data.message || redcoSiteHealth.scan_error_message, {
                            title: redcoSiteHealth.scan_error_title
                        });

                        // Reset button
                        $button.prop('disabled', false).html(originalText);
                    }
                },
                error: function() {
                    // Close scanning notification
                    closeNotification('redco-scanning-notification');

                    // Show error notification
                    showNotification('error', redcoSiteHealth.scan_error_message, {
                        title: redcoSiteHealth.scan_error_title
                    });

                    // Reset button
                    $button.prop('disabled', false).html(originalText);
                }
            });
        });

        // Toggle issue details
        $('.redco-toggle-details').on('click', function() {
            var $button = $(this);
            var $content = $button.next('.redco-issue-details-content');

            if ($content.is(':visible')) {
                $content.slideUp(200);
                $button.text(redcoSiteHealth.show_details_text);
            } else {
                $content.slideDown(200);
                $button.text(redcoSiteHealth.hide_details_text);
            }
        });

        // Export issues report
        $('#redco-export-issues').on('click', function() {
            // Get the scan results
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_export_site_health_report',
                    nonce: redcoSiteHealth.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Create a download link
                        var blob = new Blob([response.data.report], {type: 'text/plain'});
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        a.download = response.data.filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);

                        // Show success notification
                        showNotification('success', redcoSiteHealth.export_success_message, {
                            title: redcoSiteHealth.export_success_title
                        });
                    } else {
                        // Show error notification
                        showNotification('error', response.data.message || redcoSiteHealth.export_error_message, {
                            title: redcoSiteHealth.export_error_title
                        });
                    }
                },
                error: function() {
                    // Show error notification
                    showNotification('error', redcoSiteHealth.export_error_message, {
                        title: redcoSiteHealth.export_error_title
                    });
                }
            });
        });

        // Filter issues by severity
        $('.redco-severity-filter').on('click', function() {
            var severity = $(this).data('severity');

            if (severity === 'all') {
                $('.redco-issue-item').show();
            } else {
                $('.redco-issue-item').hide();
                $('.redco-issue-item.redco-severity-' + severity).show();
            }

            // Update active filter
            $('.redco-severity-filter').removeClass('active');
            $(this).addClass('active');
        });

        // Handle plugin actions (update, deactivate, delete)
        $(document).on('click', '.redco-update-plugin, .redco-deactivate-plugin, .redco-delete-plugin', function(e) {
            e.preventDefault();

            var $button = $(this);
            var plugin = $button.data('plugin');
            var action = $button.data('action');
            var nonce = $button.data('nonce');
            var $row = $button.closest('tr');
            var confirmMessage = '';

            // Set confirmation message based on action
            if (action === 'delete') {
                confirmMessage = redcoSiteHealth.confirm_delete_plugin || 'Are you sure you want to delete this plugin?';
                if (!confirm(confirmMessage)) {
                    return;
                }
            } else if (action === 'deactivate') {
                confirmMessage = redcoSiteHealth.confirm_deactivate_plugin || 'Are you sure you want to deactivate this plugin?';
                if (!confirm(confirmMessage)) {
                    return;
                }
            }

            // Disable all buttons in the row
            $row.find('button').prop('disabled', true);

            // Add loading indicator to the clicked button
            var originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> ' + (redcoSiteHealth[action + '_text'] || 'Processing...'));

            // Show processing notification in the global notification area
            var message = 'Processing ' + action + ' action...';
            // Only show if we're not on the main dashboard page
            if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                window.location.href.indexOf('tab=') > -1) {
                $('#redco-global-notification-area').html('<div class="notice notice-info is-dismissible"><p>' + message + '</p></div>').show();
            }

            // Perform the action via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_plugin_action',
                    plugin_action: action,
                    plugin: plugin,
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        var successMessage = response.data.message || 'Plugin ' + action + ' completed successfully.';
                        // Only show if we're not on the main dashboard page
                        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                            window.location.href.indexOf('tab=') > -1) {
                            $('#redco-global-notification-area').html('<div class="notice notice-success is-dismissible"><p>' + successMessage + '</p></div>').show();
                        }

                        // Handle UI update based on action
                        if (action === 'update') {
                            // Update version numbers
                            if (response.data.new_version) {
                                $row.find('td:nth-child(2)').text(response.data.new_version);
                                $row.find('td:nth-child(3)').text(response.data.new_version);
                            }
                            // Re-enable buttons
                            $row.find('button').prop('disabled', false);
                            $button.html(originalText);
                        } else {
                            // For deactivate and delete, remove the row
                            $row.fadeOut(300, function() {
                                $(this).remove();

                                // If no more rows, refresh the page
                                if ($row.closest('tbody').find('tr').length === 0) {
                                    setTimeout(function() {
                                        location.reload();
                                    }, 1000);
                                }
                            });
                        }

                        // Auto-hide notification after 3 seconds
                        setTimeout(function() {
                            // Only hide if we're not on the main dashboard page
                            if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                                window.location.href.indexOf('tab=') > -1) {
                                $('#redco-global-notification-area').fadeOut();
                            }
                        }, 3000);
                    } else {
                        // Show error notification
                        var errorMessage = response.data.message || 'An error occurred during the ' + action + ' action.';
                        // Only show if we're not on the main dashboard page
                        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                            window.location.href.indexOf('tab=') > -1) {
                            $('#redco-global-notification-area').html('<div class="notice notice-error is-dismissible"><p>' + errorMessage + '</p></div>').show();
                        }

                        // Reset button
                        $row.find('button').prop('disabled', false);
                        $button.html(originalText);
                    }
                },
                error: function() {
                    // Show error notification
                    var errorMessage = 'An error occurred during the ' + action + ' action.';
                    // Only show if we're not on the main dashboard page
                    if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                        window.location.href.indexOf('tab=') > -1) {
                        $('#redco-global-notification-area').html('<div class="notice notice-error is-dismissible"><p>' + errorMessage + '</p></div>').show();
                    }

                    // Reset button
                    $row.find('button').prop('disabled', false);
                    $button.html(originalText);
                }
            });
        });

        // Handle content deletion (sample page, hello world post)
        $(document).on('click', '.redco-delete-content', function(e) {
            e.preventDefault();

            var $button = $(this);
            var id = $button.data('id');
            var type = $button.data('type');
            var nonce = $button.data('nonce');
            var $container = $button.closest('.redco-detail-item');

            // Confirm deletion
            var confirmMessage = redcoSiteHealth.confirm_delete_content || 'Are you sure you want to delete this content?';
            if (!confirm(confirmMessage)) {
                return;
            }

            // Disable button and show loading state
            $button.prop('disabled', true);
            var originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> ' + (redcoSiteHealth.deleting_text || 'Deleting...'));

            // Show processing notification
            // Only show if we're not on the main dashboard page
            if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                window.location.href.indexOf('tab=') > -1) {
                $('#redco-global-notification-area').html('<div class="notice notice-info is-dismissible"><p>Deleting content...</p></div>').show();
            }

            // Perform the deletion via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_delete_content',
                    content_id: id,
                    content_type: type,
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        var successMessage = response.data.message || 'Content deleted successfully.';
                        // Only show if we're not on the main dashboard page
                        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                            window.location.href.indexOf('tab=') > -1) {
                            $('#redco-global-notification-area').html('<div class="notice notice-success is-dismissible"><p>' + successMessage + '</p></div>').show();
                        }

                        // Remove the content item from the UI
                        $container.fadeOut(300, function() {
                            $(this).remove();
                        });

                        // Auto-hide notification after 3 seconds
                        setTimeout(function() {
                            // Only hide if we're not on the main dashboard page
                            if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                                window.location.href.indexOf('tab=') > -1) {
                                $('#redco-global-notification-area').fadeOut();
                            }
                        }, 3000);
                    } else {
                        // Show error notification
                        var errorMessage = response.data.message || 'An error occurred while deleting the content.';
                        // Only show if we're not on the main dashboard page
                        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                            window.location.href.indexOf('tab=') > -1) {
                            $('#redco-global-notification-area').html('<div class="notice notice-error is-dismissible"><p>' + errorMessage + '</p></div>').show();
                        }

                        // Reset button
                        $button.prop('disabled', false).html(originalText);
                    }
                },
                error: function() {
                    // Show error notification
                    // Only show if we're not on the main dashboard page
                    if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                        window.location.href.indexOf('tab=') > -1) {
                        $('#redco-global-notification-area').html('<div class="notice notice-error is-dismissible"><p>An error occurred while deleting the content.</p></div>').show();
                    }

                    // Reset button
                    $button.prop('disabled', false).html(originalText);
                }
            });
        });

        // Handle plugin actions (update, deactivate, delete) - already implemented above

        // Handle enabling Redco Optimizer modules
        $(document).on('click', '.redco-enable-module', function(e) {
            e.preventDefault();

            var $button = $(this);
            var module = $button.data('module');
            var nonce = $button.data('nonce');

            // Disable button and show loading state
            $button.prop('disabled', true);
            var originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update redco-spin"></span> ' + (redcoSiteHealth.enabling_text || 'Enabling...'));

            // Show processing notification
            // Only show if we're not on the main dashboard page
            if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                window.location.href.indexOf('tab=') > -1) {
                $('#redco-global-notification-area').html('<div class="notice notice-info is-dismissible"><p>Enabling module...</p></div>').show();
            }

            // Perform the action via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_enable_module',
                    module: module,
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        var successMessage = response.data.message || 'Module enabled successfully.';
                        // Only show if we're not on the main dashboard page
                        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                            window.location.href.indexOf('tab=') > -1) {
                            $('#redco-global-notification-area').html('<div class="notice notice-success is-dismissible"><p>' + successMessage + '</p></div>').show();
                        }

                        // Update UI - remove the issue item after a delay
                        var $issueItem = $button.closest('.redco-issue-item');
                        $issueItem.fadeOut(500, function() {
                            $(this).remove();

                            // If no more issues in this category, refresh the page
                            if ($issueItem.closest('.redco-card-content').find('.redco-issue-item').length === 0) {
                                setTimeout(function() {
                                    location.reload();
                                }, 1000);
                            }
                        });

                        // Auto-hide notification after 3 seconds
                        setTimeout(function() {
                            // Only hide if we're not on the main dashboard page
                            if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                                window.location.href.indexOf('tab=') > -1) {
                                $('#redco-global-notification-area').fadeOut();
                            }
                        }, 3000);
                    } else {
                        // Show error notification
                        var errorMessage = response.data.message || 'An error occurred while enabling the module.';
                        // Only show if we're not on the main dashboard page
                        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                            window.location.href.indexOf('tab=') > -1) {
                            $('#redco-global-notification-area').html('<div class="notice notice-error is-dismissible"><p>' + errorMessage + '</p></div>').show();
                        }

                        // Reset button
                        $button.prop('disabled', false).html(originalText);
                    }
                },
                error: function() {
                    // Show error notification
                    // Only show if we're not on the main dashboard page
                    if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                        window.location.href.indexOf('tab=') > -1) {
                        $('#redco-global-notification-area').html('<div class="notice notice-error is-dismissible"><p>An error occurred while enabling the module.</p></div>').show();
                    }

                    // Reset button
                    $button.prop('disabled', false).html(originalText);
                }
            });
        });
    });

    /**
     * Show a notification
     *
     * @param {string} type The notification type (success, error, warning, info)
     * @param {string} message The notification message
     * @param {object} options Additional options
     */
    function showNotification(type, message, options) {
        // Use the global notification area
        var noticeClass = 'notice-' + type;
        var title = options && options.title ? '<strong>' + options.title + '</strong>: ' : '';

        // Always ensure plugin notices container is visible on the plugin page
        $('.redco-plugin-notices-container').css('display', 'block');

        // Make sure we're not on the main dashboard page before showing notifications in global area
        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
            window.location.href.indexOf('tab=') > -1) {
            $('#redco-global-notification-area').html('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + title + message + '</p></div>').show();
        }

        // Auto-hide after 3 seconds unless specified otherwise
        if (!options || options.autoClose !== false) {
            setTimeout(function() {
                // Only hide if we're not on the main dashboard page
                if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
                    window.location.href.indexOf('tab=') > -1) {
                    $('#redco-global-notification-area').fadeOut();
                }
            }, 3000);
        }
    }

    /**
     * Close a notification by ID
     *
     * @param {string} id The notification ID
     */
    function closeNotification(id) {
        // Always ensure plugin notices container stays visible on the plugin page
        $('.redco-plugin-notices-container').css('display', 'block');

        // Only hide global notification area if we're not on the main dashboard page
        if (window.location.href.indexOf('page=redco-optimizer') === -1 ||
            window.location.href.indexOf('tab=') > -1) {
            $('#redco-global-notification-area').fadeOut();
        }
    }

})(jQuery);
