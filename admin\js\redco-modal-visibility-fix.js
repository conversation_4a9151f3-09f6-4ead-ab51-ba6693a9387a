/**
 * Redco Optimizer - Modal Visibility Fix
 * Ensures all elements in modals are visible
 */

(function($) {
    'use strict';

    // Run when document is ready
    $(document).ready(function() {
        // Fix modal visibility issues
        fixModalVisibility();

        // Run again after a delay to catch any late-loaded elements
        setTimeout(fixModalVisibility, 500);
        setTimeout(fixModalVisibility, 1000);
        setTimeout(fixModalVisibility, 2000);

        // Add event listeners for modal opening
        $(document).on('click', '.redco-open-modal, .redco-addon-settings', function() {
            // Fix visibility after modal is opened
            setTimeout(fixModalVisibility, 100);
            setTimeout(fixModalVisibility, 500);
            setTimeout(fixModalVisibility, 1000);

            // Initialize modal properly
            setTimeout(initializeModal, 200);
            setTimeout(initializeModal, 600);
            setTimeout(initializeModal, 1200);
        });

        // Add event listener for the test modal visibility button
        $(document).on('click', '.test-modal-visibility', function() {
            initializeModal();
        });

        // Add event listener for AJAX success to catch dynamically loaded content
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('admin-ajax.php') !== -1) {
                if (settings.data && (
                    settings.data.indexOf('redco_get_addon_settings') !== -1 ||
                    settings.data.indexOf('redco_load_addon_settings') !== -1
                )) {
                    console.log('AJAX success detected for addon settings, fixing visibility');
                    setTimeout(fixModalVisibility, 100);
                    setTimeout(fixModalVisibility, 500);
                    setTimeout(fixModalVisibility, 1000);

                    // Initialize modal properly
                    setTimeout(initializeModal, 200);
                    setTimeout(initializeModal, 600);
                    setTimeout(initializeModal, 1200);

                    // Check if this is the Advanced Cache Preloader modal
                    if (settings.data.indexOf('advanced-cache-preloader') !== -1) {
                        console.log('Advanced Cache Preloader modal detected, applying specific fixes');
                        setTimeout(fixAdvancedCachePreloaderModal, 300);
                        setTimeout(fixAdvancedCachePreloaderModal, 800);
                        setTimeout(fixAdvancedCachePreloaderModal, 1500);
                    }
                }
            }
        });
    });

    /**
     * Initialize modal properly
     */
    function initializeModal() {
        console.log('Initializing modal');

        // Add a test button to the modal
        if ($('.test-modal-visibility').length === 0 && $('.redco-modal-content').length > 0) {
            $('<button>')
                .addClass('redco-button redco-button-secondary test-modal-visibility')
                .text('Test Modal Visibility')
                .css({
                    'position': 'absolute',
                    'top': '10px',
                    'right': '10px',
                    'z-index': '999999'
                })
                .appendTo('.redco-modal-content');
        }

        // Fix modal structure
        $('.redco-modal').css({
            'max-height': '90vh',
            'height': 'auto',
            'display': 'flex',
            'flex-direction': 'column',
            'width': '800px',
            'max-width': '95%'
        });

        // Fix modal content area
        $('.redco-modal-content').css({
            'overflow-y': 'scroll',
            'flex': '1',
            'max-height': 'calc(90vh - 140px)',
            'scrollbar-width': 'thin',
            'padding': '25px',
            'position': 'relative'
        });

        // Check if all sections are visible
        var visibleSections = $('.redco-modal-section:visible').length;
        var totalSections = $('.redco-modal-section').length;

        console.log('Visible sections: ' + visibleSections + ' / ' + totalSections);

        // If not all sections are visible, try to fix it
        if (visibleSections < totalSections) {
            console.log('Not all sections are visible, trying to fix...');

            // Force all sections to be visible
            $('.redco-modal-section').show().css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Check again
            visibleSections = $('.redco-modal-section:visible').length;
            console.log('Visible sections after fix: ' + visibleSections + ' / ' + totalSections);
        }
    }

    /**
     * Fix modal visibility issues
     */
    function fixModalVisibility() {
        console.log('Fixing modal visibility');

        // Fix modal size and structure
        $('.redco-modal').css({
            'max-height': '90vh !important',
            'height': 'auto !important',
            'display': 'flex !important',
            'flex-direction': 'column !important',
            'width': '800px !important',
            'max-width': '95% !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'max-height: 90vh !important; height: auto !important; display: flex !important; flex-direction: column !important; width: 800px !important; max-width: 95% !important;';
        });

        // Fix modal content area
        $('.redco-modal-content').css({
            'overflow-y': 'scroll !important',
            'flex': '1 !important',
            'max-height': 'calc(90vh - 140px) !important',
            'scrollbar-width': 'thin !important',
            'padding': '25px !important',
            'display': 'block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'position': 'relative !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'overflow-y: scroll !important; flex: 1 !important; max-height: calc(90vh - 140px) !important; scrollbar-width: thin !important; padding: 25px !important; display: block !important; visibility: visible !important; opacity: 1 !important; position: relative !important;';
        });

        // Ensure modal sections are visible
        $('.redco-modal-section').each(function() {
            $(this).css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'margin-bottom': '25px !important',
                'position': 'relative !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;';
            });
        });

        // Ensure form rows are visible
        $('.redco-form-row, .redco-setting-row').each(function() {
            $(this).css({
                'display': 'flex !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'margin-bottom': '15px !important',
                'position': 'relative !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 15px !important; position: relative !important;';
            });
        });

        // Ensure toggle rows are visible
        $('.redco-toggle-row').each(function() {
            $(this).css({
                'display': 'flex !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'justify-content': 'space-between !important',
                'align-items': 'center !important',
                'margin-bottom': '15px !important',
                'position': 'relative !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; justify-content: space-between !important; align-items: center !important; margin-bottom: 15px !important; position: relative !important;';
            });
        });

        // Ensure form fields are visible
        $('.redco-form-field, .redco-setting-field').each(function() {
            $(this).css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'position': 'relative !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; position: relative !important;';
            });
        });

        // Ensure form labels are visible
        $('.redco-form-label, .redco-setting-label').each(function() {
            $(this).css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'position': 'relative !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; position: relative !important;';
            });
        });

        // Ensure toggle info is visible
        $('.redco-toggle-info').each(function() {
            $(this).css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'position': 'relative !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; position: relative !important;';
            });
        });

        // Ensure toggle controls are visible
        $('.redco-toggle-control').each(function() {
            $(this).css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'position': 'relative !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; position: relative !important;';
            });
        });

        // Ensure form inputs are visible
        $('.redco-input, .redco-select, .redco-textarea').each(function() {
            $(this).css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'position': 'relative !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; position: relative !important;';
            });
        });

        // Log visibility status
        console.log('Modal sections visible:', $('.redco-modal-section:visible').length);
        console.log('Form rows visible:', $('.redco-form-row:visible, .redco-setting-row:visible').length);
        console.log('Toggle rows visible:', $('.redco-toggle-row:visible').length);
    }

    /**
     * Fix Advanced Cache Preloader modal specifically
     */
    function fixAdvancedCachePreloaderModal() {
        console.log('Fixing Advanced Cache Preloader modal');

        // Check if we're in the Advanced Cache Preloader modal
        if ($('.redco-modal-content:visible').length > 0 &&
            ($('.redco-modal-header h2:contains("Advanced Cache Preloader")').length > 0 ||
             $('.redco-modal-content:contains("Cache Preloader Settings")').length > 0)) {

            console.log('Advanced Cache Preloader modal confirmed');

            // First, let's make sure the modal is properly sized
            $('.redco-modal').css({
                'max-height': '95vh',
                'height': 'auto',
                'width': '800px',
                'max-width': '95%',
                'display': 'flex',
                'flex-direction': 'column',
                'overflow': 'hidden'
            });

            $('.redco-modal-content').css({
                'max-height': 'calc(95vh - 140px)',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
                'flex': '1 1 auto',
                'padding': '25px',
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Add a debug message to the modal
            if ($('.redco-modal-debug-message').length === 0) {
                $('<div>')
                    .addClass('redco-modal-debug-message')
                    .html('<div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; text-align: center;">' +
                          '<p style="font-weight: bold; margin: 0;">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                          '<div style="margin-top: 10px; font-size: 24px; animation: bounce 1s infinite;">↓</div>' +
                          '</div>' +
                          '<style>@keyframes bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }</style>')
                    .prependTo('.redco-modal-content');
            }

            // Check for missing sections
            var expectedSections = [
                'Advanced Settings',
                'Content Type Settings',
                'Priority Settings'
            ];

            var missingSections = [];

            expectedSections.forEach(function(section) {
                if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                    missingSections.push(section);
                }
            });

            if (missingSections.length > 0) {
                console.log('Missing sections detected: ' + missingSections.join(', '));

                // Try to load the missing sections from the template
                loadMissingSections(missingSections);
            } else {
                console.log('All expected sections found');
            }

            // Force all sections to be visible
            $('.redco-modal-section').show().css({
                'display': 'block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'margin-bottom': '25px !important'
            }).attr('style', function(i, s) {
                return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important;';
            });

            // Add a scroll indicator at the bottom
            if ($('.redco-scroll-indicator-bottom').length === 0) {
                $('<div>')
                    .addClass('redco-scroll-indicator-bottom')
                    .html('<div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-top: 20px; text-align: center;">' +
                          '<p style="font-weight: bold; margin: 0;">End of settings</p>' +
                          '<div style="margin-top: 5px;">Don\'t forget to save your changes!</div>' +
                          '</div>')
                    .appendTo('.redco-modal-content');
            }
        }
    }

    /**
     * Load missing sections for the Advanced Cache Preloader modal
     */
    function loadMissingSections(missingSections) {
        console.log('Loading missing sections: ' + missingSections.join(', '));

        // Check if Advanced Settings section is missing
        if (missingSections.includes('Advanced Settings')) {
            console.log('Adding Advanced Settings section');

            var advancedSettingsHtml =
                '<div class="redco-modal-section" style="display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;">' +
                    '<h3 style="display: block !important; visibility: visible !important; opacity: 1 !important;">Advanced Settings</h3>' +

                    '<div class="redco-form-row">' +
                        '<div class="redco-form-label">' +
                            '<label for="preload_throttle">Preload Throttle</label>' +
                        '</div>' +
                        '<div class="redco-form-field">' +
                            '<input type="number" id="preload_throttle" name="preload_throttle" class="redco-input" value="3" min="0" max="60">' +
                            '<p class="redco-form-help">Delay in seconds between preloading each URL. Set to 0 for no delay.</p>' +
                        '</div>' +
                    '</div>' +

                    '<div class="redco-form-row">' +
                        '<div class="redco-form-label">' +
                            '<label for="preload_batch_size">Batch Size</label>' +
                        '</div>' +
                        '<div class="redco-form-field">' +
                            '<input type="number" id="preload_batch_size" name="preload_batch_size" class="redco-input" value="20" min="1" max="100">' +
                            '<p class="redco-form-help">Number of URLs to preload in each batch. Lower values reduce server load.</p>' +
                        '</div>' +
                    '</div>' +

                    '<div class="redco-toggle-row">' +
                        '<div class="redco-toggle-info">' +
                            '<h4>Mobile Preload</h4>' +
                            '<p>Preload cache for mobile devices.</p>' +
                        '</div>' +
                        '<div class="redco-toggle-control">' +
                            '<label class="redco-switch">' +
                                '<input type="checkbox" name="mobile_preload" value="1">' +
                                '<span class="redco-slider"></span>' +
                            '</label>' +
                        '</div>' +
                    '</div>' +

                    '<div class="redco-toggle-row">' +
                        '<div class="redco-toggle-info">' +
                            '<h4>Desktop Preload</h4>' +
                            '<p>Preload cache for desktop devices.</p>' +
                        '</div>' +
                        '<div class="redco-toggle-control">' +
                            '<label class="redco-switch">' +
                                '<input type="checkbox" name="desktop_preload" value="1" checked>' +
                                '<span class="redco-slider"></span>' +
                            '</label>' +
                        '</div>' +
                    '</div>' +
                '</div>';

            // Append to the modal content before the footer
            $('.redco-modal-footer').before(advancedSettingsHtml);
        }

        // Check if Content Type Settings section is missing
        if (missingSections.includes('Content Type Settings')) {
            console.log('Adding Content Type Settings section');

            var contentTypeSettingsHtml =
                '<div class="redco-modal-section" style="display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;">' +
                    '<h3 style="display: block !important; visibility: visible !important; opacity: 1 !important;">Content Type Settings</h3>' +

                    '<div class="redco-toggle-row">' +
                        '<div class="redco-toggle-info">' +
                            '<h4>Preload Posts</h4>' +
                            '<p>Preload cache for posts.</p>' +
                        '</div>' +
                        '<div class="redco-toggle-control">' +
                            '<label class="redco-switch">' +
                                '<input type="checkbox" name="preload_posts" value="1" checked>' +
                                '<span class="redco-slider"></span>' +
                            '</label>' +
                        '</div>' +
                    '</div>' +

                    '<div class="redco-toggle-row">' +
                        '<div class="redco-toggle-info">' +
                            '<h4>Preload Pages</h4>' +
                            '<p>Preload cache for pages.</p>' +
                        '</div>' +
                        '<div class="redco-toggle-control">' +
                            '<label class="redco-switch">' +
                                '<input type="checkbox" name="preload_pages" value="1" checked>' +
                                '<span class="redco-slider"></span>' +
                            '</label>' +
                        '</div>' +
                    '</div>' +

                    '<div class="redco-toggle-row">' +
                        '<div class="redco-toggle-info">' +
                            '<h4>Preload Categories</h4>' +
                            '<p>Preload cache for category archives.</p>' +
                        '</div>' +
                        '<div class="redco-toggle-control">' +
                            '<label class="redco-switch">' +
                                '<input type="checkbox" name="preload_categories" value="1" checked>' +
                                '<span class="redco-slider"></span>' +
                            '</label>' +
                        '</div>' +
                    '</div>' +
                '</div>';

            // Append to the modal content before the footer
            $('.redco-modal-footer').before(contentTypeSettingsHtml);
        }

        // Check if Priority Settings section is missing
        if (missingSections.includes('Priority Settings')) {
            console.log('Adding Priority Settings section');

            var prioritySettingsHtml =
                '<div class="redco-modal-section" style="display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;">' +
                    '<h3 style="display: block !important; visibility: visible !important; opacity: 1 !important;">Priority Settings</h3>' +

                    '<div class="redco-form-row">' +
                        '<div class="redco-form-label">' +
                            '<label for="preload_priority">Preload Priority</label>' +
                        '</div>' +
                        '<div class="redco-form-field">' +
                            '<select id="preload_priority" name="preload_priority" class="redco-select">' +
                                '<option value="homepage_first" selected>Homepage First</option>' +
                                '<option value="recent_content">Recent Content First</option>' +
                                '<option value="popular_content">Popular Content First</option>' +
                                '<option value="sequential">Sequential (No Priority)</option>' +
                            '</select>' +
                            '<p class="redco-form-help">Determine which content should be preloaded first.</p>' +
                        '</div>' +
                    '</div>' +

                    '<div class="redco-form-row">' +
                        '<div class="redco-form-label">' +
                            '<label for="preload_depth">Preload Depth</label>' +
                        '</div>' +
                        '<div class="redco-form-field">' +
                            '<select id="preload_depth" name="preload_depth" class="redco-select">' +
                                '<option value="1">Level 1 (Homepage Only)</option>' +
                                '<option value="2" selected>Level 2 (Homepage + Main Pages)</option>' +
                                '<option value="3">Level 3 (Deep Preload)</option>' +
                                '<option value="all">All Pages (Complete Preload)</option>' +
                            '</select>' +
                            '<p class="redco-form-help">How deep to follow links when preloading. Higher values may increase server load.</p>' +
                        '</div>' +
                    '</div>' +
                '</div>';

            // Append to the modal content before the footer
            $('.redco-modal-footer').before(prioritySettingsHtml);
        }
    }

})(jQuery);
