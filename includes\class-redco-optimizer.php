<?php
/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * Also maintains the unique identifier of this plugin as well as the current
 * version of the plugin.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer {

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Redco_Optimizer_Loader    $loader    Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * The unique identifier of this plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    The string used to uniquely identify this plugin.
     */
    protected $plugin_name;

    /**
     * The current version of the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    The current version of the plugin.
     */
    protected $version;

    /**
     * The modules of this plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      array    $modules    The modules of this plugin.
     */
    protected $modules = array();

    /**
     * Define the core functionality of the plugin.
     *
     * Set the plugin name and the plugin version that can be used throughout the plugin.
     * Load the dependencies, define the locale, and set the hooks for the admin area and
     * the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->version = REDCO_OPTIMIZER_VERSION;
        $this->plugin_name = REDCO_OPTIMIZER_PLUGIN_NAME;

        $this->load_dependencies();
        $this->set_locale();
        $this->load_modules();
        $this->load_addons();
        $this->define_admin_hooks();
        $this->define_public_hooks();
    }

    /**
     * Load the addons for this plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_addons() {
        // Initialize the addons class
        global $redco_optimizer_addons;
        $redco_optimizer_addons = new Redco_Optimizer_Addons();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * Include the following files that make up the plugin:
     *
     * - Redco_Optimizer_Loader. Orchestrates the hooks of the plugin.
     * - Redco_Optimizer_i18n. Defines internationalization functionality.
     * - Redco_Optimizer_Admin. Defines all hooks for the admin area.
     * - Redco_Optimizer_Public. Defines all hooks for the public side of the site.
     *
     * Create an instance of the loader which will be used to register the hooks
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * The class responsible for orchestrating the actions and filters of the
         * core plugin.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-loader.php';

        /**
         * The class responsible for defining internationalization functionality
         * of the plugin.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-i18n.php';

        /**
         * The class responsible for defining all actions that occur in the admin area.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-admin.php';

        /**
         * The class responsible for defining all actions that occur in the public-facing
         * side of the site.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-public.php';

        /**
         * The base module class that all modules will extend.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'modules/class-redco-optimizer-module.php';

        /**
         * The base class for all add-ons.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-addon.php';

        /**
         * The class responsible for handling addons.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-addons.php';

        /**
         * The class responsible for handling addon AJAX requests.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-addons-ajax.php';

        /**
         * The class responsible for handling AJAX requests.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-ajax.php';

        /**
         * The class responsible for error logging.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-error-logger.php';

        /**
         * The class responsible for handling error logs AJAX requests.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-error-logs-ajax.php';

        /**
         * The class responsible for handling error logs downloads.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-error-logs-download.php';

        /**
         * The class responsible for handling UI state.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-ui-state.php';

        /**
         * The class responsible for help documentation.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-help.php';

        /**
         * The class responsible for the standalone help page.
         */
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-help-page.php';

        $this->loader = new Redco_Optimizer_Loader();
    }

    /**
     * Define the locale for this plugin for internationalization.
     *
     * Uses the Redco_Optimizer_i18n class in order to set the domain and to register the hook
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function set_locale() {
        $plugin_i18n = new Redco_Optimizer_i18n();
        $this->loader->add_action( 'plugins_loaded', $plugin_i18n, 'load_plugin_textdomain' );
    }

    /**
     * Load the modules for this plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_modules() {
        // Get the modules from options
        $modules_config = get_option( 'redco_optimizer_modules', array() );

        // Load each module
        foreach ( $modules_config as $module_id => $module_config ) {
            $module_file = REDCO_OPTIMIZER_PLUGIN_PATH . 'modules/' . $module_id . '/class-redco-optimizer-' . $module_id . '.php';

            if ( file_exists( $module_file ) ) {
                require_once $module_file;

                $class_name = 'Redco_Optimizer_' . str_replace( '-', '_', ucwords( $module_id, '-' ) );

                if ( class_exists( $class_name ) ) {
                    $this->modules[$module_id] = new $class_name( $module_id, $this->loader );
                }
            }
        }
    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_admin_hooks() {
        $plugin_admin = new Redco_Optimizer_Admin( $this->get_plugin_name(), $this->get_version() );

        // Make the admin instance globally available
        global $redco_optimizer_admin;
        $redco_optimizer_admin = $plugin_admin;

        // Register admin hooks
        $this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_styles' );
        $this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts' );
        $this->loader->add_action( 'admin_menu', $plugin_admin, 'add_admin_menu' );

        // Register AJAX hooks
        $this->loader->add_action( 'wp_ajax_redco_optimizer_save_settings', $plugin_admin, 'save_settings' );
        $this->loader->add_action( 'wp_ajax_redco_optimizer_reset_settings', $plugin_admin, 'reset_settings' );
        $this->loader->add_action( 'wp_ajax_redco_optimizer_export_settings', $plugin_admin, 'export_settings' );
        $this->loader->add_action( 'wp_ajax_redco_optimizer_import_settings', $plugin_admin, 'import_settings' );
        $this->loader->add_action( 'wp_ajax_redco_optimizer_get_dashboard_stats', $plugin_admin, 'get_dashboard_stats' );

        // Add action for site health refresh
        $this->loader->add_action( 'redco_refresh_site_health', $plugin_admin, 'refresh_site_health' );

        // Initialize the addons AJAX handler
        new Redco_Optimizer_Addons_Ajax();

        // Initialize the error logs AJAX handler
        new Redco_Optimizer_Error_Logs_Ajax();

        // Initialize the error logs download handler
        new Redco_Optimizer_Error_Logs_Download();

        // Initialize the UI state handler
        new Redco_Optimizer_UI_State();

        // Initialize the help documentation
        new Redco_Optimizer_Help($this->get_plugin_name(), $this->get_version());

        // Initialize the standalone help page
        new Redco_Optimizer_Help_Page($this->get_plugin_name(), $this->get_version());
    }

    /**
     * Register all of the hooks related to the public-facing functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_public_hooks() {
        $plugin_public = new Redco_Optimizer_Public( $this->get_plugin_name(), $this->get_version() );

        $this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_styles' );
        $this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_scripts' );

        // Initialize features
        $this->initialize_caching();
        $this->initialize_file_optimization();
        $this->initialize_lazyload();
        $this->initialize_preload();
        $this->initialize_database();
        $this->initialize_cdn();
        $this->initialize_site_health_inspector();

        // Add buffer hook for processing HTML
        add_action( 'template_redirect', array( $this, 'start_output_buffer' ) );
    }

    /**
     * Start output buffer for processing HTML.
     *
     * @since    1.0.0
     */
    public function start_output_buffer() {
        // Don't buffer admin pages
        if ( is_admin() ) {
            return;
        }

        // Start output buffering
        ob_start( array( $this, 'process_output_buffer' ) );
    }

    /**
     * Process output buffer.
     *
     * @since    1.0.0
     * @param    string    $buffer    The output buffer.
     * @return   string    The processed output buffer.
     */
    public function process_output_buffer( $buffer ) {
        // Don't process if buffer is empty or contains an error
        if ( empty( $buffer ) || is_404() || is_search() || is_feed() || is_trackback() ) {
            return $buffer;
        }

        // Apply filters to the buffer
        $buffer = apply_filters( 'redco_buffer', $buffer );

        return $buffer;
    }

    /**
     * Initialize caching functionality.
     *
     * @since    1.0.0
     * @access   private
     */
    private function initialize_caching() {
        // Get caching settings
        global $redco_optimizer_admin;
        $caching_settings = $redco_optimizer_admin->get_settings('caching');

        // Check if caching is enabled
        if (!$caching_settings['enable_page_caching']) {
            return;
        }

        // Include the caching class
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-caching.php';

        // Initialize the caching class
        $caching = new Redco_Optimizer_Caching($this->get_plugin_name(), $this->get_version());

        // Initialize caching
        $caching->init();
    }

    /**
     * Initialize file optimization functionality.
     *
     * @since    1.0.0
     * @access   private
     */
    private function initialize_file_optimization() {
        global $redco_optimizer_admin;
        $file_optimization_settings = $redco_optimizer_admin->get_settings('file-optimization');

        // Initialize File Exclusions (always load this)
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-file-exclusions.php';

        // Initialize HTML Minification
        if (isset($file_optimization_settings['minify_html']) && $file_optimization_settings['minify_html']) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-html-minification.php';
            $html_minification = new Redco_Optimizer_HTML_Minification($this->get_plugin_name(), $this->get_version());
            $html_minification->init();
        }

        // Initialize JavaScript Defer
        if (isset($file_optimization_settings['defer_js']) && $file_optimization_settings['defer_js']) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-js-defer.php';
            $js_defer = new Redco_Optimizer_JS_Defer($this->get_plugin_name(), $this->get_version());
            $js_defer->init();
        }

        // Initialize Remove Unused CSS
        if (isset($file_optimization_settings['remove_unused_css']) && $file_optimization_settings['remove_unused_css']) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-unused-css.php';
            $unused_css = new Redco_Optimizer_Unused_CSS($this->get_plugin_name(), $this->get_version());
            $unused_css->init();
        }

        // Initialize Delay JavaScript
        if (isset($file_optimization_settings['delay_js']) && $file_optimization_settings['delay_js']) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-delay-js.php';
            $delay_js = new Redco_Optimizer_Delay_JS($this->get_plugin_name(), $this->get_version());
            $delay_js->init();
        }

        // Initialize Async CSS
        if (isset($file_optimization_settings['async_css']) && $file_optimization_settings['async_css']) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-async-css.php';
            $async_css = new Redco_Optimizer_Async_CSS($this->get_plugin_name(), $this->get_version());
            $async_css->init();
        }

        // Initialize Google Fonts Optimization
        if (isset($file_optimization_settings['optimize_google_fonts']) && $file_optimization_settings['optimize_google_fonts']) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-google-fonts.php';
            $google_fonts = new Redco_Optimizer_Google_Fonts($this->get_plugin_name(), $this->get_version());
            $google_fonts->init();
        }

        // Always load the Image Optimizer module for admin functions
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'modules/image-optimizer/class-redco-optimizer-image-optimizer.php';
    }

    /**
     * Initialize lazyload functionality.
     *
     * @since    1.0.0
     * @access   private
     */
    private function initialize_lazyload() {
        global $redco_optimizer_admin;
        $lazyload_settings = $redco_optimizer_admin->get_settings('lazyload');

        // Initialize LazyLoad CSS Background Images
        if (isset($lazyload_settings['lazyload_css_bg']) && $lazyload_settings['lazyload_css_bg']) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-lazyload-css-bg.php';
            $lazyload_css_bg = new Redco_Optimizer_LazyLoad_CSS_BG($this->get_plugin_name(), $this->get_version());
            $lazyload_css_bg->init();
        }

        // Initialize LazyLoad Iframes and Videos
        if ((isset($lazyload_settings['lazyload_iframes']) && $lazyload_settings['lazyload_iframes']) ||
            (isset($lazyload_settings['lazyload_videos']) && $lazyload_settings['lazyload_videos'])) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-lazyload-iframe.php';
            $lazyload_iframe = new Redco_Optimizer_LazyLoad_Iframe($this->get_plugin_name(), $this->get_version());
            $lazyload_iframe->init();
        }
    }

    /**
     * Initialize preload functionality.
     *
     * @since    1.0.0
     * @access   private
     */
    private function initialize_preload() {
        global $redco_optimizer_admin;
        $preload_settings = $redco_optimizer_admin->get_settings('preload');

        // Check if any preload feature is enabled
        if ((isset($preload_settings['preload_fonts']) && $preload_settings['preload_fonts']) ||
            (isset($preload_settings['preload_links']) && $preload_settings['preload_links']) ||
            (isset($preload_settings['prefetch_dns']) && $preload_settings['prefetch_dns']) ||
            (isset($preload_settings['preload_cache']) && $preload_settings['preload_cache'])) {

            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-preload.php';
            $preload = new Redco_Optimizer_Preload($this->get_plugin_name(), $this->get_version());
            $preload->init();
        }
    }

    /**
     * Initialize database optimization functionality.
     *
     * @since    1.0.0
     * @access   private
     */
    private function initialize_database() {
        global $redco_optimizer_admin;
        $database_settings = $redco_optimizer_admin->get_settings('database');

        // Check if database optimization is enabled
        if (isset($database_settings['schedule_cleanup']) && $database_settings['schedule_cleanup']) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-redco-optimizer-database.php';
            $database = new Redco_Optimizer_Database($this->get_plugin_name(), $this->get_version());
            $database->init();
        }
    }

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    1.0.0
     */
    public function run() {
        $this->loader->run();
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @since     1.0.0
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * The reference to the class that orchestrates the hooks with the plugin.
     *
     * @since     1.0.0
     * @return    Redco_Optimizer_Loader    Orchestrates the hooks of the plugin.
     */
    public function get_loader() {
        return $this->loader;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     1.0.0
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }

    /**
     * Initialize CDN functionality.
     *
     * @since    1.0.0
     * @access   private
     */
    private function initialize_cdn() {
        global $redco_optimizer_admin;

        // Get the modules settings
        $modules = get_option('redco_optimizer_modules', array());

        // Check if CDN module is enabled
        if (!isset($modules['cdn']['enabled']) || !$modules['cdn']['enabled']) {
            return;
        }

        // Get CDN settings
        $cdn_settings = get_option('redco_optimizer_cdn_settings', array());

        // Check if CDN is enabled in settings
        if (!isset($cdn_settings['enabled']) || !$cdn_settings['enabled']) {
            return;
        }

        // Include the CDN class
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'modules/cdn/class-redco-optimizer-cdn.php';

        // Initialize the CDN module
        $cdn = new Redco_Optimizer_Cdn('cdn', $this->loader);
    }

    /**
     * Initialize site health inspector functionality.
     *
     * @since    1.0.0
     * @access   private
     */
    private function initialize_site_health_inspector() {
        // Include the site health inspector module
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'modules/site-health-inspector/class-redco-optimizer-site-health-inspector-module.php';

        // Initialize the site health inspector module
        $site_health_inspector = new Redco_Optimizer_Site_Health_Inspector_Module($this->get_plugin_name(), $this->get_version());
    }

    /**
     * Get the modules of the plugin.
     *
     * @since     1.0.0
     * @return    array    The modules of the plugin.
     */
    public function get_modules() {
        return $this->modules;
    }

    /**
     * Log an error message.
     *
     * @since     1.0.0
     * @param     string    $message    The error message.
     * @param     string    $level      The error level (error, warning, info).
     * @param     string    $context    The context of the error.
     */
    public static function log_error($message, $level = 'error', $context = '') {
        // Initialize the error logger
        $error_logger = new Redco_Optimizer_Error_Logger();

        // Log the error
        $error_logger->log($message, $level, $context);
    }
}
