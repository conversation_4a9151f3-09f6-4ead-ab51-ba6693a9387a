<?php
/**
 * Critical CSS Generator Add-on
 *
 * @link              https://redco-optimizer.com
 * @since             1.0.0
 * @package           Redco_Optimizer
 *
 * @wordpress-plugin
 * Addon Name:        Critical CSS Generator
 * Description:       Automatically generates and inlines critical CSS for faster page rendering.
 * Version:           1.0.0
 * Author:            Redco
 * Author URI:        https://redco-optimizer.com
 * Premium:           false
 * Coming_Soon:       true
 * Has Settings:      true
 * Icon:              dashicons-editor-code
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Critical CSS Generator class.
 */
class Redco_Optimizer_Critical_CSS_Generator {

    /**
     * The settings for this addon.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for this addon.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Load settings
        $this->settings = get_option('redco_critical_css_generator_settings', array(
            'enabled' => false,
            'async_css' => true,
            'remove_unused_css' => true,
            'viewport_width' => 1366,
            'viewport_height' => 768,
            'mobile_critical_css' => true,
            'mobile_viewport_width' => 375,
            'mobile_viewport_height' => 667,
            'critical_css_timeout' => 30,
            'css_exclusions' => '',
            'fallback_css' => true,
            'regenerate_on_update' => true,
            'include_google_fonts' => true,
            'include_custom_fonts' => true,
            'include_inline_css' => true,
        ));

        // Register hooks
        $this->register_hooks();
    }

    /**
     * Register all hooks for this addon.
     *
     * @since    1.0.0
     */
    private function register_hooks() {
        // Add admin page
        add_action('admin_menu', array($this, 'add_admin_page'));

        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add AJAX handlers
        add_action('wp_ajax_redco_generate_critical_css', array($this, 'ajax_generate_critical_css'));
        add_action('wp_ajax_redco_clear_critical_css', array($this, 'ajax_clear_critical_css'));

        // Add critical CSS to head if enabled
        if ($this->is_enabled()) {
            add_action('wp_head', array($this, 'add_critical_css'), 1);
            add_filter('style_loader_tag', array($this, 'modify_stylesheet_tag'), 10, 4);
        }
    }

    /**
     * Check if Critical CSS Generator is enabled.
     *
     * @since    1.0.0
     * @return   bool    True if Critical CSS Generator is enabled, false otherwise.
     */
    public function is_enabled() {
        return isset($this->settings['enabled']) && $this->settings['enabled'];
    }

    /**
     * Add admin page.
     *
     * @since    1.0.0
     */
    public function add_admin_page() {
        add_submenu_page(
            'redco-optimizer-addons',
            __('Critical CSS Generator', 'redco-optimizer'),
            __('Critical CSS Generator', 'redco-optimizer'),
            'manage_options',
            'redco-optimizer-critical-css-generator',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Enqueue scripts and styles.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts($hook) {
        if ($hook === 'redco-optimizer_page_redco-optimizer-critical-css-generator') {
            wp_enqueue_script('redco-critical-css-generator', plugin_dir_url(__FILE__) . 'js/critical-css-generator.js', array('jquery'), '1.0.0', true);
            wp_localize_script('redco-critical-css-generator', 'redco_critical_css', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('redco_critical_css_nonce'),
            ));
        }

        // Enqueue the modal fix script on all admin pages
        if (is_admin()) {
            wp_enqueue_script('redco-critical-css-generator-modal-fix', plugin_dir_url(__FILE__) . 'js/modal-fix.js', array('jquery'), '1.0.0', true);
        }
    }

    /**
     * Render the settings page.
     *
     * @since    1.0.0
     */
    public function render_settings_page() {
        // Check if this addon's settings are being displayed
        if (!isset($_GET['addon']) || $_GET['addon'] !== 'critical-css-generator') {
            return;
        }

        // Save settings if form is submitted
        if (isset($_POST['redco_critical_css_generator_save_settings'])) {
            $this->save_settings();
        }

        // Include settings template
        include_once plugin_dir_path(__FILE__) . 'templates/settings.php';
    }

    /**
     * Save settings.
     *
     * @since    1.0.0
     */
    public function save_settings() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Verify nonce
        if (!isset($_POST['redco_critical_css_generator_nonce']) || !wp_verify_nonce($_POST['redco_critical_css_generator_nonce'], 'redco_critical_css_generator_save_settings')) {
            add_settings_error('redco_critical_css_generator', 'redco_critical_css_generator_nonce', __('Security check failed.', 'redco-optimizer'), 'error');
            return;
        }

        // Get settings
        $settings = array(
            'enabled' => isset($_POST['enabled']) ? 1 : 0,
            'async_css' => isset($_POST['async_css']) ? 1 : 0,
            'remove_unused_css' => isset($_POST['remove_unused_css']) ? 1 : 0,
            'viewport_width' => intval($_POST['viewport_width']),
            'viewport_height' => intval($_POST['viewport_height']),
            'mobile_critical_css' => isset($_POST['mobile_critical_css']) ? 1 : 0,
            'mobile_viewport_width' => intval($_POST['mobile_viewport_width']),
            'mobile_viewport_height' => intval($_POST['mobile_viewport_height']),
            'critical_css_timeout' => intval($_POST['critical_css_timeout']),
            'css_exclusions' => sanitize_textarea_field($_POST['css_exclusions']),
            'fallback_css' => isset($_POST['fallback_css']) ? 1 : 0,
            'regenerate_on_update' => isset($_POST['regenerate_on_update']) ? 1 : 0,
            'include_google_fonts' => isset($_POST['include_google_fonts']) ? 1 : 0,
            'include_custom_fonts' => isset($_POST['include_custom_fonts']) ? 1 : 0,
            'include_inline_css' => isset($_POST['include_inline_css']) ? 1 : 0,
        );

        // Update settings
        update_option('redco_critical_css_generator_settings', $settings);
        $this->settings = $settings;

        // Add success message
        add_settings_error('redco_critical_css_generator', 'redco_critical_css_generator_updated', __('Settings saved.', 'redco-optimizer'), 'success');
    }

    /**
     * Generate critical CSS via AJAX.
     *
     * @since    1.0.0
     */
    public function ajax_generate_critical_css() {
        // Check nonce
        check_ajax_referer('redco_critical_css_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get URL to generate critical CSS for
        $url = isset($_POST['url']) ? esc_url_raw($_POST['url']) : home_url();

        // Generate critical CSS
        $result = $this->generate_critical_css($url);

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        } else {
            wp_send_json_success(array('message' => __('Critical CSS generated successfully!', 'redco-optimizer')));
        }
    }

    /**
     * Clear critical CSS via AJAX.
     *
     * @since    1.0.0
     */
    public function ajax_clear_critical_css() {
        // Check nonce
        check_ajax_referer('redco_critical_css_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Clear critical CSS
        $this->clear_critical_css();

        wp_send_json_success(array('message' => __('Critical CSS cleared successfully!', 'redco-optimizer')));
    }

    /**
     * Generate critical CSS.
     *
     * @since    1.0.0
     * @param    string    $url    The URL to generate critical CSS for.
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function generate_critical_css($url) {
        // This is a placeholder. In a real implementation, this would generate the critical CSS.
        return true;
    }

    /**
     * Clear critical CSS.
     *
     * @since    1.0.0
     */
    private function clear_critical_css() {
        // This is a placeholder. In a real implementation, this would clear the critical CSS.
        return true;
    }

    /**
     * Add critical CSS to head.
     *
     * @since    1.0.0
     */
    public function add_critical_css() {
        // This is a placeholder. In a real implementation, this would add the critical CSS to the head.
        echo '<style id="redco-critical-css">/* Critical CSS would be added here */</style>';
    }

    /**
     * Modify stylesheet tag.
     *
     * @since    1.0.0
     * @param    string    $tag        The link tag for the enqueued style.
     * @param    string    $handle     The style's registered handle.
     * @param    string    $href       The stylesheet's source URL.
     * @param    string    $media      The stylesheet's media attribute.
     * @return   string    The modified link tag.
     */
    public function modify_stylesheet_tag($tag, $handle, $href, $media) {
        // This is a placeholder. In a real implementation, this would modify the stylesheet tag.
        return $tag;
    }
}

// Initialize the add-on
$redco_critical_css_generator = new Redco_Optimizer_Critical_CSS_Generator();
