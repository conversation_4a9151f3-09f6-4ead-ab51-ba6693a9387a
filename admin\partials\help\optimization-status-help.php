<?php
/**
 * Optimization Status Help Documentation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials/help
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="redco-help-section" id="optimization-status-help">
    <div class="redco-help-header">
        <h2><?php esc_html_e('Optimization Status', 'redco-optimizer'); ?></h2>
        <p class="redco-help-description"><?php esc_html_e('The Optimization Status section provides a visual overview of all enabled and disabled optimization features.', 'redco-optimizer'); ?></p>
    </div>

    <div class="redco-help-content">
        <h3><?php esc_html_e('Understanding the Status Indicators', 'redco-optimizer'); ?></h3>
        <p><?php esc_html_e('Each optimization feature is represented by a status indicator:', 'redco-optimizer'); ?></p>
        <ul>
            <li><strong><?php esc_html_e('Green Circle', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Feature is enabled and working properly', 'redco-optimizer'); ?></li>
            <li><strong><?php esc_html_e('Gray Circle', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Feature is disabled or not configured', 'redco-optimizer'); ?></li>
        </ul>
        
        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Caching Features', 'redco-optimizer'); ?></h4>
            <ul>
                <li><strong><?php esc_html_e('Page Caching', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Creates static HTML files of your pages to reduce server processing time. This is one of the most effective ways to improve site speed.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Browser Caching', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Instructs visitors\' browsers to store certain files locally, reducing load times for returning visitors.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Mobile Caching', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Creates separate cache files optimized for mobile devices.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Preload Cache', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Automatically generates cache files for your most important pages, ensuring they\'re always cached.', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('File Optimization Features', 'redco-optimizer'); ?></h4>
            <ul>
                <li><strong><?php esc_html_e('HTML Minification', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Removes unnecessary characters from HTML files to reduce their size.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('CSS Minification', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Removes unnecessary characters from CSS files to reduce their size.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('JS Minification', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Removes unnecessary characters from JavaScript files to reduce their size.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('CSS Combination', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Combines multiple CSS files into one to reduce HTTP requests.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('JS Combination', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Combines multiple JavaScript files into one to reduce HTTP requests.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('CSS Defer Optimization', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Loads CSS files asynchronously to prevent render-blocking.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('JS Defer Optimization', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Defers the loading of JavaScript files to improve page rendering speed.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Google Fonts Optimization', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Optimizes the loading of Google Fonts to improve performance.', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Media Optimization Features', 'redco-optimizer'); ?></h4>
            <ul>
                <li><strong><?php esc_html_e('Image Optimization', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Compresses images to reduce their file size while maintaining quality.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Lazy Loading Images', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Delays loading of images until they enter the viewport, improving initial page load time.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('WebP Conversion', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Converts images to the WebP format, which provides better compression than JPEG or PNG.', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Advanced Features', 'redco-optimizer'); ?></h4>
            <ul>
                <li><strong><?php esc_html_e('Database Cleanup', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Removes unnecessary data from your database to improve performance.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Heartbeat Control', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Manages the WordPress Heartbeat API to reduce server load.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('DNS Prefetching', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Pre-resolves domain names for external resources to reduce lookup time.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('CDN Integration', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Integrates with Content Delivery Networks to serve assets from global locations.', 'redco-optimizer'); ?></li>
                <li><strong><?php esc_html_e('Site Health Scanning', 'redco-optimizer'); ?></strong>: <?php esc_html_e('Monitors your site for potential issues and provides recommendations.', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Best Practices for Optimization', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('For optimal performance, we recommend enabling these core features:', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Page Caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Browser Caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Image Optimization', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Lazy Loading Images', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('CSS and JS Minification', 'redco-optimizer'); ?></li>
            </ol>
            <p><?php esc_html_e('Advanced users may benefit from enabling additional features, but we recommend testing each change individually to ensure compatibility with your site.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h4><?php esc_html_e('Troubleshooting', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('If you experience issues after enabling certain features:', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Clear your browser cache and the Redco Optimizer cache', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Disable the most recently enabled feature to see if it resolves the issue', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Check for conflicts with other plugins by temporarily disabling them', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Review your site in an incognito/private browsing window to rule out browser extension conflicts', 'redco-optimizer'); ?></li>
            </ol>
        </div>
    </div>
</div>
