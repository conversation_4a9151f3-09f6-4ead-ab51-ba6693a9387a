<?php
/**
 * Advanced Cache Preloader Settings Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/advanced-cache-preloader/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings and status from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
$status = isset($status) ? $status : (isset($this->status) ? $this->status : array(
    'is_preloading' => false,
    'last_preload' => 0,
    'next_preload' => 0,
    'total_urls' => 0,
    'processed_urls' => 0,
    'success_urls' => 0,
    'failed_urls' => 0,
    'current_url' => '',
    'log' => array(),
));
?>

<div class="redco-addon-settings-form">
    <form method="post" action="" class="redco-addon-settings-form" data-addon="advanced-cache-preloader">
        <?php wp_nonce_field('redco_advanced_cache_preloader_save_settings', 'redco_advanced_cache_preloader_nonce'); ?>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Cache Preloader Settings', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Enable Cache Preloader', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Enable automatic cache preloading.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="enabled" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="preload_method"><?php esc_html_e('Preload Method', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <select id="preload_method" name="preload_method" class="redco-select">
                            <option value="sitemap" <?php selected(isset($settings['preload_method']) ? $settings['preload_method'] : 'sitemap', 'sitemap'); ?>><?php esc_html_e('Sitemap', 'redco-optimizer'); ?></option>
                            <option value="custom" <?php selected(isset($settings['preload_method']) ? $settings['preload_method'] : 'sitemap', 'custom'); ?>><?php esc_html_e('Custom URLs', 'redco-optimizer'); ?></option>
                            <option value="all" <?php selected(isset($settings['preload_method']) ? $settings['preload_method'] : 'sitemap', 'all'); ?>><?php esc_html_e('All Content', 'redco-optimizer'); ?></option>
                        </select>
                    </div>
                </div>

                <div class="redco-form-row" id="sitemap-url-row">
                    <div class="redco-form-label">
                        <label for="sitemap_url"><?php esc_html_e('Sitemap URL', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="url" id="sitemap_url" name="sitemap_url" class="redco-input" value="<?php echo esc_url(isset($settings['sitemap_url']) ? $settings['sitemap_url'] : ''); ?>" placeholder="https://example.com/sitemap.xml">
                        <p class="redco-form-help"><?php esc_html_e('Enter your sitemap URL. Leave empty to use the default WordPress sitemap.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-form-row" id="custom-urls-row">
                    <div class="redco-form-label">
                        <label for="custom_urls"><?php esc_html_e('Custom URLs', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <textarea id="custom_urls" name="custom_urls" class="redco-textarea" rows="5"><?php echo esc_textarea(isset($settings['custom_urls']) ? $settings['custom_urls'] : ''); ?></textarea>
                        <p class="redco-form-help"><?php esc_html_e('Enter one URL per line.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Schedule Settings', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="preload_schedule"><?php esc_html_e('Preload Schedule', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <select id="preload_schedule" name="preload_schedule" class="redco-select">
                            <option value="hourly" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'hourly'); ?>><?php esc_html_e('Hourly', 'redco-optimizer'); ?></option>
                            <option value="twicedaily" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'twicedaily'); ?>><?php esc_html_e('Twice Daily', 'redco-optimizer'); ?></option>
                            <option value="daily" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'daily'); ?>><?php esc_html_e('Daily', 'redco-optimizer'); ?></option>
                            <option value="weekly" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'weekly'); ?>><?php esc_html_e('Weekly', 'redco-optimizer'); ?></option>
                        </select>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="preload_time"><?php esc_html_e('Preload Time', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="time" id="preload_time" name="preload_time" class="redco-input" value="<?php echo esc_attr(isset($settings['preload_time']) ? $settings['preload_time'] : '00:00'); ?>">
                        <p class="redco-form-help"><?php esc_html_e('Set the time of day to start preloading (server time).', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Preload on Publish', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Preload cache when a post is published.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="preload_on_publish" <?php checked(isset($settings['preload_on_publish']) ? $settings['preload_on_publish'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Preload on Update', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Preload cache when a post is updated.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="preload_on_update" <?php checked(isset($settings['preload_on_update']) ? $settings['preload_on_update'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Preload on Comment', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Preload cache when a comment is posted.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="preload_on_comment" <?php checked(isset($settings['preload_on_comment']) ? $settings['preload_on_comment'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Content Settings', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Preload Homepage', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Preload the homepage.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="preload_homepage" <?php checked(isset($settings['preload_homepage']) ? $settings['preload_homepage'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Preload Categories', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Preload category archives.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="preload_categories" <?php checked(isset($settings['preload_categories']) ? $settings['preload_categories'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Preload Tags', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Preload tag archives.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="preload_tags" <?php checked(isset($settings['preload_tags']) ? $settings['preload_tags'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="preload_post_types"><?php esc_html_e('Post Types', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="text" id="preload_post_types" name="preload_post_types" class="redco-input" value="<?php echo esc_attr(isset($settings['preload_post_types']) ? $settings['preload_post_types'] : 'post,page'); ?>" placeholder="post,page">
                        <p class="redco-form-help"><?php esc_html_e('Comma-separated list of post types to preload.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="preload_depth"><?php esc_html_e('Preload Depth', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="number" id="preload_depth" name="preload_depth" class="redco-input" value="<?php echo intval(isset($settings['preload_depth']) ? $settings['preload_depth'] : 2); ?>" min="1" max="10">
                        <p class="redco-form-help"><?php esc_html_e('Number of pages to preload for paginated content (archives, etc).', 'redco-optimizer'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Advanced Settings', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="preload_throttle"><?php esc_html_e('Preload Throttle', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="number" id="preload_throttle" name="preload_throttle" class="redco-input" value="<?php echo intval(isset($settings['preload_throttle']) ? $settings['preload_throttle'] : 3); ?>" min="0" max="60">
                        <p class="redco-form-help"><?php esc_html_e('Delay in seconds between preloading each URL. Set to 0 for no delay.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="user_agent"><?php esc_html_e('User Agent', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <input type="text" id="user_agent" name="user_agent" class="redco-input" value="<?php echo esc_attr(isset($settings['user_agent']) ? $settings['user_agent'] : 'Redco Cache Preloader'); ?>" placeholder="Redco Cache Preloader">
                        <p class="redco-form-help"><?php esc_html_e('User agent string to use when preloading.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="priority_urls"><?php esc_html_e('Priority URLs', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <textarea id="priority_urls" name="priority_urls" class="redco-textarea" rows="3"><?php echo esc_textarea(isset($settings['priority_urls']) ? $settings['priority_urls'] : ''); ?></textarea>
                        <p class="redco-form-help"><?php esc_html_e('URLs to preload first. Enter one URL per line.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-form-row">
                    <div class="redco-form-label">
                        <label for="exclude_urls"><?php esc_html_e('Exclude URLs', 'redco-optimizer'); ?></label>
                    </div>
                    <div class="redco-form-field">
                        <textarea id="exclude_urls" name="exclude_urls" class="redco-textarea" rows="3"><?php echo esc_textarea(isset($settings['exclude_urls']) ? $settings['exclude_urls'] : ''); ?></textarea>
                        <p class="redco-form-help"><?php esc_html_e('URLs to exclude from preloading. Enter one URL per line.', 'redco-optimizer'); ?></p>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Mobile Preload', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Preload cache for mobile devices.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="mobile_preload" <?php checked(isset($settings['mobile_preload']) ? $settings['mobile_preload'] : 0, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="redco-toggle-row">
                    <div class="redco-toggle-info">
                        <h4><?php esc_html_e('Desktop Preload', 'redco-optimizer'); ?></h4>
                        <p><?php esc_html_e('Preload cache for desktop devices.', 'redco-optimizer'); ?></p>
                    </div>
                    <div class="redco-toggle-control">
                        <label class="redco-switch">
                            <input type="checkbox" name="desktop_preload" <?php checked(isset($settings['desktop_preload']) ? $settings['desktop_preload'] : 1, 1); ?> value="1">
                            <span class="redco-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Preload Status', 'redco-optimizer'); ?></h3>
            </div>
            <div class="redco-card-content">
                <div class="redco-preload-status">
                    <div class="redco-preload-status-info">
                        <p>
                            <strong><?php esc_html_e('Last Preload:', 'redco-optimizer'); ?></strong>
                            <span id="last-preload"><?php echo !empty($status['last_preload']) ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $status['last_preload']) : __('Never', 'redco-optimizer'); ?></span>
                        </p>
                        <p>
                            <strong><?php esc_html_e('Next Preload:', 'redco-optimizer'); ?></strong>
                            <span id="next-preload"><?php echo !empty($status['next_preload']) ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $status['next_preload']) : __('Not scheduled', 'redco-optimizer'); ?></span>
                        </p>
                        <p>
                            <strong><?php esc_html_e('Status:', 'redco-optimizer'); ?></strong>
                            <span id="preload-status"><?php echo isset($status['is_preloading']) && $status['is_preloading'] ? __('Preloading...', 'redco-optimizer') : __('Idle', 'redco-optimizer'); ?></span>
                        </p>
                    </div>

                    <div class="redco-preload-progress">
                        <div class="redco-progress-bar">
                            <div class="redco-progress-bar-inner" style="width: <?php echo isset($status['total_urls']) && $status['total_urls'] > 0 ? (isset($status['processed_urls']) ? ($status['processed_urls'] / $status['total_urls'] * 100) : 0) : 0; ?>%"></div>
                        </div>
                        <div class="redco-progress-stats">
                            <span id="processed-urls"><?php echo isset($status['processed_urls']) ? intval($status['processed_urls']) : 0; ?></span> / <span id="total-urls"><?php echo isset($status['total_urls']) ? intval($status['total_urls']) : 0; ?></span> <?php esc_html_e('URLs processed', 'redco-optimizer'); ?>
                        </div>
                    </div>

                    <div class="redco-preload-actions">
                        <button type="button" id="start-preload" class="redco-button redco-button-secondary" <?php echo isset($status['is_preloading']) && $status['is_preloading'] ? 'disabled' : ''; ?>>
                            <span class="dashicons dashicons-controls-play"></span>
                            <?php esc_html_e('Start Preload', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" id="stop-preload" class="redco-button redco-button-secondary" <?php echo !isset($status['is_preloading']) || !$status['is_preloading'] ? 'disabled' : ''; ?>>
                            <span class="dashicons dashicons-controls-pause"></span>
                            <?php esc_html_e('Stop Preload', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-form-actions">
            <button type="submit" name="redco_advanced_cache_preloader_save_settings" class="redco-button redco-button-primary">
                <span class="dashicons dashicons-yes"></span>
                <?php esc_html_e('Save Settings', 'redco-optimizer'); ?>
            </button>
        </div>
    </form>
</div>
