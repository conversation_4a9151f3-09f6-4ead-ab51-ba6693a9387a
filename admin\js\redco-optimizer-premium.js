/**
 * Premium features management JavaScript
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/js
 */

(function($) {
    'use strict';

    /**
     * Premium features configuration
     */
    const premiumFeatures = {
        // Cache Module - Enterprise Features
        'separate_mobile_cache': 'coming_soon',
        'cache_logged_in_users': 'coming_soon',
        'cache_preloading': 'coming_soon',

        // File Optimization Module - Enterprise Features
        'critical_css': 'coming_soon',
        'async_css': 'coming_soon',
        'self_host_google_fonts': 'coming_soon',
        'advanced_minification': 'coming_soon',
        'optimize_css_delivery': 'coming_soon',

        // Media Module - Enterprise Features
        'bulk_optimization': 'coming_soon',
        'lazyload_videos': 'coming_soon',
        'lazyload_iframes': 'coming_soon',
        'lazyload_css_bg': 'coming_soon',
        'advanced_webp': 'coming_soon',

        // CDN Module - Enterprise Features
        'multi_cdn_providers': 'coming_soon',
        'cdn_analytics': 'coming_soon',
        'advanced_cdn_rules': 'coming_soon',

        // Database Module - Enterprise Features
        'schedule_cleanup': 'coming_soon',
        'advanced_optimization': 'coming_soon',
        'cleanup_orphaned_data': 'coming_soon',
        'cleanup_postmeta': 'coming_soon',
        'cleanup_commentmeta': 'coming_soon',
        'cleanup_orphaned_term_relationships': 'coming_soon',
        'cleanup_wp_options': 'coming_soon',

        // Preload Module - Enterprise Features
        'prefetch_dns': 'coming_soon',
        'preload_links': 'coming_soon',
        'advanced_preload_strategies': 'coming_soon',

        // Site Health Module - Enterprise Features
        'advanced_security_scans': 'coming_soon',
        'performance_monitoring': 'coming_soon',
        'detailed_reports': 'coming_soon',

        // Heartbeat Module - Enterprise Features
        'advanced_heartbeat_control': 'coming_soon'
    };

    /**
     * Initialize premium features management
     */
    function initPremiumFeatures() {
        // Apply premium badges and disable features
        applyPremiumFeatures();

        // Handle premium feature interactions
        handlePremiumInteractions();

        // Monitor for dynamic content changes
        observeContentChanges();
    }

    /**
     * Apply premium features styling and functionality
     */
    function applyPremiumFeatures() {
        Object.keys(premiumFeatures).forEach(function(featureId) {
            const status = premiumFeatures[featureId];
            const $feature = $('[name="' + featureId + '"], #' + featureId);

            if ($feature.length) {
                markFeatureAsPremium($feature, featureId, status);
            }
        });
    }

    /**
     * Mark a feature as premium
     */
    function markFeatureAsPremium($feature, featureId, status) {
        // Look for various container types including the database cleanup structure
        const $container = $feature.closest('.redco-settings-field, .redco-toggle-row, .redco-checkbox-item, .redco-premium-feature');

        if ($container.length) {
            // Add premium class if not already present
            if (!$container.hasClass('redco-premium-feature')) {
                $container.addClass('redco-premium-feature redco-disabled');
            }

            // Disable the input
            $feature.prop('disabled', true);
            $feature.prop('checked', false); // Uncheck if checked

            // Add premium badge to label if not already present
            const badge = createPremiumBadge(status);
            const $label = $container.find('label .redco-checkbox-label, label, h4').first();

            if ($label.length && !$label.find('.redco-premium-badge').length) {
                $label.append(' ' + badge);
            }

            // Add click handler to show premium info
            $container.off('click.premium').on('click.premium', function(e) {
                e.preventDefault();
                e.stopPropagation();
                showPremiumInfo(featureId, status);
                return false;
            });

            // Prevent form submission for premium features
            $feature.off('change.premium').on('change.premium', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).prop('checked', false);
                showPremiumInfo(featureId, status);
                return false;
            });
        }
    }

    /**
     * Create premium badge HTML
     */
    function createPremiumBadge(status) {
        const badgeClass = status === 'coming_soon' ? 'redco-coming-soon-badge' : 'redco-premium-only-badge';
        const badgeText = status === 'coming_soon' ? 'Coming Soon' : 'Pro';

        return '<span class="redco-premium-badge ' + badgeClass + '">' + badgeText + '</span>';
    }

    /**
     * Show premium feature information
     */
    function showPremiumInfo(featureId, status) {
        const title = status === 'coming_soon' ? 'Feature Coming Soon' : 'Pro Feature';
        const message = status === 'coming_soon'
            ? 'This feature is coming soon! We\'re working hard to bring you advanced functionality.'
            : 'This feature is available in the Pro version for advanced functionality.';

        // Show notification or modal
        if (typeof redcoShowNotification === 'function') {
            redcoShowNotification('info', title, message);
        } else {
            alert(message);
        }
    }

    /**
     * Handle premium feature interactions
     */
    function handlePremiumInteractions() {
        // Prevent clicks on premium features
        $(document).on('click', '.redco-premium-feature.redco-disabled', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const featureId = $(this).find('input').attr('name') || $(this).find('input').attr('id');
            if (featureId && premiumFeatures[featureId]) {
                showPremiumInfo(featureId, premiumFeatures[featureId]);
            }

            return false;
        });

        // Prevent form changes on premium features
        $(document).on('change', '.redco-premium-feature.redco-disabled input', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).prop('checked', false);

            const featureId = $(this).attr('name') || $(this).attr('id');
            if (featureId && premiumFeatures[featureId]) {
                showPremiumInfo(featureId, premiumFeatures[featureId]);
            }

            return false;
        });
    }

    /**
     * Observe content changes for dynamic premium feature application
     */
    function observeContentChanges() {
        // Use MutationObserver to watch for dynamically added content
        if (window.MutationObserver) {
            const observer = new MutationObserver(function(mutations) {
                let shouldReapply = false;

                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // Check if any added nodes contain form elements
                        for (let i = 0; i < mutation.addedNodes.length; i++) {
                            const node = mutation.addedNodes[i];
                            if (node.nodeType === 1) { // Element node
                                const $node = $(node);
                                if ($node.find('input, select, textarea').length > 0 || $node.is('input, select, textarea')) {
                                    shouldReapply = true;
                                    break;
                                }
                            }
                        }
                    }
                });

                if (shouldReapply) {
                    // Delay to ensure DOM is fully updated
                    setTimeout(applyPremiumFeatures, 100);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    /**
     * Check if user has premium access
     */
    function hasPremiumAccess() {
        // This will be implemented with license checking
        // For now, always return false (free version)
        return false;
    }

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        initPremiumFeatures();

        // Re-apply premium features when tabs are switched
        $(document).on('redco-tab-switched', function() {
            setTimeout(applyPremiumFeatures, 100);
        });

        // Re-apply premium features when settings are loaded
        $(document).on('redco-settings-loaded', function() {
            setTimeout(applyPremiumFeatures, 100);
        });
    });

    // Expose functions globally for debugging
    window.redcoPremium = {
        applyPremiumFeatures: applyPremiumFeatures,
        premiumFeatures: premiumFeatures,
        hasPremiumAccess: hasPremiumAccess
    };

})(jQuery);
