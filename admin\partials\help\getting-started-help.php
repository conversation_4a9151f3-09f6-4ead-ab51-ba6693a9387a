<?php
/**
 * Getting Started Help Documentation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials/help
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="redco-help-section" id="getting-started-help">
    <div class="redco-help-header">
        <h2><?php esc_html_e('Getting Started with Redco Optimizer', 'redco-optimizer'); ?></h2>
        <p class="redco-help-description"><?php esc_html_e('Welcome to Redco Optimizer! This guide will help you get started with optimizing your WordPress site for maximum performance.', 'redco-optimizer'); ?></p>
    </div>

    <div class="redco-help-content">
        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Quick Start Guide', 'redco-optimizer'); ?></h3>
            <p><?php esc_html_e('Follow these steps to quickly optimize your WordPress site:', 'redco-optimizer'); ?></p>
            <ol>
                <li>
                    <strong><?php esc_html_e('Enable Caching', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Navigate to the Caching tab and enable Page Caching. This single step can improve your site speed by 2-5x.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Optimize Images', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Go to the Media tab and enable Image Optimization and Lazy Loading. Then click "Optimize All Images" to compress existing images.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Minify Files', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Visit the File Optimization tab and enable HTML, CSS, and JavaScript minification to reduce file sizes.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Clean Database', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Go to the Database tab and run a database cleanup to remove unnecessary data.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Check Performance', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Return to the Dashboard to see your improved performance score and further optimization recommendations.', 'redco-optimizer'); ?></p>
                </li>
            </ol>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Understanding the Modules', 'redco-optimizer'); ?></h3>
            <p><?php esc_html_e('Redco Optimizer is organized into modules, each focusing on a specific aspect of website optimization:', 'redco-optimizer'); ?></p>
            <ul>
                <li>
                    <strong><?php esc_html_e('Caching', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Creates static versions of your pages to reduce server processing time.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('File Optimization', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Minifies and combines CSS and JavaScript files to reduce their size and the number of HTTP requests.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Media', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Optimizes images and implements lazy loading to improve page load times.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Preload', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Preloads cache files and resources to ensure fast loading for all visitors.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Database', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Cleans and optimizes your WordPress database to improve performance.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Heartbeat', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Controls the WordPress Heartbeat API to reduce server load.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('CDN', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Integrates with Content Delivery Networks to serve your assets from global locations.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Site Health', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Scans your site for potential issues and provides recommendations for improvement.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Tools', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Provides utilities for importing, exporting, and managing your optimization settings.', 'redco-optimizer'); ?></p>
                </li>
                <li>
                    <strong><?php esc_html_e('Add-Ons', 'redco-optimizer'); ?></strong>
                    <p><?php esc_html_e('Extends the functionality of Redco Optimizer with additional features.', 'redco-optimizer'); ?></p>
                </li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Recommended Configuration', 'redco-optimizer'); ?></h3>
            <p><?php esc_html_e('For most WordPress sites, we recommend the following configuration:', 'redco-optimizer'); ?></p>
            <h4><?php esc_html_e('Caching', 'redco-optimizer'); ?></h4>
            <ul>
                <li><?php esc_html_e('Enable Page Caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable Browser Caching', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Set Cache Lifespan to 10 hours', 'redco-optimizer'); ?></li>
            </ul>

            <h4><?php esc_html_e('File Optimization', 'redco-optimizer'); ?></h4>
            <ul>
                <li><?php esc_html_e('Enable HTML Minification', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable CSS Minification', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable JavaScript Minification', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable JavaScript Defer (test thoroughly after enabling)', 'redco-optimizer'); ?></li>
            </ul>

            <h4><?php esc_html_e('Media', 'redco-optimizer'); ?></h4>
            <ul>
                <li><?php esc_html_e('Enable Image Optimization', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable Lazy Loading for Images', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable WebP Conversion (if available)', 'redco-optimizer'); ?></li>
            </ul>

            <h4><?php esc_html_e('Database', 'redco-optimizer'); ?></h4>
            <ul>
                <li><?php esc_html_e('Run Database Cleanup weekly', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Enable cleanup of post revisions, auto drafts, and transients', 'redco-optimizer'); ?></li>
            </ul>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Testing Your Optimizations', 'redco-optimizer'); ?></h3>
            <p><?php esc_html_e('After implementing optimizations, it\'s important to test your site thoroughly:', 'redco-optimizer'); ?></p>
            <ol>
                <li><?php esc_html_e('Check your site on different devices (desktop, tablet, mobile)', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Test in different browsers (Chrome, Firefox, Safari, Edge)', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Verify that all functionality works correctly (forms, e-commerce, membership areas)', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Use tools like Google PageSpeed Insights to measure improvements', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Monitor your site for any errors or issues', 'redco-optimizer'); ?></li>
            </ol>
            <p><?php esc_html_e('If you encounter any issues, you can easily disable specific optimizations or clear the cache to restore normal functionality.', 'redco-optimizer'); ?></p>
        </div>

        <div class="redco-help-subsection">
            <h3><?php esc_html_e('Getting Help', 'redco-optimizer'); ?></h3>
            <p><?php esc_html_e('If you need assistance with Redco Optimizer:', 'redco-optimizer'); ?></p>
            <ul>
                <li><?php esc_html_e('Consult this help documentation for detailed information about each feature', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Check the FAQ section for answers to common questions', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Use the Tools tab to export your settings or view error logs', 'redco-optimizer'); ?></li>
                <li><?php esc_html_e('Contact our support team if you continue to experience issues', 'redco-optimizer'); ?></li>
            </ul>
        </div>
    </div>
</div>
