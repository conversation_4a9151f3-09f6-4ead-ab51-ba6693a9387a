<?php
/**
 * Final comprehensive script to replace ALL remaining toggle switches
 */

// Change to the plugin directory
chdir(__DIR__);

$files_to_process = [
    'admin/partials/redco-optimizer-admin-display.php',
    'admin/partials/redco-optimizer-admin-modules.php',
    'admin/partials/redco-optimizer-admin-modules-tab.php'
];

foreach ($files_to_process as $file_path) {

if (!file_exists($file_path)) {
    echo "File not found: $file_path\n";
    continue;
}

$content = file_get_contents($file_path);
$original_content = $content;

echo "Processing: $file_path\n";

// Fix syntax errors first
$content = preg_replace('/v value="1"alue="1"/', 'value="1"', $content);
$content = preg_replace('/value="1" value="1"/', 'value="1"', $content);

// Find all toggle switches and replace them manually
$lines = explode("\n", $content);
$new_lines = [];
$in_toggle = false;
$toggle_buffer = [];

for ($i = 0; $i < count($lines); $i++) {
    $line = $lines[$i];

    // Check if this line starts a toggle switch
    if (preg_match('/<label\s+class="[^"]*redco-switch[^"]*"/', $line)) {
        $in_toggle = true;
        $toggle_buffer = [$line];
        continue;
    }

    if ($in_toggle) {
        $toggle_buffer[] = $line;

        // Check if this line ends the toggle switch
        if (preg_match('/<\/label>/', $line)) {
            $in_toggle = false;

            // Extract the input element from the toggle buffer
            $toggle_content = implode("\n", $toggle_buffer);

            // Extract the input tag
            if (preg_match('/(<input[^>]*type="checkbox"[^>]*>)/', $toggle_content, $matches)) {
                $input_tag = $matches[1];

                // Get the indentation from the original label line
                preg_match('/^(\s*)/', $toggle_buffer[0], $indent_matches);
                $indent = $indent_matches[1] ?? '';

                // Create the new label structure
                $new_lines[] = $indent . '<label>';
                $new_lines[] = $indent . '    ' . $input_tag;
                $new_lines[] = $indent . '    <?php esc_html_e(\'Enable\', \'redco-optimizer\'); ?>';
                $new_lines[] = $indent . '</label>';
            } else {
                // If we can't extract the input, keep the original
                $new_lines = array_merge($new_lines, $toggle_buffer);
            }

            $toggle_buffer = [];
            continue;
        }
    }

    if (!$in_toggle) {
        $new_lines[] = $line;
    }
}

$content = implode("\n", $new_lines);

// Remove debug fields
$content = preg_replace('/<!-- Add hidden debug field to track checkbox state -->\s*<input[^>]*class="redco-checkbox-debug-field"[^>]*>\s*/s', '', $content);
$content = preg_replace('/<!-- DIRECT FIX: Add hidden debug field to track checkbox state -->\s*<input[^>]*class="redco-checkbox-debug-field"[^>]*>\s*/s', '', $content);
$content = preg_replace('/<input[^>]*class="redco-checkbox-debug-field"[^>]*>\s*/s', '', $content);

if ($content !== $original_content) {
    file_put_contents($file_path, $content);
    echo "Updated: $file_path\n";

    // Count remaining toggle switches
    $remaining_switches = preg_match_all('/class="[^"]*redco-switch/', $content);
    $remaining_sliders = preg_match_all('/class="[^"]*redco-slider/', $content);
    echo "  Remaining switches: $remaining_switches, sliders: $remaining_sliders\n";
} else {
    echo "No changes needed: $file_path\n";
}

}

echo "Toggle replacement complete!\n";
?>
