/**
 * JavaScript for handling exclusions grid in Redco Optimizer
 * Simple grid layout without collapsible sections
 */
jQuery(document).ready(function($) {
    
    // Handle Delay JavaScript toggle visibility
    function handleDelayJsToggle() {
        const $delayJsToggle = $('#delay_js');
        const $exclusionsGrid = $('.redco-exclusions-grid');
        const $exclusionsRow = $('#delay-js-exclusions-row');
        const $safeModeRow = $('#delay-js-safe-mode-row');
        
        // Set initial state
        const isChecked = $delayJsToggle.is(':checked');
        toggleGridVisibility(isChecked);
        
        // Listen for changes on the Delay JavaScript toggle
        $delayJsToggle.on('change', function() {
            toggleGridVisibility($(this).is(':checked'));
        });
    }
    
    // Toggle grid visibility based on Delay JavaScript toggle state
    function toggleGridVisibility(isVisible) {
        const $exclusionsGrid = $('.redco-exclusions-grid');
        const $exclusionsRow = $('#delay-js-exclusions-row');
        const $safeModeRow = $('#delay-js-safe-mode-row');
        
        if (isVisible) {
            $exclusionsGrid.removeClass('hidden').show();
            $exclusionsRow.show();
            $safeModeRow.show();
        } else {
            $exclusionsGrid.addClass('hidden').hide();
            $exclusionsRow.hide();
            $safeModeRow.hide();
        }
    }
    
    // Handle checkbox changes to update exclusions textarea
    function handleExclusionCheckboxes() {
        // Listen for checkbox changes
        $('.redco-exclusions-grid input[type="checkbox"]').on('change', function() {
            updateExclusionsFromCheckboxes();
            
            // Add visual indicator that settings need to be saved
            const $saveButton = $('.redco-form-actions .redco-button-primary');
            if ($saveButton.length) {
                $saveButton.addClass('redco-button-highlight').css({
                    'animation': 'redco-pulse 1.5s infinite'
                });
                
                // Add CSS for the pulse animation if it doesn't exist
                if (!$('#redco-pulse-animation').length) {
                    $('head').append(`
                        <style id="redco-pulse-animation">
                            @keyframes redco-pulse {
                                0% { box-shadow: 0 0 0 0 rgba(0, 166, 107, 0.7); }
                                70% { box-shadow: 0 0 0 10px rgba(0, 166, 107, 0); }
                                100% { box-shadow: 0 0 0 0 rgba(0, 166, 107, 0); }
                            }
                            .redco-button-highlight {
                                animation: redco-pulse 1.5s infinite;
                            }
                        </style>
                    `);
                }
            }
        });
    }
    
    // Update exclusions textarea based on checked checkboxes
    function updateExclusionsFromCheckboxes() {
        const $exclusionsTextarea = $('#delay_js_exclusions');
        if (!$exclusionsTextarea.length) return;
        
        let customExclusions = $exclusionsTextarea.val().split('\n');
        
        // Filter out any exclusions that match our checkbox patterns
        customExclusions = customExclusions.filter(line => {
            return line.trim() && !isLineMatchingCheckboxPattern(line);
        });
        
        // Get all checked exclusions
        const checkedExclusions = [];
        $('.redco-exclusions-grid input[type="checkbox"]:checked').each(function() {
            const $label = $(this).siblings('.redco-checkbox-text');
            let exclusionText = $label.text().trim();
            
            // Convert display text to script patterns
            exclusionText = convertToScriptPattern(exclusionText);
            
            if (exclusionText) {
                checkedExclusions.push(exclusionText);
            }
        });
        
        // Combine custom exclusions with checked exclusions
        const allExclusions = [...checkedExclusions, ...customExclusions].filter(line => line.trim());
        
        // Update the textarea
        $exclusionsTextarea.val(allExclusions.join('\n'));
    }
    
    // Convert display text to script pattern
    function convertToScriptPattern(displayText) {
        const patterns = {
            'jquery.js': 'jquery',
            'jquery-migrate.min.js': 'jquery-migrate',
            'jquery.min.js': 'jquery',
            'wp-embed.min.js': 'wp-embed',
            'wp-includes': 'wp-includes',
            'comment-reply.min.js': 'comment-reply',
            'Google Analytics': 'analytics',
            'Google Tag': 'gtag',
            'Google Tag Manager': 'gtm',
            'Facebook Pixel': 'fbevents',
            'Hotjar Analytics': 'hotjar',
            'Microsoft Clarity': 'clarity',
            'Google AdSense': 'adsense',
            'Google Ad Manager': 'admanager',
            'Amazon Ads': 'amazon',
            'Stripe': 'stripe',
            'PayPal': 'paypal',
            'Square': 'square',
            'Google reCAPTCHA': 'recaptcha',
            'LiveChat': 'livechat',
            'Intercom Chat': 'intercom',
            'Zopim Chat': 'zopim',
            'Zendesk Support': 'zendesk',
            'Contact Form 7': 'contact-form-7',
            'Contact Form 7 API': 'wpcf7',
            'Formidable Forms': 'formidable',
            'WordPress Media Player': 'mediaelement',
            'Vimeo Player': 'vimeo',
            'YouTube Player': 'youtube',
            'WooCommerce': 'woocommerce',
            'Elementor': 'elementor',
            'Yoast SEO': 'yoast',
            'Jetpack': 'jetpack'
        };
        
        return patterns[displayText] || displayText.toLowerCase();
    }
    
    // Check if a line matches any checkbox pattern
    function isLineMatchingCheckboxPattern(line) {
        if (!line.trim()) return false;
        
        const patterns = [
            'jquery', 'jquery-migrate', 'wp-embed', 'wp-includes', 'comment-reply',
            'analytics', 'gtag', 'gtm', 'fbevents', 'hotjar', 'clarity',
            'adsense', 'admanager', 'amazon', 'stripe', 'paypal', 'square', 'recaptcha',
            'livechat', 'intercom', 'zopim', 'zendesk', 'contact-form-7', 'wpcf7',
            'formidable', 'mediaelement', 'vimeo', 'youtube', 'woocommerce',
            'elementor', 'yoast', 'jetpack'
        ];
        
        return patterns.some(pattern => line.includes(pattern));
    }
    
    // Remove highlight from save button when form is submitted
    $('.redco-form').on('submit', function() {
        $('.redco-button-highlight').removeClass('redco-button-highlight').css({
            'animation': 'none'
        });
    });
    
    // Initialize everything
    handleDelayJsToggle();
    handleExclusionCheckboxes();
});
