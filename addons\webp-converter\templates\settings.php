<?php
/**
 * WebP Converter Settings Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/webp-converter/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
?>

<form method="post" action="" class="redco-addon-settings-form" data-addon="webp-converter">
    <?php wp_nonce_field('redco_webp_converter_save_settings', 'redco_webp_converter_nonce'); ?>

    <div class="redco-card">
        <div class="redco-card-header">
            <h3><?php esc_html_e('WebP Converter Settings', 'redco-optimizer'); ?></h3>
        </div>
        <div class="redco-card-content">
            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Enable WebP Converter', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Automatically convert your images to WebP format for faster loading and better performance.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label>
                        <input type="checkbox" name="enabled" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : 0, 1); ?> value="1">
                        <?php esc_html_e('Enable', 'redco-optimizer'); ?>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="redco-form-actions">
        <button type="submit" class="redco-button redco-button-primary"><?php esc_html_e('Save Settings', 'redco-optimizer'); ?></button>
    </div>
</form>
