<?php
/**
 * Schema Markup Generator Meta Box Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/schema-markup-generator/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}
?>

<div class="redco-schema-meta-box">
    <div class="redco-toggle-row">
        <div class="redco-toggle-info">
            <h4><?php esc_html_e('Enable Custom Schema', 'redco-optimizer'); ?></h4>
            <p><?php esc_html_e('Enable custom schema for this post.', 'redco-optimizer'); ?></p>
        </div>
        <div class="redco-toggle-control">
            <label class="redco-switch">
                <input type="checkbox" name="redco_schema_enabled" id="redco_schema_enabled" <?php checked($schema_enabled, 1); ?> value="1">
                <span class="redco-slider"></span>
            </label>
        </div>
    </div>

    <div class="redco-form-row">
        <div class="redco-form-label">
            <label for="redco_schema_type"><?php esc_html_e('Schema Type', 'redco-optimizer'); ?></label>
        </div>
        <div class="redco-form-field">
            <select id="redco_schema_type" name="redco_schema_type" class="redco-select">
                <option value="Article" <?php selected($schema_type, 'Article'); ?>><?php esc_html_e('Article', 'redco-optimizer'); ?></option>
                <option value="BlogPosting" <?php selected($schema_type, 'BlogPosting'); ?>><?php esc_html_e('Blog Post', 'redco-optimizer'); ?></option>
                <option value="NewsArticle" <?php selected($schema_type, 'NewsArticle'); ?>><?php esc_html_e('News Article', 'redco-optimizer'); ?></option>
                <option value="Product" <?php selected($schema_type, 'Product'); ?>><?php esc_html_e('Product', 'redco-optimizer'); ?></option>
                <option value="FAQPage" <?php selected($schema_type, 'FAQPage'); ?>><?php esc_html_e('FAQ Page', 'redco-optimizer'); ?></option>
                <option value="HowTo" <?php selected($schema_type, 'HowTo'); ?>><?php esc_html_e('How-To', 'redco-optimizer'); ?></option>
                <option value="Recipe" <?php selected($schema_type, 'Recipe'); ?>><?php esc_html_e('Recipe', 'redco-optimizer'); ?></option>
                <option value="Review" <?php selected($schema_type, 'Review'); ?>><?php esc_html_e('Review', 'redco-optimizer'); ?></option>
                <option value="Event" <?php selected($schema_type, 'Event'); ?>><?php esc_html_e('Event', 'redco-optimizer'); ?></option>
                <option value="Service" <?php selected($schema_type, 'Service'); ?>><?php esc_html_e('Service', 'redco-optimizer'); ?></option>
                <option value="SoftwareApplication" <?php selected($schema_type, 'SoftwareApplication'); ?>><?php esc_html_e('Software Application', 'redco-optimizer'); ?></option>
                <option value="WebPage" <?php selected($schema_type, 'WebPage'); ?>><?php esc_html_e('Web Page', 'redco-optimizer'); ?></option>
                <option value="Custom" <?php selected($schema_type, 'Custom'); ?>><?php esc_html_e('Custom', 'redco-optimizer'); ?></option>
            </select>
        </div>
    </div>

    <div class="redco-form-row">
        <div class="redco-form-label">
            <label for="redco_schema_data"><?php esc_html_e('Schema Data (JSON)', 'redco-optimizer'); ?></label>
        </div>
        <div class="redco-form-field">
            <textarea id="redco_schema_data" name="redco_schema_data" class="redco-textarea" rows="10"><?php echo esc_textarea($schema_data); ?></textarea>
            <p class="redco-form-help"><?php esc_html_e('Enter custom schema JSON data. This will override the default schema for this post.', 'redco-optimizer'); ?></p>
        </div>
    </div>

    <div class="redco-schema-templates">
        <h4><?php esc_html_e('Schema Templates', 'redco-optimizer'); ?></h4>
        <p><?php esc_html_e('Click a template to load it into the editor.', 'redco-optimizer'); ?></p>

        <div class="redco-schema-template-buttons">
            <button type="button" class="redco-button redco-button-secondary redco-schema-template" data-template="article">
                <?php esc_html_e('Article', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="redco-button redco-button-secondary redco-schema-template" data-template="product">
                <?php esc_html_e('Product', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="redco-button redco-button-secondary redco-schema-template" data-template="faq">
                <?php esc_html_e('FAQ', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="redco-button redco-button-secondary redco-schema-template" data-template="howto">
                <?php esc_html_e('How-To', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="redco-button redco-button-secondary redco-schema-template" data-template="recipe">
                <?php esc_html_e('Recipe', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="redco-button redco-button-secondary redco-schema-template" data-template="review">
                <?php esc_html_e('Review', 'redco-optimizer'); ?>
            </button>
        </div>
    </div>

    <div class="redco-schema-preview">
        <h4><?php esc_html_e('Schema Preview', 'redco-optimizer'); ?></h4>
        <button type="button" class="redco-button redco-button-secondary" id="redco-preview-schema">
            <span class="dashicons dashicons-visibility"></span>
            <?php esc_html_e('Preview Schema', 'redco-optimizer'); ?>
        </button>

        <div id="redco-schema-preview-container" style="display: none;">
            <pre id="redco-schema-preview-content"></pre>
        </div>
    </div>
</div>

<script type="text/javascript">
    jQuery(document).ready(function($) {
        // Schema templates
        var schemaTemplates = {
            article: {
                '@context': 'https://schema.org',
                '@type': 'Article',
                'headline': '<?php echo esc_js(get_the_title()); ?>',
                'description': '<?php echo esc_js(get_the_excerpt()); ?>',
                'image': '<?php echo esc_js(get_the_post_thumbnail_url()); ?>',
                'datePublished': '<?php echo esc_js(get_the_date('c')); ?>',
                'dateModified': '<?php echo esc_js(get_the_modified_date('c')); ?>',
                'author': {
                    '@type': 'Person',
                    'name': '<?php echo esc_js(get_the_author()); ?>'
                },
                'publisher': {
                    '@type': 'Organization',
                    'name': '<?php echo esc_js(get_bloginfo('name')); ?>',
                    'logo': {
                        '@type': 'ImageObject',
                        'url': ''
                    }
                },
                'mainEntityOfPage': '<?php echo esc_js(get_permalink()); ?>'
            },
            product: {
                '@context': 'https://schema.org',
                '@type': 'Product',
                'name': '<?php echo esc_js(get_the_title()); ?>',
                'description': '<?php echo esc_js(get_the_excerpt()); ?>',
                'image': '<?php echo esc_js(get_the_post_thumbnail_url()); ?>',
                'sku': '',
                'brand': {
                    '@type': 'Brand',
                    'name': ''
                },
                'offers': {
                    '@type': 'Offer',
                    'price': '',
                    'priceCurrency': 'USD',
                    'availability': 'https://schema.org/InStock',
                    'url': '<?php echo esc_js(get_permalink()); ?>'
                },
                'aggregateRating': {
                    '@type': 'AggregateRating',
                    'ratingValue': '4.5',
                    'reviewCount': '10'
                }
            },
            faq: {
                '@context': 'https://schema.org',
                '@type': 'FAQPage',
                'mainEntity': [
                    {
                        '@type': 'Question',
                        'name': '<?php echo esc_js(__('What is', 'redco-optimizer') . ' ' . get_bloginfo('name') . '?'); ?>',
                        'acceptedAnswer': {
                            '@type': 'Answer',
                            'text': '<?php echo esc_js(get_bloginfo('description')); ?>'
                        }
                    },
                    {
                        '@type': 'Question',
                        'name': '<?php echo esc_js(__('How can I contact', 'redco-optimizer') . ' ' . get_bloginfo('name') . '?'); ?>',
                        'acceptedAnswer': {
                            '@type': 'Answer',
                            'text': '<?php echo esc_js(__('You can contact us through our website or email us at', 'redco-optimizer') . ' ' . get_option('admin_email')); ?>'
                        }
                    }
                ]
            },
            howto: {
                '@context': 'https://schema.org',
                '@type': 'HowTo',
                'name': '<?php echo esc_js(get_the_title()); ?>',
                'description': '<?php echo esc_js(get_the_excerpt()); ?>',
                'image': '<?php echo esc_js(get_the_post_thumbnail_url()); ?>',
                'step': [
                    {
                        '@type': 'HowToStep',
                        'name': '<?php echo esc_js(__('Step 1: Visit our website', 'redco-optimizer')); ?>',
                        'text': '<?php echo esc_js(__('Navigate to', 'redco-optimizer') . ' ' . home_url()); ?>',
                        'image': '<?php echo esc_js(get_the_post_thumbnail_url()); ?>',
                        'url': '<?php echo esc_js(home_url()); ?>'
                    },
                    {
                        '@type': 'HowToStep',
                        'name': '<?php echo esc_js(__('Step 2: Contact us', 'redco-optimizer')); ?>',
                        'text': '<?php echo esc_js(__('Reach out to us at', 'redco-optimizer') . ' ' . get_option('admin_email')); ?>',
                        'image': '<?php echo esc_js(get_the_post_thumbnail_url()); ?>',
                        'url': '<?php echo esc_js(home_url('/contact/')); ?>'
                    }
                ]
            },
            recipe: {
                '@context': 'https://schema.org',
                '@type': 'Recipe',
                'name': '<?php echo esc_js(get_the_title()); ?>',
                'description': '<?php echo esc_js(get_the_excerpt()); ?>',
                'image': '<?php echo esc_js(get_the_post_thumbnail_url()); ?>',
                'author': {
                    '@type': 'Person',
                    'name': '<?php echo esc_js(get_the_author()); ?>'
                },
                'prepTime': 'PT15M',
                'cookTime': 'PT1H',
                'totalTime': 'PT1H15M',
                'recipeYield': '4 servings',
                'recipeIngredient': [
                    'Ingredient 1',
                    'Ingredient 2',
                    'Ingredient 3'
                ],
                'recipeInstructions': [
                    {
                        '@type': 'HowToStep',
                        'text': 'Step 1'
                    },
                    {
                        '@type': 'HowToStep',
                        'text': 'Step 2'
                    }
                ]
            },
            review: {
                '@context': 'https://schema.org',
                '@type': 'Review',
                'itemReviewed': {
                    '@type': 'Product',
                    'name': 'Product Name'
                },
                'reviewRating': {
                    '@type': 'Rating',
                    'ratingValue': '4.5',
                    'bestRating': '5'
                },
                'name': '<?php echo esc_js(get_the_title()); ?>',
                'author': {
                    '@type': 'Person',
                    'name': '<?php echo esc_js(get_the_author()); ?>'
                },
                'reviewBody': '<?php echo esc_js(get_the_excerpt()); ?>',
                'datePublished': '<?php echo esc_js(get_the_date('c')); ?>'
            }
        };

        // Load schema template
        $('.redco-schema-template').on('click', function() {
            var template = $(this).data('template');
            var schemaType = template.charAt(0).toUpperCase() + template.slice(1);

            $('#redco_schema_type').val(schemaType);
            $('#redco_schema_data').val(JSON.stringify(schemaTemplates[template], null, 2));
        });

        // Preview schema
        $('#redco-preview-schema').on('click', function() {
            var schemaData = $('#redco_schema_data').val();

            try {
                var formattedSchema = JSON.stringify(JSON.parse(schemaData), null, 2);
                $('#redco-schema-preview-content').text(formattedSchema);
                $('#redco-schema-preview-container').show();
            } catch (e) {
                alert('Invalid JSON: ' + e.message);
            }
        });
    });
</script>
