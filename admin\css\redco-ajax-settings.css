/**
 * Redco Optimizer - AJAX Settings Styles
 * Styles for AJAX-based settings saving without page reloads
 */

/* Loading state for forms */
.redco-form.redco-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.redco-form.redco-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.5);
    z-index: 10;
}

/* Settings saved highlight effect */
.redco-settings-saved {
    animation: redco-settings-saved-pulse 1.5s ease-in-out;
}

@keyframes redco-settings-saved-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 166, 107, 0);
    }
    20% {
        box-shadow: 0 0 0 10px rgba(0, 166, 107, 0.2);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 166, 107, 0);
    }
}

/* Loading state for dashboard stats */
.redco-dashboard-stats.redco-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.redco-dashboard-stats.redco-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.5);
    z-index: 10;
}

/* Loading state for modules grid */
.redco-modules-grid.redco-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.redco-modules-grid.redco-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.5);
    z-index: 10;
}

/* Ensure toggle switches maintain their state */
input[type="checkbox"]:disabled + .redco-slider {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Ensure toggle switches are green when checked */
input[type="checkbox"]:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Ensure toggle switches are gray when unchecked */
input[type="checkbox"]:not(:checked) + .redco-slider {
    background-color: #cbd5e1 !important;
}

/* Ensure toggle switches are always clickable */
.redco-switch {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
}

input[type="checkbox"] {
    pointer-events: auto !important;
    opacity: 1 !important;
}

.redco-slider {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
}

/* Force enable all toggle switches */
.redco-switch input[type="checkbox"],
.redco-switch input[type="checkbox"] + .redco-slider,
.redco-form input[type="checkbox"],
.redco-form input[type="checkbox"] + .redco-slider {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
}

/* Override any disabled attributes */
.redco-switch input[type="checkbox"][disabled],
.redco-form input[type="checkbox"][disabled] {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
}
