<?php
/**
 * Configuration Manager Class
 *
 * This class handles all configuration settings for the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * Configuration Manager Class
 *
 * This class centralizes all configuration settings for the plugin,
 * eliminating hardcoded values and providing a consistent interface
 * for retrieving and updating settings.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Config {

    /**
     * The singleton instance of this class.
     *
     * @since    1.0.0
     * @access   private
     * @var      Redco_Optimizer_Config    $instance    The singleton instance.
     */
    private static $instance = null;

    /**
     * The cached settings.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings_cache    The cached settings.
     */
    private $settings_cache = array();

    /**
     * Get the singleton instance of this class.
     *
     * @since    1.0.0
     * @return   Redco_Optimizer_Config    The singleton instance.
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor.
     *
     * @since    1.0.0
     */
    private function __construct() {
        // Private constructor to prevent direct instantiation
    }

    /**
     * Get a setting value.
     *
     * @since    1.0.0
     * @param    string    $module     The module name.
     * @param    string    $key        The setting key.
     * @param    mixed     $default    The default value if the setting doesn't exist.
     * @return   mixed     The setting value.
     */
    public function get($module, $key = null, $default = null) {
        // Get the module settings
        $settings = $this->get_module_settings($module);

        // If no key is provided, return all settings for the module
        if (null === $key) {
            return $settings;
        }

        // Return the specific setting or default
        return isset($settings[$key]) ? $settings[$key] : $default;
    }

    /**
     * Get all settings for a module.
     *
     * @since    1.0.0
     * @param    string    $module    The module name.
     * @return   array     The module settings.
     */
    public function get_module_settings($module) {
        // Check if settings are already cached
        if (isset($this->settings_cache[$module])) {
            return $this->settings_cache[$module];
        }

        // Normalize module name for option key
        $option_key = 'redco_optimizer_' . str_replace('-', '_', $module) . '_settings';

        // Get settings from database
        $settings = get_option($option_key, array());

        // Get default settings
        $defaults = $this->get_default_settings($module);

        // Merge with defaults
        $settings = wp_parse_args($settings, $defaults);

        // Cache the settings
        $this->settings_cache[$module] = $settings;

        // Allow filtering
        return apply_filters('redco_optimizer_' . $module . '_settings', $settings);
    }

    /**
     * Get default settings for a module.
     *
     * @since    1.0.0
     * @param    string    $module    The module name.
     * @return   array     The default settings.
     */
    public function get_default_settings($module) {
        switch ($module) {
            case 'file-optimization':
                require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-file-defaults.php';
                return redco_get_file_optimization_defaults();

            case 'caching':
                return $this->get_caching_defaults();

            case 'media':
                return $this->get_media_defaults();

            case 'preload':
                return $this->get_preload_defaults();

            case 'database':
                return $this->get_database_defaults();

            case 'heartbeat':
                return $this->get_heartbeat_defaults();

            case 'lazyload':
                return $this->get_lazyload_defaults();

            case 'cdn':
                return $this->get_cdn_defaults();

            case 'site-health-inspector':
                return $this->get_site_health_defaults();

            default:
                return array();
        }
    }

    /**
     * Get default settings for caching.
     *
     * @since    1.0.0
     * @return   array    The default settings.
     */
    private function get_caching_defaults() {
        $defaults = array(
            'enable_page_caching' => 0,
            'enable_browser_caching' => 0,
            'enable_mobile_caching' => 0,
            'separate_mobile_cache' => 0,
            'cache_lifespan' => 10,
            'cache_lifespan_unit' => 'hours',
            'clear_on_post_edit' => 1,
            'clear_on_comment' => 1,
            'cache_logged_in_users' => 0,
            'cache_ssl' => 1,
            'cache_404' => 0,
            'cache_query_strings' => 0,
            'cache_exclusions' => "/cart/\n/checkout/\n/my-account/",
        );

        return apply_filters('redco_caching_defaults', $defaults);
    }

    /**
     * Get default settings for media optimization.
     *
     * @since    1.0.0
     * @return   array    The default settings.
     */
    private function get_media_defaults() {
        $defaults = array(
            'image_quality' => 82,
            'resize_large_images' => 0,
            'max_image_width' => 1920,
            'lazy_load_images' => 1,
            'lazy_load_threshold' => 3,
            'webp_conversion' => 0,
            'image_dimensions' => 1,
            'exclude_lazy_load' => '',
            'bulk_optimization_status' => 'not_started',
            'bulk_optimization_progress' => 0,
        );

        return apply_filters('redco_media_defaults', $defaults);
    }

    /**
     * Get default settings for preload.
     *
     * @since    1.0.0
     * @return   array    The default settings.
     */
    private function get_preload_defaults() {
        $defaults = array(
            'preload_fonts' => 0,
            'preload_links' => 0,
            'prefetch_dns' => 0,
            'preload_cache' => 0,
            'preload_fonts_list' => '',
            'preload_links_list' => '',
            'prefetch_dns_list' => '',
            'preload_cache_urls' => '',
        );

        return apply_filters('redco_preload_defaults', $defaults);
    }

    /**
     * Get default settings for database optimization.
     *
     * @since    1.0.0
     * @return   array    The default settings.
     */
    private function get_database_defaults() {
        $defaults = array(
            'schedule_cleanup' => 0,
            'schedule_frequency' => 'weekly',
            'cleanup_post_revisions' => 1,
            'cleanup_auto_drafts' => 1,
            'cleanup_trashed_posts' => 1,
            'cleanup_spam_comments' => 1,
            'cleanup_trashed_comments' => 1,
            'cleanup_expired_transients' => 1,
            'cleanup_all_transients' => 0,
            'cleanup_optimize_tables' => 1,
            'cleanup_postmeta' => 0,
            'cleanup_commentmeta' => 0,
            'cleanup_orphaned_term_relationships' => 0,
            'cleanup_wp_options' => 0,
        );

        return apply_filters('redco_database_defaults', $defaults);
    }

    /**
     * Get default settings for heartbeat control.
     *
     * @since    1.0.0
     * @return   array    The default settings.
     */
    private function get_heartbeat_defaults() {
        $defaults = array(
            'control_heartbeat' => 0,
            'heartbeat_behavior_backend' => 'default',
            'heartbeat_behavior_editor' => 'default',
            'heartbeat_behavior_frontend' => 'disable',
        );

        return apply_filters('redco_heartbeat_defaults', $defaults);
    }

    /**
     * Get default settings for lazy loading.
     *
     * @since    1.0.0
     * @return   array    The default settings.
     */
    private function get_lazyload_defaults() {
        $defaults = array(
            'lazyload_images' => 1,
            'lazyload_iframes' => 0,
            'lazyload_videos' => 0,
            'lazyload_css_bg' => 0,
            'lazyload_threshold' => 3,
            'lazyload_exclusions' => '',
        );

        return apply_filters('redco_lazyload_defaults', $defaults);
    }

    /**
     * Get default settings for CDN.
     *
     * @since    1.0.0
     * @return   array    The default settings.
     */
    private function get_cdn_defaults() {
        $defaults = array(
            'enabled' => 0,
            'cdn_url' => '',
            'cdn_include_dirs' => 'wp-content,wp-includes',
            'cdn_exclusions' => '.php',
            'cdn_relative_path' => 1,
            'cdn_https' => 1,
        );

        return apply_filters('redco_cdn_defaults', $defaults);
    }

    /**
     * Get default settings for site health inspector.
     *
     * @since    1.0.0
     * @return   array    The default settings.
     */
    private function get_site_health_defaults() {
        $defaults = array(
            'auto_scan' => 0,
            'scan_frequency' => 'weekly',
            'notify_admin' => 0,
            'scan_plugins' => 1,
            'scan_themes' => 1,
            'scan_core' => 1,
            'scan_database' => 1,
            'scan_files' => 1,
        );

        return apply_filters('redco_site_health_defaults', $defaults);
    }

    /**
     * Update a setting value.
     *
     * @since    1.0.0
     * @param    string    $module     The module name.
     * @param    string    $key        The setting key.
     * @param    mixed     $value      The setting value.
     * @return   boolean   True if the setting was updated, false otherwise.
     */
    public function update($module, $key, $value) {
        // Get the module settings
        $settings = $this->get_module_settings($module);

        // Update the setting
        $settings[$key] = $value;

        // Save the settings
        return $this->save_module_settings($module, $settings);
    }

    /**
     * Save all settings for a module.
     *
     * @since    1.0.0
     * @param    string    $module     The module name.
     * @param    array     $settings   The module settings.
     * @return   boolean   True if the settings were saved, false otherwise.
     */
    public function save_module_settings($module, $settings) {
        // Normalize module name for option key
        $option_key = 'redco_optimizer_' . str_replace('-', '_', $module) . '_settings';

        // Save settings to database
        $result = update_option($option_key, $settings);

        // Update cache
        if ($result) {
            $this->settings_cache[$module] = $settings;
        }

        return $result;
    }

    /**
     * Clear the settings cache.
     *
     * @since    1.0.0
     * @param    string    $module    The module name (optional).
     * @return   void
     */
    public function clear_cache($module = null) {
        if (null === $module) {
            $this->settings_cache = array();
        } else {
            unset($this->settings_cache[$module]);
        }
    }
}
