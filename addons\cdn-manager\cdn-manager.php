<?php
/**
 * CDN Manager Add-on
 *
 * @link              https://redco-optimizer.com
 * @since             1.0.0
 * @package           Redco_Optimizer
 *
 * @wordpress-plugin
 * Addon Name:        CDN Manager
 * Description:       Advanced CDN integration with multiple providers including Cloudflare, BunnyCDN, KeyCDN, and more.
 * Version:           1.0.0
 * Author:            Redco
 * Author URI:        https://redco-optimizer.com
 * Premium:           false
 * Coming_Soon:       true
 * Has Settings:      true
 * Icon:              dashicons-networking
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * CDN Manager class.
 */
class Redco_Optimizer_CDN_Manager {

    /**
     * The settings for this addon.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for this addon.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Load settings
        $this->settings = get_option('redco_cdn_manager_settings', array(
            'enabled' => false,
            'cdn_provider' => 'custom',
            'cdn_url' => '',
            'cdn_zone' => '',
            'cdn_key' => '',
            'cdn_email' => '',
            'exclude_extensions' => 'php,html,htm',
            'include_directories' => 'wp-content,wp-includes',
            'exclude_directories' => 'wp-content/uploads/cache',
            'cloudflare_enabled' => false,
            'cloudflare_api_key' => '',
            'cloudflare_email' => '',
            'cloudflare_zone_id' => '',
            'bunnycdn_enabled' => false,
            'bunnycdn_api_key' => '',
            'bunnycdn_pull_zone' => '',
            'keycdn_enabled' => false,
            'keycdn_api_key' => '',
            'keycdn_zone_id' => '',
            'stackpath_enabled' => false,
            'stackpath_client_id' => '',
            'stackpath_client_secret' => '',
            'stackpath_stack_id' => '',
        ));

        // Register hooks
        $this->register_hooks();
    }

    /**
     * Register all hooks for this addon.
     *
     * @since    1.0.0
     */
    private function register_hooks() {
        // Add admin page
        add_action('admin_menu', array($this, 'add_admin_page'));

        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add AJAX handlers
        add_action('wp_ajax_redco_cdn_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_redco_cdn_purge_cache', array($this, 'ajax_purge_cache'));

        // Add CDN rewrite if enabled
        if ($this->is_enabled()) {
            add_action('template_redirect', array($this, 'start_buffer'), -1);
            add_action('shutdown', array($this, 'end_buffer'), 999);
        }
    }

    /**
     * Check if CDN is enabled.
     *
     * @since    1.0.0
     * @return   bool    True if CDN is enabled, false otherwise.
     */
    public function is_enabled() {
        return isset($this->settings['enabled']) && $this->settings['enabled'];
    }

    /**
     * Add admin page.
     *
     * @since    1.0.0
     */
    public function add_admin_page() {
        add_submenu_page(
            'redco-optimizer-addons',
            __('CDN Manager', 'redco-optimizer'),
            __('CDN Manager', 'redco-optimizer'),
            'manage_options',
            'redco-optimizer-cdn-manager',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Enqueue scripts and styles.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts($hook) {
        if ($hook === 'redco-optimizer_page_redco-optimizer-cdn-manager') {
            wp_enqueue_script('redco-cdn-manager', plugin_dir_url(__FILE__) . 'js/cdn-manager.js', array('jquery'), '1.0.0', true);
            wp_localize_script('redco-cdn-manager', 'redco_cdn', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('redco_cdn_nonce'),
            ));
        }

        // Enqueue the modal fix script on all admin pages
        if (is_admin()) {
            wp_enqueue_script('redco-cdn-manager-modal-fix', plugin_dir_url(__FILE__) . 'js/modal-fix.js', array('jquery'), '1.0.0', true);
        }
    }

    /**
     * Render the settings page.
     *
     * @since    1.0.0
     */
    public function render_settings_page() {
        // Check if this addon's settings are being displayed
        if (!isset($_GET['addon']) || $_GET['addon'] !== 'cdn-manager') {
            return;
        }

        // Save settings if form is submitted
        if (isset($_POST['redco_cdn_manager_save_settings'])) {
            $this->save_settings();
        }

        // Include settings template
        include_once plugin_dir_path(__FILE__) . 'templates/settings.php';
    }

    /**
     * Save settings.
     *
     * @since    1.0.0
     */
    public function save_settings() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Verify nonce
        if (!isset($_POST['redco_cdn_manager_nonce']) || !wp_verify_nonce($_POST['redco_cdn_manager_nonce'], 'redco_cdn_manager_save_settings')) {
            add_settings_error('redco_cdn_manager', 'redco_cdn_manager_nonce', __('Security check failed.', 'redco-optimizer'), 'error');
            return;
        }

        // Get settings
        $settings = array(
            'enabled' => isset($_POST['enabled']) ? 1 : 0,
            'cdn_provider' => sanitize_text_field($_POST['cdn_provider']),
            'cdn_url' => esc_url_raw($_POST['cdn_url']),
            'cdn_zone' => sanitize_text_field($_POST['cdn_zone']),
            'cdn_key' => sanitize_text_field($_POST['cdn_key']),
            'cdn_email' => sanitize_email($_POST['cdn_email']),
            'exclude_extensions' => sanitize_text_field($_POST['exclude_extensions']),
            'include_directories' => sanitize_text_field($_POST['include_directories']),
            'exclude_directories' => sanitize_text_field($_POST['exclude_directories']),
            'cloudflare_enabled' => isset($_POST['cloudflare_enabled']) ? 1 : 0,
            'cloudflare_api_key' => sanitize_text_field($_POST['cloudflare_api_key']),
            'cloudflare_email' => sanitize_email($_POST['cloudflare_email']),
            'cloudflare_zone_id' => sanitize_text_field($_POST['cloudflare_zone_id']),
            'bunnycdn_enabled' => isset($_POST['bunnycdn_enabled']) ? 1 : 0,
            'bunnycdn_api_key' => sanitize_text_field($_POST['bunnycdn_api_key']),
            'bunnycdn_pull_zone' => sanitize_text_field($_POST['bunnycdn_pull_zone']),
            'keycdn_enabled' => isset($_POST['keycdn_enabled']) ? 1 : 0,
            'keycdn_api_key' => sanitize_text_field($_POST['keycdn_api_key']),
            'keycdn_zone_id' => sanitize_text_field($_POST['keycdn_zone_id']),
            'stackpath_enabled' => isset($_POST['stackpath_enabled']) ? 1 : 0,
            'stackpath_client_id' => sanitize_text_field($_POST['stackpath_client_id']),
            'stackpath_client_secret' => sanitize_text_field($_POST['stackpath_client_secret']),
            'stackpath_stack_id' => sanitize_text_field($_POST['stackpath_stack_id']),
        );

        // Update settings
        update_option('redco_cdn_manager_settings', $settings);
        $this->settings = $settings;

        // Add success message
        add_settings_error('redco_cdn_manager', 'redco_cdn_manager_updated', __('Settings saved.', 'redco-optimizer'), 'success');
    }

    /**
     * Start output buffer.
     *
     * @since    1.0.0
     */
    public function start_buffer() {
        ob_start(array($this, 'rewrite_urls'));
    }

    /**
     * End output buffer.
     *
     * @since    1.0.0
     */
    public function end_buffer() {
        if (ob_get_length()) {
            ob_end_flush();
        }
    }

    /**
     * Rewrite URLs to use CDN.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The modified HTML content.
     */
    public function rewrite_urls($html) {
        // Check if CDN is enabled
        if (!$this->is_enabled()) {
            return $html;
        }

        // Get CDN URL
        $cdn_url = $this->get_cdn_url();
        if (empty($cdn_url)) {
            return $html;
        }

        // Get site URL
        $site_url = site_url();
        $site_url_no_scheme = preg_replace('#^https?://#', '', $site_url);

        // Get include directories
        $include_dirs = $this->get_include_directories();

        // Get exclude directories
        $exclude_dirs = $this->get_exclude_directories();

        // Get exclude extensions
        $exclude_exts = $this->get_exclude_extensions();

        // Replace URLs
        $html = preg_replace_callback(
            '#(?<=[(\"\'])(?:' . preg_quote($site_url, '#') . ')?/(?:' . implode('|', $include_dirs) . ')/([^\"\')]+)(?=[\"\')])#',
            function($matches) use ($cdn_url, $exclude_dirs, $exclude_exts) {
                $url = $matches[0];
                $path = $matches[1];

                // Check if path is in exclude directories
                foreach ($exclude_dirs as $dir) {
                    if (strpos($path, $dir) === 0) {
                        return $url;
                    }
                }

                // Check if extension is in exclude extensions
                $ext = pathinfo($path, PATHINFO_EXTENSION);
                if (in_array($ext, $exclude_exts)) {
                    return $url;
                }

                // Replace URL with CDN URL
                return $cdn_url . '/' . $path;
            },
            $html
        );

        return $html;
    }

    /**
     * Get CDN URL.
     *
     * @since    1.0.0
     * @return   string    The CDN URL.
     */
    private function get_cdn_url() {
        $provider = isset($this->settings['cdn_provider']) ? $this->settings['cdn_provider'] : 'custom';

        switch ($provider) {
            case 'cloudflare':
                return isset($this->settings['cloudflare_enabled']) && $this->settings['cloudflare_enabled'] ? $this->settings['cdn_url'] : '';
            case 'bunnycdn':
                return isset($this->settings['bunnycdn_enabled']) && $this->settings['bunnycdn_enabled'] ? 'https://' . $this->settings['bunnycdn_pull_zone'] . '.b-cdn.net' : '';
            case 'keycdn':
                return isset($this->settings['keycdn_enabled']) && $this->settings['keycdn_enabled'] ? 'https://' . $this->settings['keycdn_zone_id'] . '.kxcdn.com' : '';
            case 'stackpath':
                return isset($this->settings['stackpath_enabled']) && $this->settings['stackpath_enabled'] ? $this->settings['cdn_url'] : '';
            case 'custom':
            default:
                return isset($this->settings['cdn_url']) ? $this->settings['cdn_url'] : '';
        }
    }

    /**
     * Get include directories.
     *
     * @since    1.0.0
     * @return   array    The include directories.
     */
    private function get_include_directories() {
        $dirs = isset($this->settings['include_directories']) ? $this->settings['include_directories'] : 'wp-content,wp-includes';
        return array_map('trim', explode(',', $dirs));
    }

    /**
     * Get exclude directories.
     *
     * @since    1.0.0
     * @return   array    The exclude directories.
     */
    private function get_exclude_directories() {
        $dirs = isset($this->settings['exclude_directories']) ? $this->settings['exclude_directories'] : 'wp-content/uploads/cache';
        return array_map('trim', explode(',', $dirs));
    }

    /**
     * Get exclude extensions.
     *
     * @since    1.0.0
     * @return   array    The exclude extensions.
     */
    private function get_exclude_extensions() {
        $exts = isset($this->settings['exclude_extensions']) ? $this->settings['exclude_extensions'] : 'php,html,htm';
        return array_map('trim', explode(',', $exts));
    }

    /**
     * Test CDN connection.
     *
     * @since    1.0.0
     */
    public function ajax_test_connection() {
        // Check nonce
        check_ajax_referer('redco_cdn_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get provider
        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'custom';

        // Test connection based on provider
        switch ($provider) {
            case 'cloudflare':
                $result = $this->test_cloudflare_connection();
                break;
            case 'bunnycdn':
                $result = $this->test_bunnycdn_connection();
                break;
            case 'keycdn':
                $result = $this->test_keycdn_connection();
                break;
            case 'stackpath':
                $result = $this->test_stackpath_connection();
                break;
            case 'custom':
            default:
                $result = $this->test_custom_connection();
                break;
        }

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        } else {
            wp_send_json_success(array('message' => __('Connection successful!', 'redco-optimizer')));
        }
    }

    /**
     * Test Cloudflare connection.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function test_cloudflare_connection() {
        // This is a placeholder. In a real implementation, this would test the Cloudflare API connection.
        return true;
    }

    /**
     * Test BunnyCDN connection.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function test_bunnycdn_connection() {
        // This is a placeholder. In a real implementation, this would test the BunnyCDN API connection.
        return true;
    }

    /**
     * Test KeyCDN connection.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function test_keycdn_connection() {
        // This is a placeholder. In a real implementation, this would test the KeyCDN API connection.
        return true;
    }

    /**
     * Test StackPath connection.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function test_stackpath_connection() {
        // This is a placeholder. In a real implementation, this would test the StackPath API connection.
        return true;
    }

    /**
     * Test custom CDN connection.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function test_custom_connection() {
        // This is a placeholder. In a real implementation, this would test the custom CDN connection.
        return true;
    }

    /**
     * Purge CDN cache.
     *
     * @since    1.0.0
     */
    public function ajax_purge_cache() {
        // Check nonce
        check_ajax_referer('redco_cdn_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get provider
        $provider = isset($this->settings['cdn_provider']) ? $this->settings['cdn_provider'] : 'custom';

        // Purge cache based on provider
        switch ($provider) {
            case 'cloudflare':
                $result = $this->purge_cloudflare_cache();
                break;
            case 'bunnycdn':
                $result = $this->purge_bunnycdn_cache();
                break;
            case 'keycdn':
                $result = $this->purge_keycdn_cache();
                break;
            case 'stackpath':
                $result = $this->purge_stackpath_cache();
                break;
            case 'custom':
            default:
                $result = $this->purge_custom_cache();
                break;
        }

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        } else {
            wp_send_json_success(array('message' => __('Cache purged successfully!', 'redco-optimizer')));
        }
    }

    /**
     * Purge Cloudflare cache.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function purge_cloudflare_cache() {
        // This is a placeholder. In a real implementation, this would purge the Cloudflare cache.
        return true;
    }

    /**
     * Purge BunnyCDN cache.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function purge_bunnycdn_cache() {
        // This is a placeholder. In a real implementation, this would purge the BunnyCDN cache.
        return true;
    }

    /**
     * Purge KeyCDN cache.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function purge_keycdn_cache() {
        // This is a placeholder. In a real implementation, this would purge the KeyCDN cache.
        return true;
    }

    /**
     * Purge StackPath cache.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function purge_stackpath_cache() {
        // This is a placeholder. In a real implementation, this would purge the StackPath cache.
        return true;
    }

    /**
     * Purge custom CDN cache.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    private function purge_custom_cache() {
        // This is a placeholder. In a real implementation, this would purge the custom CDN cache.
        return true;
    }

    /**
     * Save settings via AJAX.
     *
     * @since    1.0.0
     * @param    array    $settings    The settings to save.
     * @return   mixed    True on success, WP_Error on failure.
     */
    public function save_settings_ajax($settings) {
        if (!current_user_can('manage_options')) {
            return new WP_Error('permission_denied', __('You do not have permission to perform this action.', 'redco-optimizer'));
        }

        // Sanitize settings
        $sanitized_settings = array(
            'enabled' => isset($settings['enabled']) ? 1 : 0,
            'cdn_provider' => sanitize_text_field($settings['cdn_provider']),
            'cdn_url' => esc_url_raw($settings['cdn_url']),
            'cdn_zone' => sanitize_text_field($settings['cdn_zone']),
            'cdn_key' => sanitize_text_field($settings['cdn_key']),
            'cdn_email' => sanitize_email($settings['cdn_email']),
            'exclude_extensions' => sanitize_text_field($settings['exclude_extensions']),
            'include_directories' => sanitize_text_field($settings['include_directories']),
            'exclude_directories' => sanitize_text_field($settings['exclude_directories']),
            'cloudflare_enabled' => isset($settings['cloudflare_enabled']) ? 1 : 0,
            'cloudflare_api_key' => sanitize_text_field($settings['cloudflare_api_key']),
            'cloudflare_email' => sanitize_email($settings['cloudflare_email']),
            'cloudflare_zone_id' => sanitize_text_field($settings['cloudflare_zone_id']),
            'bunnycdn_enabled' => isset($settings['bunnycdn_enabled']) ? 1 : 0,
            'bunnycdn_api_key' => sanitize_text_field($settings['bunnycdn_api_key']),
            'bunnycdn_pull_zone' => sanitize_text_field($settings['bunnycdn_pull_zone']),
            'keycdn_enabled' => isset($settings['keycdn_enabled']) ? 1 : 0,
            'keycdn_api_key' => sanitize_text_field($settings['keycdn_api_key']),
            'keycdn_zone_id' => sanitize_text_field($settings['keycdn_zone_id']),
            'stackpath_enabled' => isset($settings['stackpath_enabled']) ? 1 : 0,
            'stackpath_client_id' => sanitize_text_field($settings['stackpath_client_id']),
            'stackpath_client_secret' => sanitize_text_field($settings['stackpath_client_secret']),
            'stackpath_stack_id' => sanitize_text_field($settings['stackpath_stack_id']),
        );

        // Update settings
        update_option('redco_cdn_manager_settings', $sanitized_settings);
        $this->settings = $sanitized_settings;

        return true;
    }
}

// Initialize the addon
new Redco_Optimizer_CDN_Manager();
