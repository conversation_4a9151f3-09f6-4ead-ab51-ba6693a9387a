<?php
/**
 * Standalone Help Page
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Define a constant to indicate we're on the help page
define('REDCO_HELP_PAGE', true);

// Get WordPress admin header
require_once(ABSPATH . 'wp-admin/admin-header.php');

// Prevent any add-on settings modals from appearing
?>
<script>
jQuery(document).ready(function($) {
    // Remove any existing modals
    $('.redco-modal-overlay').remove();

    // Prevent any new modals from appearing
    $(document).off('click', '.redco-addon-configure');

    console.log('Help page loaded, modals disabled');
});
</script>
<?php
?>

<div class="wrap redco-help-page-wrap">
    <h1><?php esc_html_e('Redco Optimizer Help Documentation', 'redco-optimizer'); ?></h1>

    <div class="redco-help-container" style="display: flex; background: #fff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); margin: 20px 0; overflow: hidden; min-height: 600px;">
        <!-- Help Sidebar -->
        <div class="redco-help-sidebar" style="width: 280px; background: #f8f9fa; border-right: 1px solid #e2e4e7; padding: 0; flex-shrink: 0;">
            <div class="redco-help-search" style="padding: 15px; border-bottom: 1px solid #e2e4e7;">
                <input type="text" id="redco-help-search-input" placeholder="<?php esc_attr_e('Search help topics...', 'redco-optimizer'); ?>" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
            </div>
            <ul class="redco-help-topics" style="margin: 0; padding: 0; list-style: none;">
                <li class="redco-help-topic active" data-topic="getting-started" style="padding: 12px 15px; border-bottom: 1px solid #e2e4e7; cursor: pointer; transition: background 0.2s ease; display: flex; align-items: center; background: #00A66B; color: #fff;">
                    <span class="dashicons dashicons-admin-home" style="margin-right: 10px; font-size: 18px; width: 18px; height: 18px;"></span>
                    <?php esc_html_e('Getting Started', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="caching" style="padding: 12px 15px; border-bottom: 1px solid #e2e4e7; cursor: pointer; transition: background 0.2s ease; display: flex; align-items: center;">
                    <span class="dashicons dashicons-superhero" style="margin-right: 10px; font-size: 18px; width: 18px; height: 18px;"></span>
                    <?php esc_html_e('Caching', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="optimization" style="padding: 12px 15px; border-bottom: 1px solid #e2e4e7; cursor: pointer; transition: background 0.2s ease; display: flex; align-items: center;">
                    <span class="dashicons dashicons-performance" style="margin-right: 10px; font-size: 18px; width: 18px; height: 18px;"></span>
                    <?php esc_html_e('Optimization', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="faq" style="padding: 12px 15px; border-bottom: 1px solid #e2e4e7; cursor: pointer; transition: background 0.2s ease; display: flex; align-items: center;">
                    <span class="dashicons dashicons-editor-help" style="margin-right: 10px; font-size: 18px; width: 18px; height: 18px;"></span>
                    <?php esc_html_e('FAQ', 'redco-optimizer'); ?>
                </li>
                <li class="redco-help-topic" data-topic="troubleshooting" style="padding: 12px 15px; border-bottom: 1px solid #e2e4e7; cursor: pointer; transition: background 0.2s ease; display: flex; align-items: center;">
                    <span class="dashicons dashicons-warning" style="margin-right: 10px; font-size: 18px; width: 18px; height: 18px;"></span>
                    <?php esc_html_e('Troubleshooting', 'redco-optimizer'); ?>
                </li>
            </ul>
        </div>

        <!-- Help Content -->
        <div class="redco-help-content" style="flex-grow: 1; padding: 30px; overflow-y: auto;">
            <!-- Getting Started Content -->
            <div id="redco-help-getting-started" class="redco-help-content-section" style="display: block;">
                <h2><?php esc_html_e('Getting Started with Redco Optimizer', 'redco-optimizer'); ?></h2>
                <p><?php esc_html_e('Welcome to Redco Optimizer! This guide will help you get started with optimizing your WordPress site for maximum performance.', 'redco-optimizer'); ?></p>

                <h3><?php esc_html_e('Quick Start Guide', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('Follow these steps to quickly optimize your WordPress site:', 'redco-optimizer'); ?></p>
                <ol>
                    <li>
                        <strong><?php esc_html_e('Enable Caching', 'redco-optimizer'); ?></strong>
                        <p><?php esc_html_e('Navigate to the Caching tab and enable Page Caching. This single step can improve your site speed by 2-5x.', 'redco-optimizer'); ?></p>
                    </li>
                    <li>
                        <strong><?php esc_html_e('Optimize Images', 'redco-optimizer'); ?></strong>
                        <p><?php esc_html_e('Go to the Media tab and enable Image Optimization and Lazy Loading. Then click "Optimize All Images" to compress existing images.', 'redco-optimizer'); ?></p>
                    </li>
                    <li>
                        <strong><?php esc_html_e('Minify Files', 'redco-optimizer'); ?></strong>
                        <p><?php esc_html_e('Visit the File Optimization tab and enable HTML, CSS, and JavaScript minification to reduce file sizes.', 'redco-optimizer'); ?></p>
                    </li>
                    <li>
                        <strong><?php esc_html_e('Clean Database', 'redco-optimizer'); ?></strong>
                        <p><?php esc_html_e('Go to the Database tab and run a database cleanup to remove unnecessary data.', 'redco-optimizer'); ?></p>
                    </li>
                </ol>
            </div>

            <!-- Caching Content -->
            <div id="redco-help-caching" class="redco-help-content-section" style="display: none;">
                <h2><?php esc_html_e('Caching', 'redco-optimizer'); ?></h2>
                <p><?php esc_html_e('Caching creates static versions of your dynamic content, which significantly reduces the processing time needed to generate a page view.', 'redco-optimizer'); ?></p>

                <h3><?php esc_html_e('Page Caching', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('Page caching stores the full HTML of your pages, allowing them to be served instantly to visitors without having to be generated on each visit.', 'redco-optimizer'); ?></p>

                <h3><?php esc_html_e('Browser Caching', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('Browser caching instructs visitors\' browsers to store static resources locally, reducing the need to download them on subsequent visits.', 'redco-optimizer'); ?></p>

                <h3><?php esc_html_e('Cache Lifespan', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('This setting determines how long cached files are stored before being regenerated. A longer lifespan improves performance but may delay content updates from appearing.', 'redco-optimizer'); ?></p>
            </div>

            <!-- Optimization Content -->
            <div id="redco-help-optimization" class="redco-help-content-section" style="display: none;">
                <h2><?php esc_html_e('Optimization Features', 'redco-optimizer'); ?></h2>
                <p><?php esc_html_e('Redco Optimizer provides several optimization features to improve your website\'s performance.', 'redco-optimizer'); ?></p>

                <h3><?php esc_html_e('File Optimization', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('Minify and combine CSS and JavaScript files to reduce their size and the number of HTTP requests.', 'redco-optimizer'); ?></p>

                <h3><?php esc_html_e('Media Optimization', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('Compress images, enable lazy loading, and convert images to WebP format for faster loading times.', 'redco-optimizer'); ?></p>

                <h3><?php esc_html_e('Database Optimization', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('Clean up your database by removing post revisions, auto drafts, spam comments, and transients.', 'redco-optimizer'); ?></p>
            </div>

            <!-- FAQ Content -->
            <div id="redco-help-faq" class="redco-help-content-section" style="display: none;">
                <h2><?php esc_html_e('Frequently Asked Questions', 'redco-optimizer'); ?></h2>

                <div class="redco-help-faq-item">
                    <h3><?php esc_html_e('Will Redco Optimizer slow down my site?', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('No, Redco Optimizer is designed to improve your site\'s performance. The plugin itself has minimal overhead and the optimization features it provides will make your site faster.', 'redco-optimizer'); ?></p>
                </div>

                <div class="redco-help-faq-item">
                    <h3><?php esc_html_e('Is it safe to enable all optimization features?', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('While most features are safe to enable, we recommend testing your site after enabling each feature to ensure compatibility with your theme and plugins. Some features like JavaScript minification and defer may require exclusions for certain scripts.', 'redco-optimizer'); ?></p>
                </div>

                <div class="redco-help-faq-item">
                    <h3><?php esc_html_e('How often should I clean my database?', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('For most sites, running a database cleanup once a week is sufficient. You can schedule automatic cleanups in the Database tab.', 'redco-optimizer'); ?></p>
                </div>

                <div class="redco-help-faq-item">
                    <h3><?php esc_html_e('What should I do if I notice issues after enabling an optimization?', 'redco-optimizer'); ?></h3>
                    <p><?php esc_html_e('If you notice any issues, you can disable the specific optimization feature that caused the problem. You can also clear the cache to restore your site to its original state.', 'redco-optimizer'); ?></p>
                </div>
            </div>

            <!-- Troubleshooting Content -->
            <div id="redco-help-troubleshooting" class="redco-help-content-section" style="display: none;">
                <h2><?php esc_html_e('Troubleshooting', 'redco-optimizer'); ?></h2>
                <p><?php esc_html_e('If you encounter issues with Redco Optimizer, here are some troubleshooting steps to help resolve them.', 'redco-optimizer'); ?></p>

                <h3><?php esc_html_e('Common Issues and Solutions', 'redco-optimizer'); ?></h3>

                <div class="redco-help-troubleshooting-item">
                    <h4><?php esc_html_e('Broken Layout or Functionality After Enabling Optimization', 'redco-optimizer'); ?></h4>
                    <ol>
                        <li><?php esc_html_e('Clear the cache by clicking the "Clear Cache" button in the dashboard.', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Disable the most recently enabled optimization feature.', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('If the issue persists, try disabling all optimization features and re-enabling them one by one.', 'redco-optimizer'); ?></li>
                    </ol>
                </div>

                <div class="redco-help-troubleshooting-item">
                    <h4><?php esc_html_e('JavaScript Errors After Enabling JS Optimization', 'redco-optimizer'); ?></h4>
                    <ol>
                        <li><?php esc_html_e('Add the problematic script to the exclusions list in the File Optimization tab.', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('If you\'re using Defer JavaScript, try disabling it for specific scripts.', 'redco-optimizer'); ?></li>
                    </ol>
                </div>

                <div class="redco-help-troubleshooting-item">
                    <h4><?php esc_html_e('Cache Not Working', 'redco-optimizer'); ?></h4>
                    <ol>
                        <li><?php esc_html_e('Verify that page caching is enabled in the Caching tab.', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Check if your server has the required permissions to create cache files.', 'redco-optimizer'); ?></li>
                        <li><?php esc_html_e('Ensure that you\'re not logged in (logged-in users typically bypass the cache).', 'redco-optimizer'); ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Help topic navigation
    $('.redco-help-topic').on('click', function() {
        var topic = $(this).data('topic');

        // Update active topic in sidebar
        $('.redco-help-topic').removeClass('active').css('background', '').css('color', '');
        $(this).addClass('active').css('background', '#00A66B').css('color', '#fff');

        // Show selected content
        $('.redco-help-content-section').hide();
        $('#redco-help-' + topic).show();
    });

    // Search functionality
    $('#redco-help-search-input').on('keyup', function() {
        var searchTerm = $(this).val().toLowerCase();

        if (searchTerm.length < 2) {
            // Reset if search term is too short
            $('.redco-help-topic:first').click();
            return;
        }

        // Search in all content sections
        var foundInSection = false;
        $('.redco-help-content-section').each(function() {
            var sectionText = $(this).text().toLowerCase();
            var sectionId = $(this).attr('id');
            var topicId = sectionId.replace('redco-help-', '');

            if (sectionText.indexOf(searchTerm) !== -1) {
                // Show this section if it contains the search term
                $('.redco-help-content-section').hide();
                $(this).show();

                // Update sidebar active state
                $('.redco-help-topic').removeClass('active').css('background', '').css('color', '');
                $('.redco-help-topic[data-topic="' + topicId + '"]').addClass('active').css('background', '#00A66B').css('color', '#fff');

                foundInSection = true;
                return false; // Break the loop after finding the first match
            }
        });

        // If no matches found, show a message
        if (!foundInSection) {
            $('.redco-help-content-section').hide();
            $('.redco-help-content').append('<div id="redco-help-no-results" class="redco-help-content-section" style="display:block;"><h3><?php esc_html_e("No results found", "redco-optimizer"); ?></h3><p><?php esc_html_e("Try a different search term.", "redco-optimizer"); ?></p></div>');
        } else {
            $('#redco-help-no-results').remove();
        }
    });
});
</script>

<?php
// Get WordPress admin footer
require_once(ABSPATH . 'wp-admin/admin-footer.php');
?>
