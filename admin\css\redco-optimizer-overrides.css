/* ===================================
   REDCO OPTIMIZER - LAYOUT OVERRIDES
   Clean and organized overrides for WordPress admin
   Version: 2.0.0 - Consolidated and optimized
   =================================== */

/* WordPress Admin Reset */
.redco-layout .wrap {
    margin: 0;
    padding: 0 0 40px 0;
}

.redco-layout .wrap h1 {
    display: none; /* Hide default WordPress page title */
}

/* ===================================
   BADGE SYSTEM - UNIFIED APPROACH
   =================================== */

/* FORCE ALL BADGES TO PURPLE - HIGHEST PRIORITY OVERRIDE */
.redco-premium-badge,
.redco-coming-soon-badge,
.redco-premium-only-badge,
.redco-premium-badge-below,
span[class*="premium-badge"],
span[class*="coming-soon"],
*[style*="background"][style*="orange"],
*[style*="background"][style*="yellow"],
*[style*="background"][style*="#F7B500"],
*[style*="background"][style*="#00C67F"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #fff !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    padding: 2px 8px !important;
    border-radius: 12px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3) !important;
    position: relative !important;
    z-index: 9999 !important;
}

/* Override any CSS variables that might create orange badges */
:root {
    --warning-color: #667eea !important;
    --premium-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* ELIMINATE ALL PSEUDO-ELEMENT BADGES */
.redco-premium-feature::after,
.redco-premium-feature::before,
.redco-toggle-control::after,
.redco-toggle-control::before,
*[class*="premium"]::after,
*[class*="premium"]::before,
*[class*="coming-soon"]::after,
*[class*="coming-soon"]::before {
    display: none !important;
    content: none !important;
    background: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Orange badges permanently removed from source code */

/* FORCE EXCLUSIONS GRID STYLES - HIGHEST PRIORITY */
.redco-exclusions-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 20px !important;
    margin-top: 20px !important;
}

.redco-exclusion-section {
    background: #f9fafb !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 6px !important;
    padding: 16px !important;
}

.redco-exclusion-section h4 {
    margin: 0 0 12px 0 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    padding-bottom: 8px !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.redco-checkbox-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
}

.redco-checkbox-item {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 6px 0 !important;
    cursor: pointer !important;
    font-size: 13px !important;
    color: #374151 !important;
}

.redco-checkbox-item input[type="checkbox"] {
    margin: 0 !important;
    width: 16px !important;
    height: 16px !important;
    accent-color: #2563eb !important;
}

.redco-checkbox-text {
    line-height: 1.3 !important;
}

/* REMOVE ALL EXPANDABLE SECTIONS */
.redco-expandable-section {
    display: none !important;
}

.redco-expandable-header {
    display: none !important;
}

.redco-expandable-toggle {
    display: none !important;
}

.redco-expandable-content {
    display: none !important;
}

/* Mobile responsive for exclusions grid */
@media (max-width: 768px) {
    .redco-exclusions-grid {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }

    .redco-exclusion-section {
        padding: 12px !important;
    }
}

/* Override existing card styles */
.redco-layout .redco-card,
.redco-layout .redco-performance-card,
.redco-layout .redco-quick-actions-card,
.redco-layout .redco-optimization-status {
    background: #ffffff !important;
    border: 1px solid #e1e5e9 !important;
    border-radius: 16px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
    margin-bottom: 30px !important;
    overflow: hidden !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.redco-layout .redco-card:hover,
.redco-layout .redco-performance-card:hover,
.redco-layout .redco-quick-actions-card:hover,
.redco-layout .redco-optimization-status:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-2px) !important;
}

/* Override existing button styles */
.redco-layout .redco-button,
.redco-layout .button,
.redco-layout .button-primary,
.redco-layout .button-secondary {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: 1px solid transparent !important;
    text-align: center !important;
    justify-content: center !important;
}

.redco-layout .redco-button-primary,
.redco-layout .button-primary {
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%) !important;
    color: #ffffff !important;
    border-color: #00925f !important;
    box-shadow: 0 2px 8px rgba(0, 166, 107, 0.2) !important;
}

.redco-layout .redco-button-primary:hover,
.redco-layout .button-primary:hover {
    background: linear-gradient(135deg, #00925f 0%, #007a51 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 16px rgba(0, 166, 107, 0.3) !important;
}

.redco-layout .redco-button-secondary,
.redco-layout .button-secondary {
    background: #ffffff !important;
    color: #495057 !important;
    border-color: #dee2e6 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.redco-layout .redco-button-secondary:hover,
.redco-layout .button-secondary:hover {
    background: #f8f9fa !important;
    border-color: #adb5bd !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Override form styles */
.redco-layout input[type="text"],
.redco-layout input[type="email"],
.redco-layout input[type="url"],
.redco-layout input[type="password"],
.redco-layout input[type="number"],
.redco-layout select,
.redco-layout textarea {
    width: 100% !important;
    padding: 14px 18px !important;
    border: 2px solid #e1e5e9 !important;
    border-radius: 10px !important;
    font-size: 14px !important;
    font-family: inherit !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: #ffffff !important;
    color: #495057 !important;
    box-shadow: none !important;
}

.redco-layout input[type="text"]:focus,
.redco-layout input[type="email"]:focus,
.redco-layout input[type="url"]:focus,
.redco-layout input[type="password"]:focus,
.redco-layout input[type="number"]:focus,
.redco-layout select:focus,
.redco-layout textarea:focus {
    outline: none !important;
    border-color: #00a66b !important;
    box-shadow: 0 0 0 4px rgba(0, 166, 107, 0.1) !important;
    background: #ffffff !important;
}

/* ===================================
   CHECKBOX SYSTEM - UNIFIED APPROACH
   =================================== */

/* Override ALL checkbox styles with consistent design */
.redco-layout input[type="checkbox"],
input[type="checkbox"] {
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #cbd5e1 !important;
    border-radius: 4px !important;
    background: #ffffff !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0 !important;
    margin: 0 10px 0 0 !important;
    position: relative !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    vertical-align: middle !important;
    display: inline-block !important;
}

.redco-layout input[type="checkbox"]:checked,
input[type="checkbox"]:checked {
    background: #00a66b !important;
    border-color: #00a66b !important;
    box-shadow: 0 0 0 3px rgba(0, 166, 107, 0.1) !important;
}

/* Checkmark for checked state */
.redco-layout input[type="checkbox"]:checked::before,
input[type="checkbox"]:checked::before {
    content: '✓' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    color: #ffffff !important;
    font-size: 12px !important;
    font-weight: 700 !important;
    line-height: 1 !important;
}

/* ===================================
   FORM FIELD SYSTEM - UNIFIED APPROACH
   =================================== */

/* Consistent form field widths */
.redco-layout .redco-form-field,
.redco-form-field {
    width: 100% !important;
    max-width: none !important;
    flex: 1 !important;
}

.redco-layout .redco-input,
.redco-layout .redco-textarea,
.redco-layout .redco-select,
.redco-input,
.redco-textarea,
.redco-select {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

/* Checkbox checked state - same as cleanup items */
.redco-layout input[type="checkbox"]:checked,
input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%) !important;
    border-color: #00925f !important;
    box-shadow: 0 2px 8px rgba(0, 166, 107, 0.3) !important;
}

.redco-layout input[type="checkbox"]:checked::after,
input[type="checkbox"]:checked::after {
    content: '✓' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    color: #ffffff !important;
    font-size: 14px !important;
    font-weight: 700 !important;
}

/* Hover and focus states */
.redco-layout input[type="checkbox"]:hover,
input[type="checkbox"]:hover {
    border-color: #00a66b !important;
}

.redco-layout input[type="checkbox"]:focus,
input[type="checkbox"]:focus {
    box-shadow: 0 0 0 2px rgba(0, 166, 107, 0.2) !important;
}

/* Override table styles */
.redco-layout table {
    width: 100% !important;
    border-collapse: collapse !important;
    background: #ffffff !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
}

.redco-layout table th,
.redco-layout table td {
    padding: 16px 20px !important;
    text-align: left !important;
    border-bottom: 1px solid #f1f3f4 !important;
}

.redco-layout table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    font-size: 14px !important;
}

.redco-layout table td {
    color: #495057 !important;
    font-size: 14px !important;
}

.redco-layout table tr:hover {
    background: #f8f9fa !important;
}

/* Override notification styles */
.redco-layout .notice,
.redco-layout .redco-notice {
    border-radius: 12px !important;
    border-left: 4px solid #00a66b !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
    padding: 16px 20px !important;
    margin: 16px 0 !important;
}

.redco-layout .notice-success,
.redco-layout .redco-notice-success {
    border-left-color: #28a745 !important;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
}

.redco-layout .notice-warning,
.redco-layout .redco-notice-warning {
    border-left-color: #ffc107 !important;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
}

.redco-layout .notice-error,
.redco-layout .redco-notice-error {
    border-left-color: #dc3545 !important;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
}

/* Override modal styles */
.redco-layout .redco-modal-overlay {
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(4px) !important;
}

.redco-layout .redco-modal-content {
    background: #ffffff !important;
    border-radius: 16px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    border: none !important;
    max-width: 600px !important;
    width: 90% !important;
}

.redco-layout .redco-modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    padding: 24px 30px !important;
    border-bottom: 1px solid #e1e5e9 !important;
    border-radius: 16px 16px 0 0 !important;
}

.redco-layout .redco-modal-body {
    padding: 30px !important;
}

.redco-layout .redco-modal-footer {
    background: #f8f9fa !important;
    padding: 20px 30px !important;
    border-top: 1px solid #e1e5e9 !important;
    border-radius: 0 0 16px 16px !important;
    display: flex !important;
    gap: 12px !important;
    justify-content: flex-end !important;
}

/* Override progress bar styles */
.redco-layout .redco-progress {
    width: 100% !important;
    height: 8px !important;
    background: #e9ecef !important;
    border-radius: 4px !important;
    overflow: hidden !important;
    margin: 12px 0 !important;
}

.redco-layout .redco-progress-bar {
    height: 100% !important;
    background: linear-gradient(135deg, #00a66b 0%, #00925f 100%) !important;
    border-radius: 4px !important;
    transition: width 0.6s ease-in-out !important;
}

/* Override spacing and layout */
.redco-layout .redco-form-table {
    margin: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.redco-layout .redco-form-table th,
.redco-layout .redco-form-table td {
    padding: 20px 0 !important;
    border: none !important;
    border-bottom: 1px solid #f1f3f4 !important;
    background: transparent !important;
}

.redco-layout .redco-form-table th {
    width: 200px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    vertical-align: top !important;
    padding-top: 24px !important;
}

.redco-layout .redco-form-table td {
    color: #495057 !important;
}

/* Hide old sidebar styles */
.redco-layout .redco-sidebar {
    display: none !important;
}

/* ===================================
   BUTTON AND SPACING SYSTEM - UNIFIED APPROACH
   =================================== */

/* Form actions with proper bottom margin */
.redco-layout .redco-form-actions,
.redco-form-actions {
    margin-bottom: 40px !important;
    padding: 20px 0 !important;
    border-top: 1px solid #e5e7eb !important;
    margin-top: 30px !important;
}

/* Tab content with proper bottom spacing */
.redco-layout .redco-tab-content,
.redco-tab-content {
    padding-bottom: 40px !important;
    min-height: calc(100vh - 200px) !important;
}

/* Card spacing consistency */
.redco-layout .redco-card,
.redco-card {
    margin-bottom: 25px !important;
}

.redco-layout .redco-card:last-child,
.redco-card:last-child {
    margin-bottom: 40px !important; /* Extra space for last card */
}

/* Ensure main content takes full width */
.redco-layout .redco-main-content {
    width: 100% !important;
    margin-left: 0 !important;
    padding: 0 0 40px 0 !important; /* Add bottom padding */
}

/* Override any conflicting styles */
.redco-layout * {
    box-sizing: border-box !important;
}

/* Ensure proper font rendering */
.redco-layout {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    line-height: 1.6 !important;
    color: #1e1e1e !important;
    background: #f8f9fa !important;
}

/* ===================================
   LABEL AND ALIGNMENT SYSTEM - UNIFIED APPROACH
   =================================== */

/* UNIFIED CHECKBOX LAYOUT - SAME AS CLEANUP ITEMS */
.redco-layout .redco-checkbox-item,
.redco-checkbox-item,
.redco-layout label,
label,
.redco-toggle-row,
.redco-checkbox-group label,
.redco-checkbox {
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
    padding: 16px !important;
    background: #fff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin: 0 0 12px 0 !important;
}

.redco-layout .redco-checkbox-item:hover,
.redco-checkbox-item:hover,
.redco-layout label:hover,
label:hover,
.redco-toggle-row:hover,
.redco-checkbox-group label:hover,
.redco-checkbox:hover {
    border-color: #00a66b !important;
    box-shadow: 0 2px 4px rgba(0, 166, 107, 0.1) !important;
}

/* Checkbox positioning - same as cleanup items */
.redco-layout input[type="checkbox"],
input[type="checkbox"] {
    margin: 0 !important;
    flex-shrink: 0 !important;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #cbd5e1 !important;
    border-radius: 4px !important;
    background: #ffffff !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    vertical-align: middle !important;
    display: inline-block !important;
}

/* Content container - same as cleanup items */
.redco-layout .redco-checkbox-text,
.redco-checkbox-text,
.redco-layout .redco-checkbox-label,
.redco-checkbox-label,
.redco-toggle-info,
.redco-checkbox-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 4px !important;
    margin: 0 !important;
    line-height: 1.4 !important;
    font-size: 14px !important;
    color: #374151 !important;
}

/* Toggle control positioning - same as cleanup items */
.redco-layout .redco-toggle-control,
.redco-toggle-control {
    flex-shrink: 0 !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
}

/* Special handling for toggle controls inside toggle rows */
.redco-toggle-row .redco-toggle-control {
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    margin: 0 !important;
}

/* Specific component overrides to match cleanup item layout */
.redco-file-optimization-tab .redco-toggle-row,
.redco-media-tab .redco-toggle-row,
.redco-caching-tab .redco-toggle-row,
.redco-heartbeat-tab .redco-toggle-row,
.redco-cdn-tab .redco-toggle-row,
.redco-site-health-inspector-tab .redco-toggle-row {
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
    padding: 16px !important;
    background: #fff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin: 0 0 12px 0 !important;
}

/* CDN checkbox group specific styling - FORCE CLEANUP ITEM LAYOUT */
.redco-checkbox-group {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
}

.redco-checkbox-group .redco-checkbox,
.redco-checkbox-group label {
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
    padding: 16px !important;
    background: #fff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin: 0 !important;
}

.redco-checkbox-group .redco-checkbox:hover,
.redco-checkbox-group label:hover {
    border-color: #00a66b !important;
    box-shadow: 0 2px 4px rgba(0, 166, 107, 0.1) !important;
}

/* CDN checkbox input positioning */
.redco-checkbox-group input[type="checkbox"] {
    margin: 0 !important;
    flex-shrink: 0 !important;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #cbd5e1 !important;
    border-radius: 4px !important;
    background: #ffffff !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    vertical-align: middle !important;
    display: inline-block !important;
}

/* CDN checkbox label styling */
.redco-checkbox-group .redco-checkbox-label,
.redco-checkbox-group span {
    flex: 1 !important;
    margin: 0 !important;
    line-height: 1.4 !important;
    font-size: 14px !important;
    color: #374151 !important;
    font-weight: 600 !important;
}

/* CDN tab specific overrides */
#redco-cdn-tab .redco-toggle-row {
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
    padding: 16px !important;
    background: #fff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin: 0 0 12px 0 !important;
}

#redco-cdn-tab .redco-toggle-row:hover {
    border-color: #00a66b !important;
    box-shadow: 0 2px 4px rgba(0, 166, 107, 0.1) !important;
}

#redco-cdn-tab .redco-toggle-control {
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    margin: 0 !important;
}

#redco-cdn-tab .redco-toggle-control label {
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    margin: 0 !important;
}

#redco-cdn-tab input[type="checkbox"] {
    margin: 0 !important;
    flex-shrink: 0 !important;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #cbd5e1 !important;
    border-radius: 4px !important;
    background: #ffffff !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    vertical-align: middle !important;
    display: inline-block !important;
}

/* Override any existing gradients or backgrounds */
.redco-layout .redco-card-header,
.redco-layout .redco-form-section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* Ensure proper z-index for dropdowns */
.redco-layout .redco-nav-dropdown-menu {
    z-index: 1001 !important;
}

.redco-layout .redco-modal-overlay {
    z-index: 9999 !important;
}

/* Override any existing animations */
.redco-layout .redco-card,
.redco-layout .redco-button,
.redco-layout .redco-nav-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* ===================================
   REPLACEMENT CSS CLASSES FOR INLINE STYLES
   =================================== */

.redco-header-icon-visible {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 9999 !important;
}

.redco-header-icon-inner-visible {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 9999 !important;
}

.redco-notices-protected,
.redco-plugin-notices-container {
    position: relative !important;
    z-index: 1000 !important;
    margin: 0 0 20px 0 !important;
    padding: 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-global-notification {
    margin-bottom: 20px !important;
}

.redco-tab-hidden {
    display: none !important;
}

.redco-card-content-visible {
    display: block !important;
    visibility: visible !important;
}

.redco-help-no-results-visible {
    display: block !important;
}

/* Modal debug warning styles */
.redco-modal-debug-warning {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    padding: 10px !important;
    border-radius: 4px !important;
    margin-bottom: 20px !important;
    text-align: center !important;
}

.redco-modal-debug-text {
    font-weight: bold !important;
    margin: 0 !important;
}

.redco-modal-debug-arrow {
    margin-top: 10px !important;
    font-size: 24px !important;
    animation: redco-bounce 1s infinite !important;
}

@keyframes redco-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(10px); }
}

/* Addon card styles */
.redco-addon-card {
    border: 1px solid #e5e5e5 !important;
    border-radius: 8px !important;
    padding: 20px !important;
    background: white !important;
}

.redco-addon-header {
    margin-bottom: 15px !important;
}

.redco-addon-icon {
    width: 40px !important;
    height: 40px !important;
    background: #f0f7f2 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 10px !important;
}

.redco-addon-icon-svg {
    color: #00a66b !important;
}

/* Modules actions */
.redco-modules-actions {
    text-align: right !important;
    margin-bottom: 20px !important;
}

/* Modal section visibility */
.redco-modal-section-visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 25px !important;
}

/* Modal scroll indicator */
.redco-modal-scroll-indicator {
    background-color: #d4edda !important;
    color: #155724 !important;
    padding: 10px !important;
    border-radius: 4px !important;
    margin-top: 20px !important;
    text-align: center !important;
}

.redco-modal-scroll-text {
    font-weight: bold !important;
    margin: 0 !important;
}

.redco-modal-scroll-reminder {
    margin-top: 5px !important;
}

/* Purple badge class */
.redco-purple-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}
