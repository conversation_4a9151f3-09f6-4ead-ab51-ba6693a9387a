<?php

/**
 * The Google Fonts optimization functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The Google Fonts optimization functionality of the plugin.
 *
 * Defines the functionality for optimizing Google Fonts.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_Google_Fonts {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for Google Fonts optimization.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for Google Fonts optimization.
     */
    private $settings;

    /**
     * The cache directory for Google Fonts.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $fonts_dir    The cache directory for Google Fonts.
     */
    private $fonts_dir;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
        $this->fonts_dir = WP_CONTENT_DIR . '/cache/redco-optimizer/google-fonts/';
    }

    /**
     * Get Google Fonts optimization settings.
     *
     * @since    1.0.0
     * @return   array    The Google Fonts optimization settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_file_optimization_settings', array() );
        
        // Default settings
        $defaults = array(
            'optimize_google_fonts' => 0,
            'self_host_google_fonts' => 0,
        );
        
        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize Google Fonts optimization.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if Google Fonts optimization is enabled
        if ( ! $this->settings['optimize_google_fonts'] ) {
            return;
        }

        // Create fonts directory if it doesn't exist
        if ( ! file_exists( $this->fonts_dir ) ) {
            wp_mkdir_p( $this->fonts_dir );
        }

        // Add filter to process HTML
        add_filter( 'redco_buffer', array( $this, 'process_html' ), 15 );
    }

    /**
     * Process HTML to optimize Google Fonts.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    public function process_html( $html ) {
        // Don't process if user is logged in
        if ( is_user_logged_in() ) {
            return $html;
        }
        
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }
        
        // Extract Google Fonts
        preg_match_all( '/<link[^>]*href=["\']https?:\/\/fonts\.googleapis\.com\/css[^>]*>/i', $html, $matches );
        
        if ( empty( $matches[0] ) ) {
            return $html;
        }
        
        foreach ( $matches[0] as $google_font_tag ) {
            // Extract href
            preg_match( '/href=["\']([^"\']+)["\']/i', $google_font_tag, $href_match );
            
            if ( empty( $href_match[1] ) ) {
                continue;
            }
            
            $google_font_url = $href_match[1];
            
            if ( $this->settings['self_host_google_fonts'] ) {
                // Self-host Google Fonts
                $replacement = $this->self_host_google_font( $google_font_url );
            } else {
                // Optimize Google Fonts
                $replacement = $this->optimize_google_font_tag( $google_font_tag, $google_font_url );
            }
            
            // Replace original tag with optimized version
            $html = str_replace( $google_font_tag, $replacement, $html );
        }
        
        return $html;
    }

    /**
     * Optimize Google Font tag.
     *
     * @since    1.0.0
     * @param    string    $original_tag    The original Google Font tag.
     * @param    string    $font_url        The Google Font URL.
     * @return   string    The optimized Google Font tag.
     */
    private function optimize_google_font_tag( $original_tag, $font_url ) {
        // Add display=swap parameter if not already present
        if ( strpos( $font_url, 'display=' ) === false ) {
            $font_url .= ( strpos( $font_url, '?' ) !== false ? '&' : '?' ) . 'display=swap';
        }
        
        // Create preload tag
        $preload_tag = '<link rel="preload" href="' . esc_url( $font_url ) . '" as="style" crossorigin>';
        
        // Update original tag with new URL
        $optimized_tag = str_replace( 'href="' . $font_url . '"', 'href="' . esc_url( $font_url ) . '"', $original_tag );
        
        return $preload_tag . $optimized_tag;
    }

    /**
     * Self-host Google Font.
     *
     * @since    1.0.0
     * @param    string    $font_url    The Google Font URL.
     * @return   string    The self-hosted font tags.
     */
    private function self_host_google_font( $font_url ) {
        // Generate a unique hash for this font URL
        $font_hash = md5( $font_url );
        
        // Check if we already have the font files
        $font_css_file = $this->fonts_dir . $font_hash . '.css';
        
        if ( ! file_exists( $font_css_file ) ) {
            // Download and process the font
            $this->download_and_process_font( $font_url, $font_hash );
        }
        
        // Check if the CSS file exists now
        if ( file_exists( $font_css_file ) ) {
            // Get the CSS content
            $css_content = file_get_contents( $font_css_file );
            
            // Create the self-hosted font tags
            $font_tags = '<style id="redco-google-fonts-' . $font_hash . '">' . $css_content . '</style>';
            
            return $font_tags;
        }
        
        // If something went wrong, return the original URL with preload
        return $this->optimize_google_font_tag( '<link rel="stylesheet" href="' . esc_url( $font_url ) . '">', $font_url );
    }

    /**
     * Download and process Google Font.
     *
     * @since    1.0.0
     * @param    string    $font_url     The Google Font URL.
     * @param    string    $font_hash    The hash of the font URL.
     */
    private function download_and_process_font( $font_url, $font_hash ) {
        // Add user agent to get WOFF2 format
        $args = array(
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36',
        );
        
        // Get the CSS from Google Fonts
        $response = wp_remote_get( $font_url, $args );
        
        if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
            return;
        }
        
        $css = wp_remote_retrieve_body( $response );
        
        // Create font directory
        $font_dir = $this->fonts_dir . $font_hash . '/';
        if ( ! file_exists( $font_dir ) ) {
            wp_mkdir_p( $font_dir );
        }
        
        // Extract font URLs
        preg_match_all( '/url\(([^)]+)\)/i', $css, $font_urls );
        
        if ( empty( $font_urls[1] ) ) {
            return;
        }
        
        // Download each font file
        foreach ( $font_urls[1] as $url ) {
            // Clean the URL
            $url = str_replace( array( '"', "'" ), '', $url );
            
            // Generate a filename
            $filename = basename( $url );
            $local_path = $font_dir . $filename;
            
            // Download the font file
            $font_response = wp_remote_get( $url, $args );
            
            if ( is_wp_error( $font_response ) || 200 !== wp_remote_retrieve_response_code( $font_response ) ) {
                continue;
            }
            
            $font_content = wp_remote_retrieve_body( $font_response );
            
            // Save the font file
            file_put_contents( $local_path, $font_content );
            
            // Replace the URL in the CSS
            $css = str_replace( $url, content_url( 'cache/redco-optimizer/google-fonts/' . $font_hash . '/' . $filename ), $css );
        }
        
        // Add font-display: swap
        $css = preg_replace( '/@font-face\s*{/i', '@font-face { font-display: swap;', $css );
        
        // Save the modified CSS
        file_put_contents( $this->fonts_dir . $font_hash . '.css', $css );
    }

    /**
     * Clear Google Fonts cache.
     *
     * @since    1.0.0
     */
    public function clear_fonts_cache() {
        $this->delete_directory_contents( $this->fonts_dir );
        return true;
    }

    /**
     * Delete directory contents.
     *
     * @since    1.0.0
     * @param    string    $dir    The directory to delete contents from.
     */
    private function delete_directory_contents( $dir ) {
        if ( ! is_dir( $dir ) ) {
            return;
        }
        
        $files = scandir( $dir );
        foreach ( $files as $file ) {
            if ( $file === '.' || $file === '..' ) {
                continue;
            }
            
            $path = $dir . '/' . $file;
            if ( is_dir( $path ) ) {
                $this->delete_directory_contents( $path );
                rmdir( $path );
            } else {
                unlink( $path );
            }
        }
    }

    /**
     * AJAX handler for clearing Google Fonts cache.
     *
     * @since    1.0.0
     */
    public function ajax_clear_fonts_cache() {
        // Check nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'redco_optimizer_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed.', 'redco-optimizer' ) ) );
        }
        
        // Check user capabilities
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to perform this action.', 'redco-optimizer' ) ) );
        }
        
        // Clear fonts cache
        $result = $this->clear_fonts_cache();
        
        if ( $result ) {
            wp_send_json_success( array( 'message' => __( 'Google Fonts cache cleared successfully.', 'redco-optimizer' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to clear Google Fonts cache.', 'redco-optimizer' ) ) );
        }
    }
}
