/**
 * Help Documentation JavaScript
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/js
 */

(function($) {
    'use strict';

    /**
     * Initialize the help documentation functionality
     */
    function initHelpDocumentation() {
        // Help topic navigation
        $('.redco-help-topic').on('click', function() {
            var topic = $(this).data('topic');

            // Update active topic in sidebar
            $('.redco-help-topic').removeClass('active');
            $(this).addClass('active');

            // Show selected content
            $('.redco-help-content').removeClass('active');
            $('#redco-help-' + topic).addClass('active');

            // Scroll to top of content
            $('.redco-help-content-container').scrollTop(0);
        });

        // Search functionality
        $('#redco-help-search-input').on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();

            if (searchTerm.length < 3) {
                // Show all sections if search term is too short
                $('.redco-help-section').show();
                return;
            }

            // Hide all sections first
            $('.redco-help-section').hide();

            // Show sections that match the search term
            $('.redco-help-section').each(function() {
                var sectionText = $(this).text().toLowerCase();
                if (sectionText.indexOf(searchTerm) !== -1) {
                    $(this).show();

                    // Expand the parent content area if it's not already visible
                    var parentContent = $(this).closest('.redco-help-content');
                    if (!parentContent.hasClass('active')) {
                        $('.redco-help-content').removeClass('active');
                        parentContent.addClass('active');

                        // Update sidebar active state
                        var topicId = parentContent.attr('id').replace('redco-help-', '');
                        $('.redco-help-topic').removeClass('active');
                        $('.redco-help-topic[data-topic="' + topicId + '"]').addClass('active');
                    }
                }
            });
        });

        // Handle direct links to specific help topics
        function handleDirectLinks() {
            // Check if URL has a hash
            if (window.location.hash) {
                var hash = window.location.hash.substring(1); // Remove the # character

                // Check if hash corresponds to a help topic
                if (hash.startsWith('help-')) {
                    var topic = hash.replace('help-', '');

                    // Activate the corresponding topic
                    $('.redco-help-topic[data-topic="' + topic + '"]').trigger('click');

                    // If there's a specific section ID, scroll to it
                    if (hash.indexOf('-section-') > -1) {
                        var sectionId = hash.split('-section-')[1];
                        var $section = $('#' + sectionId);

                        if ($section.length) {
                            setTimeout(function() {
                                var scrollPos = $section.offset().top - $('.redco-help-content-container').offset().top + $('.redco-help-content-container').scrollTop();
                                $('.redco-help-content-container').scrollTop(scrollPos);
                            }, 100);
                        }
                    }
                }
            }
        }

        // Initialize direct links
        handleDirectLinks();

        // Update hash when clicking on a topic
        $('.redco-help-topic').on('click', function() {
            var topic = $(this).data('topic');
            window.location.hash = 'help-' + topic;
        });

        // Make section headers linkable
        $('.redco-help-subsection h3, .redco-help-subsection h4').each(function() {
            var $header = $(this);
            var text = $header.text().trim();
            var id = 'section-' + text.toLowerCase().replace(/[^a-z0-9]+/g, '-');

            $header.attr('id', id);

            // Add link icon
            $header.append(' <a href="#help-' + $('.redco-help-topic.active').data('topic') + '-section-' + id + '" class="redco-help-section-link"><span class="dashicons dashicons-admin-links"></span></a>');

            // Hide link icon by default
            $header.find('.redco-help-section-link').css('opacity', 0);

            // Show link icon on hover
            $header.hover(
                function() {
                    $(this).find('.redco-help-section-link').css('opacity', 1);
                },
                function() {
                    $(this).find('.redco-help-section-link').css('opacity', 0);
                }
            );

            // Update hash when clicking on section link
            $header.find('.redco-help-section-link').on('click', function(e) {
                e.preventDefault();
                var hash = $(this).attr('href').substring(1);
                window.location.hash = hash;
            });
        });
    }

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Initializing Redco Optimizer Help Documentation');
        try {
            initHelpDocumentation();
            console.log('Help Documentation initialized successfully');

            // Force the first help content to be visible
            setTimeout(function() {
                if ($('.redco-help-content.active').length === 0) {
                    console.log('No active help content found, forcing first content to be visible');
                    $('.redco-help-content:first').addClass('active');
                    $('.redco-help-topic:first').addClass('active');
                }
            }, 500);
        } catch (e) {
            console.error('Error initializing Help Documentation:', e);
        }
    });

})(jQuery);
