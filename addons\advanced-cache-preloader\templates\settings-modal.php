<?php
/**
 * Advanced Cache Preloader Settings Modal Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/advanced-cache-preloader/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings and status from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
$status = isset($status) ? $status : (isset($this->status) ? $this->status : array(
    'is_preloading' => false,
    'last_preload' => 0,
    'next_preload' => 0,
    'total_urls' => 0,
    'processed_urls' => 0,
    'success_urls' => 0,
    'failed_urls' => 0,
    'current_url' => '',
    'log' => array(),
));
?>

<form method="post" action="" class="redco-addon-settings-form" data-addon="advanced-cache-preloader" style="display: block !important; visibility: visible !important; opacity: 1 !important; overflow: visible !important;">
    <?php wp_nonce_field('redco_advanced_cache_preloader_save_settings', 'redco_advanced_cache_preloader_nonce'); ?>

    <div class="redco-modal-section" style="display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;">
        <h3 style="display: block !important; visibility: visible !important; opacity: 1 !important;"><?php esc_html_e('Cache Preloader Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Enable Cache Preloader', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Enable automatic cache preloading.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="enabled" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_method"><?php esc_html_e('Preload Method', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_method" name="preload_method" class="redco-select">
                    <option value="sitemap" <?php selected(isset($settings['preload_method']) ? $settings['preload_method'] : 'sitemap', 'sitemap'); ?>><?php esc_html_e('Sitemap', 'redco-optimizer'); ?></option>
                    <option value="custom" <?php selected(isset($settings['preload_method']) ? $settings['preload_method'] : 'sitemap', 'custom'); ?>><?php esc_html_e('Custom URLs', 'redco-optimizer'); ?></option>
                    <option value="all" <?php selected(isset($settings['preload_method']) ? $settings['preload_method'] : 'sitemap', 'all'); ?>><?php esc_html_e('All Content', 'redco-optimizer'); ?></option>
                </select>
            </div>
        </div>

        <div class="redco-form-row" id="sitemap-url-row">
            <div class="redco-form-label">
                <label for="sitemap_url"><?php esc_html_e('Sitemap URL', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="url" id="sitemap_url" name="sitemap_url" class="redco-input" value="<?php echo esc_url(isset($settings['sitemap_url']) ? $settings['sitemap_url'] : ''); ?>" placeholder="<?php echo esc_url(home_url('/sitemap.xml')); ?>">
                <p class="redco-form-help"><?php esc_html_e('Enter your sitemap URL. Leave empty to use the default WordPress sitemap.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-form-row" id="custom-urls-row">
            <div class="redco-form-label">
                <label for="custom_urls"><?php esc_html_e('Custom URLs', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <textarea id="custom_urls" name="custom_urls" class="redco-textarea" rows="5"><?php echo esc_textarea(isset($settings['custom_urls']) ? $settings['custom_urls'] : ''); ?></textarea>
                <p class="redco-form-help"><?php esc_html_e('Enter one URL per line.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section" style="display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;">
        <h3 style="display: block !important; visibility: visible !important; opacity: 1 !important;"><?php esc_html_e('Schedule Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_schedule"><?php esc_html_e('Preload Schedule', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_schedule" name="preload_schedule" class="redco-select">
                    <option value="hourly" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'hourly'); ?>><?php esc_html_e('Hourly', 'redco-optimizer'); ?></option>
                    <option value="twicedaily" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'twicedaily'); ?>><?php esc_html_e('Twice Daily', 'redco-optimizer'); ?></option>
                    <option value="daily" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'daily'); ?>><?php esc_html_e('Daily', 'redco-optimizer'); ?></option>
                    <option value="weekly" <?php selected(isset($settings['preload_schedule']) ? $settings['preload_schedule'] : 'daily', 'weekly'); ?>><?php esc_html_e('Weekly', 'redco-optimizer'); ?></option>
                </select>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_time"><?php esc_html_e('Preload Time', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="time" id="preload_time" name="preload_time" class="redco-input" value="<?php echo esc_attr(isset($settings['preload_time']) ? $settings['preload_time'] : '00:00'); ?>">
                <p class="redco-form-help"><?php esc_html_e('Set the time of day to start preloading (server time).', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section" style="display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;">
        <h3 style="display: block !important; visibility: visible !important; opacity: 1 !important;"><?php esc_html_e('Advanced Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_throttle"><?php esc_html_e('Preload Throttle', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="number" id="preload_throttle" name="preload_throttle" class="redco-input" value="<?php echo intval(isset($settings['preload_throttle']) ? $settings['preload_throttle'] : 3); ?>" min="0" max="60">
                <p class="redco-form-help"><?php esc_html_e('Delay in seconds between preloading each URL. Set to 0 for no delay.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_batch_size"><?php esc_html_e('Batch Size', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="number" id="preload_batch_size" name="preload_batch_size" class="redco-input" value="<?php echo intval(isset($settings['preload_batch_size']) ? $settings['preload_batch_size'] : 20); ?>" min="1" max="100">
                <p class="redco-form-help"><?php esc_html_e('Number of URLs to preload in each batch. Lower values reduce server load.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_timeout"><?php esc_html_e('Request Timeout', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="number" id="preload_timeout" name="preload_timeout" class="redco-input" value="<?php echo intval(isset($settings['preload_timeout']) ? $settings['preload_timeout'] : 30); ?>" min="5" max="120">
                <p class="redco-form-help"><?php esc_html_e('Maximum time in seconds to wait for each URL to load.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Mobile Preload', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload cache for mobile devices.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="mobile_preload" <?php checked(isset($settings['mobile_preload']) ? $settings['mobile_preload'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Desktop Preload', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload cache for desktop devices.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="desktop_preload" <?php checked(isset($settings['desktop_preload']) ? $settings['desktop_preload'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload on Content Update', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Automatically preload cache when content is updated.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_on_update" <?php checked(isset($settings['preload_on_update']) ? $settings['preload_on_update'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>

    <div class="redco-modal-section" style="display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;">
        <h3 style="display: block !important; visibility: visible !important; opacity: 1 !important;"><?php esc_html_e('Content Type Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Posts', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload cache for posts.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_posts" <?php checked(isset($settings['preload_posts']) ? $settings['preload_posts'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Pages', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload cache for pages.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_pages" <?php checked(isset($settings['preload_pages']) ? $settings['preload_pages'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Categories', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload cache for category archives.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_categories" <?php checked(isset($settings['preload_categories']) ? $settings['preload_categories'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Tags', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload cache for tag archives.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_tags" <?php checked(isset($settings['preload_tags']) ? $settings['preload_tags'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e('Preload Custom Post Types', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Preload cache for custom post types.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_custom_post_types" <?php checked(isset($settings['preload_custom_post_types']) ? $settings['preload_custom_post_types'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>

    <div class="redco-modal-section" style="display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;">
        <h3 style="display: block !important; visibility: visible !important; opacity: 1 !important;"><?php esc_html_e('Priority Settings', 'redco-optimizer'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_priority"><?php esc_html_e('Preload Priority', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_priority" name="preload_priority" class="redco-select">
                    <option value="homepage_first" <?php selected(isset($settings['preload_priority']) ? $settings['preload_priority'] : 'homepage_first', 'homepage_first'); ?>><?php esc_html_e('Homepage First', 'redco-optimizer'); ?></option>
                    <option value="recent_content" <?php selected(isset($settings['preload_priority']) ? $settings['preload_priority'] : 'homepage_first', 'recent_content'); ?>><?php esc_html_e('Recent Content First', 'redco-optimizer'); ?></option>
                    <option value="popular_content" <?php selected(isset($settings['preload_priority']) ? $settings['preload_priority'] : 'homepage_first', 'popular_content'); ?>><?php esc_html_e('Popular Content First', 'redco-optimizer'); ?></option>
                    <option value="sequential" <?php selected(isset($settings['preload_priority']) ? $settings['preload_priority'] : 'homepage_first', 'sequential'); ?>><?php esc_html_e('Sequential (No Priority)', 'redco-optimizer'); ?></option>
                </select>
                <p class="redco-form-help"><?php esc_html_e('Determine which content should be preloaded first.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_depth"><?php esc_html_e('Preload Depth', 'redco-optimizer'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_depth" name="preload_depth" class="redco-select">
                    <option value="1" <?php selected(isset($settings['preload_depth']) ? $settings['preload_depth'] : '2', '1'); ?>><?php esc_html_e('Level 1 (Homepage Only)', 'redco-optimizer'); ?></option>
                    <option value="2" <?php selected(isset($settings['preload_depth']) ? $settings['preload_depth'] : '2', '2'); ?>><?php esc_html_e('Level 2 (Homepage + Main Pages)', 'redco-optimizer'); ?></option>
                    <option value="3" <?php selected(isset($settings['preload_depth']) ? $settings['preload_depth'] : '2', '3'); ?>><?php esc_html_e('Level 3 (Deep Preload)', 'redco-optimizer'); ?></option>
                    <option value="all" <?php selected(isset($settings['preload_depth']) ? $settings['preload_depth'] : '2', 'all'); ?>><?php esc_html_e('All Pages (Complete Preload)', 'redco-optimizer'); ?></option>
                </select>
                <p class="redco-form-help"><?php esc_html_e('How deep to follow links when preloading. Higher values may increase server load.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-footer" style="display: flex !important; justify-content: flex-end !important; gap: 10px !important; padding: 20px !important; border-top: 1px solid var(--border-color) !important; visibility: visible !important; opacity: 1 !important;">
        <button type="submit" class="redco-button redco-button-primary" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important;">
            <span class="dashicons dashicons-yes" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important;"></span>
            <?php esc_html_e('Save Settings', 'redco-optimizer'); ?>
        </button>
        <button type="button" class="redco-button redco-modal-cancel" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important;">
            <?php esc_html_e('Cancel', 'redco-optimizer'); ?>
        </button>
    </div>
</form>
