<?php

/**
 * The LazyLoad for iframes and videos functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The LazyLoad for iframes and videos functionality of the plugin.
 *
 * Defines the functionality for lazy loading iframes and videos.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_LazyLoad_Iframe {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for LazyLoad iframes and videos.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for LazyLoad iframes and videos.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
    }

    /**
     * Get LazyLoad iframes and videos settings.
     *
     * @since    1.0.0
     * @return   array    The LazyLoad iframes and videos settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_lazyload_settings', array() );
        
        // Default settings
        $defaults = array(
            'lazyload_iframes' => 0,
            'lazyload_videos' => 0,
            'lazyload_iframe_exclusions' => '',
            'youtube_preview_quality' => 'hqdefault',
        );
        
        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize LazyLoad iframes and videos.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if LazyLoad iframes or videos is enabled
        if ( ! $this->settings['lazyload_iframes'] && ! $this->settings['lazyload_videos'] ) {
            return;
        }

        // Add filter to process HTML
        add_filter( 'redco_buffer', array( $this, 'process_html' ), 40 );
    }

    /**
     * Process HTML to lazy load iframes and videos.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    public function process_html( $html ) {
        // Don't process if user is logged in
        if ( is_user_logged_in() ) {
            return $html;
        }
        
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }
        
        // Process iframes if enabled
        if ( $this->settings['lazyload_iframes'] ) {
            $html = $this->process_iframes( $html );
        }
        
        // Process videos if enabled
        if ( $this->settings['lazyload_videos'] ) {
            $html = $this->process_videos( $html );
        }
        
        return $html;
    }

    /**
     * Process iframes to lazy load them.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    private function process_iframes( $html ) {
        // Get exclusions
        $exclusions = $this->get_exclusions();
        
        // Process iframe tags
        $html = preg_replace_callback(
            '/<iframe\s+([^>]*)>/is',
            function( $matches ) use ( $exclusions ) {
                $tag = $matches[0];
                $attributes = $matches[1];
                
                // Skip if excluded
                foreach ( $exclusions as $exclusion ) {
                    if ( strpos( $attributes, $exclusion ) !== false ) {
                        return $tag;
                    }
                }
                
                // Skip if already processed
                if ( strpos( $attributes, 'data-src' ) !== false || strpos( $attributes, 'data-lazy-src' ) !== false ) {
                    return $tag;
                }
                
                // Extract src attribute
                preg_match( '/src=[\'"](.*?)[\'"]/i', $attributes, $src_matches );
                
                if ( empty( $src_matches ) ) {
                    return $tag;
                }
                
                $src = $src_matches[1];
                
                // Check if it's a YouTube iframe
                if ( strpos( $src, 'youtube.com' ) !== false || strpos( $src, 'youtu.be' ) !== false ) {
                    return $this->process_youtube_iframe( $tag, $src, $attributes );
                }
                
                // Create new tag with data-src attribute
                $new_attributes = str_replace( 'src="' . $src . '"', 'src="about:blank" data-lazy-src="' . $src . '"', $attributes );
                $new_tag = '<iframe ' . $new_attributes . ' class="redco-lazyload">';
                
                // Add noscript tag
                $noscript = '<noscript><iframe src="' . $src . '"></iframe></noscript>';
                
                return $new_tag . $noscript;
            },
            $html
        );
        
        return $html;
    }

    /**
     * Process YouTube iframe to replace with preview image.
     *
     * @since    1.0.0
     * @param    string    $tag          The iframe tag.
     * @param    string    $src          The iframe src attribute.
     * @param    string    $attributes   The iframe attributes.
     * @return   string    The processed iframe tag.
     */
    private function process_youtube_iframe( $tag, $src, $attributes ) {
        // Extract YouTube video ID
        $video_id = $this->get_youtube_id( $src );
        
        if ( ! $video_id ) {
            return $tag;
        }
        
        // Get preview image quality
        $quality = $this->settings['youtube_preview_quality'];
        
        // Get preview image URL
        $preview_url = 'https://img.youtube.com/vi/' . $video_id . '/' . $quality . '.jpg';
        
        // Extract width and height
        $width = '100%';
        $height = '100%';
        
        if ( preg_match( '/width=[\'"](.*?)[\'"]/i', $attributes, $width_matches ) ) {
            $width = $width_matches[1];
        }
        
        if ( preg_match( '/height=[\'"](.*?)[\'"]/i', $attributes, $height_matches ) ) {
            $height = $height_matches[1];
        }
        
        // Create wrapper with preview image
        $wrapper = '<div class="redco-youtube-container" style="position:relative;width:' . $width . ';height:' . $height . ';background-image:url(' . $preview_url . ');background-size:cover;background-position:center;cursor:pointer;" data-youtube-id="' . $video_id . '" data-youtube-src="' . $src . '">';
        $wrapper .= '<div class="redco-youtube-play" style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:68px;height:48px;background-color:#f00;border-radius:10px;"><div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);border-style:solid;border-width:12px 0 12px 20px;border-color:transparent transparent transparent #fff;"></div></div>';
        $wrapper .= '</div>';
        
        // Add noscript tag
        $noscript = '<noscript>' . $tag . '</noscript>';
        
        return $wrapper . $noscript;
    }

    /**
     * Process videos to lazy load them.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    private function process_videos( $html ) {
        // Get exclusions
        $exclusions = $this->get_exclusions();
        
        // Process video tags
        $html = preg_replace_callback(
            '/<video\s+([^>]*)>(.*?)<\/video>/is',
            function( $matches ) use ( $exclusions ) {
                $tag = $matches[0];
                $attributes = $matches[1];
                $content = $matches[2];
                
                // Skip if excluded
                foreach ( $exclusions as $exclusion ) {
                    if ( strpos( $attributes, $exclusion ) !== false ) {
                        return $tag;
                    }
                }
                
                // Skip if already processed
                if ( strpos( $attributes, 'data-src' ) !== false || strpos( $attributes, 'data-lazy-src' ) !== false ) {
                    return $tag;
                }
                
                // Extract src attribute
                $src = '';
                if ( preg_match( '/src=[\'"](.*?)[\'"]/i', $attributes, $src_matches ) ) {
                    $src = $src_matches[1];
                    
                    // Create new attributes with data-src
                    $new_attributes = str_replace( 'src="' . $src . '"', 'data-lazy-src="' . $src . '"', $attributes );
                } else {
                    $new_attributes = $attributes;
                }
                
                // Process source tags
                $new_content = preg_replace_callback(
                    '/<source\s+([^>]*)>/is',
                    function( $source_matches ) {
                        $source_attributes = $source_matches[1];
                        
                        // Extract src attribute
                        if ( preg_match( '/src=[\'"](.*?)[\'"]/i', $source_attributes, $src_matches ) ) {
                            $src = $src_matches[1];
                            
                            // Create new attributes with data-src
                            $new_source_attributes = str_replace( 'src="' . $src . '"', 'data-lazy-src="' . $src . '"', $source_attributes );
                            
                            return '<source ' . $new_source_attributes . '>';
                        }
                        
                        return $source_matches[0];
                    },
                    $content
                );
                
                // Add class to video tag
                if ( strpos( $new_attributes, 'class=' ) !== false ) {
                    $new_attributes = preg_replace( '/class=(["\'])(.*?)(["\'])/i', 'class=$1$2 redco-lazyload$3', $new_attributes );
                } else {
                    $new_attributes .= ' class="redco-lazyload"';
                }
                
                // Create new video tag
                $new_tag = '<video ' . $new_attributes . '>' . $new_content . '</video>';
                
                // Add noscript tag
                $noscript = '<noscript>' . $tag . '</noscript>';
                
                return $new_tag . $noscript;
            },
            $html
        );
        
        return $html;
    }

    /**
     * Get exclusions for LazyLoad iframes and videos.
     *
     * @since    1.0.0
     * @return   array    The exclusions for LazyLoad iframes and videos.
     */
    private function get_exclusions() {
        $exclusions = array();
        
        // Add default exclusions
        $exclusions[] = 'no-lazyload';
        $exclusions[] = 'skip-lazy';
        $exclusions[] = 'data-no-lazy';
        
        // Add user exclusions
        if ( ! empty( $this->settings['lazyload_iframe_exclusions'] ) ) {
            $user_exclusions = explode( "\n", $this->settings['lazyload_iframe_exclusions'] );
            $exclusions = array_merge( $exclusions, array_map( 'trim', $user_exclusions ) );
        }
        
        return array_unique( $exclusions );
    }

    /**
     * Get YouTube video ID from URL.
     *
     * @since    1.0.0
     * @param    string    $url    The YouTube URL.
     * @return   string    The YouTube video ID.
     */
    private function get_youtube_id( $url ) {
        $video_id = '';
        
        // Match youtube.com URLs
        if ( preg_match( '/youtube\.com\/embed\/([^\/\?]+)/i', $url, $matches ) ) {
            $video_id = $matches[1];
        } elseif ( preg_match( '/youtube\.com\/watch\?v=([^&]+)/i', $url, $matches ) ) {
            $video_id = $matches[1];
        } elseif ( preg_match( '/youtu\.be\/([^\/\?]+)/i', $url, $matches ) ) {
            $video_id = $matches[1];
        }
        
        return $video_id;
    }

    /**
     * Add YouTube iframe replacement script.
     *
     * @since    1.0.0
     */
    public function add_youtube_script() {
        // Don't add script if user is logged in
        if ( is_user_logged_in() ) {
            return;
        }
        
        // Don't add script on admin pages
        if ( is_admin() ) {
            return;
        }
        
        // Don't add script if LazyLoad videos is disabled
        if ( ! $this->settings['lazyload_videos'] ) {
            return;
        }
        
        // Add script
        $script = $this->get_youtube_script();
        echo $script;
    }

    /**
     * Get YouTube iframe replacement script.
     *
     * @since    1.0.0
     * @return   string    The YouTube iframe replacement script.
     */
    private function get_youtube_script() {
        $script = <<<EOT
<script>
(function() {
    'use strict';
    
    // Load YouTube videos on click
    function setupYouTubeVideos() {
        var containers = document.querySelectorAll('.redco-youtube-container');
        
        if (!containers.length) {
            return;
        }
        
        containers.forEach(function(container) {
            container.addEventListener('click', function() {
                var videoId = this.getAttribute('data-youtube-id');
                var videoSrc = this.getAttribute('data-youtube-src');
                
                if (!videoSrc) {
                    return;
                }
                
                var iframe = document.createElement('iframe');
                iframe.setAttribute('src', videoSrc);
                iframe.setAttribute('frameborder', '0');
                iframe.setAttribute('allowfullscreen', '1');
                iframe.setAttribute('allow', 'accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture');
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                
                this.innerHTML = '';
                this.appendChild(iframe);
            });
        });
    }
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setupYouTubeVideos);
    } else {
        setupYouTubeVideos();
    }
})();
</script>
EOT;

        return $script;
    }
}
