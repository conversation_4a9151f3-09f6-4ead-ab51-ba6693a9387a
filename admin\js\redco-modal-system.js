/**
 * Redco Optimizer - Modal System
 * A clean, modern modal system for Redco Optimizer
 */

(function($) {
    'use strict';

    // Modal System
    const RedcoModal = {
        /**
         * Initialize the modal system
         */
        init: function() {
            this.bindEvents();
            this.createModalContainer();
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            // Open modal
            $(document).on('click', '.redco-open-modal', this.openModal.bind(this));

            // Close modal
            $(document).on('click', '.redco-modal-close, .redco-modal-cancel', this.closeModal.bind(this));

            // Close modal when clicking outside
            $(document).on('click', '.redco-modal-overlay', function(e) {
                if ($(e.target).hasClass('redco-modal-overlay')) {
                    RedcoModal.closeModal(e);
                }
            });

            // Handle form submission
            $(document).on('submit', '.redco-addon-settings-form', this.handleFormSubmit.bind(this));

            // Handle ESC key
            $(document).keydown(function(e) {
                if (e.keyCode === 27) { // ESC key
                    RedcoModal.closeModal(e);
                }
            });
        },

        /**
         * Create modal container
         */
        createModalContainer: function() {
            if ($('.redco-modal-container').length === 0) {
                $('body').append('<div class="redco-modal-container"></div>');
            }
        },

        /**
         * Open modal
         */
        openModal: function(e) {
            e.preventDefault();

            const $button = $(e.currentTarget);
            const addonId = $button.data('addon');
            const modalTitle = $button.data('modal-title') || 'Settings';

            // Show loading state
            this.showLoading(modalTitle);

            // Fetch modal content
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_get_addon_settings',
                    addon: addonId,
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        RedcoModal.renderModal(modalTitle, response.data.html);
                    } else {
                        RedcoModal.showError(response.data.message || 'Error loading settings');
                    }
                },
                error: function() {
                    RedcoModal.showError('Error loading settings. Please try again.');
                }
            });
        },

        /**
         * Show loading state
         */
        showLoading: function(title) {
            const loadingHtml = `
                <div class="redco-modal-overlay">
                    <div class="redco-modal">
                        <div class="redco-modal-header">
                            <h2>${title}</h2>
                            <button type="button" class="redco-modal-close">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>
                        <div class="redco-modal-content">
                            <div class="redco-modal-loading">
                                <svg class="redco-spin" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#00A66B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M12 6v2"></path>
                                </svg>
                                <p>Loading settings...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('.redco-modal-container').html(loadingHtml);
        },

        /**
         * Show error
         */
        showError: function(message) {
            const errorHtml = `
                <div class="redco-modal-loading">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#ef4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    <p>${message}</p>
                </div>
                <div class="redco-modal-footer">
                    <button type="button" class="redco-button redco-button-secondary redco-modal-close">Close</button>
                </div>
            `;

            $('.redco-modal .redco-modal-content').html(errorHtml);
        },

        /**
         * Render modal
         */
        renderModal: function(title, content) {
            const modalHtml = `
                <div class="redco-modal-overlay">
                    <div class="redco-modal">
                        <div class="redco-modal-header">
                            <h2>${title}</h2>
                            <button type="button" class="redco-modal-close">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>
                        <div class="redco-modal-content">
                            ${content}
                        </div>
                    </div>
                </div>
            `;

            $('.redco-modal-container').html(modalHtml);

            // Initialize form elements
            this.initFormElements();
        },

        /**
         * Initialize form elements
         */
        initFormElements: function() {
            // Initialize select2 if available
            if ($.fn.select2) {
                $('.redco-modal .redco-select').select2({
                    minimumResultsForSearch: 10,
                    dropdownParent: $('.redco-modal-content')
                });
            }

            // Ensure toggle switches are green when checked
            $('.redco-modal-content input:checked + .redco-slider').css('background-color', '#00A66B');

            // Add change event to toggle switches
            $('.redco-modal-content input[type="checkbox"]').on('change', function() {
                if ($(this).is(':checked')) {
                    $(this).next('.redco-slider').css('background-color', '#00A66B');
                } else {
                    $(this).next('.redco-slider').css('background-color', '#cbd5e1');
                }
            });

            // Initialize any other form elements here
        },

        /**
         * Close modal
         */
        closeModal: function(e) {
            e.preventDefault();
            $('.redco-modal-container').empty();
        },

        /**
         * Handle form submission
         */
        handleFormSubmit: function(e) {
            e.preventDefault();

            const $form = $(e.currentTarget);
            const addonId = $form.data('addon');

            console.log('Form submitted for addon:', addonId);

            // Show loading state
            const $submitButton = $form.find('button[type="submit"]');
            const originalText = $submitButton.html();
            $submitButton.prop('disabled', true).html('<svg class="redco-spin" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 6v2"></path></svg> Saving...');

            // Create FormData object to properly handle all form fields
            let formDataObj = new FormData($form[0]);

            // Add all checkboxes with value 0 if unchecked (they're not included in FormData by default)
            $form.find('input[type="checkbox"]').each(function() {
                const $checkbox = $(this);
                const name = $checkbox.attr('name');

                // If checkbox exists and is not disabled
                if (name && !$checkbox.prop('disabled')) {
                    // If it's not checked, explicitly set it to 0
                    if (!$checkbox.is(':checked')) {
                        formDataObj.set(name, '0');
                        console.log('Set unchecked checkbox to 0:', name);
                    } else {
                        // Ensure checked boxes have value 1
                        formDataObj.set(name, '1');
                        console.log('Set checked checkbox to 1:', name);
                    }
                }
            });

            // Convert FormData to serialized string
            let formData = '';
            for (let pair of formDataObj.entries()) {
                console.log('Form field:', pair[0], pair[1]);
                formData += '&' + encodeURIComponent(pair[0]) + '=' + encodeURIComponent(pair[1]);
            }

            if (formData.length > 0) {
                formData = formData.substring(1); // Remove the leading &
            }

            console.log('Enhanced form data:', formData);

            console.log('Form data:', formData);

            // Submit form
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_save_addon_settings',
                    addon: addonId,
                    nonce: redco_optimizer.nonce,
                    form_data: formData
                },
                success: function(response) {
                    console.log('AJAX response:', response);

                    if (response.success) {
                        // Show success message
                        RedcoNotification.show('success', response.data.message || 'Settings saved successfully');

                        // Close modal
                        RedcoModal.closeModal(e);

                        // Refresh page if needed
                        if (response.data.refresh) {
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        }
                    } else {
                        // Show error message
                        RedcoNotification.show('error', response.data.message || 'Error saving settings');

                        // Re-enable submit button
                        $submitButton.prop('disabled', false).html(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', status, error);

                    // Show error message
                    RedcoNotification.show('error', 'Error saving settings. Please try again.');

                    // Re-enable submit button
                    $submitButton.prop('disabled', false).html(originalText);
                }
            });
        }
    };

    // Notification System
    const RedcoNotification = {
        /**
         * Show notification
         */
        show: function(type, message, duration = 4000) {
            // Remove existing notifications
            $('.redco-notification').remove();

            // Create notification
            const icon = type === 'success'
                ? '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>'
                : '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';

            const notificationHtml = `
                <div class="redco-notification redco-notification-${type}">
                    <div class="redco-notification-icon">${icon}</div>
                    <div class="redco-notification-content">${message}</div>
                    <button type="button" class="redco-notification-close">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            `;

            // Append notification
            $('body').append(notificationHtml);

            // Show notification
            setTimeout(function() {
                $('.redco-notification').addClass('redco-notification-show');
            }, 10);

            // Hide notification after duration
            setTimeout(function() {
                RedcoNotification.hide();
            }, duration);

            // Close notification on click
            $(document).on('click', '.redco-notification-close', function() {
                RedcoNotification.hide();
            });
        },

        /**
         * Hide notification
         */
        hide: function() {
            $('.redco-notification').removeClass('redco-notification-show');

            setTimeout(function() {
                $('.redco-notification').remove();
            }, 300);
        }
    };

    // Initialize modal system
    $(document).ready(function() {
        RedcoModal.init();
    });

})(jQuery);
