<?php

/**
 * The Preload functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The Preload functionality of the plugin.
 *
 * Defines the functionality for preloading resources.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_Preload {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for preload.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for preload.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
    }

    /**
     * Get preload settings.
     *
     * @since    1.0.0
     * @return   array    The preload settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_preload_settings', array() );

        // Default settings
        $defaults = array(
            'preload_fonts' => 0,
            'preload_links' => 0,
            'prefetch_dns' => 0,
            'preload_cache' => 0,
            'preload_sitemap' => 0,
            'preload_fonts_list' => '',
            'prefetch_dns_list' => '',
            'preload_links_exclusions' => '',
            'sitemap_urls' => '',
        );

        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize preload.
     *
     * @since    1.0.0
     */
    public function init() {
        // Add filter to process HTML
        if ( $this->settings['preload_fonts'] || $this->settings['prefetch_dns'] ) {
            add_filter( 'redco_buffer', array( $this, 'process_html' ), 10 );
        }

        // Add preload links
        if ( $this->settings['preload_links'] ) {
            add_action( 'wp_head', array( $this, 'add_preload_links' ) );
        }

        // Add preload cache
        if ( $this->settings['preload_cache'] ) {
            add_action( 'init', array( $this, 'schedule_preload_cache' ) );
            add_action( 'redco_preload_cache', array( $this, 'preload_cache' ) );
            add_action( 'save_post', array( $this, 'preload_cache_post' ) );
        }

        // Add sitemap-based preloading
        if ( $this->settings['preload_sitemap'] ) {
            add_action( 'init', array( $this, 'schedule_sitemap_preload' ) );
            add_action( 'redco_sitemap_preload', array( $this, 'preload_sitemaps' ) );
        }
    }

    /**
     * Process HTML to add preload resources.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The processed HTML content.
     */
    public function process_html( $html ) {
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }

        $preload_tags = '';

        // Add preload fonts
        if ( $this->settings['preload_fonts'] ) {
            $preload_tags .= $this->get_preload_fonts_tags();
        }

        // Add prefetch DNS
        if ( $this->settings['prefetch_dns'] ) {
            $preload_tags .= $this->get_prefetch_dns_tags();
        }

        // Add preload tags to HTML
        if ( ! empty( $preload_tags ) ) {
            $html = str_replace( '</head>', $preload_tags . '</head>', $html );
        }

        return $html;
    }

    /**
     * Get preload fonts tags.
     *
     * @since    1.0.0
     * @return   string    The preload fonts tags.
     */
    private function get_preload_fonts_tags() {
        $tags = '';

        // Get fonts list
        $fonts_list = $this->settings['preload_fonts_list'];

        if ( empty( $fonts_list ) ) {
            return $tags;
        }

        $fonts = explode( "\n", $fonts_list );

        foreach ( $fonts as $font ) {
            $font = trim( $font );

            if ( empty( $font ) ) {
                continue;
            }

            // Determine font type
            $font_type = 'font/woff2';

            if ( strpos( $font, '.woff' ) !== false ) {
                $font_type = 'font/woff';
            } elseif ( strpos( $font, '.woff2' ) !== false ) {
                $font_type = 'font/woff2';
            } elseif ( strpos( $font, '.ttf' ) !== false ) {
                $font_type = 'font/ttf';
            } elseif ( strpos( $font, '.eot' ) !== false ) {
                $font_type = 'font/eot';
            } elseif ( strpos( $font, '.svg' ) !== false ) {
                $font_type = 'image/svg+xml';
            }

            // Create preload tag
            $tags .= '<link rel="preload" href="' . esc_url( $font ) . '" as="font" type="' . $font_type . '" crossorigin>' . "\n";
        }

        return $tags;
    }

    /**
     * Get prefetch DNS tags.
     *
     * @since    1.0.0
     * @return   string    The prefetch DNS tags.
     */
    private function get_prefetch_dns_tags() {
        $tags = '';

        // Get DNS list
        $dns_list = $this->settings['prefetch_dns_list'];

        if ( empty( $dns_list ) ) {
            return $tags;
        }

        $domains = explode( "\n", $dns_list );

        foreach ( $domains as $domain ) {
            $domain = trim( $domain );

            if ( empty( $domain ) ) {
                continue;
            }

            // Create prefetch tag
            $tags .= '<link rel="dns-prefetch" href="' . esc_url( $domain ) . '">' . "\n";
            $tags .= '<link rel="preconnect" href="' . esc_url( $domain ) . '" crossorigin>' . "\n";
        }

        return $tags;
    }

    /**
     * Add preload links to head.
     *
     * @since    1.0.0
     */
    public function add_preload_links() {
        // Don't add links on admin pages
        if ( is_admin() ) {
            return;
        }

        // Get exclusions
        $exclusions = $this->get_exclusions();

        // Get current URL
        $current_url = $this->get_current_url();

        // Get links from current page
        $links = $this->get_links_from_page( $current_url );

        if ( empty( $links ) ) {
            return;
        }

        // Process links
        foreach ( $links as $link ) {
            // Skip if excluded
            foreach ( $exclusions as $exclusion ) {
                if ( strpos( $link, $exclusion ) !== false ) {
                    continue 2;
                }
            }

            // Skip external links
            if ( ! $this->is_internal_link( $link ) ) {
                continue;
            }

            // Skip non-HTML links
            if ( ! $this->is_html_link( $link ) ) {
                continue;
            }

            // Add preload link
            echo '<link rel="preload" href="' . esc_url( $link ) . '" as="document">' . "\n";
        }
    }

    /**
     * Get exclusions for preload links.
     *
     * @since    1.0.0
     * @return   array    The exclusions for preload links.
     */
    private function get_exclusions() {
        $exclusions = array();

        // Add default exclusions
        $exclusions[] = 'wp-admin';
        $exclusions[] = 'wp-login';
        $exclusions[] = 'cart';
        $exclusions[] = 'checkout';
        $exclusions[] = 'my-account';

        // Add user exclusions
        if ( ! empty( $this->settings['preload_links_exclusions'] ) ) {
            $user_exclusions = explode( "\n", $this->settings['preload_links_exclusions'] );
            $exclusions = array_merge( $exclusions, array_map( 'trim', $user_exclusions ) );
        }

        return array_unique( $exclusions );
    }

    /**
     * Get current URL.
     *
     * @since    1.0.0
     * @return   string    The current URL.
     */
    private function get_current_url() {
        $protocol = ( isset( $_SERVER['HTTPS'] ) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http' );
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'];

        return $protocol . '://' . $host . $uri;
    }

    /**
     * Get links from page.
     *
     * @since    1.0.0
     * @param    string    $url    The URL to get links from.
     * @return   array     The links from the page.
     */
    private function get_links_from_page( $url ) {
        $links = array();

        // Get page content
        $response = wp_remote_get( $url );

        if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
            return $links;
        }

        $html = wp_remote_retrieve_body( $response );

        // Extract links
        preg_match_all( '/<a\s+[^>]*href=["\']([^"\']+)["\'][^>]*>/i', $html, $matches );

        if ( empty( $matches[1] ) ) {
            return $links;
        }

        // Process links
        foreach ( $matches[1] as $link ) {
            // Skip anchors
            if ( strpos( $link, '#' ) === 0 ) {
                continue;
            }

            // Skip mailto links
            if ( strpos( $link, 'mailto:' ) === 0 ) {
                continue;
            }

            // Skip tel links
            if ( strpos( $link, 'tel:' ) === 0 ) {
                continue;
            }

            // Add link to list
            $links[] = $link;
        }

        return array_unique( $links );
    }

    /**
     * Check if link is internal.
     *
     * @since    1.0.0
     * @param    string    $link    The link to check.
     * @return   bool      True if link is internal, false otherwise.
     */
    private function is_internal_link( $link ) {
        $site_url = site_url();

        // Check if link starts with site URL
        if ( strpos( $link, $site_url ) === 0 ) {
            return true;
        }

        // Check if link starts with /
        if ( strpos( $link, '/' ) === 0 && strpos( $link, '//' ) !== 0 ) {
            return true;
        }

        return false;
    }

    /**
     * Check if link is HTML.
     *
     * @since    1.0.0
     * @param    string    $link    The link to check.
     * @return   bool      True if link is HTML, false otherwise.
     */
    private function is_html_link( $link ) {
        $extensions = array( '.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.css', '.js', '.pdf', '.zip', '.rar', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx' );

        foreach ( $extensions as $extension ) {
            if ( strpos( $link, $extension ) !== false ) {
                return false;
            }
        }

        return true;
    }

    /**
     * Schedule preload cache.
     *
     * @since    1.0.0
     */
    public function schedule_preload_cache() {
        if ( ! wp_next_scheduled( 'redco_preload_cache' ) ) {
            wp_schedule_event( time(), 'daily', 'redco_preload_cache' );
        }
    }

    /**
     * Preload cache.
     *
     * @since    1.0.0
     */
    public function preload_cache() {
        // Get home URL
        $home_url = home_url();

        // Preload home page
        $this->preload_url( $home_url );

        // Get sitemap URL
        $sitemap_url = $home_url . '/sitemap.xml';

        // Check if sitemap exists
        $response = wp_remote_get( $sitemap_url );

        if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
            // Sitemap not found, preload recent posts
            $this->preload_recent_posts();
        } else {
            // Sitemap found, preload URLs from sitemap
            $this->preload_sitemap( $sitemap_url );
        }
    }

    /**
     * Preload URL.
     *
     * @since    1.0.0
     * @param    string    $url    The URL to preload.
     */
    private function preload_url( $url ) {
        // Get caching class
        $caching = new Redco_Optimizer_Caching( $this->plugin_name, $this->version );

        // Get URL content
        $response = wp_remote_get( $url );

        if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
            return;
        }

        // Extract links from page
        $html = wp_remote_retrieve_body( $response );

        // Process page to generate cache
        $caching->process_output( $html );
    }

    /**
     * Preload recent posts.
     *
     * @since    1.0.0
     */
    private function preload_recent_posts() {
        // Get recent posts
        $posts = get_posts( array(
            'posts_per_page' => 10,
            'post_type' => 'post',
            'post_status' => 'publish',
        ) );

        if ( empty( $posts ) ) {
            return;
        }

        // Preload each post
        foreach ( $posts as $post ) {
            $url = get_permalink( $post->ID );
            $this->preload_url( $url );
        }
    }

    /**
     * Preload sitemap.
     *
     * @since    1.0.0
     * @param    string    $sitemap_url    The sitemap URL.
     */
    private function preload_sitemap( $sitemap_url ) {
        // Get sitemap content
        $response = wp_remote_get( $sitemap_url );

        if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
            return;
        }

        $xml = wp_remote_retrieve_body( $response );

        // Check if this is a sitemap index
        if ( strpos( $xml, '<sitemapindex' ) !== false ) {
            // Extract sitemap URLs from sitemap index
            preg_match_all( '/<loc>(.*?)<\/loc>/i', $xml, $matches );

            if ( ! empty( $matches[1] ) ) {
                // Preload each sitemap
                foreach ( $matches[1] as $sitemap ) {
                    $this->preload_sitemap( $sitemap );
                }
            }

            return;
        }

        // Extract URLs from sitemap
        preg_match_all( '/<loc>(.*?)<\/loc>/i', $xml, $matches );

        if ( empty( $matches[1] ) ) {
            return;
        }

        // Preload each URL
        foreach ( $matches[1] as $url ) {
            $this->preload_url( $url );
        }
    }

    /**
     * Schedule sitemap preload.
     *
     * @since    1.0.0
     */
    public function schedule_sitemap_preload() {
        if ( ! wp_next_scheduled( 'redco_sitemap_preload' ) ) {
            wp_schedule_event( time(), 'daily', 'redco_sitemap_preload' );
        }
    }

    /**
     * Preload sitemaps.
     *
     * @since    1.0.0
     */
    public function preload_sitemaps() {
        // Get sitemap URLs
        $sitemap_urls = $this->get_sitemap_urls();

        if ( empty( $sitemap_urls ) ) {
            // Try to detect sitemaps from popular SEO plugins
            $detected_sitemaps = $this->detect_sitemaps();

            if ( ! empty( $detected_sitemaps ) ) {
                $sitemap_urls = $detected_sitemaps;
            } else {
                // Default to WordPress sitemap
                $sitemap_urls = array( home_url( '/wp-sitemap.xml' ) );
            }
        }

        // Preload each sitemap
        foreach ( $sitemap_urls as $sitemap_url ) {
            $this->preload_sitemap( $sitemap_url );
        }
    }

    /**
     * Get sitemap URLs from settings.
     *
     * @since    1.0.0
     * @return   array    The sitemap URLs.
     */
    private function get_sitemap_urls() {
        $urls = array();

        if ( ! empty( $this->settings['sitemap_urls'] ) ) {
            $urls = explode( "\n", $this->settings['sitemap_urls'] );
            $urls = array_map( 'trim', $urls );
            $urls = array_filter( $urls );
        }

        return $urls;
    }

    /**
     * Detect sitemaps from popular SEO plugins.
     *
     * @since    1.0.0
     * @return   array    The detected sitemap URLs.
     */
    private function detect_sitemaps() {
        $sitemaps = array();
        $home_url = home_url();

        // Yoast SEO
        if ( defined( 'WPSEO_VERSION' ) ) {
            $sitemaps[] = $home_url . '/sitemap_index.xml';
        }

        // All in One SEO Pack
        if ( class_exists( 'AIOSEO' ) ) {
            $sitemaps[] = $home_url . '/sitemap.xml';
        }

        // Rank Math SEO
        if ( class_exists( 'RankMath' ) ) {
            $sitemaps[] = $home_url . '/sitemap_index.xml';
        }

        // SEOPress
        if ( defined( 'SEOPRESS_VERSION' ) ) {
            $sitemaps[] = $home_url . '/sitemaps.xml';
        }

        // The SEO Framework
        if ( class_exists( 'The_SEO_Framework' ) ) {
            $sitemaps[] = $home_url . '/sitemap.xml';
        }

        // WordPress default sitemap (WP 5.5+)
        if ( function_exists( 'wp_sitemaps_get_server' ) ) {
            $sitemaps[] = $home_url . '/wp-sitemap.xml';
        }

        return $sitemaps;
    }

    /**
     * Preload cache for a post.
     *
     * @since    1.0.0
     * @param    int    $post_id    The post ID.
     */
    public function preload_cache_post( $post_id ) {
        // Don't preload for revisions or auto-drafts
        if ( wp_is_post_revision( $post_id ) || wp_is_post_autosave( $post_id ) ) {
            return;
        }

        // Don't preload for non-published posts
        if ( get_post_status( $post_id ) !== 'publish' ) {
            return;
        }

        // Preload post URL
        $url = get_permalink( $post_id );
        $this->preload_url( $url );
    }
}
