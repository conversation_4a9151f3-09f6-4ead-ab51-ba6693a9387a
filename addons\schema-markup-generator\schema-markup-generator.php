<?php
/**
 * Schema Markup Generator Add-on
 *
 * @link              https://redco-optimizer.com
 * @since             1.0.0
 * @package           Redco_Optimizer
 *
 * @wordpress-plugin
 * Addon Name:        Schema Markup Generator
 * Description:       Generate structured data for better SEO with support for multiple schema types including Article, Product, FAQ, and more.
 * Version:           1.0.0
 * Author:            Redco
 * Author URI:        https://redco-optimizer.com
 * Premium:           false
 * Coming_Soon:       true
 * Has Settings:      true
 * Icon:              dashicons-editor-code
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Schema Markup Generator class.
 */
class Redco_Optimizer_Schema_Markup_Generator {

    /**
     * The settings for this addon.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for this addon.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Load settings
        $this->settings = get_option('redco_schema_markup_generator_settings', array(
            'enabled' => false,
            'organization_schema' => true,
            'organization_name' => get_bloginfo('name'),
            'organization_logo' => '',
            'organization_type' => 'Organization',
            'website_schema' => true,
            'breadcrumbs_schema' => true,
            'article_schema' => true,
            'article_post_types' => 'post',
            'product_schema' => false,
            'product_post_types' => 'product',
            'faq_schema' => false,
            'faq_post_types' => 'post,page',
            'local_business_schema' => false,
            'local_business_name' => get_bloginfo('name'),
            'local_business_type' => 'LocalBusiness',
            'local_business_address' => '',
            'local_business_city' => '',
            'local_business_state' => '',
            'local_business_postal_code' => '',
            'local_business_country' => '',
            'local_business_phone' => '',
            'local_business_email' => '',
            'local_business_hours' => '',
            'local_business_geo_lat' => '',
            'local_business_geo_lng' => '',
            'custom_schema' => '',
        ));

        // Register hooks
        $this->register_hooks();
    }

    /**
     * Register all hooks for this addon.
     *
     * @since    1.0.0
     */
    private function register_hooks() {
        // Add admin page
        add_action('admin_menu', array($this, 'add_admin_page'));

        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add meta box for schema settings
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));

        // Save meta box data
        add_action('save_post', array($this, 'save_meta_box_data'));

        // Add schema to head
        if ($this->is_enabled()) {
            add_action('wp_head', array($this, 'add_schema_markup'));
        }
    }

    /**
     * Check if schema markup is enabled.
     *
     * @since    1.0.0
     * @return   bool    True if schema markup is enabled, false otherwise.
     */
    public function is_enabled() {
        return isset($this->settings['enabled']) && $this->settings['enabled'];
    }

    /**
     * Add admin page.
     *
     * @since    1.0.0
     */
    public function add_admin_page() {
        add_submenu_page(
            'redco-optimizer-addons',
            __('Schema Markup Generator', 'redco-optimizer'),
            __('Schema Markup Generator', 'redco-optimizer'),
            'manage_options',
            'redco-optimizer-schema-markup-generator',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Enqueue scripts and styles.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts($hook) {
        if ($hook === 'redco-optimizer_page_redco-optimizer-schema-markup-generator' || $hook === 'post.php' || $hook === 'post-new.php') {
            wp_enqueue_script('redco-schema-markup-generator', plugin_dir_url(__FILE__) . 'js/schema-markup-generator.js', array('jquery'), '1.0.0', true);
            wp_localize_script('redco-schema-markup-generator', 'redco_schema', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('redco_schema_nonce'),
            ));
        }

        // Enqueue the modal fix script on all admin pages
        if (is_admin()) {
            wp_enqueue_script('redco-schema-markup-generator-modal-fix', plugin_dir_url(__FILE__) . 'js/modal-fix.js', array('jquery'), '1.0.0', true);
        }
    }

    /**
     * Render the settings page.
     *
     * @since    1.0.0
     */
    public function render_settings_page() {
        // Check if this addon's settings are being displayed
        if (!isset($_GET['addon']) || $_GET['addon'] !== 'schema-markup-generator') {
            return;
        }

        // Save settings if form is submitted
        if (isset($_POST['redco_schema_markup_generator_save_settings'])) {
            $this->save_settings();
        }

        // Include settings template
        include_once plugin_dir_path(__FILE__) . 'templates/settings.php';
    }

    /**
     * Save settings.
     *
     * @since    1.0.0
     */
    public function save_settings() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Verify nonce
        if (!isset($_POST['redco_schema_markup_generator_nonce']) || !wp_verify_nonce($_POST['redco_schema_markup_generator_nonce'], 'redco_schema_markup_generator_save_settings')) {
            add_settings_error('redco_schema_markup_generator', 'redco_schema_markup_generator_nonce', __('Security check failed.', 'redco-optimizer'), 'error');
            return;
        }

        // Get settings
        $settings = array(
            'enabled' => isset($_POST['enabled']) ? 1 : 0,
            'organization_schema' => isset($_POST['organization_schema']) ? 1 : 0,
            'organization_name' => sanitize_text_field($_POST['organization_name']),
            'organization_logo' => esc_url_raw($_POST['organization_logo']),
            'organization_type' => sanitize_text_field($_POST['organization_type']),
            'website_schema' => isset($_POST['website_schema']) ? 1 : 0,
            'breadcrumbs_schema' => isset($_POST['breadcrumbs_schema']) ? 1 : 0,
            'article_schema' => isset($_POST['article_schema']) ? 1 : 0,
            'article_post_types' => sanitize_text_field($_POST['article_post_types']),
            'product_schema' => isset($_POST['product_schema']) ? 1 : 0,
            'product_post_types' => sanitize_text_field($_POST['product_post_types']),
            'faq_schema' => isset($_POST['faq_schema']) ? 1 : 0,
            'faq_post_types' => sanitize_text_field($_POST['faq_post_types']),
            'local_business_schema' => isset($_POST['local_business_schema']) ? 1 : 0,
            'local_business_name' => sanitize_text_field($_POST['local_business_name']),
            'local_business_type' => sanitize_text_field($_POST['local_business_type']),
            'local_business_address' => sanitize_text_field($_POST['local_business_address']),
            'local_business_city' => sanitize_text_field($_POST['local_business_city']),
            'local_business_state' => sanitize_text_field($_POST['local_business_state']),
            'local_business_postal_code' => sanitize_text_field($_POST['local_business_postal_code']),
            'local_business_country' => sanitize_text_field($_POST['local_business_country']),
            'local_business_phone' => sanitize_text_field($_POST['local_business_phone']),
            'local_business_email' => sanitize_email($_POST['local_business_email']),
            'local_business_hours' => sanitize_textarea_field($_POST['local_business_hours']),
            'local_business_geo_lat' => sanitize_text_field($_POST['local_business_geo_lat']),
            'local_business_geo_lng' => sanitize_text_field($_POST['local_business_geo_lng']),
            'custom_schema' => sanitize_textarea_field($_POST['custom_schema']),
        );

        // Update settings
        update_option('redco_schema_markup_generator_settings', $settings);
        $this->settings = $settings;

        // Add success message
        add_settings_error('redco_schema_markup_generator', 'redco_schema_markup_generator_updated', __('Settings saved.', 'redco-optimizer'), 'success');
    }

    /**
     * Add meta boxes.
     *
     * @since    1.0.0
     */
    public function add_meta_boxes() {
        // Get post types for schema
        $post_types = $this->get_schema_post_types();

        // Add meta box to each post type
        foreach ($post_types as $post_type) {
            add_meta_box(
                'redco_schema_markup_meta_box',
                __('Schema Markup Settings', 'redco-optimizer'),
                array($this, 'render_meta_box'),
                $post_type,
                'normal',
                'high'
            );
        }
    }

    /**
     * Render meta box.
     *
     * @since    1.0.0
     * @param    WP_Post    $post    The post object.
     */
    public function render_meta_box($post) {
        // Add nonce for security
        wp_nonce_field('redco_schema_markup_meta_box', 'redco_schema_markup_meta_box_nonce');

        // Get meta data
        $schema_type = get_post_meta($post->ID, '_redco_schema_type', true);
        $schema_enabled = get_post_meta($post->ID, '_redco_schema_enabled', true);
        $schema_data = get_post_meta($post->ID, '_redco_schema_data', true);

        // Include meta box template
        include_once plugin_dir_path(__FILE__) . 'templates/meta-box.php';
    }

    /**
     * Save meta box data.
     *
     * @since    1.0.0
     * @param    int    $post_id    The post ID.
     */
    public function save_meta_box_data($post_id) {
        // Check if nonce is set
        if (!isset($_POST['redco_schema_markup_meta_box_nonce'])) {
            return;
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['redco_schema_markup_meta_box_nonce'], 'redco_schema_markup_meta_box')) {
            return;
        }

        // Check if autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save schema type
        if (isset($_POST['redco_schema_type'])) {
            update_post_meta($post_id, '_redco_schema_type', sanitize_text_field($_POST['redco_schema_type']));
        }

        // Save schema enabled
        $schema_enabled = isset($_POST['redco_schema_enabled']) ? 1 : 0;
        update_post_meta($post_id, '_redco_schema_enabled', $schema_enabled);

        // Save schema data
        if (isset($_POST['redco_schema_data'])) {
            update_post_meta($post_id, '_redco_schema_data', sanitize_textarea_field($_POST['redco_schema_data']));
        }
    }

    /**
     * Add schema markup to head.
     *
     * @since    1.0.0
     */
    public function add_schema_markup() {
        // Get schema markup
        $schema = $this->generate_schema_markup();

        // Output schema markup
        if (!empty($schema)) {
            echo '<script type="application/ld+json">' . wp_json_encode($schema) . '</script>';
        }
    }

    /**
     * Generate schema markup.
     *
     * @since    1.0.0
     * @return   array    The schema markup.
     */
    private function generate_schema_markup() {
        // Initialize schema
        $schema = array();

        // Add organization schema
        if (isset($this->settings['organization_schema']) && $this->settings['organization_schema']) {
            $schema[] = $this->generate_organization_schema();
        }

        // Add website schema
        if (isset($this->settings['website_schema']) && $this->settings['website_schema']) {
            $schema[] = $this->generate_website_schema();
        }

        // Add breadcrumbs schema
        if (isset($this->settings['breadcrumbs_schema']) && $this->settings['breadcrumbs_schema'] && !is_front_page()) {
            $schema[] = $this->generate_breadcrumbs_schema();
        }

        // Add article schema
        if (isset($this->settings['article_schema']) && $this->settings['article_schema'] && is_singular($this->get_article_post_types())) {
            $schema[] = $this->generate_article_schema();
        }

        // Add product schema
        if (isset($this->settings['product_schema']) && $this->settings['product_schema'] && is_singular($this->get_product_post_types())) {
            $schema[] = $this->generate_product_schema();
        }

        // Add FAQ schema
        if (isset($this->settings['faq_schema']) && $this->settings['faq_schema'] && is_singular($this->get_faq_post_types())) {
            $schema[] = $this->generate_faq_schema();
        }

        // Add local business schema
        if (isset($this->settings['local_business_schema']) && $this->settings['local_business_schema'] && is_front_page()) {
            $schema[] = $this->generate_local_business_schema();
        }

        // Add custom schema
        if (!empty($this->settings['custom_schema'])) {
            $custom_schema = json_decode($this->settings['custom_schema'], true);
            if ($custom_schema) {
                $schema[] = $custom_schema;
            }
        }

        // Add post-specific schema
        if (is_singular()) {
            $post_id = get_the_ID();
            $schema_enabled = get_post_meta($post_id, '_redco_schema_enabled', true);
            $schema_type = get_post_meta($post_id, '_redco_schema_type', true);
            $schema_data = get_post_meta($post_id, '_redco_schema_data', true);

            if ($schema_enabled && !empty($schema_type) && !empty($schema_data)) {
                $post_schema = json_decode($schema_data, true);
                if ($post_schema) {
                    $schema[] = $post_schema;
                }
            }
        }

        return $schema;
    }

    /**
     * Generate organization schema.
     *
     * @since    1.0.0
     * @return   array    The organization schema.
     */
    private function generate_organization_schema() {
        // This is a placeholder. In a real implementation, this would generate the organization schema.
        return array(
            '@context' => 'https://schema.org',
            '@type' => $this->settings['organization_type'],
            'name' => $this->settings['organization_name'],
            'url' => home_url(),
            'logo' => $this->settings['organization_logo'],
        );
    }

    /**
     * Generate website schema.
     *
     * @since    1.0.0
     * @return   array    The website schema.
     */
    private function generate_website_schema() {
        // This is a placeholder. In a real implementation, this would generate the website schema.
        return array(
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'potentialAction' => array(
                '@type' => 'SearchAction',
                'target' => home_url('?s={search_term_string}'),
                'query-input' => 'required name=search_term_string',
            ),
        );
    }

    /**
     * Generate breadcrumbs schema.
     *
     * @since    1.0.0
     * @return   array    The breadcrumbs schema.
     */
    private function generate_breadcrumbs_schema() {
        // This is a placeholder. In a real implementation, this would generate the breadcrumbs schema.
        return array(
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => array(
                array(
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => home_url(),
                ),
                array(
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => get_the_title(),
                    'item' => get_permalink(),
                ),
            ),
        );
    }

    /**
     * Generate article schema.
     *
     * @since    1.0.0
     * @return   array    The article schema.
     */
    private function generate_article_schema() {
        // This is a placeholder. In a real implementation, this would generate the article schema.
        return array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title(),
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c'),
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author(),
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => $this->settings['organization_logo'],
                ),
            ),
            'mainEntityOfPage' => get_permalink(),
            'image' => get_the_post_thumbnail_url(),
        );
    }

    /**
     * Generate product schema.
     *
     * @since    1.0.0
     * @return   array    The product schema.
     */
    private function generate_product_schema() {
        // This is a placeholder. In a real implementation, this would generate the product schema.
        return array(
            '@context' => 'https://schema.org',
            '@type' => 'Product',
            'name' => get_the_title(),
            'description' => get_the_excerpt(),
            'image' => get_the_post_thumbnail_url(),
            'offers' => array(
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD',
                'availability' => 'https://schema.org/InStock',
            ),
        );
    }

    /**
     * Generate FAQ schema.
     *
     * @since    1.0.0
     * @return   array    The FAQ schema.
     */
    private function generate_faq_schema() {
        // Get FAQ content from the site
        $faq_posts = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => 5,
            'meta_query' => array(
                array(
                    'key' => '_redco_faq_content',
                    'compare' => 'EXISTS',
                )
            )
        ));

        // If no FAQ posts found, try to get recent posts as a fallback
        if (empty($faq_posts)) {
            $faq_posts = get_posts(array(
                'post_type' => 'post',
                'posts_per_page' => 3,
            ));
        }

        $faq_items = array();

        foreach ($faq_posts as $post) {
            $faq_items[] = array(
                '@type' => 'Question',
                'name' => get_the_title($post),
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => wp_trim_words(get_the_excerpt($post), 30, '...'),
                ),
            );
        }

        return array(
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => $faq_items,
        );
    }

    /**
     * Generate local business schema.
     *
     * @since    1.0.0
     * @return   array    The local business schema.
     */
    private function generate_local_business_schema() {
        // This is a placeholder. In a real implementation, this would generate the local business schema.
        return array(
            '@context' => 'https://schema.org',
            '@type' => $this->settings['local_business_type'],
            'name' => $this->settings['local_business_name'],
            'address' => array(
                '@type' => 'PostalAddress',
                'streetAddress' => $this->settings['local_business_address'],
                'addressLocality' => $this->settings['local_business_city'],
                'addressRegion' => $this->settings['local_business_state'],
                'postalCode' => $this->settings['local_business_postal_code'],
                'addressCountry' => $this->settings['local_business_country'],
            ),
            'telephone' => $this->settings['local_business_phone'],
            'email' => $this->settings['local_business_email'],
            'url' => home_url(),
            'geo' => array(
                '@type' => 'GeoCoordinates',
                'latitude' => $this->settings['local_business_geo_lat'],
                'longitude' => $this->settings['local_business_geo_lng'],
            ),
        );
    }

    /**
     * Get post types for schema.
     *
     * @since    1.0.0
     * @return   array    The post types for schema.
     */
    private function get_schema_post_types() {
        $post_types = array();

        // Add article post types
        if (isset($this->settings['article_schema']) && $this->settings['article_schema']) {
            $post_types = array_merge($post_types, $this->get_article_post_types());
        }

        // Add product post types
        if (isset($this->settings['product_schema']) && $this->settings['product_schema']) {
            $post_types = array_merge($post_types, $this->get_product_post_types());
        }

        // Add FAQ post types
        if (isset($this->settings['faq_schema']) && $this->settings['faq_schema']) {
            $post_types = array_merge($post_types, $this->get_faq_post_types());
        }

        return array_unique($post_types);
    }

    /**
     * Get article post types.
     *
     * @since    1.0.0
     * @return   array    The article post types.
     */
    private function get_article_post_types() {
        $post_types = isset($this->settings['article_post_types']) ? $this->settings['article_post_types'] : 'post';
        return array_map('trim', explode(',', $post_types));
    }

    /**
     * Get product post types.
     *
     * @since    1.0.0
     * @return   array    The product post types.
     */
    private function get_product_post_types() {
        $post_types = isset($this->settings['product_post_types']) ? $this->settings['product_post_types'] : 'product';
        return array_map('trim', explode(',', $post_types));
    }

    /**
     * Get FAQ post types.
     *
     * @since    1.0.0
     * @return   array    The FAQ post types.
     */
    private function get_faq_post_types() {
        $post_types = isset($this->settings['faq_post_types']) ? $this->settings['faq_post_types'] : 'post,page';
        return array_map('trim', explode(',', $post_types));
    }

    /**
     * Save settings via AJAX.
     *
     * @since    1.0.0
     * @param    array    $settings    The settings to save.
     * @return   mixed    True on success, WP_Error on failure.
     */
    public function save_settings_ajax($settings) {
        if (!current_user_can('manage_options')) {
            return new WP_Error('permission_denied', __('You do not have permission to perform this action.', 'redco-optimizer'));
        }

        // Sanitize settings
        $sanitized_settings = array(
            'enabled' => isset($settings['enabled']) ? 1 : 0,
            'organization_schema' => isset($settings['organization_schema']) ? 1 : 0,
            'organization_name' => sanitize_text_field($settings['organization_name']),
            'organization_logo' => esc_url_raw($settings['organization_logo']),
            'organization_type' => sanitize_text_field($settings['organization_type']),
            'website_schema' => isset($settings['website_schema']) ? 1 : 0,
            'breadcrumbs_schema' => isset($settings['breadcrumbs_schema']) ? 1 : 0,
            'article_schema' => isset($settings['article_schema']) ? 1 : 0,
            'article_post_types' => sanitize_text_field($settings['article_post_types']),
            'product_schema' => isset($settings['product_schema']) ? 1 : 0,
            'product_post_types' => sanitize_text_field($settings['product_post_types']),
            'faq_schema' => isset($settings['faq_schema']) ? 1 : 0,
            'faq_post_types' => sanitize_text_field($settings['faq_post_types']),
            'local_business_schema' => isset($settings['local_business_schema']) ? 1 : 0,
            'local_business_name' => sanitize_text_field($settings['local_business_name']),
            'local_business_type' => sanitize_text_field($settings['local_business_type']),
            'local_business_address' => sanitize_text_field($settings['local_business_address']),
            'local_business_city' => sanitize_text_field($settings['local_business_city']),
            'local_business_state' => sanitize_text_field($settings['local_business_state']),
            'local_business_postal_code' => sanitize_text_field($settings['local_business_postal_code']),
            'local_business_country' => sanitize_text_field($settings['local_business_country']),
            'local_business_phone' => sanitize_text_field($settings['local_business_phone']),
            'local_business_email' => sanitize_email($settings['local_business_email']),
            'local_business_hours' => sanitize_textarea_field($settings['local_business_hours']),
            'local_business_geo_lat' => sanitize_text_field($settings['local_business_geo_lat']),
            'local_business_geo_lng' => sanitize_text_field($settings['local_business_geo_lng']),
            'custom_schema' => sanitize_textarea_field($settings['custom_schema']),
        );

        // Update settings
        update_option('redco_schema_markup_generator_settings', $sanitized_settings);
        $this->settings = $sanitized_settings;

        return true;
    }
}

// Initialize the addon
new Redco_Optimizer_Schema_Markup_Generator();
