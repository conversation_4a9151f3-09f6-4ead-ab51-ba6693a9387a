<?php
/**
 * Advanced Cache Preloader Add-on
 *
 * @link              https://redco-optimizer.com
 * @since             1.0.0
 * @package           Redco_Optimizer
 *
 * @wordpress-plugin
 * Addon Name:        Advanced Cache Preloader
 * Description:       Intelligent cache preloading with sitemap integration, scheduled preloading, and priority-based crawling.
 * Version:           1.0.0
 * Author:            Redco
 * Author URI:        https://redco-optimizer.com
 * Premium:           false
 * Has Settings:      true
 * Icon:              dashicons-performance
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Advanced Cache Preloader class.
 */
class Redco_Optimizer_Advanced_Cache_Preloader {

    /**
     * The settings for this addon.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for this addon.
     */
    private $settings;

    /**
     * The current preloading status.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $status    The current preloading status.
     */
    private $status;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Load settings
        $this->settings = get_option('redco_advanced_cache_preloader_settings', array(
            'enabled' => false,
            'preload_method' => 'sitemap',
            'sitemap_url' => '',
            'custom_urls' => '',
            'preload_schedule' => 'daily',
            'preload_time' => '00:00',
            'preload_on_publish' => true,
            'preload_on_update' => true,
            'preload_on_comment' => false,
            'preload_homepage' => true,
            'preload_categories' => true,
            'preload_tags' => false,
            'preload_post_types' => 'post,page',
            'preload_depth' => 2,
            'preload_throttle' => 3,
            'user_agent' => 'Redco Cache Preloader',
            'priority_urls' => '',
            'exclude_urls' => '',
            'mobile_preload' => false,
            'desktop_preload' => true,
        ));

        // Load status
        $this->status = get_option('redco_advanced_cache_preloader_status', array(
            'is_preloading' => false,
            'last_preload' => 0,
            'next_preload' => 0,
            'total_urls' => 0,
            'processed_urls' => 0,
            'success_urls' => 0,
            'failed_urls' => 0,
            'current_url' => '',
            'log' => array(),
        ));

        // Register hooks
        $this->register_hooks();
    }

    /**
     * Register all hooks for this addon.
     *
     * @since    1.0.0
     */
    private function register_hooks() {
        // Add admin page
        add_action('admin_menu', array($this, 'add_admin_page'));

        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add AJAX handlers
        add_action('wp_ajax_redco_start_preload', array($this, 'ajax_start_preload'));
        add_action('wp_ajax_redco_stop_preload', array($this, 'ajax_stop_preload'));
        add_action('wp_ajax_redco_get_preload_status', array($this, 'ajax_get_preload_status'));

        // Add cron events
        add_action('redco_preload_cache', array($this, 'preload_cache'));

        // Add post publish/update hooks
        if (isset($this->settings['preload_on_publish']) && $this->settings['preload_on_publish']) {
            add_action('publish_post', array($this, 'preload_on_post_publish'), 10, 2);
        }

        if (isset($this->settings['preload_on_update']) && $this->settings['preload_on_update']) {
            add_action('post_updated', array($this, 'preload_on_post_update'), 10, 3);
        }

        if (isset($this->settings['preload_on_comment']) && $this->settings['preload_on_comment']) {
            add_action('comment_post', array($this, 'preload_on_comment'), 10, 2);
        }

        // Schedule preload if enabled
        if (isset($this->settings['enabled']) && $this->settings['enabled']) {
            $this->schedule_preload();
        }
    }

    /**
     * Add admin page.
     *
     * @since    1.0.0
     */
    public function add_admin_page() {
        add_submenu_page(
            'redco-optimizer-addons',
            __('Advanced Cache Preloader', 'redco-optimizer'),
            __('Advanced Cache Preloader', 'redco-optimizer'),
            'manage_options',
            'redco-optimizer-advanced-cache-preloader',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Enqueue scripts and styles.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts($hook) {
        // Enqueue scripts for the settings page
        if ($hook === 'redco-optimizer_page_redco-optimizer-advanced-cache-preloader') {
            wp_enqueue_script('redco-advanced-cache-preloader', plugin_dir_url(__FILE__) . 'js/advanced-cache-preloader.js', array('jquery'), '1.0.0', true);
            wp_localize_script('redco-advanced-cache-preloader', 'redco_preloader', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('redco_preloader_nonce'),
                'is_preloading' => $this->status['is_preloading'],
            ));
        }

        // Enqueue the modal fix script on all admin pages
        if (is_admin()) {
            wp_enqueue_script('redco-advanced-cache-preloader-modal-fix', plugin_dir_url(__FILE__) . 'js/modal-fix.js', array('jquery'), '1.0.0', true);
        }
    }

    /**
     * Render the settings page.
     *
     * @since    1.0.0
     */
    public function render_settings_page() {
        // Check if this addon's settings are being displayed
        if (!isset($_GET['addon']) || $_GET['addon'] !== 'advanced-cache-preloader') {
            return;
        }

        // Save settings if form is submitted
        if (isset($_POST['redco_advanced_cache_preloader_save_settings'])) {
            $this->save_settings();
        }

        // Include settings template
        include_once plugin_dir_path(__FILE__) . 'templates/settings.php';
    }

    /**
     * Save settings.
     *
     * @since    1.0.0
     */
    public function save_settings() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Verify nonce
        if (!isset($_POST['redco_advanced_cache_preloader_nonce']) || !wp_verify_nonce($_POST['redco_advanced_cache_preloader_nonce'], 'redco_advanced_cache_preloader_save_settings')) {
            add_settings_error('redco_advanced_cache_preloader', 'redco_advanced_cache_preloader_nonce', __('Security check failed.', 'redco-optimizer'), 'error');
            return;
        }

        // Get settings
        $settings = array(
            'enabled' => isset($_POST['enabled']) ? 1 : 0,
            'preload_method' => sanitize_text_field($_POST['preload_method']),
            'sitemap_url' => esc_url_raw($_POST['sitemap_url']),
            'custom_urls' => sanitize_textarea_field($_POST['custom_urls']),
            'preload_schedule' => sanitize_text_field($_POST['preload_schedule']),
            'preload_time' => sanitize_text_field($_POST['preload_time']),
            'preload_on_publish' => isset($_POST['preload_on_publish']) ? 1 : 0,
            'preload_on_update' => isset($_POST['preload_on_update']) ? 1 : 0,
            'preload_on_comment' => isset($_POST['preload_on_comment']) ? 1 : 0,
            'preload_homepage' => isset($_POST['preload_homepage']) ? 1 : 0,
            'preload_categories' => isset($_POST['preload_categories']) ? 1 : 0,
            'preload_tags' => isset($_POST['preload_tags']) ? 1 : 0,
            'preload_post_types' => sanitize_text_field($_POST['preload_post_types']),
            'preload_depth' => intval($_POST['preload_depth']),
            'preload_throttle' => intval($_POST['preload_throttle']),
            'user_agent' => sanitize_text_field($_POST['user_agent']),
            'priority_urls' => sanitize_textarea_field($_POST['priority_urls']),
            'exclude_urls' => sanitize_textarea_field($_POST['exclude_urls']),
            'mobile_preload' => isset($_POST['mobile_preload']) ? 1 : 0,
            'desktop_preload' => isset($_POST['desktop_preload']) ? 1 : 0,
        );

        // Update settings
        update_option('redco_advanced_cache_preloader_settings', $settings);
        $this->settings = $settings;

        // Reschedule preload if enabled
        if ($settings['enabled']) {
            $this->schedule_preload();
        } else {
            $this->unschedule_preload();
        }

        // Add success message
        add_settings_error('redco_advanced_cache_preloader', 'redco_advanced_cache_preloader_updated', __('Settings saved.', 'redco-optimizer'), 'success');
    }

    /**
     * Schedule preload.
     *
     * @since    1.0.0
     */
    private function schedule_preload() {
        // Unschedule existing event
        $this->unschedule_preload();

        // Get schedule
        $schedule = isset($this->settings['preload_schedule']) ? $this->settings['preload_schedule'] : 'daily';
        $time = isset($this->settings['preload_time']) ? $this->settings['preload_time'] : '00:00';

        // Parse time
        list($hour, $minute) = explode(':', $time);
        $hour = intval($hour);
        $minute = intval($minute);

        // Calculate next run time
        $now = current_time('timestamp');
        $next_run = strtotime(date('Y-m-d', $now) . ' ' . $hour . ':' . $minute . ':00');
        if ($next_run < $now) {
            $next_run = strtotime('+1 day', $next_run);
        }

        // Schedule event
        wp_schedule_event($next_run, $schedule, 'redco_preload_cache');

        // Update status
        $this->status['next_preload'] = $next_run;
        update_option('redco_advanced_cache_preloader_status', $this->status);
    }

    /**
     * Unschedule preload.
     *
     * @since    1.0.0
     */
    private function unschedule_preload() {
        wp_clear_scheduled_hook('redco_preload_cache');
    }

    /**
     * Start preload via AJAX.
     *
     * @since    1.0.0
     */
    public function ajax_start_preload() {
        // Check nonce
        check_ajax_referer('redco_preloader_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Start preload
        $result = $this->start_preload();

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        } else {
            wp_send_json_success(array(
                'message' => __('Preloading started.', 'redco-optimizer'),
                'status' => $this->status,
            ));
        }
    }

    /**
     * Stop preload via AJAX.
     *
     * @since    1.0.0
     */
    public function ajax_stop_preload() {
        // Check nonce
        check_ajax_referer('redco_preloader_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Stop preload
        $this->stop_preload();

        wp_send_json_success(array(
            'message' => __('Preloading stopped.', 'redco-optimizer'),
            'status' => $this->status,
        ));
    }

    /**
     * Get preload status via AJAX.
     *
     * @since    1.0.0
     */
    public function ajax_get_preload_status() {
        // Check nonce
        check_ajax_referer('redco_preloader_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get status
        wp_send_json_success(array(
            'status' => $this->status,
        ));
    }

    /**
     * Start preload.
     *
     * @since    1.0.0
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    public function start_preload() {
        // Check if already preloading
        if ($this->status['is_preloading']) {
            return new WP_Error('already_preloading', __('Preloading is already in progress.', 'redco-optimizer'));
        }

        // Get URLs to preload
        $urls = $this->get_urls_to_preload();

        if (empty($urls)) {
            return new WP_Error('no_urls', __('No URLs to preload.', 'redco-optimizer'));
        }

        // Update status
        $this->status['is_preloading'] = true;
        $this->status['last_preload'] = current_time('timestamp');
        $this->status['total_urls'] = count($urls);
        $this->status['processed_urls'] = 0;
        $this->status['success_urls'] = 0;
        $this->status['failed_urls'] = 0;
        $this->status['current_url'] = '';
        $this->status['log'] = array();

        update_option('redco_advanced_cache_preloader_status', $this->status);

        // Schedule preload
        wp_schedule_single_event(time(), 'redco_preload_cache');

        return true;
    }

    /**
     * Stop preload.
     *
     * @since    1.0.0
     */
    public function stop_preload() {
        // Update status
        $this->status['is_preloading'] = false;
        $this->status['current_url'] = '';

        update_option('redco_advanced_cache_preloader_status', $this->status);

        // Unschedule preload
        wp_clear_scheduled_hook('redco_preload_cache');
    }

    /**
     * Preload cache.
     *
     * @since    1.0.0
     */
    public function preload_cache() {
        // Check if preloading is enabled
        if (!isset($this->settings['enabled']) || !$this->settings['enabled']) {
            return;
        }

        // Check if already preloading
        if (!$this->status['is_preloading']) {
            $this->start_preload();
        }

        // Get URLs to preload
        $urls = $this->get_urls_to_preload();

        // Get throttle
        $throttle = isset($this->settings['preload_throttle']) ? intval($this->settings['preload_throttle']) : 3;

        // Preload URLs
        $count = 0;
        foreach ($urls as $url) {
            // Check if preloading is still active
            if (!$this->status['is_preloading']) {
                break;
            }

            // Skip already processed URLs
            if ($this->status['processed_urls'] > 0 && $count < $this->status['processed_urls']) {
                $count++;
                continue;
            }

            // Update status
            $this->status['current_url'] = $url;
            $this->status['processed_urls']++;
            update_option('redco_advanced_cache_preloader_status', $this->status);

            // Preload URL
            $result = $this->preload_url($url);

            // Update status
            if ($result) {
                $this->status['success_urls']++;
                $this->status['log'][] = array(
                    'url' => $url,
                    'status' => 'success',
                    'time' => current_time('timestamp'),
                );
            } else {
                $this->status['failed_urls']++;
                $this->status['log'][] = array(
                    'url' => $url,
                    'status' => 'failed',
                    'time' => current_time('timestamp'),
                );
            }

            // Limit log size
            if (count($this->status['log']) > 100) {
                $this->status['log'] = array_slice($this->status['log'], -100);
            }

            update_option('redco_advanced_cache_preloader_status', $this->status);

            // Sleep to throttle requests
            if ($throttle > 0) {
                sleep($throttle);
            }

            $count++;
        }

        // Check if all URLs have been processed
        if ($this->status['processed_urls'] >= $this->status['total_urls']) {
            $this->stop_preload();
        } else {
            // Schedule next batch
            wp_schedule_single_event(time() + 60, 'redco_preload_cache');
        }
    }

    /**
     * Preload URL.
     *
     * @since    1.0.0
     * @param    string    $url    The URL to preload.
     * @return   bool    True on success, false on failure.
     */
    private function preload_url($url) {
        // This is a placeholder. In a real implementation, this would preload the URL.
        return true;
    }

    /**
     * Get URLs to preload.
     *
     * @since    1.0.0
     * @return   array    The URLs to preload.
     */
    private function get_urls_to_preload() {
        // This is a placeholder. In a real implementation, this would get the URLs to preload.
        return array(
            home_url(),
            home_url('/about/'),
            home_url('/contact/'),
            home_url('/blog/'),
        );
    }

    /**
     * Preload on post publish.
     *
     * @since    1.0.0
     * @param    int       $post_id    The post ID.
     * @param    WP_Post   $post       The post object.
     */
    public function preload_on_post_publish($post_id, $post) {
        // This is a placeholder. In a real implementation, this would preload the post URL.
    }

    /**
     * Preload on post update.
     *
     * @since    1.0.0
     * @param    int       $post_id    The post ID.
     * @param    WP_Post   $post_after The post object after the update.
     * @param    WP_Post   $post_before The post object before the update.
     */
    public function preload_on_post_update($post_id, $post_after, $post_before) {
        // This is a placeholder. In a real implementation, this would preload the post URL.
    }

    /**
     * Preload on comment.
     *
     * @since    1.0.0
     * @param    int       $comment_id    The comment ID.
     * @param    int       $comment_approved 1 if the comment is approved, 0 if not.
     */
    public function preload_on_comment($comment_id, $comment_approved) {
        // This is a placeholder. In a real implementation, this would preload the comment's post URL.
    }

    /**
     * Get settings modal content
     */
    public function get_settings_modal() {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'redco-optimizer')));
        }

        ob_start();
        include plugin_dir_path(__FILE__) . 'templates/settings-modal-new.php';
        $html = ob_get_clean();

        wp_send_json_success(array('html' => $html));
    }

    /**
     * Save settings via AJAX.
     *
     * @since    1.0.0
     * @param    array    $settings    The settings to save.
     * @return   mixed    True on success, WP_Error on failure.
     */
    public function save_settings_ajax($settings) {
        if (!current_user_can('manage_options')) {
            return new WP_Error('permission_denied', __('You do not have permission to perform this action.', 'redco-optimizer'));
        }

        // Debug log
        error_log('Advanced Cache Preloader - Saving settings via AJAX: ' . print_r($settings, true));

        // Ensure all checkbox fields are properly set
        $checkbox_fields = array(
            'enabled', 'preload_on_publish', 'preload_on_update', 'preload_on_comment',
            'preload_homepage', 'preload_categories', 'preload_tags', 'mobile_preload', 'desktop_preload'
        );

        foreach ($checkbox_fields as $field) {
            if (!isset($settings[$field])) {
                $settings[$field] = 0;
            }
        }

        // Sanitize settings
        $sanitized_settings = array(
            'enabled' => isset($settings['enabled']) && $settings['enabled'] ? 1 : 0,
            'preload_method' => isset($settings['preload_method']) ? sanitize_text_field($settings['preload_method']) : 'sitemap',
            'sitemap_url' => isset($settings['sitemap_url']) ? esc_url_raw($settings['sitemap_url']) : '',
            'custom_urls' => isset($settings['custom_urls']) ? sanitize_textarea_field($settings['custom_urls']) : '',
            'preload_schedule' => isset($settings['preload_schedule']) ? sanitize_text_field($settings['preload_schedule']) : 'daily',
            'preload_time' => isset($settings['preload_time']) ? sanitize_text_field($settings['preload_time']) : '00:00',
            'preload_on_publish' => isset($settings['preload_on_publish']) && $settings['preload_on_publish'] ? 1 : 0,
            'preload_on_update' => isset($settings['preload_on_update']) && $settings['preload_on_update'] ? 1 : 0,
            'preload_on_comment' => isset($settings['preload_on_comment']) && $settings['preload_on_comment'] ? 1 : 0,
            'preload_homepage' => isset($settings['preload_homepage']) && $settings['preload_homepage'] ? 1 : 0,
            'preload_categories' => isset($settings['preload_categories']) && $settings['preload_categories'] ? 1 : 0,
            'preload_tags' => isset($settings['preload_tags']) && $settings['preload_tags'] ? 1 : 0,
            'preload_post_types' => isset($settings['preload_post_types']) ? sanitize_text_field($settings['preload_post_types']) : 'post,page',
            'preload_depth' => isset($settings['preload_depth']) ? intval($settings['preload_depth']) : 2,
            'preload_throttle' => isset($settings['preload_throttle']) ? intval($settings['preload_throttle']) : 3,
            'user_agent' => isset($settings['user_agent']) ? sanitize_text_field($settings['user_agent']) : 'Redco Cache Preloader',
            'priority_urls' => isset($settings['priority_urls']) ? sanitize_textarea_field($settings['priority_urls']) : '',
            'exclude_urls' => isset($settings['exclude_urls']) ? sanitize_textarea_field($settings['exclude_urls']) : '',
            'mobile_preload' => isset($settings['mobile_preload']) && $settings['mobile_preload'] ? 1 : 0,
            'desktop_preload' => isset($settings['desktop_preload']) && $settings['desktop_preload'] ? 1 : 0,
        );

        // Debug log
        error_log('Advanced Cache Preloader - Sanitized settings: ' . print_r($sanitized_settings, true));

        try {
            // Update settings
            update_option('redco_advanced_cache_preloader_settings', $sanitized_settings);
            $this->settings = $sanitized_settings;

            // Reschedule preload if enabled
            if ($sanitized_settings['enabled']) {
                $this->schedule_preload();
            } else {
                $this->unschedule_preload();
            }

            return true;
        } catch (Exception $e) {
            error_log('Advanced Cache Preloader - Error saving settings: ' . $e->getMessage());
            return new WP_Error('save_failed', __('Failed to save settings: ', 'redco-optimizer') . $e->getMessage());
        }
    }
}

// Initialize the addon
new Redco_Optimizer_Advanced_Cache_Preloader();
