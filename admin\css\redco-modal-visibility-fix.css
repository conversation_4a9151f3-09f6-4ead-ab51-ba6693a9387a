/**
 * Redco Optimizer - Modal Visibility Fix
 * Ensures all elements in modals are visible
 */

/* Modal structure */
.redco-modal {
    max-height: 95vh !important;
    height: auto !important;
    display: flex !important;
    flex-direction: column !important;
    width: 800px !important;
    max-width: 95% !important;
    overflow: hidden !important;
    border-radius: 4px !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 999999 !important;
}

/* Modal header */
.redco-modal-header {
    padding: 15px 25px !important;
    border-bottom: 1px solid #e5e5e5 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    background-color: #f8f9fa !important;
    border-top-left-radius: 4px !important;
    border-top-right-radius: 4px !important;
}

/* Modal sections */
.redco-modal-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 25px !important;
    position: relative !important;
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
}

/* Modal section headers */
.redco-modal-section h3 {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 0 0 15px 0 !important;
    padding-bottom: 8px !important;
    border-bottom: 1px solid #e5e5e5 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #23282d !important;
    position: relative !important;
}

/* Remove green line under modal section headers */
.redco-modal-section h3::after {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    opacity: 0 !important;
}

/* Form rows */
.redco-form-row,
.redco-setting-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 15px !important;
    position: relative !important;
    flex-wrap: wrap !important;
}

/* Toggle rows */
.redco-toggle-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 15px !important;
    position: relative !important;
}

/* Form fields */
.redco-form-field,
.redco-setting-field {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    flex: 1 !important;
    min-width: 200px !important;
}

/* Form labels */
.redco-form-label,
.redco-setting-label {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    flex: 0 0 200px !important;
    padding-right: 20px !important;
}

/* Toggle info */
.redco-toggle-info {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    flex: 1 !important;
    padding-right: 20px !important;
}

/* Toggle controls */
.redco-toggle-control {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
}

/* Toggle switch */
.redco-switch {
    position: relative !important;
    display: inline-block !important;
    width: 50px !important;
    height: 24px !important;
    margin: 0 !important;
}

.redco-switch input {
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

.redco-slider {
    position: absolute !important;
    cursor: pointer !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: #ccc !important;
    transition: .4s !important;
    border-radius: 34px !important;
}

.redco-slider:before {
    position: absolute !important;
    content: "" !important;
    height: 16px !important;
    width: 16px !important;
    left: 4px !important;
    bottom: 4px !important;
    background-color: white !important;
    transition: .4s !important;
    border-radius: 50% !important;
}

input:checked + .redco-slider {
    background-color: #00A66B !important;
}

input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

input:checked + .redco-slider:before {
    transform: translateX(26px) !important;
}

/* Form inputs */
.redco-input,
.redco-select,
.redco-textarea {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    width: 100% !important;
}

/* Form help text */
.redco-form-help {
    margin: 5px 0 0 !important;
    font-size: 12px !important;
    color: #646970 !important;
    display: block !important;
    width: 100% !important;
    clear: both !important;
    order: 3 !important;
    padding-left: 0 !important;
}

/* Modal content */
.redco-modal-content {
    overflow-y: auto !important; /* Changed to auto for better behavior */
    overflow-x: hidden !important;
    flex: 1 1 auto !important;
    max-height: calc(95vh - 140px) !important;
    min-height: 300px !important;
    height: auto !important;
    scrollbar-width: thin !important;
    padding: 25px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    margin: 0 !important;
    border: none !important;
    box-sizing: border-box !important;
    background-color: #fff !important;
}

/* Debug message */
.redco-modal-debug-message {
    margin-bottom: 20px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Scroll indicator */
.redco-scroll-indicator-bottom {
    margin-top: 20px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure scrollbars are visible */
.redco-modal-content::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-modal-content::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px !important;
}

.redco-modal-content::-webkit-scrollbar-thumb {
    background: #888 !important;
    border-radius: 4px !important;
}

.redco-modal-content::-webkit-scrollbar-thumb:hover {
    background: #555 !important;
}

/* Modal footer */
.redco-modal-footer {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    justify-content: flex-end !important;
    gap: 10px !important;
    padding: 20px !important;
    border-top: 1px solid #e5e5e5 !important;
    margin-top: 20px !important;
    background-color: #f8f9fa !important;
}

/* Form actions */
.redco-form-actions {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    justify-content: flex-end !important;
    gap: 10px !important;
    padding: 20px !important;
    border-top: 1px solid #e5e5e5 !important;
    margin-top: 20px !important;
    background-color: #f8f9fa !important;
}

/* Modal form */
.redco-addon-settings-form {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    overflow: visible !important;
}

/* Buttons */
.redco-button {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    text-align: center !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.redco-button-primary {
    background-color: #00A66B !important;
    color: #fff !important;
    border: 1px solid #00A66B !important;
}

.redco-button-primary:hover {
    background-color: #008f5b !important;
    border-color: #008f5b !important;
}

.redco-button-secondary {
    background-color: #f8f9fa !important;
    color: #23282d !important;
    border: 1px solid #ccc !important;
}

.redco-button-secondary:hover {
    background-color: #f1f1f1 !important;
    border-color: #999 !important;
}

/* Responsive styles */
@media screen and (max-width: 782px) {
    .redco-form-row,
    .redco-setting-row {
        flex-direction: column !important;
    }

    .redco-form-label,
    .redco-setting-label {
        flex: 0 0 100% !important;
        padding-right: 0 !important;
        margin-bottom: 10px !important;
    }

    .redco-form-field,
    .redco-setting-field {
        flex: 0 0 100% !important;
    }
}
