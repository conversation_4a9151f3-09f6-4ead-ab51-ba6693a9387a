<?php
/**
 * Redco Optimizer Functions
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// These functions are already defined in class-redco-optimizer-addons.php
// Do not redefine them here to avoid conflicts

/**
 * Get available add-ons.
 *
 * @since    1.0.0
 * @return   array    The available add-ons.
 */
function redco_get_available_addons() {
    global $redco_optimizer_addons;

    if (!isset($redco_optimizer_addons)) {
        $redco_optimizer_addons = new Redco_Optimizer_Addons();
    }

    // Call the get_available_addons method if it exists
    if (method_exists($redco_optimizer_addons, 'get_available_addons')) {
        return $redco_optimizer_addons->get_available_addons();
    }

    // Fallback to a default implementation
    return array();
}

/**
 * Check if an add-on is active.
 *
 * @since    1.0.0
 * @param    string    $addon_slug    The add-on slug.
 * @return   bool      True if the add-on is active, false otherwise.
 */
function redco_is_addon_active($addon_slug) {
    $addons = redco_get_installed_addons();

    return isset($addons[$addon_slug]) && isset($addons[$addon_slug]['active']) && $addons[$addon_slug]['active'];
}

/**
 * Check if an add-on is installed.
 *
 * @since    1.0.0
 * @param    string    $addon_slug    The add-on slug.
 * @return   bool      True if the add-on is installed, false otherwise.
 */
function redco_is_addon_installed($addon_slug) {
    $addons = redco_get_installed_addons();

    return isset($addons[$addon_slug]);
}

/**
 * Get add-on data.
 *
 * @since    1.0.0
 * @param    string    $addon_slug    The add-on slug.
 * @return   array     The add-on data.
 */
function redco_get_addon_data($addon_slug) {
    $addons = redco_get_installed_addons();

    return isset($addons[$addon_slug]) ? $addons[$addon_slug] : array();
}

/**
 * Activate an add-on.
 *
 * @since    1.0.0
 * @param    string    $addon_slug    The add-on slug.
 * @return   mixed     True on success, WP_Error on failure.
 */
function redco_activate_addon($addon_slug) {
    global $redco_optimizer_addons;

    if (!isset($redco_optimizer_addons)) {
        $redco_optimizer_addons = new Redco_Optimizer_Addons();
    }

    return $redco_optimizer_addons->activate_addon($addon_slug);
}

/**
 * Deactivate an add-on.
 *
 * @since    1.0.0
 * @param    string    $addon_slug    The add-on slug.
 * @return   mixed     True on success, WP_Error on failure.
 */
function redco_deactivate_addon($addon_slug) {
    global $redco_optimizer_addons;

    if (!isset($redco_optimizer_addons)) {
        $redco_optimizer_addons = new Redco_Optimizer_Addons();
    }

    return $redco_optimizer_addons->deactivate_addon($addon_slug);
}

/**
 * Install an add-on.
 *
 * @since    1.0.0
 * @param    string    $addon_slug    The add-on slug.
 * @return   mixed     True on success, WP_Error on failure.
 */
function redco_install_addon($addon_slug) {
    global $redco_optimizer_addons;

    if (!isset($redco_optimizer_addons)) {
        $redco_optimizer_addons = new Redco_Optimizer_Addons();
    }

    return $redco_optimizer_addons->install_addon($addon_slug);
}

/**
 * Uninstall an add-on.
 *
 * @since    1.0.0
 * @param    string    $addon_slug    The add-on slug.
 * @return   mixed     True on success, WP_Error on failure.
 */
function redco_uninstall_addon($addon_slug) {
    global $redco_optimizer_addons;

    if (!isset($redco_optimizer_addons)) {
        $redco_optimizer_addons = new Redco_Optimizer_Addons();
    }

    return $redco_optimizer_addons->uninstall_addon($addon_slug);
}

/**
 * Refresh add-ons.
 *
 * @since    1.0.0
 * @return   bool    True on success, false on failure.
 */
function redco_refresh_addons() {
    global $redco_optimizer_addons;

    if (!isset($redco_optimizer_addons)) {
        $redco_optimizer_addons = new Redco_Optimizer_Addons();
    }

    return $redco_optimizer_addons->refresh_addons();
}

/**
 * Format file size.
 *
 * @since    1.0.0
 * @param    int       $bytes     The file size in bytes.
 * @param    int       $decimals  The number of decimal places.
 * @return   string    The formatted file size.
 */
function redco_format_file_size($bytes, $decimals = 2) {
    $size = array('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');
    $factor = floor((strlen($bytes) - 1) / 3);

    return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . ' ' . $size[$factor];
}

/**
 * Get the plugin URL.
 *
 * @since    1.0.0
 * @return   string    The plugin URL.
 */
function redco_get_plugin_url() {
    return plugin_dir_url(dirname(__FILE__));
}

/**
 * Get the plugin path.
 *
 * @since    1.0.0
 * @return   string    The plugin path.
 */
function redco_get_plugin_path() {
    return plugin_dir_path(dirname(__FILE__));
}

/**
 * Get the plugin basename.
 *
 * @since    1.0.0
 * @return   string    The plugin basename.
 */
function redco_get_plugin_basename() {
    return plugin_basename(dirname(dirname(__FILE__)) . '/redco-optimizer.php');
}

/**
 * Get the plugin version.
 *
 * @since    1.0.0
 * @return   string    The plugin version.
 */
function redco_get_plugin_version() {
    return REDCO_OPTIMIZER_VERSION;
}

/**
 * Get the plugin name.
 *
 * @since    1.0.0
 * @return   string    The plugin name.
 */
function redco_get_plugin_name() {
    return 'Redco Optimizer';
}

/**
 * Get the plugin author.
 *
 * @since    1.0.0
 * @return   string    The plugin author.
 */
function redco_get_plugin_author() {
    return 'Redco';
}

/**
 * Get the plugin author URL.
 *
 * @since    1.0.0
 * @return   string    The plugin author URL.
 */
function redco_get_plugin_author_url() {
    return 'https://redco-optimizer.com';
}

/**
 * Get the plugin description.
 *
 * @since    1.0.0
 * @return   string    The plugin description.
 */
function redco_get_plugin_description() {
    return 'Redco Optimizer is a powerful WordPress plugin that optimizes your website for better performance.';
}

/**
 * Get the plugin support URL.
 *
 * @since    1.0.0
 * @return   string    The plugin support URL.
 */
function redco_get_plugin_support_url() {
    return 'https://redco-optimizer.com/support';
}

/**
 * Get the plugin documentation URL.
 *
 * @since    1.0.0
 * @return   string    The plugin documentation URL.
 */
function redco_get_plugin_documentation_url() {
    return 'https://redco-optimizer.com/documentation';
}

/**
 * Get the plugin premium URL.
 *
 * @since    1.0.0
 * @return   string    The plugin premium URL.
 */
function redco_get_plugin_premium_url() {
    return 'https://redco-optimizer.com/premium';
}

/**
 * Get the plugin add-ons URL.
 *
 * @since    1.0.0
 * @return   string    The plugin add-ons URL.
 */
function redco_get_plugin_addons_url() {
    return 'https://redco-optimizer.com/addons';
}

/**
 * Get the plugin settings URL.
 *
 * @since    1.0.0
 * @return   string    The plugin settings URL.
 */
function redco_get_plugin_settings_url() {
    return admin_url('admin.php?page=redco-optimizer');
}

/**
 * Get the plugin modules URL.
 *
 * @since    1.0.0
 * @return   string    The plugin modules URL.
 */
function redco_get_plugin_modules_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=modules');
}

/**
 * Get the plugin add-ons URL.
 *
 * @since    1.0.0
 * @return   string    The plugin add-ons URL.
 */
function redco_get_plugin_addons_admin_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=addons');
}

/**
 * Get the plugin premium URL.
 *
 * @since    1.0.0
 * @return   string    The plugin premium URL.
 */
function redco_get_plugin_premium_admin_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=premium');
}

/**
 * Get the plugin help URL.
 *
 * @since    1.0.0
 * @return   string    The plugin help URL.
 */
function redco_get_plugin_help_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=help');
}

/**
 * Get the plugin tools URL.
 *
 * @since    1.0.0
 * @return   string    The plugin tools URL.
 */
function redco_get_plugin_tools_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=tools');
}

/**
 * Get the plugin site health inspector URL.
 *
 * @since    1.0.0
 * @return   string    The plugin site health inspector URL.
 */
function redco_get_plugin_site_health_inspector_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=site-health-inspector');
}

/**
 * Get the plugin heartbeat URL.
 *
 * @since    1.0.0
 * @return   string    The plugin heartbeat URL.
 */
function redco_get_plugin_heartbeat_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=heartbeat');
}

/**
 * Get the plugin database URL.
 *
 * @since    1.0.0
 * @return   string    The plugin database URL.
 */
function redco_get_plugin_database_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=database');
}

/**
 * Get the plugin preload URL.
 *
 * @since    1.0.0
 * @return   string    The plugin preload URL.
 */
function redco_get_plugin_preload_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=preload');
}

/**
 * Get the plugin media URL.
 *
 * @since    1.0.0
 * @return   string    The plugin media URL.
 */
function redco_get_plugin_media_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=media');
}

/**
 * Get the plugin file optimization URL.
 *
 * @since    1.0.0
 * @return   string    The plugin file optimization URL.
 */
function redco_get_plugin_file_optimization_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=file-optimization');
}

/**
 * Get the plugin caching URL.
 *
 * @since    1.0.0
 * @return   string    The plugin caching URL.
 */
function redco_get_plugin_caching_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=caching');
}

/**
 * Get the plugin cdn URL.
 *
 * @since    1.0.0
 * @return   string    The plugin cdn URL.
 */
function redco_get_plugin_cdn_url() {
    return admin_url('admin.php?page=redco-optimizer&tab=cdn');
}
