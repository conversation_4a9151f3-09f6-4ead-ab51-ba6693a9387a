<?php
/**
 * Premium tab content for Redco Optimizer.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get premium features from our premium system
$premium_features = array();
if (class_exists('Redco_Optimizer_Premium')) {
    $premium_instance = new Redco_Optimizer_Premium();
    $all_premium_features = $premium_instance->get_all_premium_features();

    // Organize features by module for better display
    foreach ($all_premium_features as $module => $features) {
        foreach ($features as $feature => $config) {
            $premium_features[] = array(
                'module' => $module,
                'feature' => $feature,
                'name' => $config['name'],
                'description' => $config['description'],
                'status' => $config['status']
            );
        }
    }
}
?>

<div class="redco-premium-container">
    <!-- Premium Hero Section -->
    <div class="redco-premium-hero">
        <div class="redco-premium-hero-content">
            <h2><?php esc_html_e('Redco Optimizer Premium', 'redco-optimizer'); ?></h2>
            <p><?php esc_html_e('Unlock enterprise-level optimization features to maximize your website performance. Get access to advanced caching, file optimization, database cleanup, and more.', 'redco-optimizer'); ?></p>
            <div class="redco-premium-stats">
                <div class="redco-premium-stat">
                    <span class="redco-premium-stat-number"><?php echo count($premium_features); ?></span>
                    <span class="redco-premium-stat-label"><?php esc_html_e('Premium Features', 'redco-optimizer'); ?></span>
                </div>
                <div class="redco-premium-stat">
                    <span class="redco-premium-stat-number">8</span>
                    <span class="redco-premium-stat-label"><?php esc_html_e('Modules Enhanced', 'redco-optimizer'); ?></span>
                </div>
                <div class="redco-premium-stat">
                    <span class="redco-premium-stat-number">50%+</span>
                    <span class="redco-premium-stat-label"><?php esc_html_e('Performance Boost', 'redco-optimizer'); ?></span>
                </div>
            </div>
            <a href="#redco-premium-plans" class="redco-button redco-button-premium">
                <?php esc_html_e('View Plans & Pricing', 'redco-optimizer'); ?>
            </a>
        </div>
    </div>

    <!-- Premium Features Overview -->
    <?php if (!empty($premium_features)) : ?>
    <div class="redco-premium-features-overview">
        <h3><?php esc_html_e('Premium Features Available', 'redco-optimizer'); ?></h3>
        <p><?php esc_html_e('These enterprise-level features are currently available with "Coming Soon" status and will be unlocked with your premium subscription.', 'redco-optimizer'); ?></p>

        <div class="redco-premium-features-list">
            <?php
            $modules_organized = array();
            foreach ($premium_features as $feature) {
                $modules_organized[$feature['module']][] = $feature;
            }

            foreach ($modules_organized as $module => $features) :
                $module_title = ucfirst(str_replace('-', ' ', $module));
                $module_icon = 'dashicons-admin-generic';

                // Set module-specific icons
                switch ($module) {
                    case 'caching':
                        $module_icon = 'dashicons-performance';
                        break;
                    case 'file-optimization':
                        $module_icon = 'dashicons-media-code';
                        break;
                    case 'media':
                        $module_icon = 'dashicons-format-image';
                        break;
                    case 'database':
                        $module_icon = 'dashicons-database';
                        break;
                    case 'preload':
                        $module_icon = 'dashicons-superhero';
                        break;
                    case 'cdn':
                        $module_icon = 'dashicons-cloud';
                        break;
                    case 'heartbeat':
                        $module_icon = 'dashicons-heart';
                        break;
                    case 'site-health-inspector':
                        $module_icon = 'dashicons-shield';
                        break;
                }
            ?>
            <div class="redco-premium-module-group">
                <div class="redco-premium-module-header">
                    <span class="redco-premium-module-icon dashicons <?php echo esc_attr($module_icon); ?>"></span>
                    <h4><?php echo esc_html($module_title); ?></h4>
                    <span class="redco-premium-module-count"><?php echo count($features); ?> <?php esc_html_e('features', 'redco-optimizer'); ?></span>
                </div>
                <div class="redco-premium-module-features">
                    <?php foreach ($features as $feature) : ?>
                    <div class="redco-premium-feature-item">
                        <div class="redco-premium-feature-info">
                            <h5><?php echo esc_html($feature['name']); ?></h5>
                            <p><?php echo esc_html($feature['description']); ?></p>
                        </div>
                        <div class="redco-premium-feature-status">
                            <span class="redco-premium-badge redco-coming-soon-badge"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Premium Benefits -->
    <div class="redco-premium-benefits-section">
        <h3><?php esc_html_e('Why Choose Premium?', 'redco-optimizer'); ?></h3>

        <div class="redco-premium-benefits-grid">
            <!-- Enterprise Performance -->
            <div class="redco-premium-benefit-card">
                <div class="redco-premium-benefit-icon">
                    <span class="dashicons dashicons-performance"></span>
                </div>
                <div class="redco-premium-benefit-content">
                    <h4><?php esc_html_e('Enterprise Performance', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Unlock advanced optimization techniques used by enterprise websites to achieve maximum performance scores.', 'redco-optimizer'); ?></p>
                </div>
            </div>

            <!-- Advanced Features -->
            <div class="redco-premium-benefit-card">
                <div class="redco-premium-benefit-icon">
                    <span class="dashicons dashicons-admin-tools"></span>
                </div>
                <div class="redco-premium-benefit-content">
                    <h4><?php esc_html_e('Advanced Features', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Access to cutting-edge optimization features including CDN integration, advanced caching, and file optimization.', 'redco-optimizer'); ?></p>
                </div>
            </div>

            <!-- Priority Support -->
            <div class="redco-premium-benefit-card">
                <div class="redco-premium-benefit-icon">
                    <span class="dashicons dashicons-superhero"></span>
                </div>
                <div class="redco-premium-benefit-content">
                    <h4><?php esc_html_e('Priority Support', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Get direct access to our expert support team with priority response times and personalized assistance.', 'redco-optimizer'); ?></p>
                </div>
            </div>

            <!-- Regular Updates -->
            <div class="redco-premium-benefit-card">
                <div class="redco-premium-benefit-icon">
                    <span class="dashicons dashicons-update"></span>
                </div>
                <div class="redco-premium-benefit-content">
                    <h4><?php esc_html_e('Regular Updates', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Stay ahead with regular feature updates, security patches, and performance improvements.', 'redco-optimizer'); ?></p>
                </div>
            </div>

            <!-- No Limits -->
            <div class="redco-premium-benefit-card">
                <div class="redco-premium-benefit-icon">
                    <span class="dashicons dashicons-unlock"></span>
                </div>
                <div class="redco-premium-benefit-content">
                    <h4><?php esc_html_e('No Limits', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Remove all limitations and unlock the full potential of your website optimization.', 'redco-optimizer'); ?></p>
                </div>
            </div>

            <!-- Money-Back Guarantee -->
            <div class="redco-premium-benefit-card">
                <div class="redco-premium-benefit-icon">
                    <span class="dashicons dashicons-shield"></span>
                </div>
                <div class="redco-premium-benefit-content">
                    <h4><?php esc_html_e('30-Day Guarantee', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Try premium risk-free with our 30-day money-back guarantee. No questions asked.', 'redco-optimizer'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Coming Soon Notice -->
    <div class="redco-premium-coming-soon">
        <div class="redco-premium-coming-soon-content">
            <div class="redco-premium-coming-soon-icon">
                <span class="dashicons dashicons-clock"></span>
            </div>
            <div class="redco-premium-coming-soon-text">
                <h3><?php esc_html_e('Premium Version Coming Soon', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('We\'re putting the finishing touches on Redco Optimizer Premium. All the features shown above are currently in development and will be available soon.', 'redco-optimizer'); ?></p>
                <div class="redco-premium-coming-soon-features">
                    <div class="redco-premium-coming-soon-feature">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <span><?php echo count($premium_features); ?> <?php esc_html_e('Premium features ready', 'redco-optimizer'); ?></span>
                    </div>
                    <div class="redco-premium-coming-soon-feature">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <span><?php esc_html_e('Enterprise-level optimization', 'redco-optimizer'); ?></span>
                    </div>
                    <div class="redco-premium-coming-soon-feature">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <span><?php esc_html_e('Priority support included', 'redco-optimizer'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Newsletter Signup -->
    <div class="redco-premium-newsletter">
        <div class="redco-premium-newsletter-content">
            <h3><?php esc_html_e('Be the First to Know', 'redco-optimizer'); ?></h3>
            <p><?php esc_html_e('Get notified when Redco Optimizer Premium launches and receive exclusive early-bird pricing.', 'redco-optimizer'); ?></p>
            <div class="redco-premium-newsletter-form">
                <input type="email" placeholder="<?php esc_attr_e('Enter your email address', 'redco-optimizer'); ?>" class="redco-premium-email-input">
                <button type="button" class="redco-button redco-button-premium redco-premium-notify-btn">
                    <?php esc_html_e('Notify Me', 'redco-optimizer'); ?>
                </button>
            </div>
            <div class="redco-premium-newsletter-benefits">
                <div class="redco-premium-newsletter-benefit">
                    <span class="dashicons dashicons-email-alt"></span>
                    <span><?php esc_html_e('Launch notification', 'redco-optimizer'); ?></span>
                </div>
                <div class="redco-premium-newsletter-benefit">
                    <span class="dashicons dashicons-tag"></span>
                    <span><?php esc_html_e('Early-bird pricing', 'redco-optimizer'); ?></span>
                </div>
                <div class="redco-premium-newsletter-benefit">
                    <span class="dashicons dashicons-star-filled"></span>
                    <span><?php esc_html_e('Exclusive updates', 'redco-optimizer'); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Free Features -->
    <div class="redco-premium-free-features">
        <h3><?php esc_html_e('Available Now - Free Features', 'redco-optimizer'); ?></h3>
        <p><?php esc_html_e('While you wait for premium features, enjoy these powerful optimization tools available right now for free.', 'redco-optimizer'); ?></p>

        <div class="redco-premium-free-grid">
            <div class="redco-premium-free-feature">
                <span class="dashicons dashicons-performance"></span>
                <h4><?php esc_html_e('Page Caching', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Speed up your website with intelligent page caching.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-premium-free-feature">
                <span class="dashicons dashicons-media-code"></span>
                <h4><?php esc_html_e('File Optimization', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Minify and optimize CSS, JavaScript, and HTML files.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-premium-free-feature">
                <span class="dashicons dashicons-format-image"></span>
                <h4><?php esc_html_e('Image Optimization', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Lazy load images and optimize image delivery.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-premium-free-feature">
                <span class="dashicons dashicons-database"></span>
                <h4><?php esc_html_e('Database Cleanup', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Clean up your database and remove unnecessary data.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-premium-free-feature">
                <span class="dashicons dashicons-heart"></span>
                <h4><?php esc_html_e('Heartbeat Control', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Optimize WordPress heartbeat for better performance.', 'redco-optimizer'); ?></p>
            </div>
            <div class="redco-premium-free-feature">
                <span class="dashicons dashicons-shield"></span>
                <h4><?php esc_html_e('Site Health Inspector', 'redco-optimizer'); ?></h4>
                <p><?php esc_html_e('Monitor your site health and get optimization recommendations.', 'redco-optimizer'); ?></p>
            </div>
        </div>

        <div class="redco-premium-free-cta">
            <a href="#" onclick="switchTab('redco-modules-tab'); return false;" class="redco-button redco-button-secondary">
                <?php esc_html_e('Explore Free Features', 'redco-optimizer'); ?>
            </a>
        </div>
    </div>
</div>
