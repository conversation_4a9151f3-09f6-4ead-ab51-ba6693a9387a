/**
 * Redco Optimizer - Notification System
 * A clean, modern notification system for Redco Optimizer
 */

/* Notification */
.redco-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    padding: 15px;
    width: 350px;
    max-width: calc(100% - 40px);
    z-index: 999999;
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.redco-notification-show {
    transform: translateY(0);
    opacity: 1;
}

.redco-notification-icon {
    flex-shrink: 0;
    margin-right: 15px;
}

.redco-notification-icon svg {
    width: 24px;
    height: 24px;
}

.redco-notification-content {
    flex: 1;
    font-size: 14px;
    line-height: 1.5;
    color: #334155;
}

.redco-notification-close {
    flex-shrink: 0;
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 5px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.redco-notification-close:hover {
    background-color: #f1f5f9;
    color: #0f172a;
}

.redco-notification-close svg {
    width: 16px;
    height: 16px;
}

/* Success Notification */
.redco-notification-success {
    border-left: 4px solid #00A66B;
}

.redco-notification-success .redco-notification-icon {
    color: #00A66B;
}

/* Error Notification */
.redco-notification-error {
    border-left: 4px solid #ef4444;
}

.redco-notification-error .redco-notification-icon {
    color: #ef4444;
}

/* Warning Notification */
.redco-notification-warning {
    border-left: 4px solid #f59e0b;
}

.redco-notification-warning .redco-notification-icon {
    color: #f59e0b;
}

/* Info Notification */
.redco-notification-info {
    border-left: 4px solid #3b82f6;
}

.redco-notification-info .redco-notification-icon {
    color: #3b82f6;
}

/* WordPress Admin Bar Compatibility */
@media screen and (max-width: 782px) {
    .redco-notification {
        top: 46px;
    }
}

@media screen and (max-width: 600px) {
    .redco-notification {
        top: 0;
    }
}

/* Animation */
@keyframes redco-notification-in {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes redco-notification-out {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-20px);
        opacity: 0;
    }
}
