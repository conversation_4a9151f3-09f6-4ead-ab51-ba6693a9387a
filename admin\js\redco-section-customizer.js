/**
 * JavaScript for customizing section behavior in specific tabs
 *
 * This script overrides the default section expander behavior for:
 * 1. Heartbeat tab - Makes all sections expanded
 * 2. Dashboard tab - Removes section expand/collapse functionality
 * 3. File Optimization tab - Ensures subsections under Delay JavaScript remain collapsed
 */
jQuery(document).ready(function($) {
    // Run the customization after a short delay to ensure the main script has run
    setTimeout(function() {
        customizeSectionBehavior();
    }, 300);

    // Also run when tab changes
    $('.redco-nav-item').on('click', function() {
        // Wait for tab content to be visible
        setTimeout(function() {
            customizeSectionBehavior();
        }, 400);
    });

    // Tab restoration is now handled in the main JS file

    // Function to customize section behavior for specific tabs
    function customizeSectionBehavior() {
        // 1. Make all sections expanded in the Heartbeat tab
        if ($('#redco-heartbeat-tab').is(':visible')) {
            expandAllSectionsInHeartbeatTab();
        }

        // 2. Remove section expand/collapse functionality from the Dashboard tab
        if ($('#redco-dashboard-tab').is(':visible')) {
            removeExpandCollapseFromDashboardTab();
        }

        // 3. Ensure subsections under Delay JavaScript remain collapsed
        if ($('#redco-file-optimization-tab').is(':visible')) {
            ensureDelayJsSubsectionsCollapsed();
        }
    }

    // Function to expand all sections in the Heartbeat tab
    function expandAllSectionsInHeartbeatTab() {
        const $heartbeatTab = $('#redco-heartbeat-tab');
        const $sections = $heartbeatTab.find('.redco-expandable-section');

        // Expand all sections
        $sections.addClass('active');
        $sections.find('.redco-expandable-content').css({
            'display': 'block',
            'visibility': 'visible'
        });

        // Remove click handlers from headers to prevent collapsing
        $heartbeatTab.find('.redco-expandable-header').off('click');
    }

    // Function to remove expand/collapse functionality from the Dashboard tab
    function removeExpandCollapseFromDashboardTab() {
        const $dashboardTab = $('#redco-dashboard-tab');

        // Remove expandable classes from all cards
        $dashboardTab.find('.redco-card').removeClass('redco-expandable-section');
        $dashboardTab.find('.redco-card-header').removeClass('redco-expandable-header');
        $dashboardTab.find('.redco-card-content').removeClass('redco-expandable-content');

        // Remove toggle icons
        $dashboardTab.find('.redco-expandable-toggle').remove();

        // Ensure all content is visible
        $dashboardTab.find('.redco-card-content').css({
            'display': 'block',
            'visibility': 'visible'
        });

        // Remove click handlers from headers
        $dashboardTab.find('.redco-card-header').off('click');
    }

    // Function to ensure all subsections under Delay JavaScript remain collapsed
    function ensureDelayJsSubsectionsCollapsed() {
        // Only run if Delay JavaScript is enabled
        if ($('#delay_js').is(':checked')) {
            // Get all expandable sections within the expandable-sections container
            const $expandableSections = $('.redco-expandable-sections .redco-expandable-section');

            // Collapse all subsections
            $expandableSections.removeClass('active');
            $expandableSections.find('.redco-expandable-content').css({
                'display': 'none',
                'visibility': 'hidden'
            });

            // Add click handler to ensure they can be expanded individually
            $('.redco-expandable-sections .redco-expandable-header').off('click').on('click', function(e) {
                e.stopPropagation(); // Prevent event bubbling

                const $section = $(this).closest('.redco-expandable-section');
                const $content = $section.find('.redco-expandable-content');

                // Toggle only this section
                $section.toggleClass('active');

                if ($section.hasClass('active')) {
                    $content.css({
                        'display': 'block',
                        'visibility': 'visible'
                    });
                } else {
                    $content.css({
                        'display': 'none',
                        'visibility': 'hidden'
                    });
                }
            });
        }
    }

    // Tab restoration is now handled in the main JS file
});
