/**
 * JavaScript for handling expandable sections in Redco Optimizer
 *
 * This script handles the expandable sections in the Delay JavaScript Execution section
 */
jQuery(document).ready(function($) {
    // Remove highlight from save button when form is submitted
    $('.redco-form').on('submit', function() {
        $('.redco-button-highlight').removeClass('redco-button-highlight').css({
            'animation': 'none'
        });
    });
    // Initialize expandable sections
    function initExpandableSections() {
        // Handle expandable section headers click
        $('.redco-expandable-header').on('click', function() {
            const $section = $(this).closest('.redco-expandable-section');
            const $content = $section.find('.redco-expandable-content');

            // Toggle active class
            $section.toggleClass('active');

            // Toggle content visibility
            if ($section.hasClass('active')) {
                $content.css({
                    'display': 'block',
                    'visibility': 'visible'
                });
            } else {
                $content.css({
                    'display': 'none',
                    'visibility': 'hidden'
                });
            }

            // Save state to localStorage
            saveExpandableSectionStates();
        });

        // Make all sections collapsed by default
        $('.redco-expandable-section').each(function() {
            const $section = $(this);
            const $content = $section.find('.redco-expandable-content');

            $section.removeClass('active');
            $content.css({
                'display': 'none',
                'visibility': 'hidden'
            });
        });

        // Open only the first section in each tab
        $('.redco-tab-content').each(function() {
            const $tab = $(this);
            const $firstSection = $tab.find('.redco-expandable-section:first-child');
            const $firstContent = $firstSection.find('.redco-expandable-content');

            $firstSection.addClass('active');
            $firstContent.css({
                'display': 'block',
                'visibility': 'visible'
            });
        });

        // Link visibility with Delay JavaScript toggle
        linkWithDelayJsToggle();
    }

    // Link expandable sections visibility with Delay JavaScript toggle
    function linkWithDelayJsToggle() {
        const $delayJsToggle = $('#delay_js');
        const $expandableSections = $('.redco-expandable-sections');
        const $exclusionsRow = $('#delay-js-exclusions-row');
        const $safeModeRow = $('#delay-js-safe-mode-row');

        // Set initial state
        const isChecked = $delayJsToggle.is(':checked');
        toggleSectionsVisibility(isChecked);

        // Ensure Custom Exclusions and Safe Mode are properly hidden/shown initially
        if (isChecked) {
            $exclusionsRow.show();
            $safeModeRow.show();
        } else {
            $exclusionsRow.hide();
            $safeModeRow.hide();
        }

        // Listen for changes on the Delay JavaScript toggle
        $delayJsToggle.on('change', function() {
            toggleSectionsVisibility($(this).is(':checked'));
        });
    }

    // Toggle sections visibility based on Delay JavaScript toggle state
    function toggleSectionsVisibility(isVisible) {
        const $expandableSections = $('.redco-expandable-sections');
        const $exclusionsRow = $('#delay-js-exclusions-row');
        const $safeModeRow = $('#delay-js-safe-mode-row');
        const $allExpandableSections = $('.redco-expandable-sections .redco-expandable-section');

        if (isVisible) {
            $expandableSections.removeClass('hidden');
            $exclusionsRow.show();
            $safeModeRow.show();

            // Collapse all subsections when they become visible
            $allExpandableSections.removeClass('active');
            $allExpandableSections.find('.redco-expandable-content').css({
                'display': 'none',
                'visibility': 'hidden'
            });

            // Make sure the subsections can still be expanded individually
            $allExpandableSections.find('.redco-expandable-header').off('click').on('click', function(e) {
                e.stopPropagation(); // Prevent event bubbling

                const $section = $(this).closest('.redco-expandable-section');
                const $content = $section.find('.redco-expandable-content');

                // Toggle only this section
                $section.toggleClass('active');

                if ($section.hasClass('active')) {
                    $content.css({
                        'display': 'block',
                        'visibility': 'visible'
                    });
                } else {
                    $content.css({
                        'display': 'none',
                        'visibility': 'hidden'
                    });
                }
            });

            saveExpandableSectionStates();
        } else {
            $expandableSections.addClass('hidden');
            $exclusionsRow.hide();
            $safeModeRow.hide();
        }

        // Force the exclusions row to be hidden when toggle is off
        if (!isVisible) {
            $exclusionsRow.css('display', 'none');
            $safeModeRow.css('display', 'none');
        }
    }

    // Save expandable section states to WordPress user meta via AJAX
    function saveExpandableSectionStates() {
        const states = {};

        $('.redco-expandable-section').each(function(index) {
            const sectionId = $(this).attr('id') || 'section-' + index;
            states[sectionId] = $(this).hasClass('active');
        });

        // Save to WordPress user meta via AJAX
        $.ajax({
            url: redco_data.ajax_url,
            type: 'POST',
            data: {
                action: 'redco_save_ui_state',
                nonce: redco_data.nonce,
                state_type: 'expandable_sections',
                state_data: JSON.stringify(states)
            }
        });
    }

    // Load expandable section states from WordPress user meta
    function loadExpandableSectionStates() {
        // Make all sections collapsed by default
        $('.redco-expandable-section').each(function() {
            const $section = $(this);
            const $content = $section.find('.redco-expandable-content');

            $section.removeClass('active');
            $content.css({
                'display': 'none',
                'visibility': 'hidden'
            });
        });

        // If server-provided states exist, apply them
        if (typeof redco_data !== 'undefined' && typeof redco_data.expandable_states !== 'undefined') {
            try {
                const states = JSON.parse(redco_data.expandable_states);

                $('.redco-expandable-section').each(function(index) {
                    const sectionId = $(this).attr('id') || 'section-' + index;

                    if (states[sectionId]) {
                        const $section = $(this);
                        const $content = $section.find('.redco-expandable-content');

                        $section.addClass('active');
                        $content.css({
                            'display': 'block',
                            'visibility': 'visible'
                        });
                    }
                });
            } catch (e) {
                console.error('Error parsing expandable section states:', e);

                // Fallback to default behavior - open first section in each tab
                $('.redco-tab-content').each(function() {
                    const $tab = $(this);
                    const $firstSection = $tab.find('.redco-expandable-section:first-child');
                    const $firstContent = $firstSection.find('.redco-expandable-content');

                    $firstSection.addClass('active');
                    $firstContent.css({
                        'display': 'block',
                        'visibility': 'visible'
                    });
                });
            }
        } else {
            // Fallback to default behavior - open first section in each tab
            $('.redco-tab-content').each(function() {
                const $tab = $(this);
                const $firstSection = $tab.find('.redco-expandable-section:first-child');
                const $firstContent = $firstSection.find('.redco-expandable-content');

                $firstSection.addClass('active');
                $firstContent.css({
                    'display': 'block',
                    'visibility': 'visible'
                });
            });
        }
    }

    // Handle checkbox changes to update exclusions textarea
    function handleExclusionCheckboxes() {
        // Initial population of exclusions from checkboxes
        updateExclusionsFromCheckboxes();

        // Listen for checkbox changes
        $('.redco-expandable-content input[type="checkbox"]').on('change', function() {
            updateExclusionsFromCheckboxes();

            // Add a visual indicator that the user needs to save settings
            const $saveButton = $('.redco-form-actions .redco-save-settings');
            if ($saveButton.length) {
                $saveButton.addClass('redco-button-highlight').css({
                    'animation': 'redco-pulse 1.5s infinite'
                });

                // Add CSS for the pulse animation if it doesn't exist
                if (!$('#redco-pulse-animation').length) {
                    $('head').append(`
                        <style id="redco-pulse-animation">
                            @keyframes redco-pulse {
                                0% { box-shadow: 0 0 0 0 rgba(0, 166, 107, 0.7); }
                                70% { box-shadow: 0 0 0 10px rgba(0, 166, 107, 0); }
                                100% { box-shadow: 0 0 0 0 rgba(0, 166, 107, 0); }
                            }
                            .redco-button-highlight {
                                animation: redco-pulse 1.5s infinite;
                            }
                        </style>
                    `);
                }
            }
        });
    }

    // Update exclusions textarea based on checked checkboxes
    function updateExclusionsFromCheckboxes() {
        const $exclusionsTextarea = $('#delay_js_exclusions');
        let customExclusions = $exclusionsTextarea.val().split('\n');

        // Filter out any exclusions that match our checkbox patterns
        customExclusions = customExclusions.filter(line => {
            // Skip empty lines
            if (!line.trim()) return true;

            // Check if this line matches any of our checkbox patterns
            return !isLineMatchingCheckboxPattern(line);
        });

        // Get all checked exclusions
        const checkedExclusions = [];
        $('.redco-expandable-content input[type="checkbox"]:checked').each(function() {
            const $label = $(this).siblings('.redco-checkbox-text');
            let exclusionText = $label.text().trim();

            // Extract the script name from parentheses if it exists
            const scriptMatch = exclusionText.match(/\(([^)]+)\)/);
            if (scriptMatch && scriptMatch[1]) {
                exclusionText = scriptMatch[1];
            } else {
                // For theme and plugin sections, we need to handle differently
                const $section = $(this).closest('.redco-expandable-section');
                const sectionTitle = $section.find('.redco-expandable-header h4').text().trim();

                if (sectionTitle.includes('Themes') || sectionTitle.includes('Plugins')) {
                    // For themes and plugins, use the checkbox ID to determine the script pattern
                    const checkboxId = $(this).attr('id');
                    if (checkboxId.includes('theme_')) {
                        exclusionText = 'themes/' + checkboxId.replace('delay_js_exclude_theme_', '');
                    } else if (checkboxId.includes('plugin_')) {
                        exclusionText = 'plugins/' + checkboxId.replace('delay_js_exclude_plugin_', '');
                    }
                }
            }

            if (exclusionText) {
                checkedExclusions.push(exclusionText);
            }
        });

        // Combine custom exclusions with checked exclusions
        const allExclusions = [...checkedExclusions, ...customExclusions].filter(line => line.trim());

        // Update the textarea
        $exclusionsTextarea.val(allExclusions.join('\n'));
    }

    // Check if a line matches any checkbox pattern
    function isLineMatchingCheckboxPattern(line) {
        if (!line.trim()) return false;

        // Check standard patterns
        const patterns = [];

        // Collect all checkbox patterns
        $('.redco-expandable-content input[type="checkbox"]').each(function() {
            const $label = $(this).siblings('.redco-checkbox-text');
            let pattern = $label.text().trim();

            // Extract the script name from parentheses if it exists
            const scriptMatch = pattern.match(/\(([^)]+)\)/);
            if (scriptMatch && scriptMatch[1]) {
                pattern = scriptMatch[1];
            }

            patterns.push(pattern);
        });

        // Check theme and plugin patterns
        if (line.includes('themes/') || line.includes('plugins/')) {
            const themesSection = $('.redco-expandable-header h4:contains("Themes")').closest('.redco-expandable-section');
            const pluginsSection = $('.redco-expandable-header h4:contains("Plugins")').closest('.redco-expandable-section');

            // Check theme patterns
            if (line.includes('themes/')) {
                const themeName = line.replace('themes/', '').trim();
                const themeCheckbox = themesSection.find(`input[id*="${themeName}"]`);
                if (themeCheckbox.length) return true;
            }

            // Check plugin patterns
            if (line.includes('plugins/')) {
                const pluginName = line.replace('plugins/', '').trim();
                const pluginCheckbox = pluginsSection.find(`input[id*="${pluginName}"]`);
                if (pluginCheckbox.length) return true;
            }
        }

        // Check if the line matches any standard pattern
        return patterns.some(pattern => line.includes(pattern));
    }

    // Dynamically load theme and plugin data
    function loadDynamicData() {
        // Check if we're on the file optimization tab
        if (!$('.redco-expandable-sections').length) return;

        // Make AJAX call to get theme and plugin data
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_themes_plugins',
                nonce: redco_data.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update theme section
                    if (response.data.themes) {
                        updateThemeSection(response.data.themes);
                    }

                    // Update plugin section
                    if (response.data.plugins) {
                        updatePluginSection(response.data.plugins);
                    }

                    // Reinitialize checkbox handlers
                    handleExclusionCheckboxes();
                }
            },
            error: function() {
                console.error('Failed to load theme and plugin data');
            }
        });
    }

    // Update theme section with dynamic data
    function updateThemeSection(themes) {
        const $themeSection = $('.redco-expandable-header h4:contains("Themes")').closest('.redco-expandable-section');
        const $checkboxList = $themeSection.find('.redco-checkbox-list');

        // Clear existing checkboxes
        $checkboxList.empty();

        // Add theme checkboxes
        themes.forEach(theme => {
            const $checkbox = $(`
                <label class="redco-checkbox-item">
                    <input type="checkbox" name="delay_js_exclude_theme_${theme.slug}" id="delay_js_exclude_theme_${theme.slug}" ${theme.active ? 'checked' : ''}>
                    <span class="redco-checkbox-text">${theme.name}${theme.parent ? ' (Child)' : ''}</span>
                </label>
            `);

            $checkboxList.append($checkbox);
        });
    }

    // Update plugin section with dynamic data
    function updatePluginSection(plugins) {
        const $pluginSection = $('.redco-expandable-header h4:contains("Plugins")').closest('.redco-expandable-section');
        const $checkboxList = $pluginSection.find('.redco-checkbox-list');

        // Clear existing checkboxes
        $checkboxList.empty();

        // Add plugin checkboxes
        plugins.forEach(plugin => {
            const $checkbox = $(`
                <label class="redco-checkbox-item">
                    <input type="checkbox" name="delay_js_exclude_plugin_${plugin.slug}" id="delay_js_exclude_plugin_${plugin.slug}" ${plugin.active ? 'checked' : ''}>
                    <span class="redco-checkbox-text">${plugin.name}</span>
                </label>
            `);

            $checkboxList.append($checkbox);
        });
    }

    // Initialize when document is ready
    initExpandableSections();
    handleExclusionCheckboxes();

    // Try to load dynamic data if we're on the right page
    if (typeof ajaxurl !== 'undefined' && typeof redco_data !== 'undefined') {
        loadDynamicData();
    }
});
