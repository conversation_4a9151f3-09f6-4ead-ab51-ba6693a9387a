<?php
/**
 * Fired during plugin activation
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Activator {

    /**
     * Activate the plugin.
     *
     * Initialize default settings and create necessary database tables.
     *
     * @since    1.0.0
     */
    public static function activate() {
        // Create default options
        $default_modules = array(
            'caching' => array(
                'title' => __('Caching', 'redco-optimizer'),
                'description' => __('Speed up your site with page caching and browser caching.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
            'file-optimization' => array(
                'title' => __('File Optimization', 'redco-optimizer'),
                'description' => __('Minify and combine CSS and JavaScript files to reduce HTTP requests.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
            'media' => array(
                'title' => __('Media Optimization', 'redco-optimizer'),
                'description' => __('Optimize images and other media files to reduce page load time.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
            'preload' => array(
                'title' => __('Preload', 'redco-optimizer'),
                'description' => __('Preload resources to improve page load time.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
            'database' => array(
                'title' => __('Database Optimization', 'redco-optimizer'),
                'description' => __('Clean up your database to improve performance.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
            'heartbeat' => array(
                'title' => __('Heartbeat Control', 'redco-optimizer'),
                'description' => __('Control the WordPress Heartbeat API to reduce server load.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
            'lazyload' => array(
                'title' => __('Lazy Loading', 'redco-optimizer'),
                'description' => __('Lazy load images and iframes to improve page load time.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
            'cdn' => array(
                'title' => __('CDN Integration', 'redco-optimizer'),
                'description' => __('Integrate with a Content Delivery Network to improve page load time.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
            'site-health-inspector' => array(
                'title' => __('Site Health Inspector', 'redco-optimizer'),
                'description' => __('Monitor your site health and get recommendations for improvement.', 'redco-optimizer'),
                'enabled' => true,
                'active' => false,
            ),
        );

        // Add default options if they don't exist
        if ( ! get_option( 'redco_optimizer_modules' ) ) {
            update_option( 'redco_optimizer_modules', $default_modules );
        }

        // Add version to options
        update_option( 'redco_optimizer_version', REDCO_OPTIMIZER_VERSION );
    }
}
