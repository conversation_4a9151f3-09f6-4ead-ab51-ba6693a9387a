/**
 * Add premium badges to premium features and ensure premium features are disabled
 *
 * This script adds premium badges to all premium features across all tabs
 * and ensures all premium features are disabled unless the plugin is licensed
 */
jQuery(document).ready(function($) {
    // Create a fallback for redco_optimizer if it's not defined
    if (typeof redco_optimizer === 'undefined') {
        console.warn('redco_optimizer is not defined, using fallback values');
        window.redco_optimizer = {
            premiumText: 'Premium',
            has_premium_access: false,
            ajax_url: ajaxurl || '',
            nonce: ''
        };
    }
    // Premium features mapping - add all premium features here
    const premiumFeatures = {
        // File Optimization tab
        'combine_css': false, // Free feature
        'optimize_css_delivery': false, // Free feature
        'combine_js': false, // Now free
        'defer_js': false, // Now free
        'remove_unused_css': false, // Now free
        'delay_js': false, // Now free
        'minify_js': false, // Now free
        'async_css': true, // Premium feature
        'self_host_google_fonts': true, // Premium feature

        // Media tab
        'webp_conversion': false, // Now free

        // Lazyload tab
        'lazyload_css_bg': true,
        'lazyload_iframes': true,
        'lazyload_videos': true,

        // Preload tab
        'preload_fonts': false, // Now free
        'prefetch_dns': true,
        'preload_links': true,

        // Database tab
        'schedule_cleanup': false, // Now free
        'cleanup_postmeta': true,
        'cleanup_commentmeta': true,
        'cleanup_orphaned_term_relationships': true,
        'cleanup_wp_options': true,

        // Heartbeat tab
        'disable_heartbeat_customizer': false, // Now free

        // CDN tab
        'cdn_integration': false // Now free
    };

    // Function to add premium badges to toggle rows and disable premium features
    function addPremiumBadges() {
        // List of free features that should not have premium badges
        const freeFeatures = [
            // File Optimization tab
            'combine_js',
            'defer_js',
            'remove_unused_css',
            'delay_js',
            'minify_js',
            // Media tab
            'webp_conversion',
            // Preload tab
            'preload_fonts',
            // Heartbeat tab
            'disable_heartbeat_customizer',
            // Database tab
            'schedule_cleanup',
            // CDN tab
            'cdn_integration',
            'cdn_enabled',
            'cdn_url',
            'cdn_zone',
            'cdn_key',
            'cdn_secret',
            'include_images',
            'include_js',
            'include_css',
            'include_media',
            'exclude_files',
            'relative_path',
            'cdn_https'
        ];

        // List of premium features that should not have premium badges
        const premiumFeaturesWithoutBadges = [
            'prefetch_dns',
            'preload_links'
        ];

        // Process all toggle rows
        $('.redco-toggle-row').each(function() {
            const $row = $(this);
            const $toggleInfo = $row.find('.redco-toggle-info');
            const $heading = $toggleInfo.find('h4');

            // Get the input ID which corresponds to the feature name
            const $input = $row.find('input[type="checkbox"]');
            if ($input.length === 0) return;

            const inputId = $input.attr('id');

            // Skip free features
            if (freeFeatures.includes(inputId)) {
                // Remove any existing premium badge
                $heading.find('.redco-premium-badge').remove();

                // Enable the checkbox
                $input.prop('disabled', false);

                // Remove disabled class from the switch
                $input.closest('.redco-switch').removeClass('redco-switch-disabled');

                return;
            }

            // Check if this is a premium feature
            if (premiumFeatures[inputId]) {
                // Skip adding badge for premium features that shouldn't have badges
                if (premiumFeaturesWithoutBadges.includes(inputId)) {
                    // Remove any existing premium badge
                    $heading.find('.redco-premium-badge').remove();
                }
                // Otherwise add badge if it doesn't exist
                else if ($heading.find('.redco-premium-badge').length === 0) {
                    // Add premium badge after the heading text
                    const headingText = $heading.contents().filter(function() {
                        return this.nodeType === 3; // Text nodes only
                    }).text().trim();

                    $heading.html(
                        headingText + ' <span class="redco-premium-badge">' + redco_optimizer.premiumText + '</span>'
                    );
                }

                // Ensure premium feature is disabled if not licensed
                if (!redco_optimizer.has_premium_access) {
                    // Disable the checkbox
                    $input.prop('disabled', true);
                    $input.prop('checked', false);

                    // Add disabled class to the switch
                    $input.closest('.redco-switch').addClass('redco-switch-disabled');
                }
            }
        });

        // Process all form rows that might contain premium features
        $('.redco-form-row').each(function() {
            const $row = $(this);
            const $label = $row.find('.redco-form-label label');

            // Get the input ID which corresponds to the feature name
            const labelFor = $label.attr('for');

            // Check if this is a premium feature
            if (premiumFeatures[labelFor]) {
                // Skip adding badge for premium features that shouldn't have badges
                if (premiumFeaturesWithoutBadges.includes(labelFor)) {
                    // Remove any existing premium badge
                    $label.find('.redco-premium-badge').remove();
                }
                // Otherwise add badge if it doesn't exist
                else if ($label.find('.redco-premium-badge').length === 0) {
                    // Add premium badge after the label text
                    const labelText = $label.contents().filter(function() {
                        return this.nodeType === 3; // Text nodes only
                    }).text().trim();

                    $label.html(
                        labelText + ' <span class="redco-premium-badge">' + redco_optimizer.premiumText + '</span>'
                    );
                }

                // Ensure premium feature is disabled if not licensed
                if (!redco_optimizer.has_premium_access) {
                    // Find all form elements in this row
                    const $formElements = $row.find('input, select, textarea');

                    // Disable all form elements
                    $formElements.each(function() {
                        const $element = $(this);

                        // Disable the element
                        $element.prop('disabled', true);

                        // If it's a checkbox, uncheck it
                        if ($element.attr('type') === 'checkbox') {
                            $element.prop('checked', false);
                        }

                        // Add disabled class
                        if ($element.is('select')) {
                            $element.addClass('redco-select-disabled');
                        } else if ($element.is('input[type="text"], textarea')) {
                            $element.addClass('redco-input-disabled');
                        }
                    });
                }
            }
        });
    }

    // Function to disable all premium features
    function disablePremiumFeatures() {
        // Only run if user doesn't have premium access
        if (!redco_optimizer.has_premium_access) {
            // Disable all premium module toggles
            $('.redco-module-card').each(function() {
                const $moduleCard = $(this);

                // Check if this is a premium module
                if ($moduleCard.hasClass('redco-module-premium')) {
                    // Find the toggle
                    const $toggle = $moduleCard.find('.redco-module-toggle');

                    // Disable and uncheck the toggle
                    $toggle.prop('disabled', true);
                    $toggle.prop('checked', false);

                    // Add disabled class to the switch
                    $toggle.closest('.redco-switch').addClass('redco-switch-disabled');
                }
            });

            // Ensure all premium features are unchecked and disabled
            $.each(premiumFeatures, function(featureId, isPremium) {
                if (isPremium) {
                    // Find the feature input
                    const $input = $('#' + featureId);

                    if ($input.length > 0) {
                        // Disable and uncheck the input
                        $input.prop('disabled', true);

                        // If it's a checkbox, uncheck it
                        if ($input.attr('type') === 'checkbox') {
                            $input.prop('checked', false);
                        }

                        // Add disabled class to the parent
                        if ($input.closest('.redco-switch').length > 0) {
                            $input.closest('.redco-switch').addClass('redco-switch-disabled');
                        }
                    }
                }
            });

            // Ensure free features are enabled
            const freeFeatures = [
                // File Optimization tab
                'combine_js',
                'defer_js',
                'remove_unused_css',
                'delay_js',
                'minify_js',
                // Media tab
                'webp_conversion', // WebP Conversion is now a free feature
                // Preload tab
                'preload_fonts', // Preload Fonts is now a free feature
                // Heartbeat tab
                'disable_heartbeat_customizer', // Heartbeat control is now a free feature
                // Database tab
                'schedule_cleanup', // Schedule Database Cleanup is now a free feature
                // CDN tab
                'cdn_integration',
                'cdn_enabled',
                'cdn_url',
                'cdn_zone',
                'cdn_key',
                'cdn_secret',
                'include_images',
                'include_js',
                'include_css',
                'include_media',
                'exclude_files',
                'relative_path',
                'cdn_https'
                // 'async_css' and 'self_host_google_fonts' are now premium features
            ];

            // Enable all free features
            $.each(freeFeatures, function(index, featureId) {
                const $input = $('#' + featureId);

                if ($input.length > 0) {
                    // Enable the input
                    $input.prop('disabled', false);

                    // Remove disabled class from the parent
                    if ($input.closest('.redco-switch').length > 0) {
                        $input.closest('.redco-switch').removeClass('redco-switch-disabled');
                    }
                }
            });
        }
    }

    // Function to remove badges from section headers
    function removeSectionHeaderBadges() {
        // Find all card headers with premium badges and remove them
        $('.redco-card-header h3 .redco-premium-badge').remove();
    }

    // Run on document ready
    addPremiumBadges();
    disablePremiumFeatures();
    removeSectionHeaderBadges();

    // Also run when tabs are switched
    $(document).on('click', '.redco-nav-item', function() {
        setTimeout(function() {
            addPremiumBadges();
            disablePremiumFeatures();
            removeSectionHeaderBadges();
        }, 100);
    });
});
