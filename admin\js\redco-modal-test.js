/**
 * Redco Optimizer - Modal Test
 * Tests the visibility of modal elements
 */

(function($) {
    'use strict';

    // Run when document is ready
    $(document).ready(function() {
        console.log('Redco Modal Test script loaded');

        // Initialize modal fixes
        if ($('.redco-optimizer-wrap').length > 0) {
            // Apply fixes when modals are opened
            $(document).on('click', '.redco-addon-settings', function() {
                var addonName = $(this).closest('.redco-addon-card').find('.redco-addon-title').text().trim();
                console.log('Add-on settings button clicked: ' + addonName);

                // Wait for the modal to open
                setTimeout(function() { fixAddonModal(addonName); }, 300);
                setTimeout(function() { fixAddonModal(addonName); }, 600);
                setTimeout(function() { fixAddonModal(addonName); }, 1200);
            });
        }
    });

    /**
     * Test modal visibility
     */
    function testModalVisibility() {
        console.log('Testing modal visibility');

        // Log the number of visible elements
        console.log('Modal sections visible:', $('.redco-modal-section:visible').length);
        console.log('Form rows visible:', $('.redco-form-row:visible, .redco-setting-row:visible').length);
        console.log('Toggle rows visible:', $('.redco-toggle-row:visible').length);
        console.log('Form fields visible:', $('.redco-form-field:visible, .redco-setting-field:visible').length);
        console.log('Form labels visible:', $('.redco-form-label:visible, .redco-setting-label:visible').length);
        console.log('Toggle info visible:', $('.redco-toggle-info:visible').length);
        console.log('Toggle controls visible:', $('.redco-toggle-control:visible').length);
        console.log('Form inputs visible:', $('.redco-input:visible, .redco-select:visible, .redco-textarea:visible').length);
        console.log('Modal footers visible:', $('.redco-modal-footer:visible').length);
        console.log('Form actions visible:', $('.redco-form-actions:visible').length);
        console.log('Buttons visible:', $('.redco-button:visible').length);

        // Apply visibility fixes
        $('.redco-modal-section').css({
            'display': 'block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '25px !important',
            'position': 'relative !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;';
        });

        $('.redco-form-row, .redco-setting-row').css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '15px !important',
            'position': 'relative !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 15px !important; position: relative !important;';
        });

        $('.redco-toggle-row').css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'justify-content': 'space-between !important',
            'align-items': 'center !important',
            'margin-bottom': '15px !important',
            'position': 'relative !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; justify-content: space-between !important; align-items: center !important; margin-bottom: 15px !important; position: relative !important;';
        });

        // Log the number of visible elements again
        console.log('After fix:');
        console.log('Modal sections visible:', $('.redco-modal-section:visible').length);
        console.log('Form rows visible:', $('.redco-form-row:visible, .redco-setting-row:visible').length);
        console.log('Toggle rows visible:', $('.redco-toggle-row:visible').length);

        // Show a success message
        alert('Modal visibility test complete. Check the console for results.');
    }

    /**
     * Test Advanced Cache Preloader modal
     */
    function testAdvancedCachePreloader() {
        console.log('Testing Advanced Cache Preloader modal');

        // Find the Advanced Cache Preloader card
        var $card = $('.redco-addon-card:contains("Advanced Cache Preloader")');

        if ($card.length > 0) {
            console.log('Advanced Cache Preloader card found');

            // Find the settings button
            var $settingsButton = $card.find('.redco-addon-settings');

            if ($settingsButton.length > 0) {
                console.log('Settings button found, clicking it');

                // Click the settings button
                $settingsButton.click();

                // Wait for the modal to open
                setTimeout(function() {
                    // Check if the modal is open
                    if ($('.redco-modal:visible').length > 0) {
                        console.log('Modal is open, applying fixes');

                        // Apply fixes
                        fixAdvancedCachePreloaderModal();

                        // Check for missing sections after a delay
                        setTimeout(function() {
                            checkAdvancedCachePreloaderSections();
                        }, 500);
                    } else {
                        console.log('Modal is not open, trying again');

                        // Try again
                        setTimeout(function() {
                            if ($('.redco-modal:visible').length > 0) {
                                console.log('Modal is now open, applying fixes');
                                fixAdvancedCachePreloaderModal();

                                // Check for missing sections after a delay
                                setTimeout(function() {
                                    checkAdvancedCachePreloaderSections();
                                }, 500);
                            } else {
                                console.log('Modal is still not open, giving up');
                            }
                        }, 1000);
                    }
                }, 500);
            } else {
                console.log('Settings button not found');
            }
        } else {
            console.log('Advanced Cache Preloader card not found');
        }
    }

    /**
     * Check for missing sections in the Advanced Cache Preloader modal
     */
    function checkAdvancedCachePreloaderSections() {
        console.log('Checking Advanced Cache Preloader sections');

        // Expected sections
        var expectedSections = [
            'Cache Preloader Settings',
            'Schedule Settings',
            'Advanced Settings',
            'Content Type Settings',
            'Priority Settings'
        ];

        // Check each section
        var missingSections = [];
        var visibleSections = [];

        expectedSections.forEach(function(section) {
            if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                missingSections.push(section);
            } else {
                visibleSections.push(section);
            }
        });

        // Log results
        console.log('Visible sections: ' + visibleSections.join(', '));

        if (missingSections.length > 0) {
            console.log('Missing sections: ' + missingSections.join(', '));

            // Try to load missing sections
            if (typeof loadMissingSections === 'function') {
                loadMissingSections(missingSections);

                // Check again after a delay
                setTimeout(function() {
                    var stillMissing = [];

                    missingSections.forEach(function(section) {
                        if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                            stillMissing.push(section);
                        }
                    });

                    if (stillMissing.length > 0) {
                        console.log('Still missing sections after fix: ' + stillMissing.join(', '));

                        // Try one more time with direct DOM manipulation
                        injectMissingSections(stillMissing);
                    } else {
                        console.log('All sections are now visible!');
                    }
                }, 500);
            } else {
                console.log('loadMissingSections function not found');
            }
        } else {
            console.log('All sections are visible!');
        }
    }

    /**
     * Inject missing sections directly into the DOM
     */
    function injectMissingSections(missingSections) {
        console.log('Injecting missing sections directly: ' + missingSections.join(', '));

        // Get the modal content element
        var $modalContent = $('.redco-modal-content');

        if ($modalContent.length === 0) {
            console.log('Modal content element not found');
            return;
        }

        // Get the footer element
        var $footer = $('.redco-modal-footer');

        // For each missing section, create a new section element
        missingSections.forEach(function(section) {
            console.log('Creating section: ' + section);

            var $section = $('<div class="redco-modal-section"></div>');
            $section.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'margin-bottom': '25px',
                'position': 'relative'
            });

            var $heading = $('<h3>' + section + '</h3>');
            $heading.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            $section.append($heading);

            // Add some sample content based on the section
            if (section === 'Advanced Settings') {
                $section.append(
                    '<div class="redco-toggle-row">' +
                        '<div class="redco-toggle-info">' +
                            '<h4>Mobile Preload</h4>' +
                            '<p>Preload cache for mobile devices.</p>' +
                        '</div>' +
                        '<div class="redco-toggle-control">' +
                            '<label class="redco-switch">' +
                                '<input type="checkbox" name="mobile_preload" value="1">' +
                                '<span class="redco-slider"></span>' +
                            '</label>' +
                        '</div>' +
                    '</div>'
                );
            } else if (section === 'Content Type Settings') {
                $section.append(
                    '<div class="redco-toggle-row">' +
                        '<div class="redco-toggle-info">' +
                            '<h4>Preload Posts</h4>' +
                            '<p>Preload cache for posts.</p>' +
                        '</div>' +
                        '<div class="redco-toggle-control">' +
                            '<label class="redco-switch">' +
                                '<input type="checkbox" name="preload_posts" value="1" checked>' +
                                '<span class="redco-slider"></span>' +
                            '</label>' +
                        '</div>' +
                    '</div>'
                );
            } else if (section === 'Priority Settings') {
                $section.append(
                    '<div class="redco-form-row">' +
                        '<div class="redco-form-label">' +
                            '<label for="preload_priority">Preload Priority</label>' +
                        '</div>' +
                        '<div class="redco-form-field">' +
                            '<select id="preload_priority" name="preload_priority" class="redco-select">' +
                                '<option value="homepage_first" selected>Homepage First</option>' +
                                '<option value="recent_content">Recent Content First</option>' +
                            '</select>' +
                        '</div>' +
                    '</div>'
                );
            }

            // Insert the section before the footer
            if ($footer.length > 0) {
                $footer.before($section);
            } else {
                $modalContent.append($section);
            }
        });

        // Force all sections to be visible
        $('.redco-modal-section').show().css({
            'display': 'block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '25px !important'
        });
    }

    /**
     * Fix Advanced Cache Preloader modal
     */
    function fixAdvancedCachePreloaderModal() {
        console.log('Fixing Advanced Cache Preloader modal');

        // Make the modal taller
        $('.redco-modal').css({
            'max-height': '95vh',
            'height': 'auto'
        });

        // Make the content area scrollable
        $('.redco-modal-content').css({
            'max-height': 'calc(95vh - 140px)',
            'overflow-y': 'scroll'
        });

        // Add a debug message
        if ($('.redco-modal-debug-message').length === 0) {
            $('<div>')
                .addClass('redco-modal-debug-message')
                .html('<div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; text-align: center;">' +
                      '<p style="font-weight: bold; margin: 0;">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                      '<div style="margin-top: 10px; font-size: 24px; animation: bounce 1s infinite;">↓</div>' +
                      '</div>' +
                      '<style>@keyframes bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }</style>')
                .prependTo('.redco-modal-content');
        }

        // Force all sections to be visible
        $('.redco-modal-section').show().css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });

        // Log the number of visible sections
        console.log('Visible sections: ' + $('.redco-modal-section:visible').length);
        console.log('Total sections: ' + $('.redco-modal-section').length);

        // Check if we need to recreate missing sections
        var expectedSections = [
            'Cache Preloader Settings',
            'Schedule Settings',
            'Advanced Settings',
            'Content Type Settings',
            'Priority Settings'
        ];

        var missingSections = [];

        expectedSections.forEach(function(section) {
            if ($('.redco-modal-content:contains("' + section + '")').length === 0) {
                missingSections.push(section);
            }
        });

        if (missingSections.length > 0) {
            console.log('Missing sections detected: ' + missingSections.join(', '));

            // Try to recreate missing sections
            recreateMissingSections();
        }

        // Add a scroll indicator
        if ($('.redco-scroll-indicator').length === 0) {
            $('<div>')
                .addClass('redco-scroll-indicator')
                .html('<p style="color: #00A66B; font-weight: bold; text-align: center; margin-top: 10px;">↓ Scroll down to see more options ↓</p>')
                .appendTo('.redco-modal-content');
        }

        // Add a scroll indicator at the bottom
        if ($('.redco-scroll-indicator-bottom').length === 0) {
            $('<div>')
                .addClass('redco-scroll-indicator-bottom')
                .html('<div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-top: 20px; text-align: center;">' +
                      '<p style="font-weight: bold; margin: 0;">End of settings</p>' +
                      '<div style="margin-top: 5px;">Don\'t forget to save your changes!</div>' +
                      '</div>')
                .appendTo('.redco-modal-content');
        }
    }

    /**
     * Recreate missing sections in the Advanced Cache Preloader modal
     */
    function recreateMissingSections() {
        console.log('Attempting to recreate missing sections');

        // Check if we already have the basic sections
        var hasBasicSections = $('.redco-modal-content:contains("Cache Preloader Settings")').length > 0;

        if (!hasBasicSections) {
            console.log('Basic sections not found, cannot recreate');
            return;
        }

        // Check for Advanced Settings section
        if ($('.redco-modal-content:contains("Advanced Settings")').length === 0) {
            console.log('Creating Advanced Settings section');

            var advancedSection = $('<div class="redco-modal-section">' +
                '<h3>Advanced Settings</h3>' +
                '<div class="redco-toggle-row">' +
                    '<div class="redco-toggle-info">' +
                        '<label>Preload Sitemap</label>' +
                        '<p class="redco-description">Preload URLs from your XML sitemap</p>' +
                    '</div>' +
                    '<div class="redco-toggle-control">' +
                        '<label class="redco-switch">' +
                            '<input type="checkbox" name="preload_sitemap" value="1">' +
                            '<span class="redco-slider"></span>' +
                        '</label>' +
                    '</div>' +
                '</div>' +
                '<div class="redco-toggle-row">' +
                    '<div class="redco-toggle-info">' +
                        '<label>Preload Archives</label>' +
                        '<p class="redco-description">Preload category, tag, and date archives</p>' +
                    '</div>' +
                    '<div class="redco-toggle-control">' +
                        '<label class="redco-switch">' +
                            '<input type="checkbox" name="preload_archives" value="1">' +
                            '<span class="redco-slider"></span>' +
                        '</label>' +
                    '</div>' +
                '</div>' +
            '</div>');

            // Append to the modal content
            $('.redco-modal-content').append(advancedSection);
        }

        // Check for Content Type Settings section
        if ($('.redco-modal-content:contains("Content Type Settings")').length === 0) {
            console.log('Creating Content Type Settings section');

            var contentTypeSection = $('<div class="redco-modal-section">' +
                '<h3>Content Type Settings</h3>' +
                '<div class="redco-toggle-row">' +
                    '<div class="redco-toggle-info">' +
                        '<label>Preload Posts</label>' +
                        '<p class="redco-description">Preload all published posts</p>' +
                    '</div>' +
                    '<div class="redco-toggle-control">' +
                        '<label class="redco-switch">' +
                            '<input type="checkbox" name="preload_posts" value="1" checked>' +
                            '<span class="redco-slider"></span>' +
                        '</label>' +
                    '</div>' +
                '</div>' +
                '<div class="redco-toggle-row">' +
                    '<div class="redco-toggle-info">' +
                        '<label>Preload Pages</label>' +
                        '<p class="redco-description">Preload all published pages</p>' +
                    '</div>' +
                    '<div class="redco-toggle-control">' +
                        '<label class="redco-switch">' +
                            '<input type="checkbox" name="preload_pages" value="1" checked>' +
                            '<span class="redco-slider"></span>' +
                        '</label>' +
                    '</div>' +
                '</div>' +
            '</div>');

            // Append to the modal content
            $('.redco-modal-content').append(contentTypeSection);
        }

        // Check for Priority Settings section
        if ($('.redco-modal-content:contains("Priority Settings")').length === 0) {
            console.log('Creating Priority Settings section');

            var prioritySection = $('<div class="redco-modal-section">' +
                '<h3>Priority Settings</h3>' +
                '<div class="redco-form-row">' +
                    '<div class="redco-form-label">' +
                        '<label>Preload Delay (ms)</label>' +
                    '</div>' +
                    '<div class="redco-form-field">' +
                        '<input type="number" name="preload_delay" value="500" min="0" max="5000" class="redco-input">' +
                        '<p class="redco-description">Delay between preloading each URL (in milliseconds)</p>' +
                    '</div>' +
                '</div>' +
                '<div class="redco-form-row">' +
                    '<div class="redco-form-label">' +
                        '<label>URLs Per Batch</label>' +
                    '</div>' +
                    '<div class="redco-form-field">' +
                        '<input type="number" name="urls_per_batch" value="5" min="1" max="20" class="redco-input">' +
                        '<p class="redco-description">Number of URLs to preload in each batch</p>' +
                    '</div>' +
                '</div>' +
            '</div>');

            // Append to the modal content
            $('.redco-modal-content').append(prioritySection);
        }

        // Force all sections to be visible again
        $('.redco-modal-section').show().css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });

        // Log the number of visible sections after recreation
        console.log('Visible sections after recreation: ' + $('.redco-modal-section:visible').length);
    }

    /**
     * Fix an add-on modal based on the add-on name
     */
    function fixAddonModal(addonName) {
        console.log('Fixing modal for add-on: ' + addonName);

        // Check if a modal is open
        if ($('.redco-modal:visible').length === 0) {
            console.log('No modal is open');
            return;
        }

        // Make the modal taller
        $('.redco-modal').css({
            'max-height': '95vh',
            'height': 'auto',
            'width': '800px',
            'max-width': '95%',
            'display': 'flex',
            'flex-direction': 'column',
            'overflow': 'hidden'
        });

        // Make the content area scrollable
        $('.redco-modal-content').css({
            'max-height': 'calc(95vh - 140px)',
            'overflow-y': 'auto',
            'overflow-x': 'hidden',
            'flex': '1 1 auto',
            'padding': '25px',
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });

        // Force all sections to be visible
        $('.redco-modal-section').show().css({
            'display': 'block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '25px !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important;';
        });

        // Force all form rows to be visible
        $('.redco-form-row, .redco-setting-row').show().css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '15px !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 15px !important;';
        });

        // Force all toggle rows to be visible
        $('.redco-toggle-row').show().css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'justify-content': 'space-between !important',
            'align-items': 'center !important',
            'margin-bottom': '15px !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; justify-content: space-between !important; align-items: center !important; margin-bottom: 15px !important;';
        });

        // Apply specific fixes based on the add-on name
        switch (addonName.toLowerCase()) {
            case 'advanced cache preloader':
                fixAdvancedCachePreloaderModal();
                break;
            case 'font optimizer':
                fixFontOptimizerModal();
                break;
            case 'cdn manager':
                fixCdnManagerModal();
                break;
            case 'schema markup generator':
                fixSchemaMarkupGeneratorModal();
                break;
            case 'advanced lazy load':
                fixAdvancedLazyLoadModal();
                break;
            case 'critical css generator':
                fixCriticalCssGeneratorModal();
                break;
            default:
                // Generic fix for other add-ons
                addScrollIndicators();
                break;
        }

        // Log the number of visible sections
        console.log('Visible sections: ' + $('.redco-modal-section:visible').length);
        console.log('Form rows visible: ' + $('.redco-form-row:visible, .redco-setting-row:visible').length);
        console.log('Toggle rows visible: ' + $('.redco-toggle-row:visible').length);
    }

    /**
     * Add scroll indicators to the modal
     */
    function addScrollIndicators() {
        // Add a debug message at the top
        if ($('.redco-modal-debug-message').length === 0) {
            $('<div>')
                .addClass('redco-modal-debug-message')
                .html('<div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; text-align: center;">' +
                      '<p style="font-weight: bold; margin: 0;">Some sections may be hidden. Try scrolling down to see all options.</p>' +
                      '<div style="margin-top: 10px; font-size: 24px; animation: bounce 1s infinite;">↓</div>' +
                      '</div>' +
                      '<style>@keyframes bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }</style>')
                .prependTo('.redco-modal-content');
        }

        // Add a scroll indicator at the bottom
        if ($('.redco-scroll-indicator-bottom').length === 0) {
            $('<div>')
                .addClass('redco-scroll-indicator-bottom')
                .html('<div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-top: 20px; text-align: center;">' +
                      '<p style="font-weight: bold; margin: 0;">End of settings</p>' +
                      '<div style="margin-top: 5px;">Don\'t forget to save your changes!</div>' +
                      '</div>')
                .appendTo('.redco-modal-content');
        }
    }

    /**
     * Fix Font Optimizer modal
     */
    function fixFontOptimizerModal() {
        console.log('Applying specific fixes for Font Optimizer modal');

        // Add scroll indicators
        addScrollIndicators();

        // Check for missing sections
        var expectedSections = [
            'Font Optimization Settings',
            'Google Fonts Settings',
            'Local Fonts Settings',
            'Font Display Settings'
        ];

        checkAndFixMissingSections(expectedSections);
    }

    /**
     * Fix CDN Manager modal
     */
    function fixCdnManagerModal() {
        console.log('Applying specific fixes for CDN Manager modal');

        // Add scroll indicators
        addScrollIndicators();

        // Check for missing sections
        var expectedSections = [
            'CDN Settings',
            'Content Settings',
            'Exclusion Settings'
        ];

        checkAndFixMissingSections(expectedSections);
    }

    /**
     * Fix Schema Markup Generator modal
     */
    function fixSchemaMarkupGeneratorModal() {
        console.log('Applying specific fixes for Schema Markup Generator modal');

        // Add scroll indicators
        addScrollIndicators();

        // Check for missing sections
        var expectedSections = [
            'Schema Settings',
            'Organization Schema',
            'Website Schema',
            'Article Schema'
        ];

        checkAndFixMissingSections(expectedSections);
    }

    /**
     * Fix Advanced Lazy Load modal
     */
    function fixAdvancedLazyLoadModal() {
        console.log('Applying specific fixes for Advanced Lazy Load modal');

        // Add scroll indicators
        addScrollIndicators();

        // Check for missing sections
        var expectedSections = [
            'Lazy Load Settings',
            'Image Settings',
            'Iframe Settings',
            'Video Settings'
        ];

        checkAndFixMissingSections(expectedSections);
    }

    /**
     * Fix Critical CSS Generator modal
     */
    function fixCriticalCssGeneratorModal() {
        console.log('Applying specific fixes for Critical CSS Generator modal');

        // Add scroll indicators
        addScrollIndicators();

        // Check for missing sections
        var expectedSections = [
            'Critical CSS Settings',
            'Generation Settings',
            'Advanced Settings'
        ];

        checkAndFixMissingSections(expectedSections);
    }

    /**
     * Check for missing sections and fix them
     */
    function checkAndFixMissingSections(expectedSections) {
        var missingSections = [];

        expectedSections.forEach(function(section) {
            if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                missingSections.push(section);
            }
        });

        if (missingSections.length > 0) {
            console.log('Missing sections detected: ' + missingSections.join(', '));

            // Add a debug message to the modal
            if ($('.redco-modal-debug-message').length === 0) {
                $('<div>')
                    .addClass('redco-modal-debug-message')
                    .html('<div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; text-align: center;">' +
                          '<p style="font-weight: bold; margin: 0;">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                          '<div style="margin-top: 10px; font-size: 24px; animation: bounce 1s infinite;">↓</div>' +
                          '</div>' +
                          '<style>@keyframes bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }</style>')
                    .prependTo('.redco-modal-content');
            }
        } else {
            console.log('All expected sections found');
        }
    }

})(jQuery);
