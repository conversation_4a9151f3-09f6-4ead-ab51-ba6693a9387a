<?php
/**
 * Site Health Inspector Tab Template
 *
 * @link       https://redco.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/site-health-inspector/admin/partials
 */

// Get the scan results
$scan_results = get_option('redco_site_scan_results', array());
$last_scan = isset($scan_results['last_scan']) ? $scan_results['last_scan'] : '';
$total_issues = isset($scan_results['total_issues']) ? $scan_results['total_issues'] : 0;
$critical_issues = isset($scan_results['critical_issues']) ? $scan_results['critical_issues'] : 0;
$warning_issues = isset($scan_results['warning_issues']) ? $scan_results['warning_issues'] : 0;
$info_issues = isset($scan_results['info_issues']) ? $scan_results['info_issues'] : 0;

// Category labels and icons
$categories = array(
    'plugins' => array(
        'label' => __('Plugins', 'redco-optimizer'),
        'icon' => 'dashicons-admin-plugins',
        'description' => __('Issues related to plugins installed on your site.', 'redco-optimizer')
    ),
    'themes' => array(
        'label' => __('Themes', 'redco-optimizer'),
        'icon' => 'dashicons-admin-appearance',
        'description' => __('Issues related to themes installed on your site.', 'redco-optimizer')
    ),
    'wordpress' => array(
        'label' => __('WordPress', 'redco-optimizer'),
        'icon' => 'dashicons-wordpress',
        'description' => __('Issues related to your WordPress installation.', 'redco-optimizer')
    ),
    'server' => array(
        'label' => __('Server', 'redco-optimizer'),
        'icon' => 'dashicons-desktop',
        'description' => __('Issues related to your server configuration.', 'redco-optimizer')
    ),
    'database' => array(
        'label' => __('Database', 'redco-optimizer'),
        'icon' => 'dashicons-database',
        'description' => __('Issues related to your WordPress database.', 'redco-optimizer')
    ),
    'security' => array(
        'label' => __('Security', 'redco-optimizer'),
        'icon' => 'dashicons-shield',
        'description' => __('Security issues that could affect your site.', 'redco-optimizer')
    ),
    'performance' => array(
        'label' => __('Performance', 'redco-optimizer'),
        'icon' => 'dashicons-performance',
        'description' => __('Issues that could affect your site\'s performance.', 'redco-optimizer')
    ),
    'javascript' => array(
        'label' => __('JavaScript', 'redco-optimizer'),
        'icon' => 'dashicons-media-code',
        'description' => __('Issues related to JavaScript files and execution.', 'redco-optimizer')
    )
);

// Severity labels and colors
$severities = array(
    'critical' => array(
        'label' => __('Critical', 'redco-optimizer'),
        'color' => '#dc3545',
        'icon' => 'dashicons-warning'
    ),
    'warning' => array(
        'label' => __('Warning', 'redco-optimizer'),
        'color' => '#ffc107',
        'icon' => 'dashicons-flag'
    ),
    'info' => array(
        'label' => __('Info', 'redco-optimizer'),
        'color' => '#17a2b8',
        'icon' => 'dashicons-info'
    )
);
?>

<!-- Site Health Inspector content starts here -->

<div class="redco-site-health-container">
    <div class="redco-site-health-summary">
        <div class="redco-card">
            <div class="redco-card-header">
                <h3><?php esc_html_e('Site Health Summary', 'redco-optimizer'); ?></h3>
                <?php if (!empty($last_scan)) : ?>
                    <span class="redco-last-scan"><?php echo sprintf(__('Last scan: %s', 'redco-optimizer'), date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($last_scan))); ?></span>
                <?php endif; ?>
            </div>
            <div class="redco-card-content">
                <div class="redco-site-health-stats">
                    <div class="redco-stat-item redco-stat-total">
                        <span class="redco-stat-number"><?php echo esc_html($total_issues); ?></span>
                        <span class="redco-stat-label"><?php esc_html_e('Total Issues', 'redco-optimizer'); ?></span>
                    </div>
                    <div class="redco-stat-item redco-stat-critical">
                        <span class="redco-stat-number"><?php echo esc_html($critical_issues); ?></span>
                        <span class="redco-stat-label"><?php esc_html_e('Critical', 'redco-optimizer'); ?></span>
                    </div>
                    <div class="redco-stat-item redco-stat-warning">
                        <span class="redco-stat-number"><?php echo esc_html($warning_issues); ?></span>
                        <span class="redco-stat-label"><?php esc_html_e('Warnings', 'redco-optimizer'); ?></span>
                    </div>
                    <div class="redco-stat-item redco-stat-info">
                        <span class="redco-stat-number"><?php echo esc_html($info_issues); ?></span>
                        <span class="redco-stat-label"><?php esc_html_e('Info', 'redco-optimizer'); ?></span>
                    </div>
                </div>

                <div class="redco-site-health-actions">
                    <button type="button" id="redco-run-site-scan" class="redco-button redco-button-primary">
                        <span class="dashicons dashicons-search"></span>
                        <?php esc_html_e('Run Site Scan', 'redco-optimizer'); ?>
                    </button>
                    <?php if ($total_issues > 0) : ?>
                        <button type="button" id="redco-export-issues" class="redco-button redco-button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            <?php esc_html_e('Export Report', 'redco-optimizer'); ?>
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="redco-site-health-issues">
        <?php if (empty($scan_results) || empty($scan_results['categories'])) : ?>
            <div class="redco-no-issues-message">
                <p><?php esc_html_e('No scan has been run yet. Click "Run Site Scan" to check your site for issues.', 'redco-optimizer'); ?></p>
            </div>
        <?php else : ?>
            <?php foreach ($categories as $category => $category_data) : ?>
                <?php
                $category_issues = isset($scan_results['categories'][$category]) ? $scan_results['categories'][$category] : array();
                if (empty($category_issues)) {
                    continue;
                }
                ?>
                <div class="redco-card redco-category-card" id="redco-category-<?php echo esc_attr($category); ?>">
                    <div class="redco-card-header">
                        <h3>
                            <span class="dashicons <?php echo esc_attr($category_data['icon']); ?>"></span>
                            <?php echo esc_html($category_data['label']); ?>
                            <span class="redco-issue-count"><?php echo count($category_issues); ?></span>
                        </h3>
                        <p><?php echo esc_html($category_data['description']); ?></p>
                    </div>
                    <div class="redco-card-content">
                        <?php foreach ($category_issues as $issue) : ?>
                            <div class="redco-issue-item redco-severity-<?php echo esc_attr($issue['severity']); ?>">
                                <div class="redco-issue-header">
                                    <h4>
                                        <span class="dashicons <?php echo esc_attr($severities[$issue['severity']]['icon']); ?>"></span>
                                        <?php echo esc_html($issue['title']); ?>
                                    </h4>
                                    <span class="redco-issue-severity" style="background-color: <?php echo esc_attr($severities[$issue['severity']]['color']); ?>">
                                        <?php echo esc_html($severities[$issue['severity']]['label']); ?>
                                    </span>
                                </div>
                                <div class="redco-issue-description">
                                    <p><?php echo esc_html($issue['description']); ?></p>

                                    <?php if (!empty($issue['data'])) : ?>
                                        <div class="redco-issue-details">
                                            <button type="button" class="redco-toggle-details"><?php esc_html_e('Show Details', 'redco-optimizer'); ?></button>
                                            <div class="redco-issue-details-content" style="display: none;">
                                                <?php foreach ($issue['data'] as $key => $value) : ?>
                                                    <?php if ($key === 'plugins' && is_array($value) && !empty($value)) : ?>
                                                        <div class="redco-detail-item redco-plugins-table-container">
                                                            <h4><?php esc_html_e('Outdated Plugins', 'redco-optimizer'); ?></h4>
                                                            <table class="redco-plugins-table widefat">
                                                                <thead>
                                                                    <tr>
                                                                        <th><?php esc_html_e('Plugin Name', 'redco-optimizer'); ?></th>
                                                                        <th><?php esc_html_e('Current Version', 'redco-optimizer'); ?></th>
                                                                        <th><?php esc_html_e('New Version', 'redco-optimizer'); ?></th>
                                                                        <th><?php esc_html_e('Actions', 'redco-optimizer'); ?></th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php foreach ($value as $plugin) : ?>
                                                                        <tr>
                                                                            <td><?php echo esc_html($plugin['name']); ?></td>
                                                                            <td><?php echo esc_html($plugin['current_version']); ?></td>
                                                                            <td><?php echo esc_html($plugin['new_version']); ?></td>
                                                                            <td class="redco-plugin-actions">
                                                                                <button type="button" class="button button-small redco-update-plugin"
                                                                                    data-plugin="<?php echo esc_attr($plugin['plugin_path']); ?>"
                                                                                    data-action="update"
                                                                                    data-nonce="<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>">
                                                                                    <span class="dashicons dashicons-update"></span> <?php esc_html_e('Update', 'redco-optimizer'); ?>
                                                                                </button>
                                                                                <button type="button" class="button button-small redco-deactivate-plugin"
                                                                                    data-plugin="<?php echo esc_attr($plugin['plugin_path']); ?>"
                                                                                    data-action="deactivate"
                                                                                    data-nonce="<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>">
                                                                                    <span class="dashicons dashicons-dismiss"></span> <?php esc_html_e('Deactivate', 'redco-optimizer'); ?>
                                                                                </button>
                                                                                <button type="button" class="button button-small redco-delete-plugin"
                                                                                    data-plugin="<?php echo esc_attr($plugin['plugin_path']); ?>"
                                                                                    data-action="delete"
                                                                                    data-nonce="<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>">
                                                                                    <span class="dashicons dashicons-trash"></span> <?php esc_html_e('Delete', 'redco-optimizer'); ?>
                                                                                </button>
                                                                            </td>
                                                                        </tr>
                                                                    <?php endforeach; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    <?php elseif ($key === 'sample_page' || $key === 'hello_world_post') : ?>
                                                        <?php if (is_array($value)) : ?>
                                                            <div class="redco-detail-item">
                                                                <strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong>
                                                                <?php echo esc_html($value['title']); ?>
                                                                <button type="button" class="button button-small redco-delete-content"
                                                                    data-id="<?php echo esc_attr($value['id']); ?>"
                                                                    data-type="<?php echo esc_attr($key); ?>"
                                                                    data-nonce="<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>">
                                                                    <span class="dashicons dashicons-trash"></span> <?php esc_html_e('Delete', 'redco-optimizer'); ?>
                                                                </button>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php elseif ($key === 'files' && is_array($value) && !empty($value)) : ?>
                                                        <div class="redco-detail-item redco-js-files-container">
                                                            <h4><?php esc_html_e('JavaScript Files', 'redco-optimizer'); ?></h4>
                                                            <table class="redco-js-files-table widefat">
                                                                <thead>
                                                                    <tr>
                                                                        <th><?php esc_html_e('File', 'redco-optimizer'); ?></th>
                                                                        <th><?php esc_html_e('Size', 'redco-optimizer'); ?></th>
                                                                        <th><?php esc_html_e('Path', 'redco-optimizer'); ?></th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php foreach ($value as $js_file) : ?>
                                                                        <tr>
                                                                            <td><?php echo esc_html($js_file['file']); ?></td>
                                                                            <td><?php echo isset($js_file['size']) ? esc_html($js_file['size']) : ''; ?></td>
                                                                            <td class="redco-file-path">
                                                                                <?php echo esc_html($js_file['path']); ?>
                                                                                <?php if (isset($js_file['path']) && filter_var($js_file['path'], FILTER_VALIDATE_URL)) : ?>
                                                                                    <a href="<?php echo esc_url($js_file['path']); ?>" target="_blank" class="button button-small">
                                                                                        <span class="dashicons dashicons-external"></span>
                                                                                    </a>
                                                                                <?php endif; ?>
                                                                            </td>
                                                                        </tr>
                                                                    <?php endforeach; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    <?php elseif ($key === 'errors' && is_array($value) && !empty($value)) : ?>
                                                        <div class="redco-detail-item redco-js-errors-container">
                                                            <h4><?php esc_html_e('JavaScript Errors', 'redco-optimizer'); ?></h4>
                                                            <table class="redco-js-errors-table widefat">
                                                                <thead>
                                                                    <tr>
                                                                        <th><?php esc_html_e('Error Message', 'redco-optimizer'); ?></th>
                                                                        <th><?php esc_html_e('File', 'redco-optimizer'); ?></th>
                                                                        <th><?php esc_html_e('Line', 'redco-optimizer'); ?></th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php foreach ($value as $error) : ?>
                                                                        <tr>
                                                                            <td><?php echo esc_html($error['message']); ?></td>
                                                                            <td>
                                                                                <?php
                                                                                    $url_parts = parse_url($error['url']);
                                                                                    echo esc_html(basename($url_parts['path']));
                                                                                ?>
                                                                            </td>
                                                                            <td><?php echo esc_html($error['line']); ?></td>
                                                                        </tr>
                                                                    <?php endforeach; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    <?php elseif (is_array($value) && $key !== 'count') : ?>
                                                        <div class="redco-detail-item">
                                                            <strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong>
                                                            <ul>
                                                                <?php foreach ($value as $subkey => $subvalue) : ?>
                                                                    <?php if (is_array($subvalue)) : ?>
                                                                        <li>
                                                                            <strong><?php echo esc_html(is_numeric($subkey) ? '' : ucfirst(str_replace('_', ' ', $subkey))); ?></strong>
                                                                            <ul>
                                                                                <?php foreach ($subvalue as $subsubkey => $subsubvalue) : ?>
                                                                                    <li><strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $subsubkey))); ?>:</strong> <?php echo esc_html($subsubvalue); ?></li>
                                                                                <?php endforeach; ?>
                                                                            </ul>
                                                                        </li>
                                                                    <?php else : ?>
                                                                        <li><?php echo is_numeric($subkey) ? esc_html($subvalue) : '<strong>' . esc_html(ucfirst(str_replace('_', ' ', $subkey))) . ':</strong> ' . esc_html($subvalue); ?></li>
                                                                    <?php endif; ?>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                    <?php elseif ($key === 'recommendation' && isset($issue['data']['module'])) : ?>
                                                        <div class="redco-detail-item redco-module-recommendation">
                                                            <strong><?php esc_html_e('Recommendation:', 'redco-optimizer'); ?></strong>
                                                            <?php echo esc_html($value); ?>
                                                            <button type="button" class="button button-primary redco-enable-module"
                                                                    data-module="<?php echo esc_attr($issue['data']['module']); ?>"
                                                                    data-nonce="<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>">
                                                                <span class="dashicons dashicons-yes"></span> <?php esc_html_e('Enable Module', 'redco-optimizer'); ?>
                                                            </button>
                                                        </div>
                                                    <?php elseif ($key !== 'count' && $key !== 'module') : ?>
                                                        <div class="redco-detail-item">
                                                            <strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong>
                                                            <?php echo esc_html($value); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>
