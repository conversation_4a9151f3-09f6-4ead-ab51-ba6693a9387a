/**
 * Redco Optimizer - Toggle Switch Fix
 * Direct approach to fix toggle switches UI
 */

(function($) {
    'use strict';

    // Run when DOM is ready
    $(document).ready(function() {
        // Completely replace the click handler for toggle switches
        $(document).off('click', '.redco-switch, .redco-slider').on('click', '.redco-switch, .redco-slider', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Get the checkbox
            const $switch = $(this);
            const $checkbox = $switch.hasClass('redco-switch') ? $switch.find('input[type="checkbox"]') : $switch.prev('input[type="checkbox"]');

            if ($checkbox.length > 0) {
                // Toggle the checkbox state programmatically
                const currentState = $checkbox.prop('checked');
                const newState = !currentState;

                // Update the checkbox state
                $checkbox.prop('checked', newState);

                // Update the UI directly
                const $slider = $checkbox.next('.redco-slider');
                if ($slider.length > 0) {
                    if (newState) {
                        $slider.css('background-color', '#00A66B');
                    } else {
                        $slider.css('background-color', '#cbd5e1');
                    }
                }

                // Add a visual feedback animation
                $slider.css('opacity', '0.7');
                setTimeout(function() {
                    $slider.css('opacity', '1');
                }, 200);

                // Trigger change event to ensure any listeners are notified
                $checkbox.trigger('change');

                console.log('Toggle clicked - New state:', newState ? 'ON' : 'OFF');
            }
        });

        // Make sure all checkboxes are enabled
        function enableAllCheckboxes() {
            $('input[type="checkbox"]:not(.redco-premium-feature)').prop('disabled', false);

            document.querySelectorAll('input[type="checkbox"]:not(.redco-premium-feature)').forEach(function(checkbox) {
                checkbox.disabled = false;
                checkbox.removeAttribute('disabled');
            });
        }

        // Run immediately and periodically
        enableAllCheckboxes();
        setInterval(enableAllCheckboxes, 1000);

        // Update UI colors based on checkbox state
        function updateToggleColors() {
            $('input[type="checkbox"]').each(function() {
                const $checkbox = $(this);
                const $slider = $checkbox.next('.redco-slider');

                if ($slider.length > 0) {
                    if ($checkbox.is(':checked')) {
                        $slider.css('background-color', '#00A66B');
                    } else {
                        $slider.css('background-color', '#cbd5e1');
                    }
                }
            });
        }

        // Run immediately and periodically
        updateToggleColors();
        setInterval(updateToggleColors, 2000);

        // Handle form submissions
        $(document).on('submit', '.redco-form', function() {
            // After form submission, update toggle colors
            setTimeout(updateToggleColors, 1000);
            setTimeout(updateToggleColors, 2000);
            setTimeout(updateToggleColors, 3000);
        });
    });

})(jQuery);
