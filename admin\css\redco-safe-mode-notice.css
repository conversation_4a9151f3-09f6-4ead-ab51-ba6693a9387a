/**
 * Redco Optimizer - Safe Mode Notice Styles
 */

/* Performance Impact Notice */
.redco-performance-impact {
    background-color: #1e1e3f;
    color: #ffffff;
    border-radius: 4px;
    margin: 15px 0;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.redco-performance-impact-icon {
    margin-right: 15px;
    flex-shrink: 0;
}

.redco-performance-impact-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #ff6b6b;
}

.redco-performance-impact-content {
    flex-grow: 1;
}

.redco-performance-impact-content p {
    margin: 0 0 15px 0;
    line-height: 1.5;
}

.redco-performance-impact #activate-safe-mode {
    background-color: #ff6b6b;
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.redco-performance-impact #activate-safe-mode:hover {
    background-color: #ff5252;
}

/* Notification Styles (if not already defined) */
.redco-notifications-container {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 99999;
    width: 350px;
    max-width: 90%;
}

.redco-notification {
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    transform: translateX(100%);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
    overflow: hidden;
    border-left: 4px solid #00A66B;
}

.redco-notification.redco-notification-error {
    border-left-color: #ef4444;
}

.redco-notification.redco-notification-info {
    border-left-color: #3b82f6;
}

.redco-notification.redco-notification-show {
    transform: translateX(0);
    opacity: 1;
}

.redco-notification-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eaeaea;
}

.redco-notification-header h3 {
    margin: 0 0 0 10px;
    font-size: 14px;
    flex-grow: 1;
}

.redco-notification-header .dashicons {
    color: #00A66B;
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.redco-notification.redco-notification-error .redco-notification-header .dashicons {
    color: #ef4444;
}

.redco-notification.redco-notification-info .redco-notification-header .dashicons {
    color: #3b82f6;
}

.redco-notification-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    color: #6b7280;
}

.redco-notification-close:hover {
    color: #374151;
}

.redco-notification-content {
    padding: 15px;
}

.redco-notification-content p {
    margin: 0;
    line-height: 1.5;
    color: #4b5563;
}
