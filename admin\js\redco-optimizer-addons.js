/**
 * Redco Optimizer Add-Ons JavaScript
 *
 * Handles all add-on related functionality including:
 * - Installing add-ons
 * - Activating add-ons
 * - Deactivating add-ons
 * - Refreshing add-ons list
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/js
 */

(function($) {
    'use strict';

    // Create a fallback for redco_optimizer if it's not defined
    if (typeof redco_optimizer === 'undefined') {
        console.warn('redco_optimizer is not defined, using fallback values');
        window.redco_optimizer = {
            premiumText: 'Premium',
            has_premium_access: false,
            ajax_url: ajaxurl || '',
            nonce: ''
        };
    }

    // Use the main notification function for add-ons
    function addonNotification(type, message, title) {
        // Remove any existing notifications with the same title to prevent duplicates
        $('.redco-notification').each(function() {
            const $this = $(this);
            if ($this.find('.redco-notification-title').text() === title) {
                $this.remove();
            }
        });

        // Check if the global showNotification function exists
        if (typeof showNotification === 'function') {
            // Use the global showNotification function
            return showNotification(type, message, {
                title: title || (typeof getNotificationTitle === 'function' ? getNotificationTitle(type) : type.charAt(0).toUpperCase() + type.slice(1)),
                duration: type === 'error' ? 10000 : 5000,
                position: 'toast',
                autoClose: true,
                showProgress: true,
                showCloseButton: true
            });
        } else {
            // Fallback notification if global function is not available
            console.log('Notification:', type, message, title);

            // Create a simple notification
            const $notification = $('<div class="redco-simple-notification redco-notification redco-notification-' + type + '">' +
                '<div class="redco-notification-title">' + (title || type.charAt(0).toUpperCase() + type.slice(1)) + '</div>' +
                '<div class="redco-notification-message">' + message + '</div>' +
                '<div class="redco-notification-close">×</div>' +
                '</div>');

            // Add to body
            $('body').append($notification);

            // Show with animation
            $notification.fadeIn(300);

            // Handle close button
            $notification.find('.redco-notification-close').on('click', function() {
                $notification.fadeOut(300, function() {
                    $notification.remove();
                });
            });

            // Auto close after 5 seconds (10 for errors)
            setTimeout(function() {
                $notification.fadeOut(300, function() {
                    $notification.remove();
                });
            }, type === 'error' ? 10000 : 5000);

            return $notification;
        }
    }

    // Function to close all modals
    function closeAllModals() {
        // Check if we're on the Redco Optimizer page
        const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                    $('body.toplevel_page_redco-optimizer').length > 0 ||
                                    $('body.redco-optimizer_page_').length > 0 ||
                                    window.location.href.indexOf('page=redco-optimizer') !== -1;

        // Check if we're on the help page
        const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

        if (!isRedcoOptimizerPage || isHelpPage) {
            console.log('Not on Redco Optimizer page or on help page, using standard close behavior for modals');
            // Use a more standard approach to close modals if not on the plugin page or if on help page
            $('.redco-modal-overlay').hide();
            return;
        }

        $('.redco-modal-overlay').css({
            'display': 'none',
            'visibility': 'hidden',
            'opacity': '0'
        }).removeClass('active');
        console.log('All modals closed');
    }

    // Add keyboard event listener to close modals with Escape key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            // Check if we're on the Redco Optimizer page
            const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                        $('body.toplevel_page_redco-optimizer').length > 0 ||
                                        $('body.redco-optimizer_page_').length > 0 ||
                                        window.location.href.indexOf('page=redco-optimizer') !== -1;

            // Check if we're on the help page
            const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

            if (!isRedcoOptimizerPage || isHelpPage) {
                console.log('Not on Redco Optimizer page or on help page, using standard close behavior for Escape key');
                // Use a more standard approach to close modals if not on the plugin page or if on help page
                $('.redco-modal-overlay').hide();
                return;
            }

            closeAllModals();
        }
    });

    $(document).ready(function() {
        // Only initialize the add-on functionality if we're on the Redco Optimizer page
        // Check if we're on the Redco Optimizer page by looking for specific elements
        const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                    $('body.toplevel_page_redco-optimizer').length > 0 ||
                                    $('body.redco-optimizer_page_').length > 0 ||
                                    window.location.href.indexOf('page=redco-optimizer') !== -1;

        console.log('Is Redco Optimizer page:', isRedcoOptimizerPage);

        if (!isRedcoOptimizerPage) {
            console.log('Not on Redco Optimizer page, skipping add-on modal initialization');
            return; // Exit early if not on the Redco Optimizer page
        }

        // Hide all modals on page load - use both CSS and inline styles to ensure they're hidden
        $('.redco-modal-overlay').css({
            'display': 'none',
            'visibility': 'hidden',
            'opacity': '0'
        }).removeClass('active');

        // Check if the add-on settings modal exists
        console.log('Add-on settings modal exists on page load:', $('#redco-addon-settings-modal').length > 0);

        // If the modal doesn't exist, create it
        if ($('#redco-addon-settings-modal').length === 0) {
            console.log('Creating add-on settings modal on page load');

            // Create the modal HTML
            const modalHTML = `
            <div id="redco-addon-settings-modal" class="redco-modal-overlay">
                <div class="redco-modal redco-addon-settings-modal">
                    <div class="redco-modal-header">
                        <h2 id="redco-addon-settings-title">Add-on Settings</h2>
                        <button type="button" class="redco-modal-close" aria-label="Close">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"></path></svg>
                        </button>
                    </div>
                    <div class="redco-modal-content" id="redco-addon-settings-content">
                        <div class="redco-modal-loading">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="40" height="40" class="redco-spin"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg>
                            <p>Loading settings...</p>
                        </div>
                    </div>
                    <div class="redco-modal-footer">
                        <button type="button" class="redco-button redco-button-secondary redco-modal-cancel">Cancel</button>
                        <button type="button" class="redco-button redco-button-primary redco-modal-save">Save Settings</button>
                    </div>
                </div>
            </div>
            `;

            // Append to body
            $('body').append(modalHTML);
            console.log('Add-on settings modal created and added to DOM');
        }
        // Handle add-on activation
        $(document).on('click', '.redco-addon-activate', function(e) {
            e.preventDefault();

            const $button = $(this);
            const addonSlug = $button.data('addon');
            const $addonCard = $button.closest('.redco-addon-card');

            // Disable the button and show loading state
            $button.prop('disabled', true).html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="redco-spin" style="vertical-align: middle; margin-right: 5px;"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg> ' + 'Activating...');

            // Send AJAX request to activate the add-on
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_activate_addon',
                    addon: addonSlug,
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        addonNotification('success', response.data.message, 'Add-On Activated');

                        // Update the add-on card to show active state
                        $addonCard.addClass('redco-addon-active');
                        $addonCard.find('.redco-addon-status').html('<span class="redco-addon-status-active">Active</span>');

                        // Replace the activate button with deactivate button
                        let buttonHtml = '';

                        // Add settings button if the add-on has settings
                        if (response.data.has_settings) {
                            buttonHtml += '<button type="button" class="redco-button redco-button-secondary redco-addon-settings" data-addon="' + addonSlug + '">';
                            buttonHtml += '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="vertical-align: middle; margin-right: 5px;"><path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" fill="currentColor"></path></svg> Settings';
                            buttonHtml += '</button>';
                        }

                        // Add deactivate button
                        buttonHtml += '<button type="button" class="redco-button redco-button-secondary redco-addon-deactivate" data-addon="' + addonSlug + '">';
                        buttonHtml += 'Deactivate';
                        buttonHtml += '</button>';

                        $addonCard.find('.redco-addon-actions').html(buttonHtml);

                        // Reload the page if specified in the response
                        if (response.data.reload) {
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        }
                    } else {
                        // Show error notification
                        addonNotification('error', response.data.message, 'Activation Failed');

                        // Restore the button
                        $button.prop('disabled', false).html('Activate');

                        // If premium feature, show premium tab
                        if (response.data.premium) {
                            // Switch to premium tab
                            $('.redco-premium-tab-link').trigger('click');
                        }
                    }
                },
                error: function() {
                    // Show error notification
                    addonNotification('error', 'Connection error. Please try again.', 'Activation Failed');

                    // Restore the button
                    $button.prop('disabled', false).html('Activate');
                }
            });
        });

        // Handle add-on deactivation
        $(document).on('click', '.redco-addon-deactivate', function(e) {
            e.preventDefault();

            const $button = $(this);
            const addonSlug = $button.data('addon');
            const $addonCard = $button.closest('.redco-addon-card');

            // Disable the button and show loading state
            $button.prop('disabled', true).html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="redco-spin" style="vertical-align: middle; margin-right: 5px;"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg> ' + 'Deactivating...');

            // Send AJAX request to deactivate the add-on
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_deactivate_addon',
                    addon: addonSlug,
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        addonNotification('success', response.data.message, 'Add-On Deactivated');

                        // Update the add-on card to show inactive state
                        $addonCard.removeClass('redco-addon-active');
                        $addonCard.find('.redco-addon-status').html('<span class="redco-addon-status-inactive">Inactive</span>');

                        // Replace the deactivate button with activate and uninstall buttons
                        let buttonHtml = '<button type="button" class="redco-button redco-button-primary redco-addon-activate" data-addon="' + addonSlug + '">Activate</button>';
                        buttonHtml += '<button type="button" class="redco-button redco-button-secondary redco-addon-uninstall" data-addon="' + addonSlug + '">Uninstall</button>';
                        $addonCard.find('.redco-addon-actions').html(buttonHtml);

                        // Reload the page if specified in the response
                        if (response.data.reload) {
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        }
                    } else {
                        // Show error notification
                        addonNotification('error', response.data.message, 'Deactivation Failed');

                        // Restore the button
                        $button.prop('disabled', false).html('Deactivate');
                    }
                },
                error: function() {
                    // Show error notification
                    addonNotification('error', 'Connection error. Please try again.', 'Deactivation Failed');

                    // Restore the button
                    $button.prop('disabled', false).html('Deactivate');
                }
            });
        });

        // Handle add-on installation
        $(document).on('click', '.redco-addon-install', function(e) {
            e.preventDefault();

            const $button = $(this);
            const addonSlug = $button.data('addon');
            const $addonCard = $button.closest('.redco-addon-card');

            // Disable the button and show loading state
            $button.prop('disabled', true).html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="redco-spin" style="vertical-align: middle; margin-right: 5px;"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg> ' + 'Installing...');

            // Send AJAX request to install the add-on
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_install_addon',
                    addon: addonSlug,
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        addonNotification('success', response.data.message, 'Add-On Installed');

                        // Reload the page to show the newly installed add-on
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        // Show error notification
                        addonNotification('error', response.data.message, 'Installation Failed');

                        // Restore the button
                        $button.prop('disabled', false).html('Install');

                        // If premium feature, show premium tab
                        if (response.data.premium) {
                            // Switch to premium tab
                            $('.redco-premium-tab-link').trigger('click');
                        }
                    }
                },
                error: function() {
                    // Show error notification
                    addonNotification('error', 'Connection error. Please try again.', 'Installation Failed');

                    // Restore the button
                    $button.prop('disabled', false).html('Install');
                }
            });
        });

        // Handle add-on uninstallation
        $(document).on('click', '.redco-addon-uninstall', function(e) {
            e.preventDefault();

            const $button = $(this);
            const addonSlug = $button.data('addon');
            const $addonCard = $button.closest('.redco-addon-card');

            // Confirm before uninstalling
            if (!confirm('Are you sure you want to uninstall this add-on? This action cannot be undone.')) {
                return;
            }

            // Disable the button and show loading state
            $button.prop('disabled', true).html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="redco-spin" style="vertical-align: middle; margin-right: 5px;"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg> ' + 'Uninstalling...');

            // Send AJAX request to uninstall the add-on
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_uninstall_addon',
                    addon: addonSlug,
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    // Log the response for debugging
                    console.log('Uninstall add-on response:', response);

                    if (response.success) {
                        // Show success notification
                        addonNotification('success', response.data.message, 'Add-On Uninstalled');

                        // Reload the page to update the UI
                        setTimeout(function() {
                            // Use the redirect URL from the server if available
                            if (response.data.redirect_url) {
                                window.location.href = response.data.redirect_url;
                                return;
                            }

                            // Otherwise, make sure we stay on the add-ons tab
                            let currentUrl = window.location.href;
                            let baseUrl = currentUrl.split('?')[0];
                            let params = new URLSearchParams(window.location.search);

                            // Ensure we're on the add-ons tab
                            params.set('page', 'redco-optimizer');
                            params.set('tab', 'addons');
                            params.set('t', new Date().getTime()); // Add timestamp to prevent caching

                            // Redirect to the add-ons tab
                            window.location.href = baseUrl + '?' + params.toString();
                        }, 1000);
                    } else {
                        // Show error notification
                        addonNotification('error', response.data.message, 'Uninstallation Failed');

                        // Restore the button
                        $button.prop('disabled', false).html('Uninstall');
                    }
                },
                error: function(xhr, status, error) {
                    // Log the error for debugging
                    console.error('Uninstall add-on error:', status, error);
                    console.error('Response text:', xhr.responseText);

                    // Show error notification
                    addonNotification('error', 'Connection error: ' + error, 'Uninstallation Failed');

                    // Restore the button
                    $button.prop('disabled', false).html('Uninstall');
                }
            });
        });

        // Handle refresh add-ons button
        $('.redco-refresh-addons').on('click', function(e) {
            e.preventDefault();

            const $button = $(this);
            const originalText = $button.html();

            // Disable the button and show loading state
            $button.prop('disabled', true).html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="redco-spin" style="vertical-align: middle; margin-right: 5px;"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg> ' + 'Refreshing...');

            // Send AJAX request to refresh add-ons
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_refresh_addons',
                    nonce: redco_optimizer.nonce
                },
                success: function(response) {
                    // Log the response for debugging
                    console.log('Refresh add-ons response:', response);

                    if (response.success) {
                        // Show success notification
                        addonNotification('success', response.data.message, 'Add-Ons Refreshed');

                        // Reload the page to show the refreshed add-ons
                        setTimeout(function() {
                            // Use the redirect URL from the server if available
                            if (response.data.redirect_url) {
                                window.location.href = response.data.redirect_url;
                                return;
                            }

                            // Otherwise, make sure we stay on the add-ons tab
                            let currentUrl = window.location.href;
                            let baseUrl = currentUrl.split('?')[0];
                            let params = new URLSearchParams(window.location.search);

                            // Ensure we're on the add-ons tab
                            params.set('page', 'redco-optimizer');
                            params.set('tab', 'addons');
                            params.set('t', new Date().getTime()); // Add timestamp to prevent caching

                            // Redirect to the add-ons tab
                            window.location.href = baseUrl + '?' + params.toString();
                        }, 1000);
                    } else {
                        // Show error notification
                        addonNotification('error', response.data.message, 'Refresh Failed');

                        // Restore the button
                        $button.prop('disabled', false).html(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    // Log the error for debugging
                    console.error('Refresh add-ons error:', status, error);
                    console.error('Response text:', xhr.responseText);

                    // Show error notification
                    addonNotification('error', 'Connection error: ' + error, 'Refresh Failed');

                    // Restore the button
                    $button.prop('disabled', false).html(originalText);
                }
            });
        });

        // Handle add-on settings button click
        $(document).on('click', '.redco-addon-settings', function(e) {
            e.preventDefault();

            // Check if we're on the Redco Optimizer page
            const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                        $('body.toplevel_page_redco-optimizer').length > 0 ||
                                        $('body.redco-optimizer_page_').length > 0 ||
                                        window.location.href.indexOf('page=redco-optimizer') !== -1;

            // Check if we're on the help page
            const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

            if (!isRedcoOptimizerPage || isHelpPage) {
                console.log('Not on Redco Optimizer page or on help page, ignoring settings button click');
                return; // Exit early if not on the Redco Optimizer page or if on help page
            }

            // Check if the clicked element has the redco-help-direct-link class
            if ($(this).hasClass('redco-help-direct-link')) {
                console.log('Help link clicked, not showing add-on settings modal');
                return;
            }

            const $button = $(this);
            const addonSlug = $button.data('addon');

            console.log('Settings button clicked for addon:', addonSlug);
            console.log('Modal element exists:', $('#redco-addon-settings-modal').length > 0);

            // Add a style to hide validation errors during loading
            if (!$('#redco-hide-validation-errors').length) {
                $('head').append('<style id="redco-hide-validation-errors">.redco-modal-content form:invalid { outline: none; } .redco-modal-content :invalid { box-shadow: none; outline: none; } .redco-modal-content input:invalid, .redco-modal-content select:invalid, .redco-modal-content textarea:invalid { box-shadow: none !important; outline: none !important; border-color: #dcdcde !important; }</style>');
            }

            // If the modal doesn't exist, create it
            if ($('#redco-addon-settings-modal').length === 0) {
                console.log('Modal not found, creating it now');

                // Create the modal HTML
                const modalHTML = `
                <div id="redco-addon-settings-modal" class="redco-modal-overlay">
                    <div class="redco-modal redco-addon-settings-modal">
                        <div class="redco-modal-header">
                            <h2 id="redco-addon-settings-title">Add-on Settings</h2>
                            <button type="button" class="redco-modal-close" aria-label="Close">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"></path></svg>
                            </button>
                        </div>
                        <div class="redco-modal-content" id="redco-addon-settings-content">
                            <div class="redco-modal-loading">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="40" height="40" class="redco-spin"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg>
                                <p>Loading settings...</p>
                            </div>
                        </div>
                    </div>
                </div>
                `;

                // Append to body
                $('body').append(modalHTML);
                console.log('Modal created and added to DOM');
            }

            // Show the modal - make sure it's visible
            $('#redco-addon-settings-modal').css({
                'display': 'flex',
                'visibility': 'visible',
                'opacity': '1',
                'z-index': '100000'
            }).addClass('active');

            // Ensure the modal is properly sized
            $('.redco-modal').css({
                'max-height': '90vh',
                'display': 'flex',
                'flex-direction': 'column',
                'width': '700px',
                'max-width': '90%'
            });

            // Ensure the modal content is properly sized
            $('.redco-modal-content').css({
                'overflow-y': 'auto',
                'flex': '1',
                'max-height': 'calc(90vh - 140px)',
                'scrollbar-width': 'thin',
                'padding': '20px'
            });

            // Load the settings content
            loadAddonSettings(addonSlug);
        });

        // Handle modal close button with improved handling
        $(document).on('click', '.redco-modal-close, .redco-modal-cancel', function() {
            // Check if we're on the Redco Optimizer page
            const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                        $('body.toplevel_page_redco-optimizer').length > 0 ||
                                        $('body.redco-optimizer_page_').length > 0 ||
                                        window.location.href.indexOf('page=redco-optimizer') !== -1;

            // Check if we're on the help page
            const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

            if (!isRedcoOptimizerPage || isHelpPage) {
                console.log('Not on Redco Optimizer page or on help page, using standard close behavior');
                // Use a more standard approach to close the modal if not on the plugin page or if on help page
                $(this).closest('.redco-modal-overlay').hide();
                return;
            }

            // Close all modals
            closeAllModals();
        });

        // Close modal when clicking outside the modal content
        $(document).on('click', '.redco-modal-overlay', function(e) {
            // Check if we're on the Redco Optimizer page
            const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                        $('body.toplevel_page_redco-optimizer').length > 0 ||
                                        $('body.redco-optimizer_page_').length > 0 ||
                                        window.location.href.indexOf('page=redco-optimizer') !== -1;

            // Check if we're on the help page
            const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

            if (!isRedcoOptimizerPage || isHelpPage) {
                console.log('Not on Redco Optimizer page or on help page, using standard close behavior');
                // Use a more standard approach to close the modal if not on the plugin page or if on help page
                $(this).hide();
                return;
            }

            // Only close if the click is directly on the overlay, not on its children
            if (e.target === this) {
                closeAllModals();
            }
        });

        // Handle save button click in modal
        $(document).on('click', '.redco-modal-save', function() {
            // Check if we're on the Redco Optimizer page
            const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                        $('body.toplevel_page_redco-optimizer').length > 0 ||
                                        $('body.redco-optimizer_page_').length > 0 ||
                                        window.location.href.indexOf('page=redco-optimizer') !== -1;

            // Check if we're on the help page
            const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

            if (!isRedcoOptimizerPage || isHelpPage) {
                console.log('Not on Redco Optimizer page or on help page, using standard close behavior');
                // Use a more standard approach to close the modal if not on the plugin page or if on help page
                $(this).closest('.redco-modal-overlay').hide();
                return;
            }

            // Find the form in the modal
            const $form = $('#redco-addon-settings-content').find('.redco-addon-settings-form');

            // Log form details for debugging
            console.log('Form found:', $form.length > 0);
            console.log('Form elements:', $form.find('input, select, textarea').length);

            // If form exists, submit it
            if ($form.length > 0) {
                $form.submit();
            } else {
                console.error('Form not found in modal');
                closeAllModals();
            }
        });

        // Handle settings form submission
        $(document).on('submit', '.redco-addon-settings-form', function(e) {
            e.preventDefault();

            // Check if we're on the Redco Optimizer page
            const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                        $('body.toplevel_page_redco-optimizer').length > 0 ||
                                        $('body.redco-optimizer_page_').length > 0 ||
                                        window.location.href.indexOf('page=redco-optimizer') !== -1;

            // Check if we're on the help page
            const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

            if (!isRedcoOptimizerPage || isHelpPage) {
                console.log('Not on Redco Optimizer page or on help page, ignoring form submission');
                // Close the modal
                $(this).closest('.redco-modal-overlay').hide();
                return;
            }

            const $form = $(this);
            const addonSlug = $form.data('addon');

            // If the addon slug is missing, try to get it from the form action or other attributes
            if (!addonSlug) {
                console.log('Addon slug not found in data-addon attribute, trying to determine from form');

                // Try to extract from form action
                const formAction = $form.attr('action') || '';
                if (formAction.indexOf('addon=') !== -1) {
                    const match = formAction.match(/addon=([^&]+)/);
                    if (match && match[1]) {
                        console.log('Found addon slug in form action:', match[1]);
                        $form.attr('data-addon', match[1]);
                    }
                }

                // Try to extract from form ID
                const formId = $form.attr('id') || '';
                if (formId.indexOf('redco-') !== -1) {
                    const match = formId.match(/redco-([^-]+)-settings-form/);
                    if (match && match[1]) {
                        console.log('Found addon slug in form ID:', match[1]);
                        $form.attr('data-addon', match[1]);
                    }
                }

                // Try to extract from form class
                const formClass = $form.attr('class') || '';
                if (formClass.indexOf('redco-') !== -1) {
                    const match = formClass.match(/redco-([^-]+)-settings-form/);
                    if (match && match[1]) {
                        console.log('Found addon slug in form class:', match[1]);
                        $form.attr('data-addon', match[1]);
                    }
                }

                // Try to extract from nonce field
                const nonceField = $form.find('input[name$="_nonce"]');
                if (nonceField.length > 0) {
                    const nonceName = nonceField.attr('name');
                    const match = nonceName.match(/redco_([^_]+)_nonce/);
                    if (match && match[1]) {
                        console.log('Found addon slug in nonce field:', match[1]);
                        $form.attr('data-addon', match[1].replace(/_/g, '-'));
                    }
                }
            }

            // Get the updated addon slug
            const updatedAddonSlug = $form.data('addon');

            if (!updatedAddonSlug) {
                console.error('Could not determine addon slug, using "unknown-addon"');
                $form.attr('data-addon', 'unknown-addon');
            }

            const $submitButton = $form.find('button[type="submit"], .redco-modal-save').first();
            const originalText = $submitButton.html();

            // Log form data for debugging
            console.log('Form submission for addon:', updatedAddonSlug);
            console.log('Form elements:', $form.find('input, select, textarea').length);

            // Disable the button and show loading state
            $submitButton.prop('disabled', true).html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" class="redco-spin" style="vertical-align: middle; margin-right: 5px;"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg> ' + 'Saving...');

            // Clear any existing notifications
            $('.redco-notification').remove();

            // Create FormData object to properly handle all form fields
            let formData = new FormData($form[0]);

            // Add all checkboxes with value 0 if unchecked (they're not included in FormData by default)
            $form.find('input[type="checkbox"]').each(function() {
                const $checkbox = $(this);
                const name = $checkbox.attr('name');

                // If checkbox exists and is not disabled
                if (name && !$checkbox.prop('disabled')) {
                    // If it's not checked, explicitly set it to 0
                    if (!$checkbox.is(':checked')) {
                        formData.set(name, '0');
                        console.log('Set unchecked checkbox to 0:', name);
                    } else {
                        // Ensure checked boxes have value 1
                        formData.set(name, '1');
                        console.log('Set checked checkbox to 1:', name);
                    }
                }
            });

            // Process numeric fields
            $form.find('input[type="number"]').each(function() {
                const name = $(this).attr('name');
                if (formData.has(name)) {
                    let value = formData.get(name);
                    // Parse as integer and ensure it's a valid number
                    let numValue = parseInt(value);
                    if (!isNaN(numValue)) {
                        // Replace the value with the integer
                        formData.set(name, numValue);
                        console.log('Converted ' + name + ' to number: ' + numValue);
                    }
                }
            });

            // Process select fields
            $form.find('select').each(function() {
                const name = $(this).attr('name');
                if (formData.has(name)) {
                    let value = formData.get(name);
                    console.log('Select field ' + name + ' value: ' + value);

                    // If the value is numeric, convert it to a number
                    if (!isNaN(parseInt(value))) {
                        let numValue = parseInt(value);
                        formData.set(name, numValue);
                        console.log('Converted select field ' + name + ' to number: ' + numValue);
                    }
                }
            });

            // Log all form data for debugging
            console.log('Form data entries after processing:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1] + ' (type: ' + typeof pair[1] + ')');
            }

            // Convert FormData to URL-encoded string
            let serializedData = '';
            for (let pair of formData.entries()) {
                console.log('Form field:', pair[0], pair[1]);
                serializedData += '&' + encodeURIComponent(pair[0]) + '=' + encodeURIComponent(pair[1]);
            }

            if (serializedData.length > 0) {
                serializedData = serializedData.substring(1); // Remove the leading &
            }

            console.log('Enhanced form data:', serializedData);

            // Send AJAX request to save settings
            $.ajax({
                url: redco_optimizer.ajax_url,
                type: 'POST',
                data: {
                    action: 'redco_optimizer_save_addon_settings',
                    nonce: redco_optimizer.nonce,
                    addon: updatedAddonSlug,
                    form_data: serializedData
                },
                success: function(response) {
                    console.log('AJAX response:', response);

                    if (response.success) {
                        // Show success notification
                        addonNotification('success', response.data.message, 'Settings Saved');

                        // Always reload the page after a short delay to ensure settings are properly reflected
                        setTimeout(function() {
                            closeAllModals();
                            window.location.reload();
                        }, 2000);
                    } else {
                        // Show error notification
                        addonNotification('error', response.data.message || 'Unknown error occurred.', 'Save Failed');

                        // Restore the button
                        $submitButton.prop('disabled', false).html(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    // Log error details
                    console.error('AJAX error:', status, error);
                    console.error('Response:', xhr.responseText);

                    // Try to parse the response for more details
                    let errorMessage = 'Connection error. Please try again.';
                    try {
                        if (xhr.responseText) {
                            const jsonResponse = JSON.parse(xhr.responseText);
                            if (jsonResponse.data && jsonResponse.data.message) {
                                errorMessage = jsonResponse.data.message;
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing response:', e);
                    }

                    // Show error notification
                    addonNotification('error', errorMessage, 'Save Failed');

                    // Restore the button
                    $submitButton.prop('disabled', false).html(originalText);
                }
            });
        });
    });

    /**
     * Load add-on settings via AJAX
     *
     * @param {string} addonSlug The add-on slug
     */
    function loadAddonSettings(addonSlug) {
        // Check if we're on the Redco Optimizer page
        const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                    $('body.toplevel_page_redco-optimizer').length > 0 ||
                                    $('body.redco-optimizer_page_').length > 0 ||
                                    window.location.href.indexOf('page=redco-optimizer') !== -1;

        // Check if we're on the help page
        const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

        if (!isRedcoOptimizerPage || isHelpPage) {
            console.log('Not on Redco Optimizer page or on help page, skipping add-on settings loading');
            return; // Exit early if not on the Redco Optimizer page or if on help page
        }

        console.log('Loading settings for addon:', addonSlug);
        console.log('Settings content element exists:', $('#redco-addon-settings-content').length > 0);

        // Add a style to hide validation errors during loading
        if (!$('#redco-hide-validation-errors').length) {
            $('head').append('<style id="redco-hide-validation-errors">.redco-modal-content form:invalid { outline: none; } .redco-modal-content :invalid { box-shadow: none; outline: none; } .redco-modal-content input:invalid, .redco-modal-content select:invalid, .redco-modal-content textarea:invalid { box-shadow: none !important; outline: none !important; border-color: #dcdcde !important; }</style>');
        }

        // Show loading state
        $('#redco-addon-settings-content').html('<div class="redco-modal-loading"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="40" height="40" class="redco-spin"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" fill="currentColor"></path></svg><p>Loading settings...</p></div>');

        // Log AJAX parameters
        console.log('AJAX parameters:', {
            url: redco_optimizer.ajax_url,
            nonce: redco_optimizer.nonce,
            addon: addonSlug
        });

        // Send AJAX request to load settings
        $.ajax({
            url: redco_optimizer.ajax_url,
            type: 'POST',
            data: {
                action: 'redco_optimizer_load_addon_settings',
                nonce: redco_optimizer.nonce,
                addon: addonSlug
            },
            success: function(response) {
                // Debug log
                console.log('AJAX response:', response);

                if (response.success) {
                    // Update modal title
                    $('#redco-addon-settings-title').text(response.data.title);

                    // Debug log
                    console.log('Modal content length:', response.data.content.length);
                    console.log('Modal content preview:', response.data.content.substring(0, 200));

                    // Update modal content
                    $('#redco-addon-settings-content').html(response.data.content);

                    // Debug log
                    console.log('Form elements after update:', $('#redco-addon-settings-content .redco-toggle-row').length);
                    console.log('Form structure:', $('#redco-addon-settings-content').html().substring(0, 200));
                    console.log('Modal sections:', $('#redco-addon-settings-content .redco-modal-section').length);
                    console.log('Form rows:', $('#redco-addon-settings-content .redco-form-row').length);

                    // Add data-addon attribute to the form
                    $('.redco-addon-settings-form').attr('data-addon', addonSlug);

                    // Add a hidden input with the addon slug if it doesn't exist
                    if ($('.redco-addon-settings-form input[name="addon_slug"]').length === 0) {
                        $('.redco-addon-settings-form').append('<input type="hidden" name="addon_slug" value="' + addonSlug + '">');
                    }

                    // Add a hidden input with the action if it doesn't exist
                    if ($('.redco-addon-settings-form input[name="action"]').length === 0) {
                        $('.redco-addon-settings-form').append('<input type="hidden" name="action" value="redco_optimizer_save_addon_settings">');
                    }

                    // Add a hidden input with the nonce if it doesn't exist
                    if ($('.redco-addon-settings-form input[name="nonce"]').length === 0) {
                        $('.redco-addon-settings-form').append('<input type="hidden" name="nonce" value="' + redco_optimizer.nonce + '">');
                    }

                    // Ensure the modal is properly sized
                    $('.redco-modal').css({
                        'max-height': '90vh',
                        'display': 'flex',
                        'flex-direction': 'column'
                    });

                    // Ensure the modal content is scrollable
                    $('.redco-modal-content').css({
                        'overflow-y': 'auto',
                        'flex': '1',
                        'max-height': 'calc(90vh - 140px)'
                    });

                    // Initialize any form elements
                    initializeFormElements();

                    // Remove the validation error hiding style after a short delay
                    setTimeout(function() {
                        $('#redco-hide-validation-errors').remove();
                    }, 1000);

                    // Show the form fields and ensure they're visible
                    $('#redco-addon-settings-content .redco-modal-section').css({
                        'display': 'block',
                        'visibility': 'visible',
                        'opacity': '1',
                        'margin-bottom': '25px'
                    });

                    $('#redco-addon-settings-content .redco-form-row').css({
                        'display': 'flex',
                        'visibility': 'visible',
                        'opacity': '1',
                        'margin-bottom': '15px',
                        'align-items': 'flex-start'
                    });

                    $('#redco-addon-settings-content .redco-toggle-row').css({
                        'display': 'flex',
                        'visibility': 'visible',
                        'opacity': '1',
                        'justify-content': 'space-between',
                        'align-items': 'center',
                        'padding': '15px 0',
                        'border-bottom': '1px solid var(--border-color)'
                    });

                    $('#redco-addon-settings-content .redco-setting-row, ' +
                     '#redco-addon-settings-content .redco-card, ' +
                     '#redco-addon-settings-content .redco-card-header, ' +
                     '#redco-addon-settings-content .redco-card-content, ' +
                     '#redco-addon-settings-content .redco-settings-section-title, ' +
                     '#redco-addon-settings-content .redco-form-actions').css({
                        'display': 'block',
                        'visibility': 'visible',
                        'opacity': '1'
                     });

                    // Add a footer with save button if it doesn't exist
                    if ($('.redco-modal-footer').length === 0) {
                        console.log('Adding modal footer with save button');

                        var $footer = $('<div class="redco-modal-footer"></div>');
                        $footer.css({
                            'display': 'flex',
                            'justify-content': 'flex-end',
                            'gap': '10px',
                            'padding': '20px',
                            'border-top': '1px solid var(--border-color)',
                            'visibility': 'visible',
                            'opacity': '1'
                        });

                        var $saveButton = $('<button type="button" class="redco-button redco-button-primary redco-modal-save">Save Settings</button>');
                        var $cancelButton = $('<button type="button" class="redco-button redco-modal-cancel">Cancel</button>');

                        $footer.append($cancelButton).append($saveButton);
                        $('.redco-modal').append($footer);
                    }

                    // Force display of all elements with multiple attempts
                    setTimeout(function() {
                        initializeFormElements();

                        // Try again after a longer delay to catch any elements that might load later
                        setTimeout(function() {
                            initializeFormElements();

                            // One final attempt with direct CSS application
                            $('#redco-addon-settings-content *').css({
                                'visibility': 'visible',
                                'opacity': '1'
                            });

                            // Ensure the modal is properly sized
                            $('.redco-modal').css({
                                'max-height': '90vh',
                                'display': 'flex',
                                'flex-direction': 'column'
                            });

                            // Ensure the modal content is scrollable
                            $('.redco-modal-content').css({
                                'overflow-y': 'auto',
                                'flex': '1'
                            });

                            // Trigger a custom event to notify any addon-specific scripts
                            $(document).trigger('redco_addon_settings_loaded', [addonSlug]);
                        }, 300);
                    }, 100);
                } else {
                    // Show error notification
                    addonNotification('error', response.data.message || 'Failed to load settings', 'Load Failed');
                    console.error('Error loading settings:', response.data);

                    // Close the modal
                    closeAllModals();
                }
            },
            error: function(xhr, status, error) {
                // Show error notification
                console.error('AJAX error:', status, error);
                console.error('Response:', xhr.responseText);

                addonNotification('error', 'Connection error: ' + error, 'Load Failed');

                // Close the modal
                closeAllModals();
            }
        });
    }

    /**
     * Initialize form elements
     */
    function initializeFormElements() {
        // Check if we're on the Redco Optimizer page
        const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                    $('body.toplevel_page_redco-optimizer').length > 0 ||
                                    $('body.redco-optimizer_page_').length > 0 ||
                                    window.location.href.indexOf('page=redco-optimizer') !== -1;

        // Check if we're on the help page
        const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

        if (!isRedcoOptimizerPage || isHelpPage) {
            console.log('Not on Redco Optimizer page or on help page, skipping form element initialization');
            return; // Exit early if not on the Redco Optimizer page or if on help page
        }

        console.log('Initializing form elements...');

        // Ensure the modal is properly sized
        $('.redco-modal').css({
            'max-height': '90vh',
            'display': 'flex',
            'flex-direction': 'column'
        });

        // Ensure the modal content is scrollable
        $('.redco-modal-content').css({
            'overflow-y': 'auto',
            'flex': '1',
            'max-height': 'calc(90vh - 140px)',
            'scrollbar-width': 'thin',
            'padding': '20px'
        });

        // Ensure all modal sections are visible with !important flags
        $('.redco-modal-section').css({
            'display': 'block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '25px !important',
            'position': 'relative !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important; position: relative !important;';
        });

        // Ensure all form rows are visible and properly styled with !important flags
        $('.redco-form-row').css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '15px !important',
            'align-items': 'flex-start !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 15px !important; align-items: flex-start !important;';
        });

        // Ensure all toggle rows are visible and properly styled with !important flags
        $('.redco-toggle-row').css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'justify-content': 'space-between !important',
            'align-items': 'center !important',
            'padding': '15px 0 !important',
            'border-bottom': '1px solid var(--border-color) !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; justify-content: space-between !important; align-items: center !important; padding: 15px 0 !important; border-bottom: 1px solid var(--border-color) !important;';
        });

        // Make the form take up all available space but let the modal handle scrolling
        $('.redco-addon-settings-form').css({
            'overflow-y': 'visible !important',
            'max-height': 'none !important',
            'padding-bottom': '30px !important', // Add padding at the bottom for better visibility
            'display': 'block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'overflow-y: visible !important; max-height: none !important; padding-bottom: 30px !important; display: block !important; visibility: visible !important; opacity: 1 !important;';
        });

        // Special handling for form actions
        $('.redco-form-actions').css({
            'display': 'flex !important',
            'justify-content': 'flex-end !important',
            'gap': '10px !important',
            'margin-top': '20px !important',
            'visibility': 'visible !important',
            'opacity': '1 !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; justify-content: flex-end !important; gap: 10px !important; margin-top: 20px !important; visibility: visible !important; opacity: 1 !important;';
        });

        // Log the visibility status
        console.log('Modal sections visible:', $('.redco-modal-section:visible').length);
        console.log('Form rows visible:', $('.redco-form-row:visible').length);
        console.log('Toggle rows visible:', $('.redco-toggle-row:visible').length);
        console.log('Setting rows visible:', $('.redco-setting-row:visible').length);

        // Force all elements to be visible
        $('#redco-addon-settings-content *').css({
            'visibility': 'visible !important',
            'opacity': '1 !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'visibility: visible !important; opacity: 1 !important;';
        });
    }

    /**
     * Ensure dashicons in add-on cards are visible
     */
    function ensureAddOnDashiconsVisible() {
        // Check if we're on the Redco Optimizer page
        const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                    $('body.toplevel_page_redco-optimizer').length > 0 ||
                                    $('body.redco-optimizer_page_').length > 0 ||
                                    window.location.href.indexOf('page=redco-optimizer') !== -1;

        // Check if we're on the help page
        const isHelpPage = window.location.href.indexOf('page=redco-optimizer-help') !== -1;

        if (!isRedcoOptimizerPage || isHelpPage) {
            console.log('Not on Redco Optimizer page or on help page, skipping dashicon visibility fix');
            return; // Exit early if not on the Redco Optimizer page or if on help page
        }

        // Force dashicons in add-on cards to be visible
        $('.redco-addon-icon .dashicons').css({
            'display': 'inline-block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'z-index': '100 !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: inline-block !important; visibility: visible !important; opacity: 1 !important; z-index: 100 !important;';
        });
    }

    // Call the function on page load
    ensureAddOnDashiconsVisible();

    // Call the function again after a short delay to ensure it works
    setTimeout(function() {
        // Check if we're on the Redco Optimizer page before calling
        const isRedcoOptimizerPage = $('.redco-optimizer-wrap').length > 0 ||
                                    $('body.toplevel_page_redco-optimizer').length > 0 ||
                                    $('body.redco-optimizer_page_').length > 0 ||
                                    window.location.href.indexOf('page=redco-optimizer') !== -1;

        if (isRedcoOptimizerPage) {
            ensureAddOnDashiconsVisible();
        }
    }, 500);
})(jQuery);
