<?php
/**
 * Font Optimizer Add-on
 *
 * @link              https://redco-optimizer.com
 * @since             1.0.0
 * @package           Redco_Optimizer
 *
 * @wordpress-plugin
 * Addon Name:        Font Optimizer
 * Description:       Optimize web fonts with local hosting, subsetting, preloading, and font-display optimization for better performance.
 * Version:           1.0.0
 * Author:            Redco
 * Author URI:        https://redco-optimizer.com
 * Premium:           false
 * Has Settings:      true
 * Icon:              dashicons-editor-textcolor
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Font Optimizer class.
 */
class Redco_Optimizer_Font_Optimizer {

    /**
     * The settings for this addon.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for this addon.
     */
    private $settings;

    /**
     * The detected fonts.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $detected_fonts    The detected fonts.
     */
    private $detected_fonts;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Load settings
        $this->settings = get_option('redco_font_optimizer_settings', array(
            'enabled' => false,
            'local_hosting' => true,
            'preload_fonts' => true,
            'font_display' => 'swap',
            'subset_fonts' => false,
            'subset_latin' => true,
            'subset_latin_ext' => false,
            'subset_cyrillic' => false,
            'subset_cyrillic_ext' => false,
            'subset_greek' => false,
            'subset_greek_ext' => false,
            'subset_vietnamese' => false,
            'optimize_google_fonts' => true,
            'optimize_typekit_fonts' => false,
            'optimize_custom_fonts' => false,
            'custom_fonts' => '',
            'excluded_fonts' => '',
            'font_face_observer' => false,
            'async_css' => false,
            'remove_unused_variants' => true,
            'font_loading_mode' => 'default',
        ));

        // Load detected fonts
        $this->detected_fonts = get_option('redco_font_optimizer_detected_fonts', array());

        // Register hooks
        $this->register_hooks();
    }

    /**
     * Register all hooks for this addon.
     *
     * @since    1.0.0
     */
    private function register_hooks() {
        // Add admin page
        add_action('admin_menu', array($this, 'add_admin_page'));

        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add AJAX handlers
        add_action('wp_ajax_redco_detect_fonts', array($this, 'ajax_detect_fonts'));
        add_action('wp_ajax_redco_download_font', array($this, 'ajax_download_font'));
        add_action('wp_ajax_redco_reset_fonts', array($this, 'ajax_reset_fonts'));

        // Add frontend optimization if enabled
        if ($this->is_enabled()) {
            // Filter for Google Fonts
            if ($this->settings['optimize_google_fonts']) {
                add_filter('style_loader_tag', array($this, 'optimize_google_fonts'), 10, 4);
            }

            // Filter for Typekit Fonts
            if ($this->settings['optimize_typekit_fonts']) {
                add_filter('script_loader_tag', array($this, 'optimize_typekit_fonts'), 10, 3);
            }

            // Add preload tags
            if ($this->settings['preload_fonts']) {
                add_action('wp_head', array($this, 'add_preload_tags'), 1);
            }

            // Add font face observer
            if ($this->settings['font_face_observer']) {
                add_action('wp_enqueue_scripts', array($this, 'enqueue_font_face_observer'));
            }

            // Add async CSS
            if ($this->settings['async_css']) {
                add_filter('style_loader_tag', array($this, 'async_css'), 10, 4);
            }
        }
    }

    /**
     * Check if font optimizer is enabled.
     *
     * @since    1.0.0
     * @return   bool    True if font optimizer is enabled, false otherwise.
     */
    public function is_enabled() {
        return isset($this->settings['enabled']) && $this->settings['enabled'];
    }

    /**
     * Add admin page.
     *
     * @since    1.0.0
     */
    public function add_admin_page() {
        add_submenu_page(
            'redco-optimizer-addons',
            __('Font Optimizer', 'redco-optimizer'),
            __('Font Optimizer', 'redco-optimizer'),
            'manage_options',
            'redco-optimizer-font-optimizer',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Enqueue scripts and styles.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts($hook) {
        if ($hook === 'redco-optimizer_page_redco-optimizer-font-optimizer') {
            wp_enqueue_script('redco-font-optimizer', plugin_dir_url(__FILE__) . 'js/font-optimizer.js', array('jquery'), '1.0.0', true);
            wp_localize_script('redco-font-optimizer', 'redco_font', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('redco_font_nonce'),
            ));
        }

        // Enqueue the modal fix script on all admin pages
        if (is_admin()) {
            wp_enqueue_script('redco-font-optimizer-modal-fix', plugin_dir_url(__FILE__) . 'js/modal-fix.js', array('jquery'), '1.0.0', true);
        }
    }

    /**
     * Render the settings page.
     *
     * @since    1.0.0
     */
    public function render_settings_page() {
        // Check if this addon's settings are being displayed
        if (!isset($_GET['addon']) || $_GET['addon'] !== 'font-optimizer') {
            return;
        }

        // Save settings if form is submitted
        if (isset($_POST['redco_font_optimizer_save_settings'])) {
            $this->save_settings();
        }

        // Include settings template
        include_once plugin_dir_path(__FILE__) . 'templates/settings.php';
    }

    /**
     * Save settings.
     *
     * @since    1.0.0
     */
    public function save_settings() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Verify nonce
        if (!isset($_POST['redco_font_optimizer_nonce']) || !wp_verify_nonce($_POST['redco_font_optimizer_nonce'], 'redco_font_optimizer_save_settings')) {
            add_settings_error('redco_font_optimizer', 'redco_font_optimizer_nonce', __('Security check failed.', 'redco-optimizer'), 'error');
            return;
        }

        // Get settings
        $settings = array(
            'enabled' => isset($_POST['enabled']) ? 1 : 0,
            'local_hosting' => isset($_POST['local_hosting']) ? 1 : 0,
            'preload_fonts' => isset($_POST['preload_fonts']) ? 1 : 0,
            'font_display' => sanitize_text_field($_POST['font_display']),
            'subset_fonts' => isset($_POST['subset_fonts']) ? 1 : 0,
            'subset_latin' => isset($_POST['subset_latin']) ? 1 : 0,
            'subset_latin_ext' => isset($_POST['subset_latin_ext']) ? 1 : 0,
            'subset_cyrillic' => isset($_POST['subset_cyrillic']) ? 1 : 0,
            'subset_cyrillic_ext' => isset($_POST['subset_cyrillic_ext']) ? 1 : 0,
            'subset_greek' => isset($_POST['subset_greek']) ? 1 : 0,
            'subset_greek_ext' => isset($_POST['subset_greek_ext']) ? 1 : 0,
            'subset_vietnamese' => isset($_POST['subset_vietnamese']) ? 1 : 0,
            'optimize_google_fonts' => isset($_POST['optimize_google_fonts']) ? 1 : 0,
            'optimize_typekit_fonts' => isset($_POST['optimize_typekit_fonts']) ? 1 : 0,
            'optimize_custom_fonts' => isset($_POST['optimize_custom_fonts']) ? 1 : 0,
            'custom_fonts' => sanitize_textarea_field($_POST['custom_fonts']),
            'excluded_fonts' => sanitize_textarea_field($_POST['excluded_fonts']),
            'font_face_observer' => isset($_POST['font_face_observer']) ? 1 : 0,
            'async_css' => isset($_POST['async_css']) ? 1 : 0,
            'remove_unused_variants' => isset($_POST['remove_unused_variants']) ? 1 : 0,
            'font_loading_mode' => sanitize_text_field($_POST['font_loading_mode']),
        );

        // Update settings
        update_option('redco_font_optimizer_settings', $settings);
        $this->settings = $settings;

        // Add success message
        add_settings_error('redco_font_optimizer', 'redco_font_optimizer_updated', __('Settings saved.', 'redco-optimizer'), 'success');
    }

    /**
     * Detect fonts via AJAX.
     *
     * @since    1.0.0
     */
    public function ajax_detect_fonts() {
        // Check nonce
        check_ajax_referer('redco_font_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Detect fonts
        $fonts = $this->detect_fonts();

        // Update detected fonts
        update_option('redco_font_optimizer_detected_fonts', $fonts);
        $this->detected_fonts = $fonts;

        // Return fonts
        wp_send_json_success(array(
            'message' => __('Fonts detected successfully.', 'redco-optimizer'),
            'fonts' => $fonts,
        ));
    }

    /**
     * Download font via AJAX.
     *
     * @since    1.0.0
     */
    public function ajax_download_font() {
        // Check nonce
        check_ajax_referer('redco_font_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get font URL
        $font_url = isset($_POST['font_url']) ? esc_url_raw($_POST['font_url']) : '';

        if (empty($font_url)) {
            wp_send_json_error(array('message' => __('Invalid font URL.', 'redco-optimizer')));
        }

        // Download font
        $result = $this->download_font($font_url);

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        } else {
            wp_send_json_success(array(
                'message' => __('Font downloaded successfully.', 'redco-optimizer'),
                'local_url' => $result,
            ));
        }
    }

    /**
     * Reset fonts via AJAX.
     *
     * @since    1.0.0
     */
    public function ajax_reset_fonts() {
        // Check nonce
        check_ajax_referer('redco_font_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Reset fonts
        delete_option('redco_font_optimizer_detected_fonts');
        $this->detected_fonts = array();

        // Return success
        wp_send_json_success(array(
            'message' => __('Fonts reset successfully.', 'redco-optimizer'),
        ));
    }

    /**
     * Detect fonts.
     *
     * @since    1.0.0
     * @return   array    The detected fonts.
     */
    private function detect_fonts() {
        // This is a placeholder. In a real implementation, this would detect fonts from the site.
        return array(
            'google' => array(
                array(
                    'family' => 'Roboto',
                    'variants' => array('regular', '500', '700'),
                    'subsets' => array('latin', 'latin-ext'),
                    'url' => 'https://fonts.googleapis.com/css?family=Roboto:400,500,700&display=swap',
                ),
                array(
                    'family' => 'Open Sans',
                    'variants' => array('regular', '600', '700'),
                    'subsets' => array('latin', 'latin-ext'),
                    'url' => 'https://fonts.googleapis.com/css?family=Open+Sans:400,600,700&display=swap',
                ),
            ),
            'typekit' => array(
                array(
                    'family' => 'proxima-nova',
                    'variants' => array('regular', '600', '700'),
                    'url' => 'https://use.typekit.net/abc123.css',
                ),
            ),
            'custom' => array(
                array(
                    'family' => 'Custom Font',
                    'variants' => array('regular', 'bold'),
                    'url' => 'https://example.com/fonts/custom-font.css',
                ),
            ),
        );
    }

    /**
     * Download font.
     *
     * @since    1.0.0
     * @param    string    $font_url    The font URL.
     * @return   string|WP_Error    The local URL on success, WP_Error on failure.
     */
    private function download_font($font_url) {
        // This is a placeholder. In a real implementation, this would download the font and return the local URL.
        return 'https://example.com/wp-content/uploads/fonts/font.woff2';
    }

    /**
     * Optimize Google Fonts.
     *
     * @since    1.0.0
     * @param    string    $tag    The link tag.
     * @param    string    $handle    The style handle.
     * @param    string    $href    The stylesheet URL.
     * @param    string    $media    The media attribute.
     * @return   string    The modified link tag.
     */
    public function optimize_google_fonts($tag, $handle, $href, $media) {
        // This is a placeholder. In a real implementation, this would optimize Google Fonts.
        return $tag;
    }

    /**
     * Optimize Typekit Fonts.
     *
     * @since    1.0.0
     * @param    string    $tag    The script tag.
     * @param    string    $handle    The script handle.
     * @param    string    $src    The script URL.
     * @return   string    The modified script tag.
     */
    public function optimize_typekit_fonts($tag, $handle, $src) {
        // This is a placeholder. In a real implementation, this would optimize Typekit Fonts.
        return $tag;
    }

    /**
     * Add preload tags.
     *
     * @since    1.0.0
     */
    public function add_preload_tags() {
        // This is a placeholder. In a real implementation, this would add preload tags for fonts.
    }

    /**
     * Enqueue font face observer.
     *
     * @since    1.0.0
     */
    public function enqueue_font_face_observer() {
        // This is a placeholder. In a real implementation, this would enqueue the font face observer script.
    }

    /**
     * Async CSS.
     *
     * @since    1.0.0
     * @param    string    $tag    The link tag.
     * @param    string    $handle    The style handle.
     * @param    string    $href    The stylesheet URL.
     * @param    string    $media    The media attribute.
     * @return   string    The modified link tag.
     */
    public function async_css($tag, $handle, $href, $media) {
        // This is a placeholder. In a real implementation, this would add async loading to CSS.
        return $tag;
    }

    /**
     * Save settings via AJAX.
     *
     * @since    1.0.0
     * @param    array    $settings    The settings to save.
     * @return   mixed    True on success, WP_Error on failure.
     */
    public function save_settings_ajax($settings) {
        if (!current_user_can('manage_options')) {
            return new WP_Error('permission_denied', __('You do not have permission to perform this action.', 'redco-optimizer'));
        }

        // Debug log
        error_log('Font Optimizer - Saving settings via AJAX: ' . print_r($settings, true));

        // Ensure all checkbox fields are properly set
        $checkbox_fields = array(
            'enabled', 'local_hosting', 'preload_fonts', 'subset_fonts', 'subset_latin',
            'subset_latin_ext', 'subset_cyrillic', 'subset_cyrillic_ext', 'subset_greek',
            'subset_greek_ext', 'subset_vietnamese', 'optimize_google_fonts', 'optimize_typekit_fonts',
            'optimize_custom_fonts', 'font_face_observer', 'async_css', 'remove_unused_variants'
        );

        foreach ($checkbox_fields as $field) {
            if (!isset($settings[$field])) {
                $settings[$field] = 0;
            }
        }

        // Sanitize settings
        $sanitized_settings = array(
            'enabled' => isset($settings['enabled']) && $settings['enabled'] ? 1 : 0,
            'local_hosting' => isset($settings['local_hosting']) && $settings['local_hosting'] ? 1 : 0,
            'preload_fonts' => isset($settings['preload_fonts']) && $settings['preload_fonts'] ? 1 : 0,
            'font_display' => isset($settings['font_display']) ? sanitize_text_field($settings['font_display']) : 'swap',
            'subset_fonts' => isset($settings['subset_fonts']) && $settings['subset_fonts'] ? 1 : 0,
            'subset_latin' => isset($settings['subset_latin']) && $settings['subset_latin'] ? 1 : 0,
            'subset_latin_ext' => isset($settings['subset_latin_ext']) && $settings['subset_latin_ext'] ? 1 : 0,
            'subset_cyrillic' => isset($settings['subset_cyrillic']) && $settings['subset_cyrillic'] ? 1 : 0,
            'subset_cyrillic_ext' => isset($settings['subset_cyrillic_ext']) && $settings['subset_cyrillic_ext'] ? 1 : 0,
            'subset_greek' => isset($settings['subset_greek']) && $settings['subset_greek'] ? 1 : 0,
            'subset_greek_ext' => isset($settings['subset_greek_ext']) && $settings['subset_greek_ext'] ? 1 : 0,
            'subset_vietnamese' => isset($settings['subset_vietnamese']) && $settings['subset_vietnamese'] ? 1 : 0,
            'optimize_google_fonts' => isset($settings['optimize_google_fonts']) && $settings['optimize_google_fonts'] ? 1 : 0,
            'optimize_typekit_fonts' => isset($settings['optimize_typekit_fonts']) && $settings['optimize_typekit_fonts'] ? 1 : 0,
            'optimize_custom_fonts' => isset($settings['optimize_custom_fonts']) && $settings['optimize_custom_fonts'] ? 1 : 0,
            'custom_fonts' => isset($settings['custom_fonts']) ? sanitize_textarea_field($settings['custom_fonts']) : '',
            'excluded_fonts' => isset($settings['excluded_fonts']) ? sanitize_textarea_field($settings['excluded_fonts']) : '',
            'font_face_observer' => isset($settings['font_face_observer']) && $settings['font_face_observer'] ? 1 : 0,
            'async_css' => isset($settings['async_css']) && $settings['async_css'] ? 1 : 0,
            'remove_unused_variants' => isset($settings['remove_unused_variants']) && $settings['remove_unused_variants'] ? 1 : 0,
            'font_loading_mode' => isset($settings['font_loading_mode']) ? sanitize_text_field($settings['font_loading_mode']) : 'default',
        );

        // Debug log
        error_log('Font Optimizer - Sanitized settings: ' . print_r($sanitized_settings, true));

        try {
            // Update settings
            update_option('redco_font_optimizer_settings', $sanitized_settings);
            $this->settings = $sanitized_settings;

            return true;
        } catch (Exception $e) {
            error_log('Font Optimizer - Error saving settings: ' . $e->getMessage());
            return new WP_Error('save_failed', __('Failed to save settings: ', 'redco-optimizer') . $e->getMessage());
        }
    }
}

// Initialize the addon
new Redco_Optimizer_Font_Optimizer();
