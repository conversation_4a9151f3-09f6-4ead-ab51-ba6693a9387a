<?php
/**
 * Advanced Lazy Load Settings Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/advanced-lazy-load/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
?>

<form method="post" action="" class="redco-addon-settings-form" data-addon="advanced-lazy-load">
    <?php wp_nonce_field('redco_advanced_lazy_load_save_settings', 'redco_advanced_lazy_load_nonce'); ?>

    <div class="redco-card">
        <div class="redco-card-header">
            <h3><?php esc_html_e('Advanced Lazy Load Settings', 'redco-optimizer'); ?></h3>
        </div>
        <div class="redco-card-content">
            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Enable Advanced Lazy Load', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Enhanced lazy loading for images, iframes, and videos with advanced options and exclusions.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="enabled" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : 0, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <h4 class="redco-settings-section-title"><?php esc_html_e('General Settings', 'redco-optimizer'); ?></h4>

            <div class="redco-setting-row">
                <div class="redco-setting-label">
                    <label for="threshold"><?php esc_html_e('Loading Threshold (px)', 'redco-optimizer'); ?></label>
                    <p class="redco-setting-description"><?php esc_html_e('Distance in pixels from the viewport to start loading images.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-setting-field">
                    <input type="number" id="threshold" name="threshold" value="<?php echo isset($settings['threshold']) ? intval($settings['threshold']) : 200; ?>" min="0" max="1000" step="10">
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Fade In Effect', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Fade in images smoothly when they are loaded.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="fade_in" <?php checked(isset($settings['fade_in']) ? $settings['fade_in'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-setting-row">
                <div class="redco-setting-label">
                    <label for="fade_in_duration"><?php esc_html_e('Fade In Duration (ms)', 'redco-optimizer'); ?></label>
                    <p class="redco-setting-description"><?php esc_html_e('Duration of the fade in effect in milliseconds.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-setting-field">
                    <input type="number" id="fade_in_duration" name="fade_in_duration" value="<?php echo isset($settings['fade_in_duration']) ? intval($settings['fade_in_duration']) : 400; ?>" min="0" max="2000" step="50">
                </div>
            </div>

            <h4 class="redco-settings-section-title"><?php esc_html_e('Media Types', 'redco-optimizer'); ?></h4>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Lazy Load Images', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Apply lazy loading to images in your content.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="lazy_load_images" <?php checked(isset($settings['lazy_load_images']) ? $settings['lazy_load_images'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Lazy Load Iframes', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Apply lazy loading to iframes (like YouTube videos) in your content.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="lazy_load_iframes" <?php checked(isset($settings['lazy_load_iframes']) ? $settings['lazy_load_iframes'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Lazy Load Videos', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Apply lazy loading to HTML5 video elements in your content.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="lazy_load_videos" <?php checked(isset($settings['lazy_load_videos']) ? $settings['lazy_load_videos'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Lazy Load Background Images', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Apply lazy loading to CSS background images.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="lazy_load_backgrounds" <?php checked(isset($settings['lazy_load_backgrounds']) ? $settings['lazy_load_backgrounds'] : 0, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <h4 class="redco-settings-section-title"><?php esc_html_e('Page Locations', 'redco-optimizer'); ?></h4>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Apply to Post Content', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Apply lazy loading to images and media in post and page content.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="apply_to_content" <?php checked(isset($settings['apply_to_content']) ? $settings['apply_to_content'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Apply to Widgets', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Apply lazy loading to images and media in widgets.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="apply_to_widgets" <?php checked(isset($settings['apply_to_widgets']) ? $settings['apply_to_widgets'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Apply to Thumbnails', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Apply lazy loading to post thumbnails.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="apply_to_thumbnails" <?php checked(isset($settings['apply_to_thumbnails']) ? $settings['apply_to_thumbnails'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Apply to Avatars', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Apply lazy loading to user avatars.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="apply_to_avatars" <?php checked(isset($settings['apply_to_avatars']) ? $settings['apply_to_avatars'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <h4 class="redco-settings-section-title"><?php esc_html_e('Advanced Options', 'redco-optimizer'); ?></h4>

            <div class="redco-setting-row">
                <div class="redco-setting-label">
                    <label for="exclusions"><?php esc_html_e('Exclusions', 'redco-optimizer'); ?></label>
                    <p class="redco-setting-description"><?php esc_html_e('Enter CSS selectors to exclude elements from lazy loading (one per line).', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-setting-field">
                    <textarea id="exclusions" name="exclusions" rows="4" placeholder=".no-lazy, .slider img, .hero-image"><?php echo isset($settings['exclusions']) ? esc_textarea($settings['exclusions']) : ''; ?></textarea>
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Native Lazy Loading', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Use browser\'s native lazy loading when available (loading="lazy" attribute).', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="use_native_lazy_loading" <?php checked(isset($settings['use_native_lazy_loading']) ? $settings['use_native_lazy_loading'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Load First "n" Images Immediately', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Load the first few images immediately without lazy loading for better user experience.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="skip_first_images" <?php checked(isset($settings['skip_first_images']) ? $settings['skip_first_images'] : 1, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>

            <div class="redco-setting-row">
                <div class="redco-setting-label">
                    <label for="skip_first_images_count"><?php esc_html_e('Number of Images to Load Immediately', 'redco-optimizer'); ?></label>
                    <p class="redco-setting-description"><?php esc_html_e('How many images should be loaded immediately without lazy loading.', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-setting-field">
                    <input type="number" id="skip_first_images_count" name="skip_first_images_count" value="<?php echo isset($settings['skip_first_images_count']) ? intval($settings['skip_first_images_count']) : 3; ?>" min="1" max="10" step="1">
                </div>
            </div>

            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e('Low-Quality Image Placeholders', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('Show low-quality blurred versions of images while loading (LQIP).', 'redco-optimizer'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="use_lqip" <?php checked(isset($settings['use_lqip']) ? $settings['use_lqip'] : 0, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="redco-form-actions">
        <button type="submit" class="redco-button redco-button-primary"><?php esc_html_e('Save Settings', 'redco-optimizer'); ?></button>
    </div>
</form>
