<?php
/**
 * Help Tab Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="redco-help-container" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
    <div class="redco-help-sidebar">
        <div class="redco-help-search">
            <input type="text" id="redco-help-search-input" placeholder="<?php esc_attr_e('Search help topics...', 'redco-optimizer'); ?>">
        </div>
        <ul class="redco-help-topics">
            <li class="redco-help-topic active" data-topic="getting-started">
                <span class="dashicons dashicons-admin-home"></span>
                <?php esc_html_e('Getting Started', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="dashboard">
                <span class="dashicons dashicons-dashboard"></span>
                <?php esc_html_e('Dashboard', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="caching">
                <span class="dashicons dashicons-superhero"></span>
                <?php esc_html_e('Caching', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="file-optimization">
                <span class="dashicons dashicons-media-code"></span>
                <?php esc_html_e('File Optimization', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="media">
                <span class="dashicons dashicons-format-image"></span>
                <?php esc_html_e('Media', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="preload">
                <span class="dashicons dashicons-performance"></span>
                <?php esc_html_e('Preload', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="database">
                <span class="dashicons dashicons-database"></span>
                <?php esc_html_e('Database', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="heartbeat">
                <span class="dashicons dashicons-heart"></span>
                <?php esc_html_e('Heartbeat', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="cdn">
                <span class="dashicons dashicons-admin-site"></span>
                <?php esc_html_e('CDN', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="site-health">
                <span class="dashicons dashicons-shield"></span>
                <?php esc_html_e('Site Health', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="tools">
                <span class="dashicons dashicons-admin-tools"></span>
                <?php esc_html_e('Tools', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="add-ons">
                <span class="dashicons dashicons-admin-plugins"></span>
                <?php esc_html_e('Add-Ons', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="faq">
                <span class="dashicons dashicons-editor-help"></span>
                <?php esc_html_e('FAQ', 'redco-optimizer'); ?>
            </li>
            <li class="redco-help-topic" data-topic="troubleshooting">
                <span class="dashicons dashicons-warning"></span>
                <?php esc_html_e('Troubleshooting', 'redco-optimizer'); ?>
            </li>
        </ul>
    </div>

    <div class="redco-help-content-container" style="display: block !important; flex: 1 !important; visibility: visible !important; opacity: 1 !important;">
        <div id="redco-help-getting-started" class="redco-help-content active" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
            <?php include(plugin_dir_path(__FILE__) . 'help/getting-started-help.php'); ?>
        </div>

        <div id="redco-help-dashboard" class="redco-help-content">
            <?php include(plugin_dir_path(__FILE__) . 'help/dashboard-help.php'); ?>
        </div>

        <div id="redco-help-caching" class="redco-help-content">
            <?php
            if (file_exists(plugin_dir_path(__FILE__) . 'help/caching-help.php')) {
                include(plugin_dir_path(__FILE__) . 'help/caching-help.php');
            } else {
                echo '<div class="redco-help-placeholder">';
                echo '<h3>' . esc_html__('Caching Documentation', 'redco-optimizer') . '</h3>';
                echo '<p>' . esc_html__('Documentation for this section is being developed. Please check back soon.', 'redco-optimizer') . '</p>';
                echo '</div>';
            }
            ?>
        </div>

        <div id="redco-help-file-optimization" class="redco-help-content">
            <?php
            if (file_exists(plugin_dir_path(__FILE__) . 'help/file-optimization-help.php')) {
                include(plugin_dir_path(__FILE__) . 'help/file-optimization-help.php');
            } else {
                echo '<div class="redco-help-placeholder">';
                echo '<h3>' . esc_html__('File Optimization Documentation', 'redco-optimizer') . '</h3>';
                echo '<p>' . esc_html__('Documentation for this section is being developed. Please check back soon.', 'redco-optimizer') . '</p>';
                echo '</div>';
            }
            ?>
        </div>

        <div id="redco-help-media" class="redco-help-content">
            <?php
            if (file_exists(plugin_dir_path(__FILE__) . 'help/media-help.php')) {
                include(plugin_dir_path(__FILE__) . 'help/media-help.php');
            } else {
                echo '<div class="redco-help-placeholder">';
                echo '<h3>' . esc_html__('Media Documentation', 'redco-optimizer') . '</h3>';
                echo '<p>' . esc_html__('Documentation for this section is being developed. Please check back soon.', 'redco-optimizer') . '</p>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- Additional help content sections for other modules -->

        <div id="redco-help-faq" class="redco-help-content">
            <?php include(plugin_dir_path(__FILE__) . 'help/faq-help.php'); ?>
        </div>

        <div id="redco-help-troubleshooting" class="redco-help-content">
            <?php
            if (file_exists(plugin_dir_path(__FILE__) . 'help/troubleshooting-help.php')) {
                include(plugin_dir_path(__FILE__) . 'help/troubleshooting-help.php');
            } else {
                echo '<div class="redco-help-placeholder">';
                echo '<h3>' . esc_html__('Troubleshooting Guide', 'redco-optimizer') . '</h3>';
                echo '<p>' . esc_html__('Documentation for this section is being developed. Please check back soon.', 'redco-optimizer') . '</p>';
                echo '</div>';
            }
            ?>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Help topic navigation
    $('.redco-help-topic').on('click', function() {
        var topic = $(this).data('topic');

        // Update active topic in sidebar
        $('.redco-help-topic').removeClass('active');
        $(this).addClass('active');

        // Show selected content
        $('.redco-help-content').removeClass('active');
        $('#redco-help-' + topic).addClass('active');
    });

    // Search functionality
    $('#redco-help-search-input').on('keyup', function() {
        var searchTerm = $(this).val().toLowerCase();

        if (searchTerm.length < 3) {
            // Show all sections if search term is too short
            $('.redco-help-section').show();
            return;
        }

        // Hide all sections first
        $('.redco-help-section').hide();

        // Show sections that match the search term
        $('.redco-help-section').each(function() {
            var sectionText = $(this).text().toLowerCase();
            if (sectionText.indexOf(searchTerm) !== -1) {
                $(this).show();

                // Expand the parent content area if it's not already visible
                var parentContent = $(this).closest('.redco-help-content');
                if (!parentContent.hasClass('active')) {
                    $('.redco-help-content').removeClass('active');
                    parentContent.addClass('active');

                    // Update sidebar active state
                    var topicId = parentContent.attr('id').replace('redco-help-', '');
                    $('.redco-help-topic').removeClass('active');
                    $('.redco-help-topic[data-topic="' + topicId + '"]').addClass('active');
                }
            }
        });
    });
});
</script>
