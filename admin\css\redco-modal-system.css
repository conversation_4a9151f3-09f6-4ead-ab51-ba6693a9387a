/**
 * Redco Optimizer - Modal System
 * A clean, modern modal system for Redco Optimizer
 */

/* Modal Overlay */
.redco-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999999;
    padding: 20px;
}

/* Modal Container */
.redco-modal {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    width: 800px;
    max-width: 95%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

/* Modal Header */
.redco-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f8fafc;
}

.redco-modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
}

.redco-modal-close {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.redco-modal-close:hover {
    background-color: #f1f5f9;
    color: #0f172a;
}

.redco-modal-close svg {
    width: 20px;
    height: 20px;
}

/* Modal Content */
.redco-modal-content {
    padding: 25px;
    overflow-y: auto;
    flex: 1;
    max-height: calc(90vh - 140px);
    scrollbar-width: thin;
}

/* Modal Footer */
.redco-modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: #f8fafc;
}

/* Modal Sections */
.redco-modal-section {
    margin-bottom: 30px;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 25px;
}

.redco-modal-section:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.redco-modal-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #0f172a;
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

/* Form Elements */
.redco-form-row {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
}

.redco-form-row:last-child {
    margin-bottom: 0;
}

.redco-form-label {
    flex: 0 0 200px;
    padding-right: 20px;
    margin-bottom: 8px;
}

.redco-form-label label {
    font-weight: 600;
    font-size: 14px;
    color: #334155;
    display: block;
}

@media screen and (max-width: 782px) {
    .redco-form-row {
        flex-direction: column;
    }

    .redco-form-label {
        flex: 0 0 100%;
        padding-right: 0;
    }
}

.redco-form-field {
    flex: 1;
    min-width: 200px;
}

.redco-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    background-color: #fff;
    color: #334155;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.redco-select:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.redco-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    background-color: #fff;
    color: #334155;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.redco-input:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.redco-textarea {
    width: 100%;
    padding: 10px 10px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    background-color: #fff;
    color: #334155;
    font-size: 14px;
    min-height: 100px;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.redco-textarea:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.redco-form-help {
    font-size: 12px;
    color: #64748b;
    margin: 5px 0 0 0;
    display: block;
    width: 100%;
    clear: both;
    order: 3;
    padding-left: 0 !important;
}

/* Toggle Row */
.redco-toggle-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f1f5f9;
}

.redco-toggle-row:last-child {
    border-bottom: none;
}

.redco-toggle-info {
    flex: 1;
    padding-right: 20px;
}

.redco-toggle-info h4 {
    margin: 0 0 5px 0;
    font-size: 15px;
    font-weight: 600;
    color: #334155;
}

.redco-toggle-info p {
    margin: 0;
    font-size: 13px;
    color: #64748b;
    line-height: 1.5;
}

.redco-toggle-control {
    flex-shrink: 0;
}

/* Switch */
.redco-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.redco-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.redco-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.3s;
    border-radius: 24px;
}

.redco-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .redco-slider {
    background-color: #00A66B !important;
}

input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

input:checked + .redco-slider:before {
    transform: translateX(20px);
}

/* Buttons */
.redco-button {
    padding: 10px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.redco-button-primary {
    background-color: #00A66B;
    color: #fff;
    border-color: #00A66B;
}

.redco-button-primary:hover {
    background-color: #00955f;
    border-color: #00955f;
}

.redco-button-secondary {
    background-color: #f1f5f9;
    color: #334155;
    border-color: #e2e8f0;
}

.redco-button-secondary:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
}

/* Loading State */
.redco-modal-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.redco-modal-loading p {
    margin: 15px 0 0 0;
    color: #64748b;
    font-size: 15px;
}

.redco-spin {
    animation: redco-spin 1s infinite linear;
}

@keyframes redco-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
