/**
 * Redco Optimizer - Modal Toggle Fix
 * Ensures all toggle switches in modals are green when checked
 */

/* Force green color for all checked toggle switches in modals */
.redco-modal-content input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-modal-content input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Advanced Lazy Load Settings modal specific fix */
#advanced-lazy-load-settings input:checked + .redco-slider {
    background-color: #00A66B !important;
}

#advanced-lazy-load-settings input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Override any other toggle switch styles */
.redco-switch input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-switch input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Specific fix for WordPress admin modals */
.wp-core-ui .redco-switch input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.wp-core-ui .redco-switch input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Specific fix for the Advanced Lazy Load Settings modal */
body.wp-admin #advanced-lazy-load-settings .redco-switch input:checked + .redco-slider {
    background-color: #00A66B !important;
}

body.wp-admin #advanced-lazy-load-settings .redco-switch input:focus + .redco-slider {
    box-shadow: 0 0 1px #00A66B !important;
}

/* Inline style override */
.redco-slider {
    background-color: #cbd5e1;
}

input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* !important flag to override any other styles */
input[type="checkbox"]:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Direct style for the element */
.redco-slider.redco-round {
    border-radius: 34px;
}

input:checked + .redco-slider.redco-round {
    background-color: #00A66B !important;
}

/* Specific fix for the modal content */
.ui-dialog-content input:checked + .redco-slider,
.ui-dialog input:checked + .redco-slider,
.modal-content input:checked + .redco-slider,
.modal input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for WordPress thickbox */
#TB_window input:checked + .redco-slider,
.thickbox input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for jQuery UI dialog */
.ui-dialog input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for Bootstrap modals */
.modal input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any modal with ID containing 'modal' */
[id*="modal"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with class containing 'modal' */
[class*="modal"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with ID containing 'popup' */
[id*="popup"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with class containing 'popup' */
[class*="popup"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with ID containing 'dialog' */
[id*="dialog"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with class containing 'dialog' */
[class*="dialog"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with ID containing 'lightbox' */
[id*="lightbox"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with class containing 'lightbox' */
[class*="lightbox"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with ID containing 'overlay' */
[id*="overlay"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with class containing 'overlay' */
[class*="overlay"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with ID containing 'settings' */
[id*="settings"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with class containing 'settings' */
[class*="settings"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with ID containing 'options' */
[id*="options"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with class containing 'options' */
[class*="options"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with ID containing 'config' */
[id*="config"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}

/* Specific fix for any element with class containing 'config' */
[class*="config"] input:checked + .redco-slider {
    background-color: #00A66B !important;
}
