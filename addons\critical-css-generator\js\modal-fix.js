/**
 * Critical CSS Generator Modal Fix
 * Ensures all sections in the Critical CSS Generator modal are visible
 */

(function($) {
    'use strict';

    // Run when document is ready
    $(document).ready(function() {
        // Add event listener for the Critical CSS Generator settings button
        $(document).on('click', '.redco-addon-card:contains("Critical CSS Generator") .redco-addon-settings', function() {
            console.log('Critical CSS Generator settings button clicked');

            // Wait for the modal to open
            setTimeout(fixCriticalCssGeneratorModal, 300);
            setTimeout(fixCriticalCssGeneratorModal, 600);
            setTimeout(fixCriticalCssGeneratorModal, 1200);
        });

        // Also listen for AJAX success events
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('admin-ajax.php') !== -1) {
                if (settings.data && settings.data.indexOf('critical-css-generator') !== -1) {
                    console.log('Critical CSS Generator AJAX detected');

                    // Wait for the modal to be populated
                    setTimeout(fixCriticalCssGeneratorModal, 300);
                    setTimeout(fixCriticalCssGeneratorModal, 600);
                    setTimeout(fixCriticalCssGeneratorModal, 1200);
                }
            }
        });
    });

    /**
     * Fix the Critical CSS Generator modal
     */
    function fixCriticalCssGeneratorModal() {
        console.log('Fixing Critical CSS Generator modal');

        // Check if we're in the Critical CSS Generator modal
        if ($('.redco-modal:visible').length > 0 &&
            ($('.redco-modal-header h2:contains("Critical CSS Generator")').length > 0 ||
             $('.redco-modal-content:contains("Critical CSS Settings")').length > 0)) {

            console.log('Critical CSS Generator modal confirmed');

            // Make the modal taller
            $('.redco-modal').css({
                'max-height': '95vh',
                'height': 'auto',
                'width': '800px',
                'max-width': '95%',
                'display': 'flex',
                'flex-direction': 'column',
                'overflow': 'hidden'
            });

            // Make the content area scrollable
            $('.redco-modal-content').css({
                'max-height': 'calc(95vh - 140px)',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
                'flex': '1 1 auto',
                'padding': '25px',
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Check for missing sections
            var expectedSections = [
                'Critical CSS Settings',
                'Generation Settings',
                'Advanced Settings'
            ];

            var missingSections = [];

            expectedSections.forEach(function(section) {
                if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                    missingSections.push(section);
                }
            });

            if (missingSections.length > 0) {
                console.log('Missing sections detected: ' + missingSections.join(', '));

                // Add a debug message to the modal
                if ($('.redco-modal-debug-message').length === 0) {
                    $('<div>')
                        .addClass('redco-modal-debug-message')
                        .html('<div class="redco-modal-debug-warning">' +
                              '<p class="redco-modal-debug-text">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                              '<div class="redco-modal-debug-arrow">↓</div>' +
                              '</div>')
                        .prependTo('.redco-modal-content');
                }

                // Add missing sections
                injectMissingSections(missingSections);
            } else {
                console.log('All sections found');
            }

            // Force all sections to be visible
            $('.redco-modal-section').show().addClass('redco-modal-section-visible');

            // Add a scroll indicator at the bottom
            if ($('.redco-scroll-indicator-bottom').length === 0) {
                $('<div>')
                    .addClass('redco-scroll-indicator-bottom')
                    .html('<div class="redco-modal-scroll-indicator">' +
                          '<p class="redco-modal-scroll-text">End of settings</p>' +
                          '<div class="redco-modal-scroll-reminder">Don\'t forget to save your changes!</div>' +
                          '</div>')
                    .appendTo('.redco-modal-content');
            }
        }
    }

    /**
     * Inject missing sections into the modal
     */
    function injectMissingSections(missingSections) {
        console.log('Injecting missing sections: ' + missingSections.join(', '));

        // Get the modal content and footer
        var $modalContent = $('.redco-modal-content');
        var $footer = $('.redco-modal-footer');

        if ($modalContent.length === 0) {
            console.log('Modal content not found');
            return;
        }

        // Add each missing section
        missingSections.forEach(function(section) {
            console.log('Adding section: ' + section);

            var $section = $('<div class="redco-modal-section"></div>');
            $section.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'margin-bottom': '25px',
                'position': 'relative'
            });

            var $heading = $('<h3>' + section + '</h3>');
            $heading.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            $section.append($heading);

            // Add content based on the section
            if (section === 'Critical CSS Settings') {
                addCriticalCssSettingsContent($section);
            } else if (section === 'Generation Settings') {
                addGenerationSettingsContent($section);
            } else if (section === 'Advanced Settings') {
                addAdvancedSettingsContent($section);
            }

            // Add the section to the modal
            if ($footer.length > 0) {
                $footer.before($section);
            } else {
                $modalContent.append($section);
            }
        });
    }

    /**
     * Add Critical CSS Settings content
     */
    function addCriticalCssSettingsContent($section) {
        $section.append(
            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Enable Critical CSS</h4>' +
                    '<p>Generate and use critical CSS for above-the-fold content.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="enable_critical_css" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Async CSS Loading</h4>' +
                    '<p>Load non-critical CSS asynchronously.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="async_css" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Remove Unused CSS</h4>' +
                    '<p>Remove unused CSS rules from critical CSS.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="remove_unused_css" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Generation Settings content
     */
    function addGenerationSettingsContent($section) {
        $section.append(
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="viewport_width">Viewport Width</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="number" id="viewport_width" name="viewport_width" class="redco-input" value="1366" min="320" max="2560">' +
                    '<p class="redco-form-help">Width of the viewport used for critical CSS generation.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="viewport_height">Viewport Height</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="number" id="viewport_height" name="viewport_height" class="redco-input" value="768" min="320" max="1440">' +
                    '<p class="redco-form-help">Height of the viewport used for critical CSS generation.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Mobile Critical CSS</h4>' +
                    '<p>Generate separate critical CSS for mobile devices.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="mobile_critical_css" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

    /**
     * Add Advanced Settings content
     */
    function addAdvancedSettingsContent($section) {
        $section.append(
            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="critical_css_timeout">Generation Timeout</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<input type="number" id="critical_css_timeout" name="critical_css_timeout" class="redco-input" value="30" min="10" max="120">' +
                    '<p class="redco-form-help">Maximum time in seconds to wait for critical CSS generation.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-form-row">' +
                '<div class="redco-form-label">' +
                    '<label for="css_exclusions">CSS Exclusions</label>' +
                '</div>' +
                '<div class="redco-form-field">' +
                    '<textarea id="css_exclusions" name="css_exclusions" class="redco-textarea" rows="5"></textarea>' +
                    '<p class="redco-form-help">CSS selectors to exclude from critical CSS, one per line.</p>' +
                '</div>' +
            '</div>' +

            '<div class="redco-toggle-row">' +
                '<div class="redco-toggle-info">' +
                    '<h4>Fallback CSS</h4>' +
                    '<p>Use fallback CSS if critical CSS generation fails.</p>' +
                '</div>' +
                '<div class="redco-toggle-control">' +
                    '<label class="redco-switch">' +
                        '<input type="checkbox" name="fallback_css" value="1" checked>' +
                        '<span class="redco-slider"></span>' +
                    '</label>' +
                '</div>' +
            '</div>'
        );
    }

})(jQuery);
