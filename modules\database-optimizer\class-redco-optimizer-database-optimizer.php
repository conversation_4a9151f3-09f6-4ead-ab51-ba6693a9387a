<?php
/**
 * The database optimizer module functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/database-optimizer
 */

/**
 * The database optimizer module functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for the database optimizer module.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/database-optimizer
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Database_Optimizer extends Redco_Optimizer_Module {

    /**
     * Register the hooks for this module.
     *
     * @since    1.0.0
     */
    protected function register_hooks() {
        // Add database optimization hooks
        $this->loader->add_action( 'wp_ajax_redco_optimizer_clean_database', $this, 'ajax_clean_database' );
        $this->loader->add_action( 'wp_ajax_redco_optimizer_optimize_tables', $this, 'ajax_optimize_tables' );
        
        // Add scheduled cleanup hook
        $this->loader->add_action( 'redco_optimizer_scheduled_db_cleanup', $this, 'scheduled_cleanup' );
        
        // Schedule the cleanup event if it's not already scheduled
        if ( ! wp_next_scheduled( 'redco_optimizer_scheduled_db_cleanup' ) ) {
            wp_schedule_event( time(), 'daily', 'redco_optimizer_scheduled_db_cleanup' );
        }
    }

    /**
     * Handle AJAX request to clean the database.
     *
     * @since    1.0.0
     */
    public function ajax_clean_database() {
        // Check nonce for security
        check_ajax_referer( 'redco_optimizer_nonce', 'nonce' );
        
        // Check if user has permission
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to clean the database.', 'redco-optimizer' ) ) );
        }
        
        // Get the cleanup options
        $options = isset( $_POST['options'] ) ? $_POST['options'] : array();
        
        // Perform the cleanup
        $results = $this->clean_database( $options );
        
        wp_send_json_success( array(
            'message' => __( 'Database cleanup completed successfully.', 'redco-optimizer' ),
            'results' => $results
        ) );
    }

    /**
     * Handle AJAX request to optimize database tables.
     *
     * @since    1.0.0
     */
    public function ajax_optimize_tables() {
        // Check nonce for security
        check_ajax_referer( 'redco_optimizer_nonce', 'nonce' );
        
        // Check if user has permission
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to optimize database tables.', 'redco-optimizer' ) ) );
        }
        
        // Get the tables to optimize
        $tables = isset( $_POST['tables'] ) ? $_POST['tables'] : array();
        
        // Optimize the tables
        $results = $this->optimize_tables( $tables );
        
        wp_send_json_success( array(
            'message' => __( 'Database tables optimized successfully.', 'redco-optimizer' ),
            'results' => $results
        ) );
    }

    /**
     * Perform scheduled database cleanup.
     *
     * @since    1.0.0
     */
    public function scheduled_cleanup() {
        // Get the settings
        $settings = get_option( 'redco_optimizer_settings', array() );
        
        // Check if scheduled cleanup is enabled
        if ( ! isset( $settings['scheduled_db_cleanup'] ) || ! $settings['scheduled_db_cleanup'] ) {
            return;
        }
        
        // Get the cleanup options from settings
        $options = array(
            'revisions' => isset( $settings['cleanup_revisions'] ) ? $settings['cleanup_revisions'] : true,
            'auto_drafts' => isset( $settings['cleanup_auto_drafts'] ) ? $settings['cleanup_auto_drafts'] : true,
            'trash' => isset( $settings['cleanup_trash'] ) ? $settings['cleanup_trash'] : true,
            'spam' => isset( $settings['cleanup_spam'] ) ? $settings['cleanup_spam'] : true,
            'transients' => isset( $settings['cleanup_transients'] ) ? $settings['cleanup_transients'] : true,
        );
        
        // Perform the cleanup
        $this->clean_database( $options );
    }

    /**
     * Clean the database.
     *
     * @since    1.0.0
     * @param    array    $options    The cleanup options.
     * @return   array    The cleanup results.
     */
    public function clean_database( $options = array() ) {
        global $wpdb;
        
        $results = array();
        
        // Clean post revisions
        if ( isset( $options['revisions'] ) && $options['revisions'] ) {
            $revisions = $wpdb->query( "DELETE FROM $wpdb->posts WHERE post_type = 'revision'" );
            $results['revisions'] = $revisions;
        }
        
        // Clean auto drafts
        if ( isset( $options['auto_drafts'] ) && $options['auto_drafts'] ) {
            $auto_drafts = $wpdb->query( "DELETE FROM $wpdb->posts WHERE post_status = 'auto-draft'" );
            $results['auto_drafts'] = $auto_drafts;
        }
        
        // Clean trash
        if ( isset( $options['trash'] ) && $options['trash'] ) {
            $trash = $wpdb->query( "DELETE FROM $wpdb->posts WHERE post_status = 'trash'" );
            $results['trash'] = $trash;
        }
        
        // Clean spam comments
        if ( isset( $options['spam'] ) && $options['spam'] ) {
            $spam = $wpdb->query( "DELETE FROM $wpdb->comments WHERE comment_approved = 'spam'" );
            $results['spam'] = $spam;
        }
        
        // Clean transients
        if ( isset( $options['transients'] ) && $options['transients'] ) {
            $transients = $wpdb->query( "DELETE FROM $wpdb->options WHERE option_name LIKE '%_transient_%'" );
            $results['transients'] = $transients;
        }
        
        // Clean orphaned post meta
        if ( isset( $options['orphaned_meta'] ) && $options['orphaned_meta'] ) {
            $orphaned_meta = $wpdb->query( "DELETE pm FROM $wpdb->postmeta pm LEFT JOIN $wpdb->posts p ON p.ID = pm.post_id WHERE p.ID IS NULL" );
            $results['orphaned_meta'] = $orphaned_meta;
        }
        
        // Clean orphaned comment meta
        if ( isset( $options['orphaned_comment_meta'] ) && $options['orphaned_comment_meta'] ) {
            $orphaned_comment_meta = $wpdb->query( "DELETE cm FROM $wpdb->commentmeta cm LEFT JOIN $wpdb->comments c ON c.comment_ID = cm.comment_id WHERE c.comment_ID IS NULL" );
            $results['orphaned_comment_meta'] = $orphaned_comment_meta;
        }
        
        // Clean orphaned user meta
        if ( isset( $options['orphaned_user_meta'] ) && $options['orphaned_user_meta'] ) {
            $orphaned_user_meta = $wpdb->query( "DELETE um FROM $wpdb->usermeta um LEFT JOIN $wpdb->users u ON u.ID = um.user_id WHERE u.ID IS NULL" );
            $results['orphaned_user_meta'] = $orphaned_user_meta;
        }
        
        // Clean orphaned term relationships
        if ( isset( $options['orphaned_term_relationships'] ) && $options['orphaned_term_relationships'] ) {
            $orphaned_term_relationships = $wpdb->query( "DELETE tr FROM $wpdb->term_relationships tr LEFT JOIN $wpdb->posts p ON p.ID = tr.object_id WHERE p.ID IS NULL" );
            $results['orphaned_term_relationships'] = $orphaned_term_relationships;
        }
        
        return $results;
    }

    /**
     * Optimize database tables.
     *
     * @since    1.0.0
     * @param    array    $tables    The tables to optimize. If empty, all tables will be optimized.
     * @return   array    The optimization results.
     */
    public function optimize_tables( $tables = array() ) {
        global $wpdb;
        
        $results = array();
        
        // Get all tables if none specified
        if ( empty( $tables ) ) {
            $tables = $wpdb->get_col( "SHOW TABLES LIKE '{$wpdb->prefix}%'" );
        }
        
        // Optimize each table
        foreach ( $tables as $table ) {
            $result = $wpdb->query( "OPTIMIZE TABLE $table" );
            $results[$table] = $result;
        }
        
        return $results;
    }

    /**
     * Get the module settings HTML.
     *
     * @since    1.0.0
     * @return   string    The module settings HTML.
     */
    public function get_settings_html() {
        $settings = get_option( 'redco_optimizer_settings', array() );
        
        // Cleanup options
        $cleanup_revisions = isset( $settings['cleanup_revisions'] ) ? $settings['cleanup_revisions'] : true;
        $cleanup_auto_drafts = isset( $settings['cleanup_auto_drafts'] ) ? $settings['cleanup_auto_drafts'] : true;
        $cleanup_trash = isset( $settings['cleanup_trash'] ) ? $settings['cleanup_trash'] : true;
        $cleanup_spam = isset( $settings['cleanup_spam'] ) ? $settings['cleanup_spam'] : true;
        $cleanup_transients = isset( $settings['cleanup_transients'] ) ? $settings['cleanup_transients'] : true;
        $cleanup_orphaned_meta = isset( $settings['cleanup_orphaned_meta'] ) ? $settings['cleanup_orphaned_meta'] : true;
        
        // Scheduled cleanup
        $scheduled_db_cleanup = isset( $settings['scheduled_db_cleanup'] ) ? $settings['scheduled_db_cleanup'] : false;
        
        ob_start();
        ?>
        <div class="redco-module-settings-section">
            <h3><?php esc_html_e( 'Database Cleanup Settings', 'redco-optimizer' ); ?></h3>
            
            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="cleanup_revisions" <?php checked( $cleanup_revisions, true ); ?>>
                    <?php esc_html_e( 'Clean Post Revisions', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Delete all post revisions from the database.', 'redco-optimizer' ); ?></p>
            </div>
            
            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="cleanup_auto_drafts" <?php checked( $cleanup_auto_drafts, true ); ?>>
                    <?php esc_html_e( 'Clean Auto Drafts', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Delete all auto drafts from the database.', 'redco-optimizer' ); ?></p>
            </div>
            
            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="cleanup_trash" <?php checked( $cleanup_trash, true ); ?>>
                    <?php esc_html_e( 'Clean Trash', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Delete all trashed posts and pages from the database.', 'redco-optimizer' ); ?></p>
            </div>
            
            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="cleanup_spam" <?php checked( $cleanup_spam, true ); ?>>
                    <?php esc_html_e( 'Clean Spam Comments', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Delete all spam comments from the database.', 'redco-optimizer' ); ?></p>
            </div>
            
            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="cleanup_transients" <?php checked( $cleanup_transients, true ); ?>>
                    <?php esc_html_e( 'Clean Transients', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Delete all transients from the database.', 'redco-optimizer' ); ?></p>
            </div>
            
            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="cleanup_orphaned_meta" <?php checked( $cleanup_orphaned_meta, true ); ?>>
                    <?php esc_html_e( 'Clean Orphaned Meta', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Delete all orphaned post meta, comment meta, and user meta from the database.', 'redco-optimizer' ); ?></p>
            </div>
            
            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="scheduled_db_cleanup" <?php checked( $scheduled_db_cleanup, true ); ?>>
                    <?php esc_html_e( 'Enable Scheduled Cleanup', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Automatically clean the database on a daily basis.', 'redco-optimizer' ); ?></p>
            </div>
            
            <div class="redco-settings-actions">
                <button type="button" class="redco-button" id="redco-clean-database"><?php esc_html_e( 'Clean Database Now', 'redco-optimizer' ); ?></button>
                <button type="button" class="redco-button redco-button-secondary" id="redco-optimize-tables"><?php esc_html_e( 'Optimize Tables', 'redco-optimizer' ); ?></button>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
