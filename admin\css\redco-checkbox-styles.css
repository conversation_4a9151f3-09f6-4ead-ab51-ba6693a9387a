/**
 * Redco Optimizer - Checkbox Styles
 * Ensures all checkboxes have green color when checked
 */

/* Custom checkbox styling */
input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e1;
    border-radius: 3px;
    outline: none;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
    margin-right: 8px;
    background-color: #fff;
}

/* Checked state */
input[type="checkbox"]:checked {
    background-color: #00A66B;
    border-color: #00A66B;
}

/* Checkmark */
input[type="checkbox"]:checked::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Focus state */
input[type="checkbox"]:focus {
    box-shadow: 0 0 0 2px rgba(0, 166, 107, 0.2);
}

/* Hover state */
input[type="checkbox"]:hover {
    border-color: #00A66B;
}

/* Disabled state */
input[type="checkbox"]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f0f0f1;
    border-color: #dcdcde;
}

/* Disabled and checked state */
input[type="checkbox"]:disabled:checked {
    background-color: #8c8f94;
    border-color: #8c8f94;
}

/* Specific styles for redco-checkbox class */
.redco-checkbox input[type="checkbox"] {
    margin-right: 10px;
}

/* Ensure compatibility with WordPress admin */
.wp-admin input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e1;
    border-radius: 3px;
    outline: none;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
    margin-right: 8px;
    background-color: #fff;
}

.wp-admin input[type="checkbox"]:checked {
    background-color: #00A66B;
    border-color: #00A66B;
}

.wp-admin input[type="checkbox"]:checked::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    margin: 0;
    float: none;
    font-size: 0;
    line-height: 0;
}

/* Override WordPress default checkmark */
.wp-admin input[type="checkbox"]:checked::before {
    color: transparent;
    content: '';
    width: 6px;
    height: 10px;
}

/* Ensure compatibility with specific plugin elements */
.redco-checkbox-group input[type="checkbox"],
.redco-form input[type="checkbox"],
.redco-card-content input[type="checkbox"],
.redco-tab-content input[type="checkbox"],
.redco-checkbox-item input[type="checkbox"],
.redco-expandable-content input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e1;
    border-radius: 3px;
    outline: none;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
    margin-right: 10px;
    background-color: #fff;
}

.redco-checkbox-group input[type="checkbox"]:checked,
.redco-form input[type="checkbox"]:checked,
.redco-card-content input[type="checkbox"]:checked,
.redco-tab-content input[type="checkbox"]:checked,
.redco-checkbox-item input[type="checkbox"]:checked,
.redco-expandable-content input[type="checkbox"]:checked {
    background-color: #00A66B;
    border-color: #00A66B;
}

.redco-checkbox-group input[type="checkbox"]:checked::before,
.redco-form input[type="checkbox"]:checked::before,
.redco-card-content input[type="checkbox"]:checked::before,
.redco-tab-content input[type="checkbox"]:checked::before,
.redco-checkbox-item input[type="checkbox"]:checked::before,
.redco-expandable-content input[type="checkbox"]:checked::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Specific styles for expandable sections */
.redco-expandable-content .redco-checkbox-item {
    padding: 4px 0;
}

.redco-expandable-content .redco-checkbox-text {
    font-size: 14px;
    font-weight: 400;
    color: #333;
}
