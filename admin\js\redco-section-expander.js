/**
 * JavaScript for handling expandable sections in all tabs
 *
 * This script makes all card sections expandable and collapsable
 */
jQuery(document).ready(function($) {
    // Initialize expandable sections
    initExpandableSections();

    // Function to initialize expandable sections
    function initExpandableSections() {
        // Make all card sections expandable
        makeAllSectionsExpandable();
        
        // Add click handler for expandable headers
        addExpandableHeaderClickHandlers();
        
        // Collapse all sections except the first one in each tab
        collapseAllSectionsExceptFirst();
    }
    
    // Make all card sections expandable
    function makeAllSectionsExpandable() {
        // Add expandable class to all cards that don't have it
        $('.redco-card:not(.redco-expandable-section)').each(function() {
            const $card = $(this);
            const $header = $card.find('.redco-card-header');
            const $content = $card.find('.redco-card-content');
            
            // Skip cards that don't have both header and content
            if (!$header.length || !$content.length) {
                return;
            }
            
            // Add expandable classes
            $card.addClass('redco-expandable-section');
            $header.addClass('redco-expandable-header');
            $content.addClass('redco-expandable-content');
            
            // Add dashicon to header if it doesn't have one
            if (!$header.find('.dashicons').length) {
                const $title = $header.find('h3');
                if ($title.length) {
                    $title.html('<span class="dashicons dashicons-admin-generic" style="color: #646970; margin-right: 10px;"></span>' + $title.html());
                }
            }
            
            // Add toggle icon if it doesn't exist
            if (!$header.find('.redco-expandable-toggle').length) {
                $header.append('<span class="redco-expandable-toggle dashicons dashicons-arrow-down-alt2"></span>');
            }
        });
    }
    
    // Add click handler for expandable headers
    function addExpandableHeaderClickHandlers() {
        $('.redco-expandable-header').off('click').on('click', function() {
            const $section = $(this).closest('.redco-expandable-section');
            const $content = $section.find('.redco-expandable-content');
            
            // Toggle active class
            $section.toggleClass('active');
            
            // Toggle content visibility
            if ($section.hasClass('active')) {
                $content.css({
                    'display': 'block',
                    'visibility': 'visible'
                });
            } else {
                $content.css({
                    'display': 'none',
                    'visibility': 'hidden'
                });
            }
        });
    }
    
    // Collapse all sections except the first one in each tab
    function collapseAllSectionsExceptFirst() {
        $('.redco-tab-content').each(function() {
            const $tab = $(this);
            const $sections = $tab.find('.redco-expandable-section');
            
            // Collapse all sections
            $sections.removeClass('active');
            $sections.find('.redco-expandable-content').css({
                'display': 'none',
                'visibility': 'hidden'
            });
            
            // Expand the first section
            const $firstSection = $sections.first();
            $firstSection.addClass('active');
            $firstSection.find('.redco-expandable-content').css({
                'display': 'block',
                'visibility': 'visible'
            });
        });
    }
    
    // Re-initialize when tab changes
    $('.redco-nav-item').on('click', function() {
        // Wait for tab content to be visible
        setTimeout(function() {
            // Re-initialize expandable sections
            initExpandableSections();
        }, 300);
    });
});
