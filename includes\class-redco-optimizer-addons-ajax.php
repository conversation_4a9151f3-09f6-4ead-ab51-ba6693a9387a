<?php
/**
 * The file that defines the AJAX handlers for add-ons
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The class that defines the AJAX handlers for add-ons
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Addons_Ajax {

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_redco_optimizer_activate_addon', array($this, 'activate_addon'));
        add_action('wp_ajax_redco_optimizer_deactivate_addon', array($this, 'deactivate_addon'));
        add_action('wp_ajax_redco_optimizer_install_addon', array($this, 'install_addon'));
        add_action('wp_ajax_redco_optimizer_uninstall_addon', array($this, 'uninstall_addon'));
        add_action('wp_ajax_redco_optimizer_refresh_addons', array($this, 'refresh_addons'));
        add_action('wp_ajax_redco_optimizer_load_addon_settings', array($this, 'load_addon_settings'));
        add_action('wp_ajax_redco_optimizer_save_addon_settings', array($this, 'save_addon_settings'));

        // New modal system handlers
        add_action('wp_ajax_redco_get_addon_settings', array($this, 'get_addon_settings'));
        add_action('wp_ajax_redco_save_addon_settings', array($this, 'save_addon_settings_new'));
    }

    /**
     * Handle AJAX request to activate an add-on.
     *
     * @since    1.0.0
     */
    public function activate_addon() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-on slug
        $addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';

        if (empty($addon_slug)) {
            wp_send_json_error(array(
                'message' => __('Add-on slug is required.', 'redco-optimizer')
            ));
        }

        // Get the add-ons instance
        global $redco_optimizer_addons;

        if (!isset($redco_optimizer_addons)) {
            $redco_optimizer_addons = new Redco_Optimizer_Addons();
        }

        // Get the add-on data before activation
        $addons = $redco_optimizer_addons->get_installed_addons();
        $addon = isset($addons[$addon_slug]) ? $addons[$addon_slug] : array();

        // Check if this is a premium feature
        if (isset($addon['premium']) && $addon['premium'] && !redco_is_premium()) {
            wp_send_json_error(array(
                'message' => __('This add-on requires a premium license.', 'redco-optimizer'),
                'premium' => true
            ));
            return;
        }

        // Activate the add-on
        $result = $redco_optimizer_addons->activate_addon($addon_slug);

        if (is_wp_error($result)) {
            // Check if this is a premium feature
            if ($result->get_error_code() === 'premium_required') {
                wp_send_json_error(array(
                    'message' => $result->get_error_message(),
                    'premium' => true
                ));
            } else {
                wp_send_json_error(array(
                    'message' => $result->get_error_message()
                ));
            }
        } else {
            // Get the updated add-on data
            $addons = $redco_optimizer_addons->get_installed_addons();
            $addon = isset($addons[$addon_slug]) ? $addons[$addon_slug] : array();

            // Check if the add-on has settings
            $has_settings = isset($addon['has_settings']) && $addon['has_settings'];

            wp_send_json_success(array(
                'message' => sprintf(__('Add-on "%s" has been activated successfully.', 'redco-optimizer'), $addon['name']),
                'has_settings' => $has_settings,
                'settings_url' => admin_url('admin.php?page=redco-optimizer&tab=addons&addon=' . $addon_slug),
                'reload' => true // Reload the page to ensure all add-on functionality is loaded
            ));
        }
    }

    /**
     * Handle AJAX request to deactivate an add-on.
     *
     * @since    1.0.0
     */
    public function deactivate_addon() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-on slug
        $addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';

        if (empty($addon_slug)) {
            wp_send_json_error(array(
                'message' => __('Add-on slug is required.', 'redco-optimizer')
            ));
        }

        // Get the add-ons instance
        global $redco_optimizer_addons;

        if (!isset($redco_optimizer_addons)) {
            $redco_optimizer_addons = new Redco_Optimizer_Addons();
        }

        // Get the add-on data before deactivation
        $addons = $redco_optimizer_addons->get_installed_addons();
        $addon = isset($addons[$addon_slug]) ? $addons[$addon_slug] : array();

        if (empty($addon)) {
            wp_send_json_error(array(
                'message' => __('Add-on not found.', 'redco-optimizer')
            ));
            return;
        }

        // Deactivate the add-on
        $result = $redco_optimizer_addons->deactivate_addon($addon_slug);

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => sprintf(__('Add-on "%s" has been deactivated successfully.', 'redco-optimizer'), $addon['name']),
                'reload' => true // Reload the page to ensure all add-on functionality is unloaded
            ));
        }
    }

    /**
     * Handle AJAX request to install an add-on.
     *
     * @since    1.0.0
     */
    public function install_addon() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-on slug
        $addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';

        if (empty($addon_slug)) {
            wp_send_json_error(array(
                'message' => __('Add-on slug is required.', 'redco-optimizer')
            ));
        }

        // Get the add-ons instance
        global $redco_optimizer_addons;

        if (!isset($redco_optimizer_addons)) {
            $redco_optimizer_addons = new Redco_Optimizer_Addons();
        }

        // Install the add-on
        $result = $redco_optimizer_addons->install_addon($addon_slug);

        if (is_wp_error($result)) {
            // Check if this is a premium feature
            if ($result->get_error_code() === 'premium_required') {
                wp_send_json_error(array(
                    'message' => $result->get_error_message(),
                    'premium' => true
                ));
            } else {
                wp_send_json_error(array(
                    'message' => $result->get_error_message()
                ));
            }
        } else {
            // Get the add-on data
            $addons = $redco_optimizer_addons->get_installed_addons();
            $addon = isset($addons[$addon_slug]) ? $addons[$addon_slug] : array();

            // Return success
            wp_send_json_success(array(
                'message' => sprintf(__('Add-on "%s" has been installed successfully.', 'redco-optimizer'), $addon['name']),
                'reload' => true
            ));
        }
    }

    /**
     * Handle AJAX request to uninstall an add-on.
     *
     * @since    1.0.0
     */
    public function uninstall_addon() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-on slug
        $addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';

        if (empty($addon_slug)) {
            wp_send_json_error(array(
                'message' => __('Add-on slug is required.', 'redco-optimizer')
            ));
        }

        // Get the add-ons instance
        global $redco_optimizer_addons;

        if (!isset($redco_optimizer_addons)) {
            $redco_optimizer_addons = new Redco_Optimizer_Addons();
        }

        // Get the add-on data before uninstallation
        $addons = $redco_optimizer_addons->get_installed_addons();
        $addon = isset($addons[$addon_slug]) ? $addons[$addon_slug] : array();

        if (empty($addon)) {
            wp_send_json_error(array(
                'message' => __('Add-on not found.', 'redco-optimizer')
            ));
            return;
        }

        // Check if the add-on is active
        if (isset($addon['active']) && $addon['active']) {
            wp_send_json_error(array(
                'message' => __('Please deactivate the add-on before uninstalling.', 'redco-optimizer')
            ));
            return;
        }

        // Log for debugging
        error_log('Redco Optimizer - Uninstalling add-on: ' . $addon_slug);

        // Uninstall the add-on
        $result = $redco_optimizer_addons->uninstall_addon($addon_slug);

        if (is_wp_error($result)) {
            error_log('Redco Optimizer - Error uninstalling add-on: ' . $result->get_error_message());
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            error_log('Redco Optimizer - Add-on uninstalled successfully: ' . $addon_slug);
            // Clear any transients or caches that might be storing add-on data
            delete_transient('redco_optimizer_addons_list');

            // Force refresh of available add-ons
            $redco_optimizer_addons->refresh_addons();

            wp_send_json_success(array(
                'message' => sprintf(__('Add-on "%s" has been uninstalled successfully.', 'redco-optimizer'), $addon['name']),
                'reload' => true, // Reload the page to update the UI
                'redirect_url' => admin_url('admin.php?page=redco-optimizer&tab=addons&t=' . time()) // Ensure we redirect to the add-ons tab
            ));
        }
    }

    /**
     * Handle AJAX request to refresh add-ons.
     *
     * @since    1.0.0
     */
    public function refresh_addons() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-ons instance
        global $redco_optimizer_addons;

        if (!isset($redco_optimizer_addons)) {
            $redco_optimizer_addons = new Redco_Optimizer_Addons();
        }

        // Log for debugging
        error_log('Redco Optimizer - Refreshing add-ons');

        // Clear any transients or caches that might be storing add-on data
        delete_transient('redco_optimizer_addons_list');

        // Log the refresh operation
        error_log('Redco Optimizer - Refreshing add-ons and clearing cache');

        // Refresh the add-ons
        $result = $redco_optimizer_addons->refresh_addons();

        if ($result) {
            error_log('Redco Optimizer - Add-ons refreshed successfully');
            // Return success
            wp_send_json_success(array(
                'message' => __('Add-ons have been refreshed successfully.', 'redco-optimizer'),
                'reload' => true,
                'redirect_url' => admin_url('admin.php?page=redco-optimizer&tab=addons&t=' . time()) // Ensure we redirect to the add-ons tab
            ));
        } else {
            error_log('Redco Optimizer - Failed to refresh add-ons');
            // Return error
            wp_send_json_error(array(
                'message' => __('Failed to refresh add-ons. Please try again.', 'redco-optimizer')
            ));
        }
    }

    /**
     * Handle AJAX request to load add-on settings.
     *
     * @since    1.0.0
     */
    public function load_addon_settings() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-on slug
        $addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';

        if (empty($addon_slug)) {
            wp_send_json_error(array(
                'message' => __('Invalid add-on.', 'redco-optimizer')
            ));
        }

        // Get the add-ons instance
        global $redco_optimizer_addons;

        if (!isset($redco_optimizer_addons)) {
            $redco_optimizer_addons = new Redco_Optimizer_Addons();
        }

        // Get the installed add-ons
        $addons = $redco_optimizer_addons->get_installed_addons();

        // Check if the add-on exists
        if (!isset($addons[$addon_slug])) {
            wp_send_json_error(array(
                'message' => __('Add-on not found.', 'redco-optimizer')
            ));
        }

        // Check if the add-on is active
        if (!isset($addons[$addon_slug]['active']) || !$addons[$addon_slug]['active']) {
            wp_send_json_error(array(
                'message' => __('Add-on is not active.', 'redco-optimizer')
            ));
        }

        // Get the add-on settings template
        $addon_dir = plugin_dir_path(dirname(dirname(__FILE__))) . 'addons/' . $addon_slug;

        // Debug log
        error_log('Redco Optimizer - Add-on directory: ' . $addon_dir);
        error_log('Redco Optimizer - Add-on directory exists: ' . (file_exists($addon_dir) ? 'Yes' : 'No'));

        // First, try to load the modal template
        $modal_template_file = $addon_dir . '/templates/settings-modal.php';
        error_log('Redco Optimizer - Modal template file: ' . $modal_template_file);
        error_log('Redco Optimizer - Modal template exists: ' . (file_exists($modal_template_file) ? 'Yes' : 'No'));

        // If the modal template doesn't exist, try to load the regular settings template
        if (!file_exists($modal_template_file)) {
            $settings_template_file = $addon_dir . '/templates/settings.php';
            error_log('Redco Optimizer - Settings template file: ' . $settings_template_file);
            error_log('Redco Optimizer - Settings template exists: ' . (file_exists($settings_template_file) ? 'Yes' : 'No'));

            if (file_exists($settings_template_file)) {
                error_log('Redco Optimizer - Using settings template instead of modal template');
                $modal_template_file = $settings_template_file;
            }
        }

        // If modal template doesn't exist, fall back to the regular template
        if (!file_exists($modal_template_file)) {
            $modal_template_file = $addon_dir . '/templates/settings.php';
            error_log('Redco Optimizer - Regular template file: ' . $modal_template_file);
            error_log('Redco Optimizer - Regular template exists: ' . (file_exists($modal_template_file) ? 'Yes' : 'No'));
        }

        // Check if templates directory exists
        $templates_dir = $addon_dir . '/templates';
        if (!file_exists($templates_dir)) {
            error_log('Redco Optimizer - Templates directory does not exist: ' . $templates_dir);

            // Create templates directory
            wp_mkdir_p($templates_dir);
            error_log('Redco Optimizer - Created templates directory: ' . $templates_dir);
        }

        // Create default settings template if it doesn't exist
        $settings_file = $templates_dir . '/settings.php';
        if (!file_exists($settings_file)) {
            error_log('Redco Optimizer - Settings template does not exist: ' . $settings_file);
            $settings_content = $this->get_default_settings_template($addon_slug, $addons[$addon_slug]);
            file_put_contents($settings_file, $settings_content);
            error_log('Redco Optimizer - Created settings template: ' . $settings_file);
        }

        // Create default settings modal template if it doesn't exist
        $settings_modal_file = $templates_dir . '/settings-modal.php';
        if (!file_exists($settings_modal_file)) {
            error_log('Redco Optimizer - Settings modal template does not exist: ' . $settings_modal_file);
            $settings_modal_content = $this->get_default_settings_modal_template($addon_slug, $addons[$addon_slug]);
            file_put_contents($settings_modal_file, $settings_modal_content);
            error_log('Redco Optimizer - Created settings modal template: ' . $settings_modal_file);
        }

        // Update modal template file
        $modal_template_file = $settings_modal_file;

        if (!file_exists($modal_template_file)) {
            wp_send_json_error(array(
                'message' => __('Settings template not found.', 'redco-optimizer')
            ));
        }

        // Set the template file to use
        $template_file = $modal_template_file;

        // Get the addon settings
        $option_name = 'redco_' . str_replace('-', '_', $addon_slug) . '_settings';
        $settings = get_option($option_name, array());

        // Get the addon status if applicable
        $status_option_name = 'redco_' . str_replace('-', '_', $addon_slug) . '_status';
        $status = get_option($status_option_name, array());

        // Debug log
        error_log('Redco Optimizer - Template file to include: ' . $template_file);
        error_log('Redco Optimizer - Template file size: ' . filesize($template_file) . ' bytes');
        error_log('Redco Optimizer - Template file first 100 chars: ' . substr(file_get_contents($template_file), 0, 100));

        // Start output buffering
        ob_start();

        // Include the template
        include $template_file;

        // Get the output
        $content = ob_get_clean();

        // Debug log
        error_log('Redco Optimizer - Content length: ' . strlen($content));
        error_log('Redco Optimizer - Content first 100 chars: ' . substr($content, 0, 100));
        error_log('Redco Optimizer - Content contains toggle rows: ' . (strpos($content, 'redco-toggle-row') !== false ? 'Yes' : 'No'));
        error_log('Redco Optimizer - Content contains settings sections: ' . (strpos($content, 'redco-settings-section-title') !== false ? 'Yes' : 'No'));

        // Return the settings content
        wp_send_json_success(array(
            'title' => isset($addons[$addon_slug]['name']) ? $addons[$addon_slug]['name'] . ' ' . __('Settings', 'redco-optimizer') : __('Add-on Settings', 'redco-optimizer'),
            'content' => $content,
            'debug' => array(
                'template_file' => $template_file,
                'template_exists' => file_exists($template_file),
                'template_size' => filesize($template_file),
                'content_length' => strlen($content),
                'has_toggle_rows' => strpos($content, 'redco-toggle-row') !== false,
                'has_section_titles' => strpos($content, 'redco-settings-section-title') !== false
            )
        ));
    }

    /**
     * Handle AJAX request to get add-on settings for the new modal system.
     *
     * @since    1.0.0
     */
    public function get_addon_settings() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-on slug
        $addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';
        error_log('Redco Optimizer - Getting settings for add-on: ' . $addon_slug);

        if (empty($addon_slug)) {
            wp_send_json_error(array(
                'message' => __('Invalid add-on.', 'redco-optimizer')
            ));
        }

        // Get Redco Optimizer Addons instance
        global $redco_optimizer_addons;
        if (!isset($redco_optimizer_addons)) {
            if (class_exists('Redco_Optimizer_Addons')) {
                $redco_optimizer_addons = new Redco_Optimizer_Addons();
                error_log('Redco Optimizer - Created new Redco_Optimizer_Addons instance');
            } else {
                error_log('Redco Optimizer - Redco_Optimizer_Addons class not found');
                wp_send_json_error(array(
                    'message' => __('Add-ons system not available.', 'redco-optimizer')
                ));
                return;
            }
        }

        // Get the installed add-ons
        $addons = $redco_optimizer_addons->get_installed_addons();
        error_log('Redco Optimizer - Installed add-ons: ' . print_r(array_keys($addons), true));

        // Check if the add-on exists
        if (!isset($addons[$addon_slug])) {
            error_log('Redco Optimizer - Add-on not found in installed add-ons: ' . $addon_slug);

            // Try to generate a default settings modal
            ob_start();
            include_once REDCO_OPTIMIZER_PLUGIN_PATH . 'admin/partials/redco-optimizer-admin-addon-default-modal.php';
            $html = ob_get_clean();

            wp_send_json_success(array('html' => $html));
            return;
        }

        // Get the add-on instance
        $addon_instance = $this->get_addon_instance($addon_slug);

        if (!$addon_instance) {
            error_log('Redco Optimizer - Failed to get add-on instance: ' . $addon_slug);

            // Try to generate a default settings modal
            ob_start();
            include_once REDCO_OPTIMIZER_PLUGIN_PATH . 'admin/partials/redco-optimizer-admin-addon-default-modal.php';
            $html = ob_get_clean();

            wp_send_json_success(array('html' => $html));
            return;
        }

        // Check if the add-on has a get_settings_modal method
        if (method_exists($addon_instance, 'get_settings_modal')) {
            error_log('Redco Optimizer - Add-on has get_settings_modal method: ' . $addon_slug);
            // Call the add-on's get_settings_modal method
            $addon_instance->get_settings_modal();
            return;
        }

        error_log('Redco Optimizer - Add-on does not have get_settings_modal method: ' . $addon_slug);

        // Try to load the settings from the template files
        $addon_dir = plugin_dir_path(dirname(dirname(__FILE__))) . 'addons/' . $addon_slug;

        // First, try to load the modal template
        $modal_template_file = $addon_dir . '/templates/settings-modal.php';
        error_log('Redco Optimizer - Modal template file: ' . $modal_template_file);
        error_log('Redco Optimizer - Modal template exists: ' . (file_exists($modal_template_file) ? 'Yes' : 'No'));

        // If the modal template doesn't exist, try to load the regular settings template
        if (!file_exists($modal_template_file)) {
            $settings_template_file = $addon_dir . '/templates/settings.php';
            error_log('Redco Optimizer - Settings template file: ' . $settings_template_file);
            error_log('Redco Optimizer - Settings template exists: ' . (file_exists($settings_template_file) ? 'Yes' : 'No'));

            if (file_exists($settings_template_file)) {
                error_log('Redco Optimizer - Using settings template instead of modal template');
                $modal_template_file = $settings_template_file;
            }
        }

        if (file_exists($modal_template_file)) {
            error_log('Redco Optimizer - Loading settings from template file: ' . $modal_template_file);

            // Get the add-on settings
            $settings = isset($addon_instance->settings) ? $addon_instance->settings : array();

            // Start output buffer
            ob_start();

            // Include the template file
            include $modal_template_file;

            // Get the output
            $html = ob_get_clean();

            // Add inline styles to ensure visibility
            $html .= '<style>
                .redco-modal-section, .redco-form-row, .redco-toggle-row, .redco-form-field,
                .redco-form-label, .redco-toggle-info, .redco-toggle-control,
                .redco-input, .redco-select, .redco-textarea, .redco-modal-footer {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }
                .redco-modal-section {
                    margin-bottom: 25px !important;
                }
                .redco-form-row, .redco-toggle-row {
                    margin-bottom: 15px !important;
                }
                .redco-toggle-row {
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                }
                .redco-modal-footer {
                    display: flex !important;
                    justify-content: flex-end !important;
                    gap: 10px !important;
                    padding: 20px !important;
                    border-top: 1px solid var(--border-color) !important;
                }
            </style>';

            // Send the response
            wp_send_json_success(array('html' => $html));
            return;
        }

        // If we get here, we couldn't find a template file
        error_log('Redco Optimizer - Could not find a template file for add-on: ' . $addon_slug);

        // Try to generate a default settings modal
        ob_start();
        include_once REDCO_OPTIMIZER_PLUGIN_PATH . 'admin/partials/redco-optimizer-admin-addon-default-modal.php';
        $html = ob_get_clean();

        wp_send_json_success(array('html' => $html));
    }

    /**
     * Handle AJAX request to save add-on settings for the new modal system.
     *
     * @since    1.0.0
     */
    public function save_addon_settings_new() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-on slug
        $addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';
        error_log('Redco Optimizer - Saving settings for add-on: ' . $addon_slug);

        if (empty($addon_slug)) {
            wp_send_json_error(array(
                'message' => __('Invalid add-on.', 'redco-optimizer')
            ));
        }

        // Get the form data
        $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : '';
        error_log('Redco Optimizer - Form data: ' . $form_data);

        // Parse the form data
        parse_str($form_data, $settings);
        error_log('Redco Optimizer - Parsed settings: ' . print_r($settings, true));

        // Get Redco Optimizer Addons instance
        global $redco_optimizer_addons;
        if (!isset($redco_optimizer_addons)) {
            if (class_exists('Redco_Optimizer_Addons')) {
                $redco_optimizer_addons = new Redco_Optimizer_Addons();
                error_log('Redco Optimizer - Created new Redco_Optimizer_Addons instance');
            } else {
                error_log('Redco Optimizer - Redco_Optimizer_Addons class not found');
                wp_send_json_error(array(
                    'message' => __('Add-ons system not available.', 'redco-optimizer')
                ));
                return;
            }
        }

        // Get the installed add-ons
        $addons = $redco_optimizer_addons->get_installed_addons();
        error_log('Redco Optimizer - Installed add-ons: ' . print_r(array_keys($addons), true));

        // Check if the add-on exists
        if (!isset($addons[$addon_slug])) {
            error_log('Redco Optimizer - Add-on not found in installed add-ons: ' . $addon_slug);

            // Try to save settings using default method
            $option_name = 'redco_' . str_replace('-', '_', $addon_slug) . '_settings';

            // Ensure checkbox fields are properly handled
            foreach ($settings as $key => $value) {
                // Convert string '0' and '1' to integers for checkboxes
                if ($value === '0' || $value === '1') {
                    $settings[$key] = (int) $value;
                }
            }

            // Save settings
            $result = update_option($option_name, $settings);
            error_log('Redco Optimizer - Settings saved via default method: ' . $option_name);
            error_log('Redco Optimizer - Update result: ' . ($result ? 'true' : 'false'));

            // Verify the settings were saved correctly
            $saved_settings = get_option($option_name, array());
            error_log('Redco Optimizer - Saved settings: ' . print_r($saved_settings, true));

            wp_send_json_success(array(
                'message' => __('Settings saved successfully.', 'redco-optimizer'),
                'refresh' => true
            ));
            return;
        }

        // Get the add-on instance
        $addon_instance = $this->get_addon_instance($addon_slug);

        if (!$addon_instance) {
            error_log('Redco Optimizer - Failed to get add-on instance: ' . $addon_slug);

            // Try to save settings using default method
            $option_name = 'redco_' . str_replace('-', '_', $addon_slug) . '_settings';
            $result = update_option($option_name, $settings);
            error_log('Redco Optimizer - Settings saved via default method: ' . $option_name);
            error_log('Redco Optimizer - Update result: ' . ($result ? 'true' : 'false'));

            // Verify the settings were saved correctly
            $saved_settings = get_option($option_name, array());
            error_log('Redco Optimizer - Saved settings: ' . print_r($saved_settings, true));

            wp_send_json_success(array(
                'message' => __('Settings saved successfully.', 'redco-optimizer'),
                'refresh' => true
            ));
            return;
        }

        // Check if the add-on has a save_settings_ajax method
        if (method_exists($addon_instance, 'save_settings_ajax')) {
            error_log('Redco Optimizer - Add-on has save_settings_ajax method: ' . $addon_slug);
            $result = $addon_instance->save_settings_ajax($settings);

            if (is_wp_error($result)) {
                error_log('Redco Optimizer - Error saving settings: ' . $result->get_error_message());
                wp_send_json_error(array(
                    'message' => $result->get_error_message()
                ));
            } else {
                error_log('Redco Optimizer - Settings saved successfully');
                wp_send_json_success(array(
                    'message' => __('Settings saved successfully.', 'redco-optimizer'),
                    'refresh' => true
                ));
            }
        } else {
            error_log('Redco Optimizer - Add-on does not have save_settings_ajax method: ' . $addon_slug);

            // Try to save settings using default method
            $option_name = 'redco_' . str_replace('-', '_', $addon_slug) . '_settings';

            // Ensure checkbox fields are properly handled
            foreach ($settings as $key => $value) {
                // Convert string '0' and '1' to integers for checkboxes
                if ($value === '0' || $value === '1') {
                    $settings[$key] = (int) $value;
                }
            }

            // Save settings
            $result = update_option($option_name, $settings);
            error_log('Redco Optimizer - Settings saved via default method: ' . $option_name);
            error_log('Redco Optimizer - Update result: ' . ($result ? 'true' : 'false'));

            // Verify the settings were saved correctly
            $saved_settings = get_option($option_name, array());
            error_log('Redco Optimizer - Saved settings: ' . print_r($saved_settings, true));

            wp_send_json_success(array(
                'message' => __('Settings saved successfully.', 'redco-optimizer'),
                'refresh' => true
            ));
        }
    }

    /**
     * Handle AJAX request to save add-on settings.
     *
     * @since    1.0.0
     */
    public function save_addon_settings() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'redco-optimizer')
            ));
        }

        // Get the add-on slug
        $addon_slug = isset($_POST['addon']) ? sanitize_text_field($_POST['addon']) : '';

        if (empty($addon_slug)) {
            wp_send_json_error(array(
                'message' => __('Invalid add-on.', 'redco-optimizer')
            ));
        }

        // Get the form data
        $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : '';

        if (empty($form_data)) {
            wp_send_json_error(array(
                'message' => __('No settings data provided.', 'redco-optimizer')
            ));
        }

        // Debug log
        error_log('Redco Optimizer - Saving add-on settings for: ' . $addon_slug);
        error_log('Redco Optimizer - Form data: ' . $form_data);

        // Parse the form data
        parse_str($form_data, $settings);

        // Process settings to ensure proper data types
        foreach ($settings as $key => $value) {
            // Convert string '0' and '1' to integers for checkboxes
            if ($value === '0' || $value === '1') {
                $settings[$key] = (int) $value;
                error_log('Redco Optimizer - Converted ' . $key . ' to integer: ' . $settings[$key]);
            }
            // Convert numeric strings to integers
            elseif (is_string($value) && is_numeric($value) && strpos($value, '.') === false) {
                $settings[$key] = (int) $value;
                error_log('Redco Optimizer - Converted numeric string ' . $key . ' to integer: ' . $settings[$key]);
            }
            // Convert numeric strings with decimal points to floats
            elseif (is_string($value) && is_numeric($value) && strpos($value, '.') !== false) {
                $settings[$key] = (float) $value;
                error_log('Redco Optimizer - Converted numeric string ' . $key . ' to float: ' . $settings[$key]);
            }
        }

        // Ensure all checkbox fields are properly handled for specific add-ons
        $checkbox_fields = $this->get_checkbox_fields_for_addon($addon_slug);
        foreach ($checkbox_fields as $field) {
            if (!isset($settings[$field])) {
                $settings[$field] = 0;
                error_log('Redco Optimizer - Added missing checkbox field ' . $field . ' with value 0');
            }
        }

        // Debug log
        error_log('Redco Optimizer - Parsed and processed settings: ' . print_r($settings, true));

        // Get the add-ons instance
        global $redco_optimizer_addons;

        if (!isset($redco_optimizer_addons)) {
            $redco_optimizer_addons = new Redco_Optimizer_Addons();
        }

        // Get the installed add-ons
        $addons = $redco_optimizer_addons->get_installed_addons();

        // Check if the add-on exists
        if (!isset($addons[$addon_slug])) {
            wp_send_json_error(array(
                'message' => __('Add-on not found.', 'redco-optimizer')
            ));
        }

        // Check if the add-on is active
        if (!isset($addons[$addon_slug]['active']) || !$addons[$addon_slug]['active']) {
            wp_send_json_error(array(
                'message' => __('Add-on is not active.', 'redco-optimizer')
            ));
        }

        // Get the add-on instance
        $addon_class = 'Redco_Optimizer_' . str_replace('-', '_', $addon_slug);
        $addon_instance = null;

        if (class_exists($addon_class)) {
            $addon_instance = new $addon_class();
        }

        // Debug log
        error_log('Redco Optimizer - Add-on class: ' . $addon_class);
        error_log('Redco Optimizer - Add-on instance: ' . ($addon_instance ? 'Yes' : 'No'));
        error_log('Redco Optimizer - Has save_settings_ajax method: ' . (($addon_instance && method_exists($addon_instance, 'save_settings_ajax')) ? 'Yes' : 'No'));

        // Process checkbox fields - they won't be in the form data if unchecked
        // This ensures all expected fields are present in the settings array
        if ($addon_slug === 'advanced-cache-preloader') {
            // Default values for checkboxes in Advanced Cache Preloader
            $checkbox_fields = array(
                'enabled', 'preload_on_publish', 'preload_on_update', 'preload_on_comment',
                'preload_homepage', 'preload_categories', 'preload_tags', 'mobile_preload', 'desktop_preload'
            );

            foreach ($checkbox_fields as $field) {
                if (!isset($settings[$field])) {
                    $settings[$field] = 0;
                }
            }
        } elseif ($addon_slug === 'font-optimizer') {
            // Default values for checkboxes in Font Optimizer
            $checkbox_fields = array(
                'enabled', 'local_hosting', 'preload_fonts', 'subset_fonts', 'subset_latin',
                'subset_latin_ext', 'subset_cyrillic', 'subset_cyrillic_ext', 'subset_greek',
                'subset_greek_ext', 'subset_vietnamese', 'optimize_google_fonts', 'optimize_typekit_fonts',
                'optimize_custom_fonts', 'font_face_observer', 'async_css', 'remove_unused_variants'
            );

            foreach ($checkbox_fields as $field) {
                if (!isset($settings[$field])) {
                    $settings[$field] = 0;
                }
            }
        }

        // Check if the add-on has a save_settings method
        if ($addon_instance && method_exists($addon_instance, 'save_settings_ajax')) {
            try {
                $result = $addon_instance->save_settings_ajax($settings);

                if (is_wp_error($result)) {
                    error_log('Redco Optimizer - Save settings error: ' . $result->get_error_message());
                    wp_send_json_error(array(
                        'message' => $result->get_error_message()
                    ));
                } else {
                    error_log('Redco Optimizer - Settings saved successfully via addon method');
                    wp_send_json_success(array(
                        'message' => __('Settings saved successfully.', 'redco-optimizer')
                    ));
                }
            } catch (Exception $e) {
                error_log('Redco Optimizer - Exception while saving settings: ' . $e->getMessage());
                wp_send_json_error(array(
                    'message' => __('Error saving settings: ', 'redco-optimizer') . $e->getMessage()
                ));
            }
        } else {
            // Default save settings behavior
            $option_name = 'redco_' . str_replace('-', '_', $addon_slug) . '_settings';

            try {
                // Enhanced debugging
                error_log('Redco Optimizer - Processing settings for add-on: ' . $addon_slug);
                error_log('Redco Optimizer - Raw settings before processing: ' . print_r($settings, true));

                // Process all form fields
                foreach ($settings as $key => $value) {
                    // Convert string '0' and '1' to integers for checkboxes and boolean fields
                    if ($value === '0' || $value === '1') {
                        $settings[$key] = (int) $value;
                        error_log('Redco Optimizer - Converted ' . $key . ' to integer: ' . $settings[$key]);
                    }
                    // Handle arrays (like multi-selects)
                    else if (is_array($value)) {
                        error_log('Redco Optimizer - Array field detected: ' . $key);
                    }
                }

                error_log('Redco Optimizer - Processed settings: ' . print_r($settings, true));

                // Save settings
                $result = update_option($option_name, $settings);
                error_log('Redco Optimizer - Settings saved via default method: ' . $option_name);
                error_log('Redco Optimizer - Update result: ' . ($result ? 'true' : 'false'));

                // Verify the settings were saved correctly
                $saved_settings = get_option($option_name, array());
                error_log('Redco Optimizer - Saved settings: ' . print_r($saved_settings, true));

                // Compare original and saved settings to check for discrepancies
                foreach ($settings as $key => $value) {
                    if (!isset($saved_settings[$key]) || $saved_settings[$key] !== $value) {
                        error_log('Redco Optimizer - WARNING: Setting mismatch for ' . $key .
                                 '. Original: ' . (is_array($value) ? json_encode($value) : $value) .
                                 ', Saved: ' . (isset($saved_settings[$key]) ?
                                               (is_array($saved_settings[$key]) ?
                                                json_encode($saved_settings[$key]) :
                                                $saved_settings[$key]) :
                                               'not set'));
                    }
                }

                wp_send_json_success(array(
                    'message' => __('Settings saved successfully.', 'redco-optimizer'),
                    'refresh' => true
                ));
            } catch (Exception $e) {
                error_log('Redco Optimizer - Exception while saving settings: ' . $e->getMessage());
                wp_send_json_error(array(
                    'message' => __('Error saving settings: ', 'redco-optimizer') . $e->getMessage()
                ));
            }
        }
    }

    /**
     * Get default settings template content.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The add-on slug.
     * @param    array     $addon         The add-on data.
     * @return   string    The template content.
     */
    private function get_default_settings_template($addon_slug, $addon) {
        $addon_name = isset($addon['name']) ? $addon['name'] : 'Add-on';
        $addon_description = isset($addon['description']) ? $addon['description'] : '';

        return '<?php
/**
 * ' . $addon_name . ' Settings Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/' . $addon_slug . '/templates
 */

// If this file is called directly, abort.
if (!defined(\'WPINC\')) {
    die;
}

// Get settings from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
?>

<form method="post" action="" class="redco-addon-settings-form" data-addon="' . $addon_slug . '">
    <?php wp_nonce_field(\'redco_' . $addon_slug . '_save_settings\', \'redco_' . $addon_slug . '_nonce\'); ?>

    <div class="redco-card">
        <div class="redco-card-header">
            <h3><?php esc_html_e(\'' . $addon_name . ' Settings\', \'redco-optimizer\'); ?></h3>
        </div>
        <div class="redco-card-content">
            <div class="redco-toggle-row">
                <div class="redco-toggle-info">
                    <h4><?php esc_html_e(\'Enable ' . $addon_name . '\', \'redco-optimizer\'); ?></h4>
                    <p><?php esc_html_e(\'' . $addon_description . '\', \'redco-optimizer\'); ?></p>
                </div>
                <div class="redco-toggle-control">
                    <label class="redco-switch">
                        <input type="checkbox" name="enabled" <?php checked(isset($settings[\'enabled\']) ? $settings[\'enabled\'] : 0, 1); ?> value="1">
                        <span class="redco-slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="redco-form-actions">
        <button type="submit" class="redco-button redco-button-primary"><?php esc_html_e(\'Save Settings\', \'redco-optimizer\'); ?></button>
    </div>
</form>
';
    }

    /**
     * Get all checkbox fields for a specific add-on.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The add-on slug to get checkbox fields for.
     * @return   array     Array of checkbox field names.
     */
    private function get_checkbox_fields_for_addon($addon_slug) {
        $checkbox_fields = array('enabled'); // All add-ons have an 'enabled' checkbox

        // Add specific checkbox fields based on the add-on slug
        switch ($addon_slug) {
            case 'advanced-cache-preloader':
                $checkbox_fields = array_merge($checkbox_fields, array(
                    'preload_on_publish', 'preload_on_update', 'preload_on_comment',
                    'preload_homepage', 'preload_categories', 'preload_tags',
                    'mobile_preload', 'desktop_preload'
                ));
                break;

            case 'font-optimizer':
                $checkbox_fields = array_merge($checkbox_fields, array(
                    'local_hosting', 'preload_fonts', 'subset_fonts', 'subset_latin',
                    'subset_latin_ext', 'subset_cyrillic', 'subset_cyrillic_ext', 'subset_greek',
                    'subset_greek_ext', 'subset_vietnamese', 'optimize_google_fonts', 'optimize_typekit_fonts',
                    'optimize_custom_fonts', 'font_face_observer', 'async_css', 'remove_unused_variants'
                ));
                break;

            case 'image-optimizer':
                $checkbox_fields = array_merge($checkbox_fields, array(
                    'optimize_on_upload', 'convert_to_webp', 'resize_large_images',
                    'preserve_exif', 'backup_original', 'optimize_thumbnails'
                ));
                break;

            case 'security-suite':
                $checkbox_fields = array_merge($checkbox_fields, array(
                    'enable_firewall', 'block_bad_bots', 'prevent_brute_force',
                    'hide_wp_version', 'disable_xmlrpc', 'disable_file_editing',
                    'force_ssl_admin', 'disable_login_hints', 'enable_activity_log'
                ));
                break;

            case 'performance-monitor':
                $checkbox_fields = array_merge($checkbox_fields, array(
                    'enable_monitoring', 'monitor_uptime', 'monitor_response_time',
                    'monitor_database', 'monitor_memory_usage', 'enable_alerts',
                    'email_alerts', 'slack_alerts', 'weekly_reports'
                ));
                break;

            case 'critical-css-generator':
                $checkbox_fields = array_merge($checkbox_fields, array(
                    'auto_generate', 'inline_critical_css', 'defer_non_critical',
                    'regenerate_on_update', 'include_google_fonts', 'include_custom_fonts'
                ));
                break;

            // Add more add-ons as needed
        }

        return $checkbox_fields;
    }

    /**
     * Get default settings modal template content.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The add-on slug.
     * @param    array     $addon         The add-on data.
     * @return   string    The template content.
     */
    private function get_default_settings_modal_template($addon_slug, $addon) {
        $addon_name = isset($addon['name']) ? $addon['name'] : 'Add-on';
        $addon_description = isset($addon['description']) ? $addon['description'] : '';

        // Default template with just the enable toggle
        $default_template = '<?php
/**
 * ' . $addon_name . ' Settings Modal Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/' . $addon_slug . '/templates
 */

// If this file is called directly, abort.
if (!defined(\'WPINC\')) {
    die;
}

// Get settings from the appropriate source
// This handles both direct class access and AJAX context
$settings = isset($settings) ? $settings : (isset($this->settings) ? $this->settings : array());
?>

<form method="post" action="" class="redco-addon-settings-form" data-addon="' . $addon_slug . '">
    <?php wp_nonce_field(\'redco_' . $addon_slug . '_save_settings\', \'redco_' . $addon_slug . '_nonce\'); ?>

    <div class="redco-modal-section">
        <h3><?php esc_html_e(\'' . $addon_name . ' Settings\', \'redco-optimizer\'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Enable ' . $addon_name . '\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'' . $addon_description . '\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="enabled" <?php checked(isset($settings[\'enabled\']) ? $settings[\'enabled\'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>
';

        // Add specific settings based on the add-on slug
        if ($addon_slug === 'advanced-cache-preloader') {
            $default_template .= '
    <div class="redco-modal-section">
        <h3><?php esc_html_e(\'Cache Preloader Settings\', \'redco-optimizer\'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_method"><?php esc_html_e(\'Preload Method\', \'redco-optimizer\'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_method" name="preload_method" class="redco-select">
                    <option value="sitemap" <?php selected(isset($settings[\'preload_method\']) ? $settings[\'preload_method\'] : \'sitemap\', \'sitemap\'); ?>><?php esc_html_e(\'Sitemap\', \'redco-optimizer\'); ?></option>
                    <option value="custom" <?php selected(isset($settings[\'preload_method\']) ? $settings[\'preload_method\'] : \'sitemap\', \'custom\'); ?>><?php esc_html_e(\'Custom URLs\', \'redco-optimizer\'); ?></option>
                    <option value="all" <?php selected(isset($settings[\'preload_method\']) ? $settings[\'preload_method\'] : \'sitemap\', \'all\'); ?>><?php esc_html_e(\'All Content\', \'redco-optimizer\'); ?></option>
                </select>
            </div>
        </div>

        <div class="redco-form-row" id="sitemap-url-row">
            <div class="redco-form-label">
                <label for="sitemap_url"><?php esc_html_e(\'Sitemap URL\', \'redco-optimizer\'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="url" id="sitemap_url" name="sitemap_url" class="redco-input" value="<?php echo esc_url(isset($settings[\'sitemap_url\']) ? $settings[\'sitemap_url\'] : \'\'); ?>" placeholder="https://example.com/sitemap.xml">
                <p class="redco-form-help"><?php esc_html_e(\'Enter your sitemap URL. Leave empty to use the default WordPress sitemap.\', \'redco-optimizer\'); ?></p>
            </div>
        </div>

        <div class="redco-form-row" id="custom-urls-row">
            <div class="redco-form-label">
                <label for="custom_urls"><?php esc_html_e(\'Custom URLs\', \'redco-optimizer\'); ?></label>
            </div>
            <div class="redco-form-field">
                <textarea id="custom_urls" name="custom_urls" class="redco-textarea" rows="5"><?php echo esc_textarea(isset($settings[\'custom_urls\']) ? $settings[\'custom_urls\'] : \'\'); ?></textarea>
                <p class="redco-form-help"><?php esc_html_e(\'Enter one URL per line.\', \'redco-optimizer\'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e(\'Schedule Settings\', \'redco-optimizer\'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_schedule"><?php esc_html_e(\'Preload Schedule\', \'redco-optimizer\'); ?></label>
            </div>
            <div class="redco-form-field">
                <select id="preload_schedule" name="preload_schedule" class="redco-select">
                    <option value="hourly" <?php selected(isset($settings[\'preload_schedule\']) ? $settings[\'preload_schedule\'] : \'daily\', \'hourly\'); ?>><?php esc_html_e(\'Hourly\', \'redco-optimizer\'); ?></option>
                    <option value="twicedaily" <?php selected(isset($settings[\'preload_schedule\']) ? $settings[\'preload_schedule\'] : \'daily\', \'twicedaily\'); ?>><?php esc_html_e(\'Twice Daily\', \'redco-optimizer\'); ?></option>
                    <option value="daily" <?php selected(isset($settings[\'preload_schedule\']) ? $settings[\'preload_schedule\'] : \'daily\', \'daily\'); ?>><?php esc_html_e(\'Daily\', \'redco-optimizer\'); ?></option>
                    <option value="weekly" <?php selected(isset($settings[\'preload_schedule\']) ? $settings[\'preload_schedule\'] : \'daily\', \'weekly\'); ?>><?php esc_html_e(\'Weekly\', \'redco-optimizer\'); ?></option>
                </select>
            </div>
        </div>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_time"><?php esc_html_e(\'Preload Time\', \'redco-optimizer\'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="time" id="preload_time" name="preload_time" class="redco-input" value="<?php echo esc_attr(isset($settings[\'preload_time\']) ? $settings[\'preload_time\'] : \'00:00\'); ?>">
                <p class="redco-form-help"><?php esc_html_e(\'Set the time of day to start preloading (server time).\', \'redco-optimizer\'); ?></p>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e(\'Advanced Settings\', \'redco-optimizer\'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="preload_throttle"><?php esc_html_e(\'Preload Throttle\', \'redco-optimizer\'); ?></label>
            </div>
            <div class="redco-form-field">
                <input type="number" id="preload_throttle" name="preload_throttle" class="redco-input" value="<?php echo intval(isset($settings[\'preload_throttle\']) ? $settings[\'preload_throttle\'] : 3); ?>" min="0" max="60">
                <p class="redco-form-help"><?php esc_html_e(\'Delay in seconds between preloading each URL. Set to 0 for no delay.\', \'redco-optimizer\'); ?></p>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Mobile Preload\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Preload cache for mobile devices.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="mobile_preload" <?php checked(isset($settings[\'mobile_preload\']) ? $settings[\'mobile_preload\'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Desktop Preload\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Preload cache for desktop devices.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="desktop_preload" <?php checked(isset($settings[\'desktop_preload\']) ? $settings[\'desktop_preload\'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>
';
        } else if ($addon_slug === 'font-optimizer') {
            $default_template .= '
    <div class="redco-modal-section">
        <h3><?php esc_html_e(\'Font Optimization Settings\', \'redco-optimizer\'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Local Font Hosting\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Host Google Fonts locally for better performance and privacy.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="local_hosting" <?php checked(isset($settings[\'local_hosting\']) ? $settings[\'local_hosting\'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Preload Fonts\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Add preload hints for critical fonts.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="preload_fonts" <?php checked(isset($settings[\'preload_fonts\']) ? $settings[\'preload_fonts\'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Font Subsetting\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Load only the character sets you need.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="subset_fonts" <?php checked(isset($settings[\'subset_fonts\']) ? $settings[\'subset_fonts\'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e(\'Font Sources\', \'redco-optimizer\'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Optimize Google Fonts\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Optimize Google Fonts loading.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="optimize_google_fonts" <?php checked(isset($settings[\'optimize_google_fonts\']) ? $settings[\'optimize_google_fonts\'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Optimize Custom Fonts\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Optimize custom font loading.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="optimize_custom_fonts" <?php checked(isset($settings[\'optimize_custom_fonts\']) ? $settings[\'optimize_custom_fonts\'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>
';
        } else if ($addon_slug === 'advanced-lazy-load') {
            $default_template .= '
    <div class="redco-modal-section">
        <h3><?php esc_html_e(\'Media Types\', \'redco-optimizer\'); ?></h3>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Lazy Load Images\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Apply lazy loading to images in your content.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="lazy_load_images" <?php checked(isset($settings[\'lazy_load_images\']) ? $settings[\'lazy_load_images\'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Lazy Load iFrames\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Apply lazy loading to iframes in your content.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="lazy_load_iframes" <?php checked(isset($settings[\'lazy_load_iframes\']) ? $settings[\'lazy_load_iframes\'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Lazy Load Videos\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Apply lazy loading to videos in your content.\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="lazy_load_videos" <?php checked(isset($settings[\'lazy_load_videos\']) ? $settings[\'lazy_load_videos\'] : 1, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>

    <div class="redco-modal-section">
        <h3><?php esc_html_e(\'Advanced Options\', \'redco-optimizer\'); ?></h3>

        <div class="redco-form-row">
            <div class="redco-form-label">
                <label for="exclusions"><?php esc_html_e(\'Exclusions\', \'redco-optimizer\'); ?></label>
            </div>
            <div class="redco-form-field">
                <textarea id="exclusions" name="exclusions" class="redco-textarea" rows="4" placeholder=".no-lazy, .slider img, .hero-image"><?php echo esc_textarea(isset($settings[\'exclusions\']) ? $settings[\'exclusions\'] : \'\'); ?></textarea>
                <p class="redco-form-help"><?php esc_html_e(\'Enter CSS selectors to exclude elements from lazy loading (one per line).\', \'redco-optimizer\'); ?></p>
            </div>
        </div>

        <div class="redco-toggle-row">
            <div class="redco-toggle-info">
                <h4><?php esc_html_e(\'Low-Quality Image Placeholders\', \'redco-optimizer\'); ?></h4>
                <p><?php esc_html_e(\'Show low-quality blurred versions of images while loading (LQIP).\', \'redco-optimizer\'); ?></p>
            </div>
            <div class="redco-toggle-control">
                <label class="redco-switch">
                    <input type="checkbox" name="use_lqip" <?php checked(isset($settings[\'use_lqip\']) ? $settings[\'use_lqip\'] : 0, 1); ?> value="1">
                    <span class="redco-slider"></span>
                </label>
            </div>
        </div>
    </div>
';
        }

        // Add the modal footer with submit button
        $default_template .= '
    <div class="redco-modal-footer">
        <button type="submit" class="redco-button redco-button-primary">
            <span class="dashicons dashicons-yes"></span>
            <?php esc_html_e(\'Save Settings\', \'redco-optimizer\'); ?>
        </button>
        <button type="button" class="redco-button redco-modal-cancel">
            <?php esc_html_e(\'Cancel\', \'redco-optimizer\'); ?>
        </button>
    </div>
</form>
';

        return $default_template;
    }

    /**
     * Get the add-on instance.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The add-on slug.
     * @return   object|false    The add-on instance or false if not found.
     */
    private function get_addon_instance($addon_slug) {
        // Log for debugging
        error_log('Redco Optimizer - Getting add-on instance for: ' . $addon_slug);

        // Get Redco Optimizer Addons instance
        global $redco_optimizer_addons;
        if (!isset($redco_optimizer_addons)) {
            if (class_exists('Redco_Optimizer_Addons')) {
                $redco_optimizer_addons = new Redco_Optimizer_Addons();
                error_log('Redco Optimizer - Created new Redco_Optimizer_Addons instance');
            } else {
                error_log('Redco Optimizer - Redco_Optimizer_Addons class not found');
                return false;
            }
        }

        // Get the installed add-ons
        $addons = $redco_optimizer_addons->get_installed_addons();
        error_log('Redco Optimizer - Installed add-ons: ' . print_r(array_keys($addons), true));

        // Check if the add-on exists
        if (!isset($addons[$addon_slug])) {
            error_log('Redco Optimizer - Add-on not found in installed add-ons: ' . $addon_slug);
            return false;
        }

        // Get the add-on directory
        $addon_dir = REDCO_OPTIMIZER_PLUGIN_PATH . 'addons/' . $addon_slug;
        error_log('Redco Optimizer - Add-on directory: ' . $addon_dir);

        // Check if the add-on directory exists
        if (!file_exists($addon_dir)) {
            error_log('Redco Optimizer - Add-on directory does not exist: ' . $addon_dir);
            return false;
        }

        // Get the add-on main file
        $addon_file = $addon_dir . '/' . $addon_slug . '.php';
        error_log('Redco Optimizer - Add-on main file: ' . $addon_file);

        // Check if the add-on main file exists
        if (!file_exists($addon_file)) {
            error_log('Redco Optimizer - Add-on main file does not exist: ' . $addon_file);
            return false;
        }

        // Include the add-on main file if not already included
        include_once $addon_file;

        // Get the add-on class name
        $addon_class = 'Redco_Optimizer_' . $this->slug_to_class_name($addon_slug);
        error_log('Redco Optimizer - Add-on class name: ' . $addon_class);

        // Check if the add-on class exists
        if (!class_exists($addon_class)) {
            error_log('Redco Optimizer - Add-on class does not exist: ' . $addon_class);
            return false;
        }

        // Get the add-on instance
        try {
            $addon_instance = new $addon_class();
            error_log('Redco Optimizer - Add-on instance created successfully');
            return $addon_instance;
        } catch (Exception $e) {
            error_log('Redco Optimizer - Error creating add-on instance: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Convert a slug to a class name.
     *
     * @since    1.0.0
     * @param    string    $slug    The slug.
     * @return   string    The class name.
     */
    private function slug_to_class_name($slug) {
        // Replace hyphens with underscores
        $class_name = str_replace('-', '_', $slug);

        // Capitalize each word
        $class_name = str_replace(' ', '', ucwords(str_replace('_', ' ', $class_name)));

        return $class_name;
    }
}
