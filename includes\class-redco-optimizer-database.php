<?php

/**
 * The Database Optimization functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The Database Optimization functionality of the plugin.
 *
 * Defines the functionality for optimizing the database.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_Database {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for database optimization.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for database optimization.
     */
    private $settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
    }

    /**
     * Get database optimization settings.
     *
     * @since    1.0.0
     * @return   array    The database optimization settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_database_settings', array() );

        // Default settings
        $defaults = array(
            // Premium feature - always disabled by default in free version
            'schedule_cleanup' => 0,
            'schedule_frequency' => 'weekly',
            'cleanup_post_revisions' => 1,
            'cleanup_auto_drafts' => 1,
            'cleanup_trashed_posts' => 1,
            'cleanup_spam_comments' => 1,
            'cleanup_trashed_comments' => 1,
            'cleanup_expired_transients' => 1,
            'cleanup_all_transients' => 0,
            'cleanup_optimize_tables' => 1,
            'cleanup_postmeta' => 0,
            'cleanup_commentmeta' => 0,
            'cleanup_orphaned_term_relationships' => 0,
            'cleanup_wp_options' => 0,
        );

        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize database optimization.
     *
     * @since    1.0.0
     */
    public function init() {
        // Schedule database cleanup
        if ( $this->settings['schedule_cleanup'] ) {
            $this->schedule_cleanup();
        } else {
            $this->unschedule_cleanup();
        }

        // Add AJAX handler for database cleanup
        add_action( 'wp_ajax_redco_database_cleanup', array( $this, 'ajax_database_cleanup' ) );

        // Add action for scheduled cleanup
        add_action( 'redco_database_cleanup', array( $this, 'cleanup_database' ) );
    }

    /**
     * Schedule database cleanup.
     *
     * @since    1.0.0
     */
    private function schedule_cleanup() {
        // Check if already scheduled
        if ( wp_next_scheduled( 'redco_database_cleanup' ) ) {
            // Check if frequency has changed
            $current_frequency = wp_get_schedule( 'redco_database_cleanup' );

            if ( $current_frequency === $this->settings['schedule_frequency'] ) {
                return;
            }

            // Frequency has changed, unschedule and reschedule
            $this->unschedule_cleanup();
        }

        // Schedule cleanup
        wp_schedule_event( time(), $this->settings['schedule_frequency'], 'redco_database_cleanup' );
    }

    /**
     * Unschedule database cleanup.
     *
     * @since    1.0.0
     */
    private function unschedule_cleanup() {
        wp_clear_scheduled_hook( 'redco_database_cleanup' );
    }

    /**
     * AJAX handler for database cleanup.
     *
     * @since    1.0.0
     */
    public function ajax_database_cleanup() {
        // Check nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'redco_optimizer_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed.', 'redco-optimizer' ) ) );
        }

        // Check user capabilities
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to perform this action.', 'redco-optimizer' ) ) );
        }

        // Get cleanup type
        $cleanup_type = isset( $_POST['cleanup_type'] ) ? sanitize_text_field( $_POST['cleanup_type'] ) : 'all';

        // Perform cleanup
        $result = $this->cleanup_database( $cleanup_type );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        } else {
            wp_send_json_success( array(
                'message' => __( 'Database cleanup completed successfully.', 'redco-optimizer' ),
                'stats' => $result,
            ) );
        }
    }

    /**
     * Cleanup database.
     *
     * @since    1.0.0
     * @param    string    $cleanup_type    The type of cleanup to perform.
     * @return   array|WP_Error    The cleanup stats or error.
     */
    public function cleanup_database( $cleanup_type = 'all' ) {
        global $wpdb;

        // Initialize stats
        $stats = array(
            'post_revisions' => 0,
            'auto_drafts' => 0,
            'trashed_posts' => 0,
            'spam_comments' => 0,
            'trashed_comments' => 0,
            'expired_transients' => 0,
            'all_transients' => 0,
            'optimize_tables' => 0,
            'postmeta' => 0,
            'commentmeta' => 0,
            'orphaned_term_relationships' => 0,
            'wp_options' => 0,
        );

        // Cleanup post revisions
        if ( ( $cleanup_type === 'all' || $cleanup_type === 'post_revisions' ) && $this->settings['cleanup_post_revisions'] ) {
            $stats['post_revisions'] = $this->cleanup_post_revisions();
        }

        // Cleanup auto drafts
        if ( ( $cleanup_type === 'all' || $cleanup_type === 'auto_drafts' ) && $this->settings['cleanup_auto_drafts'] ) {
            $stats['auto_drafts'] = $this->cleanup_auto_drafts();
        }

        // Cleanup trashed posts
        if ( ( $cleanup_type === 'all' || $cleanup_type === 'trashed_posts' ) && $this->settings['cleanup_trashed_posts'] ) {
            $stats['trashed_posts'] = $this->cleanup_trashed_posts();
        }

        // Cleanup spam comments
        if ( ( $cleanup_type === 'all' || $cleanup_type === 'spam_comments' ) && $this->settings['cleanup_spam_comments'] ) {
            $stats['spam_comments'] = $this->cleanup_spam_comments();
        }

        // Cleanup trashed comments
        if ( ( $cleanup_type === 'all' || $cleanup_type === 'trashed_comments' ) && $this->settings['cleanup_trashed_comments'] ) {
            $stats['trashed_comments'] = $this->cleanup_trashed_comments();
        }

        // Cleanup expired transients
        if ( ( $cleanup_type === 'all' || $cleanup_type === 'expired_transients' ) && $this->settings['cleanup_expired_transients'] ) {
            $stats['expired_transients'] = $this->cleanup_expired_transients();
        }

        // Cleanup all transients
        if ( ( $cleanup_type === 'all' || $cleanup_type === 'all_transients' ) && $this->settings['cleanup_all_transients'] ) {
            $stats['all_transients'] = $this->cleanup_all_transients();
        }

        // Check if user has premium access for advanced cleanup features
        $has_premium_access = $this->has_premium_access();

        // Cleanup postmeta (premium feature)
        if ( $has_premium_access && ( $cleanup_type === 'all' || $cleanup_type === 'postmeta' ) && $this->settings['cleanup_postmeta'] ) {
            $stats['postmeta'] = $this->cleanup_postmeta();
        }

        // Cleanup commentmeta (premium feature)
        if ( $has_premium_access && ( $cleanup_type === 'all' || $cleanup_type === 'commentmeta' ) && $this->settings['cleanup_commentmeta'] ) {
            $stats['commentmeta'] = $this->cleanup_commentmeta();
        }

        // Cleanup orphaned term relationships (premium feature)
        if ( $has_premium_access && ( $cleanup_type === 'all' || $cleanup_type === 'orphaned_term_relationships' ) && $this->settings['cleanup_orphaned_term_relationships'] ) {
            $stats['orphaned_term_relationships'] = $this->cleanup_orphaned_term_relationships();
        }

        // Cleanup wp_options (premium feature)
        if ( $has_premium_access && ( $cleanup_type === 'all' || $cleanup_type === 'wp_options' ) && $this->settings['cleanup_wp_options'] ) {
            $stats['wp_options'] = $this->cleanup_wp_options();
        }

        // Optimize tables
        if ( ( $cleanup_type === 'all' || $cleanup_type === 'optimize_tables' ) && $this->settings['cleanup_optimize_tables'] ) {
            $stats['optimize_tables'] = $this->optimize_tables();
        }

        return $stats;
    }

    /**
     * Cleanup post revisions.
     *
     * @since    1.0.0
     * @return   int    The number of post revisions deleted.
     */
    private function cleanup_post_revisions() {
        global $wpdb;

        $query = "DELETE FROM $wpdb->posts WHERE post_type = 'revision'";
        $result = $wpdb->query( $query );

        return $result ? $result : 0;
    }

    /**
     * Cleanup auto drafts.
     *
     * @since    1.0.0
     * @return   int    The number of auto drafts deleted.
     */
    private function cleanup_auto_drafts() {
        global $wpdb;

        $query = "DELETE FROM $wpdb->posts WHERE post_status = 'auto-draft'";
        $result = $wpdb->query( $query );

        return $result ? $result : 0;
    }

    /**
     * Cleanup trashed posts.
     *
     * @since    1.0.0
     * @return   int    The number of trashed posts deleted.
     */
    private function cleanup_trashed_posts() {
        global $wpdb;

        $query = "DELETE FROM $wpdb->posts WHERE post_status = 'trash'";
        $result = $wpdb->query( $query );

        return $result ? $result : 0;
    }

    /**
     * Cleanup spam comments.
     *
     * @since    1.0.0
     * @return   int    The number of spam comments deleted.
     */
    private function cleanup_spam_comments() {
        global $wpdb;

        $query = "DELETE FROM $wpdb->comments WHERE comment_approved = 'spam'";
        $result = $wpdb->query( $query );

        return $result ? $result : 0;
    }

    /**
     * Cleanup trashed comments.
     *
     * @since    1.0.0
     * @return   int    The number of trashed comments deleted.
     */
    private function cleanup_trashed_comments() {
        global $wpdb;

        $query = "DELETE FROM $wpdb->comments WHERE comment_approved = 'trash'";
        $result = $wpdb->query( $query );

        return $result ? $result : 0;
    }

    /**
     * Cleanup expired transients.
     *
     * @since    1.0.0
     * @return   int    The number of expired transients deleted.
     */
    private function cleanup_expired_transients() {
        global $wpdb;

        $time = time();
        $count = 0;

        // Delete expired transients
        $query = $wpdb->prepare(
            "DELETE FROM $wpdb->options WHERE option_name LIKE %s AND option_value < %d",
            $wpdb->esc_like( '_transient_timeout_' ) . '%',
            $time
        );
        $count += $wpdb->query( $query );

        // Delete corresponding transient values
        $query = $wpdb->prepare(
            "DELETE FROM $wpdb->options WHERE option_name LIKE %s AND NOT EXISTS (
                SELECT 1 FROM (SELECT option_name FROM $wpdb->options WHERE option_name LIKE %s) AS tmp
                WHERE SUBSTRING(tmp.option_name, 20) = SUBSTRING(option_name, 12)
            )",
            $wpdb->esc_like( '_transient_' ) . '%',
            $wpdb->esc_like( '_transient_timeout_' ) . '%'
        );
        $count += $wpdb->query( $query );

        // Delete expired site transients
        $query = $wpdb->prepare(
            "DELETE FROM $wpdb->options WHERE option_name LIKE %s AND option_value < %d",
            $wpdb->esc_like( '_site_transient_timeout_' ) . '%',
            $time
        );
        $count += $wpdb->query( $query );

        // Delete corresponding site transient values
        $query = $wpdb->prepare(
            "DELETE FROM $wpdb->options WHERE option_name LIKE %s AND NOT EXISTS (
                SELECT 1 FROM (SELECT option_name FROM $wpdb->options WHERE option_name LIKE %s) AS tmp
                WHERE SUBSTRING(tmp.option_name, 25) = SUBSTRING(option_name, 17)
            )",
            $wpdb->esc_like( '_site_transient_' ) . '%',
            $wpdb->esc_like( '_site_transient_timeout_' ) . '%'
        );
        $count += $wpdb->query( $query );

        return $count;
    }

    /**
     * Cleanup all transients.
     *
     * @since    1.0.0
     * @return   int    The number of transients deleted.
     */
    private function cleanup_all_transients() {
        global $wpdb;

        $count = 0;

        // Delete all transients
        $query = $wpdb->prepare(
            "DELETE FROM $wpdb->options WHERE option_name LIKE %s OR option_name LIKE %s OR option_name LIKE %s OR option_name LIKE %s",
            $wpdb->esc_like( '_transient_' ) . '%',
            $wpdb->esc_like( '_site_transient_' ) . '%',
            $wpdb->esc_like( '_transient_timeout_' ) . '%',
            $wpdb->esc_like( '_site_transient_timeout_' ) . '%'
        );
        $count = $wpdb->query( $query );

        return $count;
    }

    /**
     * Cleanup orphaned postmeta.
     *
     * @since    1.0.0
     * @return   int    The number of orphaned postmeta deleted.
     */
    private function cleanup_postmeta() {
        global $wpdb;

        $query = "DELETE pm FROM $wpdb->postmeta pm LEFT JOIN $wpdb->posts p ON p.ID = pm.post_id WHERE p.ID IS NULL";
        $result = $wpdb->query( $query );

        return $result ? $result : 0;
    }

    /**
     * Cleanup orphaned commentmeta.
     *
     * @since    1.0.0
     * @return   int    The number of orphaned commentmeta deleted.
     */
    private function cleanup_commentmeta() {
        global $wpdb;

        $query = "DELETE cm FROM $wpdb->commentmeta cm LEFT JOIN $wpdb->comments c ON c.comment_ID = cm.comment_id WHERE c.comment_ID IS NULL";
        $result = $wpdb->query( $query );

        return $result ? $result : 0;
    }

    /**
     * Cleanup orphaned term relationships.
     *
     * @since    1.0.0
     * @return   int    The number of orphaned term relationships deleted.
     */
    private function cleanup_orphaned_term_relationships() {
        global $wpdb;

        $query = "DELETE tr FROM $wpdb->term_relationships tr LEFT JOIN $wpdb->posts p ON p.ID = tr.object_id WHERE p.ID IS NULL";
        $result = $wpdb->query( $query );

        return $result ? $result : 0;
    }

    /**
     * Check if the user has premium access.
     *
     * @since    1.0.0
     * @return   bool    True if the user has premium access, false otherwise.
     */
    private function has_premium_access() {
        // Check if license is valid and active
        $license_status = get_option('redco_optimizer_license_status', '');
        $license_key = get_option('redco_optimizer_license_key', '');

        // Only return true if license is valid and active
        if ($license_status === 'valid' && !empty($license_key)) {
            return true;
        }

        // Default to false for free version
        return false;
    }

    /**
     * Cleanup wp_options.
     *
     * @since    1.0.0
     * @return   int    The number of options deleted.
     */
    private function cleanup_wp_options() {
        global $wpdb;

        $count = 0;

        // Delete auto-generated options
        $query = $wpdb->prepare(
            "DELETE FROM $wpdb->options WHERE option_name LIKE %s OR option_name LIKE %s OR option_name LIKE %s",
            $wpdb->esc_like( 'auto_core_update_' ) . '%',
            $wpdb->esc_like( '_wp_session_' ) . '%',
            $wpdb->esc_like( '_wp_attachment_metadata_tmp_' ) . '%'
        );
        $count = $wpdb->query( $query );

        return $count;
    }

    /**
     * Optimize database tables.
     *
     * @since    1.0.0
     * @return   int    The number of tables optimized.
     */
    private function optimize_tables() {
        global $wpdb;

        $tables = $wpdb->get_results( "SHOW TABLES LIKE '{$wpdb->prefix}%'" );
        $count = 0;

        if ( ! empty( $tables ) ) {
            $table_names = array();

            foreach ( $tables as $table ) {
                $table = (array) $table;
                $table_names[] = reset( $table );
            }

            if ( ! empty( $table_names ) ) {
                $table_list = implode( ',', $table_names );
                $result = $wpdb->query( "OPTIMIZE TABLE $table_list" );

                if ( $result ) {
                    $count = count( $table_names );
                }
            }
        }

        return $count;
    }

    /**
     * Get database stats.
     *
     * @since    1.0.0
     * @return   array    The database stats.
     */
    public function get_database_stats() {
        global $wpdb;

        $stats = array();

        // Get post revisions count
        $stats['post_revisions'] = $wpdb->get_var( "SELECT COUNT(*) FROM $wpdb->posts WHERE post_type = 'revision'" );

        // Get auto drafts count
        $stats['auto_drafts'] = $wpdb->get_var( "SELECT COUNT(*) FROM $wpdb->posts WHERE post_status = 'auto-draft'" );

        // Get trashed posts count
        $stats['trashed_posts'] = $wpdb->get_var( "SELECT COUNT(*) FROM $wpdb->posts WHERE post_status = 'trash'" );

        // Get spam comments count
        $stats['spam_comments'] = $wpdb->get_var( "SELECT COUNT(*) FROM $wpdb->comments WHERE comment_approved = 'spam'" );

        // Get trashed comments count
        $stats['trashed_comments'] = $wpdb->get_var( "SELECT COUNT(*) FROM $wpdb->comments WHERE comment_approved = 'trash'" );

        // Get expired transients count
        $time = time();
        $stats['expired_transients'] = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM $wpdb->options WHERE option_name LIKE %s AND option_value < %d",
            $wpdb->esc_like( '_transient_timeout_' ) . '%',
            $time
        ) );
        $stats['expired_transients'] += $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM $wpdb->options WHERE option_name LIKE %s AND option_value < %d",
            $wpdb->esc_like( '_site_transient_timeout_' ) . '%',
            $time
        ) );

        // Get all transients count
        $stats['all_transients'] = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM $wpdb->options WHERE option_name LIKE %s OR option_name LIKE %s OR option_name LIKE %s OR option_name LIKE %s",
            $wpdb->esc_like( '_transient_' ) . '%',
            $wpdb->esc_like( '_site_transient_' ) . '%',
            $wpdb->esc_like( '_transient_timeout_' ) . '%',
            $wpdb->esc_like( '_site_transient_timeout_' ) . '%'
        ) );

        // Get orphaned postmeta count
        $stats['postmeta'] = $wpdb->get_var( "SELECT COUNT(*) FROM $wpdb->postmeta pm LEFT JOIN $wpdb->posts p ON p.ID = pm.post_id WHERE p.ID IS NULL" );

        // Get orphaned commentmeta count
        $stats['commentmeta'] = $wpdb->get_var( "SELECT COUNT(*) FROM $wpdb->commentmeta cm LEFT JOIN $wpdb->comments c ON c.comment_ID = cm.comment_id WHERE c.comment_ID IS NULL" );

        // Get orphaned term relationships count
        $stats['orphaned_term_relationships'] = $wpdb->get_var( "SELECT COUNT(*) FROM $wpdb->term_relationships tr LEFT JOIN $wpdb->posts p ON p.ID = tr.object_id WHERE p.ID IS NULL" );

        // Get auto-generated options count
        $stats['wp_options'] = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM $wpdb->options WHERE option_name LIKE %s OR option_name LIKE %s OR option_name LIKE %s",
            $wpdb->esc_like( 'auto_core_update_' ) . '%',
            $wpdb->esc_like( '_wp_session_' ) . '%',
            $wpdb->esc_like( '_wp_attachment_metadata_tmp_' ) . '%'
        ) );

        // Get database size
        $stats['database_size'] = $this->get_database_size();

        return $stats;
    }

    /**
     * Get database size.
     *
     * @since    1.0.0
     * @return   string    The database size.
     */
    private function get_database_size() {
        global $wpdb;

        $size = 0;
        $tables = $wpdb->get_results( "SHOW TABLE STATUS" );

        if ( $tables ) {
            foreach ( $tables as $table ) {
                $size += $table->Data_length + $table->Index_length;
            }
        }

        if ( $size > 1024 * 1024 * 1024 ) {
            return round( $size / ( 1024 * 1024 * 1024 ), 2 ) . ' GB';
        } elseif ( $size > 1024 * 1024 ) {
            return round( $size / ( 1024 * 1024 ), 2 ) . ' MB';
        } elseif ( $size > 1024 ) {
            return round( $size / 1024, 2 ) . ' KB';
        }

        return $size . ' B';
    }
}
