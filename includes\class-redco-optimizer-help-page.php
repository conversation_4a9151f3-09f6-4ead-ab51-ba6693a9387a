<?php
/**
 * The help page functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The help page functionality of the plugin.
 *
 * Registers and displays the standalone help page.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Help_Page {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version    The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Register the help page
        add_action('admin_menu', array($this, 'register_help_page'));
    }

    /**
     * Register the help page.
     *
     * @since    1.0.0
     */
    public function register_help_page() {
        // Add a hidden submenu page that will be accessible via direct link
        add_submenu_page(
            null, // No parent menu
            __('Redco Optimizer Help', 'redco-optimizer'),
            __('Help', 'redco-optimizer'),
            'manage_options',
            'redco-optimizer-help',
            array($this, 'display_help_page')
        );

        // Add scripts and styles specifically for the help page
        add_action('admin_enqueue_scripts', array($this, 'enqueue_help_page_assets'));
    }

    /**
     * Enqueue scripts and styles for the help page.
     *
     * @since    1.0.0
     * @param    string    $hook    The current admin page.
     */
    public function enqueue_help_page_assets($hook) {
        // Only load on the help page
        if ($hook !== 'admin_page_redco-optimizer-help') {
            return;
        }

        // Enqueue help page specific script
        wp_enqueue_script(
            'redco-optimizer-help-page',
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/redco-optimizer-help-page.js',
            array('jquery'),
            $this->version,
            true
        );

        // Add inline script to prevent modal conflicts
        wp_add_inline_script('redco-optimizer-help-page', '
            jQuery(document).ready(function($) {
                // Ensure no modals appear on the help page
                $(".redco-modal-overlay").remove();
                $(document).off("click", ".redco-addon-configure");

                console.log("Help page assets loaded");
            });
        ');
    }

    /**
     * Display the help page.
     *
     * @since    1.0.0
     */
    public function display_help_page() {
        // Include the help page template
        include_once plugin_dir_path(dirname(__FILE__)) . 'admin/redco-help-page.php';
    }

    /**
     * Get the help page URL.
     *
     * @since    1.0.0
     * @return   string    The URL to the help page.
     */
    public static function get_help_page_url() {
        return admin_url('admin.php?page=redco-optimizer-help');
    }
}
