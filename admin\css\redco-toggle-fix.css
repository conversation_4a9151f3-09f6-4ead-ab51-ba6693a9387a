/**
 * Redco Optimizer - Toggle Switch Fix
 * Direct approach to fix toggle switches UI
 */

/* Force enable all toggle switches */
.redco-switch,
.redco-slider,
input[type="checkbox"] {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
}

/* Override any disabled attributes */
input[type="checkbox"][disabled],
input[type="checkbox"][disabled] + .redco-slider {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
}

/* CRITICAL: Force toggle switch colors based on checkbox state */
input[type="checkbox"]:checked + .redco-slider {
    background-color: #00A66B !important;
}

input[type="checkbox"]:not(:checked) + .redco-slider {
    background-color: #cbd5e1 !important;
}

/* Ensure toggle switches in all contexts are clickable */
.redco-form .redco-switch,
.redco-module-card .redco-switch,
.redco-modal-content .redco-switch,
.redco-form .redco-slider,
.redco-module-card .redco-slider,
.redco-modal-content .redco-slider,
.redco-form input[type="checkbox"],
.redco-module-card input[type="checkbox"],
.redco-modal-content input[type="checkbox"] {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
}

/* Ensure toggle switch knob moves correctly */
.redco-slider {
    position: absolute !important;
    cursor: pointer !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
}

.redco-slider:before {
    position: absolute !important;
    content: "" !important;
    height: 18px !important;
    width: 18px !important;
    left: 3px !important;
    bottom: 3px !important;
    background-color: white !important;
}

input:checked + .redco-slider:before {
    transform: translateX(24px) !important;
}

/* Override any inline styles that might be applied */
.redco-slider[style] {
    background-color: inherit !important;
}

input:checked + .redco-slider[style] {
    background-color: #00A66B !important;
}

input:not(:checked) + .redco-slider[style] {
    background-color: #cbd5e1 !important;
}

/* Fix for any other toggle switch styles */
.redco-switch input:checked + .redco-slider {
    background-color: #00A66B !important;
}

.redco-switch input:not(:checked) + .redco-slider {
    background-color: #cbd5e1 !important;
}
