/* Top Navigation Styles */
.redco-top-nav {
    background: #fff;
    border-bottom: 1px solid #e2e4e7;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 32px; /* WordPress admin bar height */
    z-index: 9999;
    margin: 0 0 20px 0;
    transition: all 0.3s ease;
}

/* Enhanced sticky behavior */
.redco-top-nav.scrolled {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* Adjust for WordPress admin bar variations */
@media screen and (max-width: 782px) {
    .redco-top-nav {
        top: 46px; /* Mobile admin bar height */
    }
}

/* Adjust for folded admin menu */
.folded .redco-top-nav {
    /* No left adjustment needed since we're using sticky, not fixed */
}

/* Add smooth scrolling behavior */
html {
    scroll-behavior: smooth;
}

.redco-nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    max-width: 1400px;
    margin: 0 auto;
    min-height: 60px;
}

/* Logo Section */
.redco-nav-logo {
    flex-shrink: 0;
    margin-right: 30px;
}

.redco-logo-text {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 18px;
    font-weight: 700;
}

.redco-logo-part-1 {
    color: #00a66b;
}

.redco-logo-part-2 {
    color: #333;
}

.redco-version {
    font-size: 11px;
    color: #666;
    background: #f0f0f1;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

/* Navigation Items Container */
.redco-nav-items {
    display: flex;
    align-items: center;
    gap: 5px;
    flex: 1;
}

/* Navigation Items */
.redco-nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    color: #50575e;
    text-decoration: none;
    white-space: nowrap;
    position: relative;
}

.redco-nav-item:hover {
    background: #f6f7f7;
    color: #00a66b;
}

.redco-nav-item.active {
    background: #00a66b;
    color: #fff;
}

.redco-nav-item .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Dropdown Styles */
.redco-nav-dropdown {
    position: relative;
}

.redco-nav-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    color: #50575e;
    white-space: nowrap;
}

.redco-nav-dropdown-toggle:hover {
    background: #f6f7f7;
    color: #00a66b;
}

.redco-nav-dropdown-toggle .dashicons-arrow-down-alt2 {
    font-size: 14px;
    transition: transform 0.2s ease;
}

.redco-nav-dropdown:hover .redco-nav-dropdown-toggle .dashicons-arrow-down-alt2 {
    transform: rotate(180deg);
}

.redco-nav-dropdown-menu {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    background: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1000;
    padding: 8px 0;
}

/* Add invisible bridge to prevent dropdown from closing */
.redco-nav-dropdown::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 8px;
    background: transparent;
    z-index: 999;
}

.redco-nav-dropdown:hover .redco-nav-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.redco-nav-dropdown-menu .redco-nav-item {
    padding: 10px 16px;
    margin: 0;
    border-radius: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
}

.redco-nav-dropdown-menu .redco-nav-item:hover {
    background: #f6f7f7;
    color: #00a66b;
}

/* Navigation Account Section */
.redco-nav-account {
    flex-shrink: 0;
    margin-left: 20px;
    padding-left: 20px;
    border-left: 1px solid #e2e4e7;
}

.redco-nav-account-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.redco-nav-account-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.redco-nav-license {
    font-size: 11px;
    font-weight: 600;
    color: #6c757d;
    background: #f0f0f1;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-nav-version {
    font-size: 10px;
    color: #999;
    font-weight: 500;
}

.redco-nav-account-actions {
    display: flex;
    align-items: center;
    gap: 6px;
}

.redco-nav-refresh,
.redco-nav-account-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #50575e;
    text-decoration: none;
}

.redco-nav-refresh:hover,
.redco-nav-account-link:hover {
    background: #f6f7f7;
    color: #00a66b;
}

.redco-nav-refresh .dashicons,
.redco-nav-account-link .dashicons {
    font-size: 14px;
}

.redco-nav-refresh.loading {
    pointer-events: none;
    opacity: 0.7;
}

.redco-spin {
    animation: redco-spin 1s linear infinite;
}

@keyframes redco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Save Button */
.redco-nav-save {
    flex-shrink: 0;
    margin-left: 20px;
}

.redco-nav-save .redco-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.redco-nav-save .redco-button-primary {
    background: #00a66b;
    color: #fff;
}

.redco-nav-save .redco-button-primary:hover {
    background: #008a5a;
}

/* Main Content Full Width */
.redco-main-content-full {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .redco-nav-wrapper {
        padding: 0 25px;
    }

    .redco-main-content-full {
        padding: 0 25px;
    }
}

@media (max-width: 768px) {
    .redco-nav-wrapper {
        flex-wrap: wrap;
        min-height: auto;
        padding: 10px 20px;
    }

    .redco-nav-logo {
        margin-right: 15px;
        margin-bottom: 10px;
    }

    .redco-nav-items {
        flex-wrap: wrap;
        gap: 5px;
    }

    .redco-nav-account {
        margin-left: 0;
        margin-top: 10px;
        padding-left: 0;
        border-left: none;
        border-top: 1px solid #e2e4e7;
        padding-top: 10px;
        width: 100%;
    }

    .redco-nav-account-info {
        justify-content: space-between;
    }

    .redco-nav-save {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }

    .redco-nav-save .redco-button {
        width: 100%;
        justify-content: center;
    }

    .redco-nav-dropdown-menu {
        position: fixed;
        left: 20px;
        right: 20px;
        width: auto;
        min-width: auto;
    }

    .redco-main-content-full {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .redco-nav-wrapper {
        padding: 10px 15px;
    }

    .redco-main-content-full {
        padding: 0 15px;
    }

    .redco-nav-dropdown-menu {
        left: 15px;
        right: 15px;
    }
}

/* Layout adjustments */
.redco-layout {
    display: block;
}

/* Remove old sidebar styles when using top nav */
.redco-layout .redco-sidebar {
    display: none;
}

/* Ensure proper spacing */
.redco-page-header {
    margin-top: 0;
}
