<?php
/**
 * Add-Ons tab content for Redco Optimizer.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get installed addons
$installed_addons = redco_get_installed_addons();

// Get available add-ons
$available_addons = redco_get_available_addons();

// Prepare arrays for each section
$installed_addons_list = array();
$available_addons_list = array();
$coming_soon_addons_list = array();

// Define the list of removed add-ons that should be excluded
$removed_addons = array('advanced-lazy-load', 'webp-converter', 'cdn-manager');

// Filter installed add-ons
foreach ($installed_addons as $slug => $addon) {
    // Skip removed add-ons
    if (in_array($slug, $removed_addons)) {
        continue;
    }

    if (isset($addon['coming_soon']) && $addon['coming_soon']) {
        $coming_soon_addons_list[$slug] = $addon;
    } else {
        $installed_addons_list[$slug] = $addon;
    }
}

// Filter available add-ons
foreach ($available_addons as $slug => $addon) {
    // Skip removed add-ons
    if (in_array($slug, $removed_addons)) {
        continue;
    }

    // Skip if already installed
    if (isset($installed_addons[$slug])) {
        continue;
    }

    // Add to coming soon or available list
    if (isset($addon['coming_soon']) && $addon['coming_soon']) {
        $coming_soon_addons_list[$slug] = $addon;
    } else {
        $available_addons_list[$slug] = $addon;
    }
}

// Debug information
error_log('Redco Optimizer - Available Add-ons: ' . print_r(array_keys($available_addons_list), true));
error_log('Redco Optimizer - Installed Add-ons: ' . print_r(array_keys($installed_addons_list), true));
error_log('Redco Optimizer - Coming Soon Add-ons: ' . print_r(array_keys($coming_soon_addons_list), true));
?>

<div class="redco-addons-container">
    <div class="redco-addons-header">
        <div class="redco-addons-header-content">
            <h2><?php esc_html_e('Add-Ons', 'redco-optimizer'); ?></h2>
            <p><?php esc_html_e('Extend Redco Optimizer with these powerful add-ons to enhance your website performance even further.', 'redco-optimizer'); ?></p>
        </div>
        <div class="redco-addons-header-actions">
            <a href="#" class="redco-refresh-addons">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" style="vertical-align: middle; margin-right: 5px;"><path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"></path></svg>
                <?php esc_html_e('Refresh Add-Ons', 'redco-optimizer'); ?>
            </a>
        </div>
    </div>

    <!-- Installed Add-Ons Section -->
    <div class="redco-addons-section">
        <h3><?php esc_html_e('Installed Add-Ons', 'redco-optimizer'); ?></h3>
        <p><?php esc_html_e('Add-ons that are currently installed on your website.', 'redco-optimizer'); ?></p>

        <div class="redco-addons-grid">
            <?php
            // Loop through installed addons
            if (!empty($installed_addons_list)) {
                foreach ($installed_addons_list as $slug => $addon) {
                    $is_active = isset($addon['active']) && $addon['active'];
                    $is_premium = isset($addon['premium']) && $addon['premium'];
                    $has_settings = isset($addon['has_settings']) && $addon['has_settings'];
                ?>
                <div class="redco-addon-card <?php echo $is_active ? 'redco-addon-active' : ''; ?> <?php echo $is_premium ? 'redco-addon-premium' : ''; ?>">
                    <div class="redco-addon-header">
                        <?php if ($is_premium) : ?>
                            <span class="redco-addon-badge redco-addon-badge-premium"><?php esc_html_e('Premium', 'redco-optimizer'); ?></span>
                        <?php endif; ?>
                        <div class="redco-addon-icon">
                            <?php
                            // Get addon slug
                            $addon_slug = isset($addon['slug']) ? $addon['slug'] : '';

                            // Check if icon is a URL or a dashicon class
                            if (!empty($addon['icon'])) {
                                if (strpos($addon['icon'], 'dashicons-') === 0) {
                                    // This is a dashicon class, use SVG instead
                                    $dashicon_map = array(
                                        'dashicons-performance' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 8l-4 4h3c0 3.31-2.69 6-6 6-1.01 0-1.97-.25-2.8-.7l-1.46 1.46C8.97 19.54 10.43 20 12 20c4.42 0 8-3.58 8-8h3l-4-4zM6 12c0-3.31 2.69-6 6-6 1.01 0 1.97.25 2.8.7l1.46-1.46C15.03 4.46 13.57 4 12 4c-4.42 0-8 3.58-8 8H1l4 4 4-4H6z" fill="currentColor"></path></svg>',
                                        'dashicons-networking' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" fill="currentColor"></path></svg>',
                                        'dashicons-editor-textcolor' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M9.93 13.5h4.14L12 7.98zM20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-4.05 16.5l-1.14-3H9.17l-1.12 3H5.96l5.11-13h1.86l5.11 13h-2.09z" fill="currentColor"></path></svg>',
                                        'dashicons-editor-code' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z" fill="currentColor"></path></svg>',
                                        'dashicons-admin-plugins' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z" fill="currentColor"></path></svg>'
                                    );

                                    if (isset($dashicon_map[$addon['icon']])) {
                                        echo $dashicon_map[$addon['icon']];
                                    } else {
                                        // Fallback for unknown dashicons
                                        echo $dashicon_map['dashicons-admin-plugins'];
                                    }
                                } else {
                                    // This is a URL, use an image tag
                                    echo '<img src="' . esc_url($addon['icon']) . '" alt="' . esc_attr($addon['name']) . '">';
                                }
                            } else {
                                // No icon specified, use default SVG
                                echo '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-5h10v2H7zm3.3-3.8L8.4 9.3 7 10.7l3.3 3.3L17 7.3l-1.4-1.4z" fill="currentColor"></path></svg>';
                            }
                            ?>
                        </div>
                        <h3 class="redco-addon-title"><?php echo esc_html($addon['name']); ?></h3>
                        <div class="redco-addon-version"><?php echo esc_html(sprintf(__('v%s', 'redco-optimizer'), $addon['version'])); ?></div>
                    </div>
                    <div class="redco-addon-content">
                        <p class="redco-addon-description"><?php echo esc_html($addon['description']); ?></p>
                    </div>
                    <div class="redco-addon-footer">
                        <div class="redco-addon-status">
                            <?php if ($is_active) : ?>
                                <span class="redco-addon-status-active"><?php esc_html_e('Active', 'redco-optimizer'); ?></span>
                            <?php else : ?>
                                <span class="redco-addon-status-inactive"><?php esc_html_e('Inactive', 'redco-optimizer'); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="redco-addon-actions">
                            <?php if ($is_active) : ?>
                                <?php if ($has_settings) : ?>
                                    <button type="button" class="redco-button redco-button-secondary redco-addon-settings" data-addon="<?php echo esc_attr($slug); ?>">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="vertical-align: middle; margin-right: 5px;"><path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" fill="currentColor"></path></svg>
                                        <?php esc_html_e('Settings', 'redco-optimizer'); ?>
                                    </button>
                                <?php endif; ?>
                                <button type="button" class="redco-button redco-button-secondary redco-addon-deactivate" data-addon="<?php echo esc_attr($slug); ?>">
                                    <?php esc_html_e('Deactivate', 'redco-optimizer'); ?>
                                </button>
                            <?php else : ?>
                                <?php if ($is_premium && !redco_is_premium()) : ?>
                                    <a href="#" class="redco-button redco-button-premium redco-premium-tab-link" data-tab="redco-premium-tab">
                                        <?php esc_html_e('Upgrade to Premium', 'redco-optimizer'); ?>
                                    </a>
                                <?php else : ?>
                                    <button type="button" class="redco-button redco-button-primary redco-addon-activate" data-addon="<?php echo esc_attr($slug); ?>">
                                        <?php esc_html_e('Activate', 'redco-optimizer'); ?>
                                    </button>
                                    <button type="button" class="redco-button redco-button-secondary redco-addon-uninstall" data-addon="<?php echo esc_attr($slug); ?>">
                                        <?php esc_html_e('Uninstall', 'redco-optimizer'); ?>
                                    </button>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php
            }
        } else {
            // No addons found
            ?>
            <div class="redco-no-addons">
                <div class="redco-no-addons-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48"><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z" fill="currentColor"></path></svg>
                </div>
                <h3><?php esc_html_e('No Add-Ons Found', 'redco-optimizer'); ?></h3>
                <p><?php esc_html_e('No add-ons are currently installed. Check back later for new add-ons.', 'redco-optimizer'); ?></p>
            </div>
            <?php
        }
        ?>
    </div>

    <!-- Available Add-Ons Section -->
    <?php if (!empty($available_addons_list)) : ?>
    <div class="redco-addons-section">
        <h3><?php esc_html_e('Available Add-Ons', 'redco-optimizer'); ?></h3>
        <p><?php esc_html_e('Discover more add-ons to enhance your website performance.', 'redco-optimizer'); ?></p>

        <div class="redco-addons-grid">
            <?php foreach ($available_addons_list as $slug => $addon) :
                $is_premium = isset($addon['premium']) && $addon['premium'];
            ?>
            <div class="redco-addon-card redco-addon-available <?php echo $is_premium ? 'redco-addon-premium' : ''; ?>">
                <div class="redco-addon-header">
                    <?php if ($is_premium) : ?>
                        <span class="redco-addon-badge redco-addon-badge-premium"><?php esc_html_e('Premium', 'redco-optimizer'); ?></span>
                    <?php endif; ?>
                    <div class="redco-addon-icon">
                        <?php
                        // Get addon icon
                        if (!empty($addon['icon'])) {
                            if (strpos($addon['icon'], 'dashicons-') === 0) {
                                // This is a dashicon class, use SVG instead
                                $dashicon_map = array(
                                    'dashicons-performance' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 8l-4 4h3c0 3.31-2.69 6-6 6-1.01 0-1.97-.25-2.8-.7l-1.46 1.46C8.97 19.54 10.43 20 12 20c4.42 0 8-3.58 8-8h3l-4-4zM6 12c0-3.31 2.69-6 6-6 1.01 0 1.97.25 2.8.7l1.46-1.46C15.03 4.46 13.57 4 12 4c-4.42 0-8 3.58-8 8H1l4 4 4-4H6z" fill="currentColor"></path></svg>',
                                    'dashicons-networking' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" fill="currentColor"></path></svg>',
                                    'dashicons-editor-textcolor' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M9.93 13.5h4.14L12 7.98zM20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-4.05 16.5l-1.14-3H9.17l-1.12 3H5.96l5.11-13h1.86l5.11 13h-2.09z" fill="currentColor"></path></svg>',
                                    'dashicons-editor-code' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z" fill="currentColor"></path></svg>',
                                    'dashicons-admin-plugins' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z" fill="currentColor"></path></svg>',
                                    'dashicons-format-image' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-4.86 8.86l-3 3.87L9 13.14 6 17h12l-3.86-5.14z" fill="currentColor"></path></svg>',
                                    'dashicons-images-alt2' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-5-7l-3 3.72L9 13l-3 4h12l-4-5z" fill="currentColor"></path></svg>'
                                );

                                if (isset($dashicon_map[$addon['icon']])) {
                                    echo $dashicon_map[$addon['icon']];
                                } else {
                                    // Fallback for unknown dashicons
                                    echo $dashicon_map['dashicons-admin-plugins'];
                                }
                            } else {
                                // This is a URL or SVG, use as is
                                echo $addon['icon'];
                            }
                        } else {
                            // No icon specified, use default SVG
                            echo '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-5h10v2H7zm3.3-3.8L8.4 9.3 7 10.7l3.3 3.3L17 7.3l-1.4-1.4z" fill="currentColor"></path></svg>';
                        }
                        ?>
                    </div>
                    <h3 class="redco-addon-title"><?php echo esc_html($addon['name']); ?></h3>
                </div>
                <div class="redco-addon-content">
                    <p class="redco-addon-description"><?php echo esc_html($addon['description']); ?></p>
                </div>
                <div class="redco-addon-footer">
                    <div class="redco-addon-actions">
                        <?php if ($is_premium && !redco_is_premium()) : ?>
                            <a href="#" class="redco-button redco-button-premium redco-premium-tab-link" data-tab="redco-premium-tab">
                                <?php esc_html_e('Upgrade to Premium', 'redco-optimizer'); ?>
                            </a>
                        <?php else : ?>
                            <button type="button" class="redco-button redco-button-primary redco-addon-install" data-addon="<?php echo esc_attr($slug); ?>">
                                <?php esc_html_e('Install', 'redco-optimizer'); ?>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Coming Soon Add-Ons Section -->
    <?php if (!empty($coming_soon_addons_list)) : ?>
    <div class="redco-addons-section">
        <h3><?php esc_html_e('Coming Soon Add-Ons', 'redco-optimizer'); ?></h3>
        <p><?php esc_html_e('These add-ons are currently in development and will be available soon.', 'redco-optimizer'); ?></p>

        <div class="redco-addons-grid">
            <?php foreach ($coming_soon_addons_list as $slug => $addon) : ?>
            <div class="redco-addon-card redco-addon-available redco-addon-coming-soon">
                <div class="redco-addon-header">
                    <span class="redco-addon-badge redco-addon-badge-coming-soon"><?php esc_html_e('Coming Soon', 'redco-optimizer'); ?></span>
                    <div class="redco-addon-icon">
                        <?php
                        // Get addon icon
                        if (!empty($addon['icon'])) {
                            if (strpos($addon['icon'], 'dashicons-') === 0) {
                                // This is a dashicon class, use SVG instead
                                $dashicon_map = array(
                                    'dashicons-performance' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 8l-4 4h3c0 3.31-2.69 6-6 6-1.01 0-1.97-.25-2.8-.7l-1.46 1.46C8.97 19.54 10.43 20 12 20c4.42 0 8-3.58 8-8h3l-4-4zM6 12c0-3.31 2.69-6 6-6 1.01 0 1.97.25 2.8.7l1.46-1.46C15.03 4.46 13.57 4 12 4c-4.42 0-8 3.58-8 8H1l4 4 4-4H6z" fill="currentColor"></path></svg>',
                                    'dashicons-networking' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" fill="currentColor"></path></svg>',
                                    'dashicons-editor-textcolor' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M9.93 13.5h4.14L12 7.98zM20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-4.05 16.5l-1.14-3H9.17l-1.12 3H5.96l5.11-13h1.86l5.11 13h-2.09z" fill="currentColor"></path></svg>',
                                    'dashicons-editor-code' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z" fill="currentColor"></path></svg>',
                                    'dashicons-admin-plugins' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z" fill="currentColor"></path></svg>',
                                    'dashicons-format-image' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-4.86 8.86l-3 3.87L9 13.14 6 17h12l-3.86-5.14z" fill="currentColor"></path></svg>',
                                    'dashicons-images-alt2' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-5-7l-3 3.72L9 13l-3 4h12l-4-5z" fill="currentColor"></path></svg>'
                                );

                                if (isset($dashicon_map[$addon['icon']])) {
                                    echo $dashicon_map[$addon['icon']];
                                } else {
                                    // Fallback for unknown dashicons
                                    echo $dashicon_map['dashicons-admin-plugins'];
                                }
                            } else {
                                // This is a URL or SVG, use as is
                                echo $addon['icon'];
                            }
                        } else {
                            // No icon specified, use default SVG
                            echo '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-5h10v2H7zm3.3-3.8L8.4 9.3 7 10.7l3.3 3.3L17 7.3l-1.4-1.4z" fill="currentColor"></path></svg>';
                        }
                        ?>
                    </div>
                    <h3 class="redco-addon-title"><?php echo esc_html($addon['name']); ?></h3>
                </div>
                <div class="redco-addon-content">
                    <p class="redco-addon-description"><?php echo esc_html($addon['description']); ?></p>
                </div>
                <!-- No footer for coming soon add-ons -->
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
// Include the add-on settings modal template
include_once plugin_dir_path(dirname(__FILE__)) . 'partials/redco-optimizer-admin-addon-modal.php';
?>
