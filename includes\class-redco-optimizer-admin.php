<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for
 * how to enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Admin {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The modules of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $modules    The modules of this plugin.
     */
    private $modules;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param      string    $plugin_name       The name of this plugin.
     * @param      string    $version    The version of this plugin.
     * @param      array     $modules    The modules of this plugin.
     */
    public function __construct( $plugin_name, $version, $modules = array() ) {

        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->modules = $modules;

        // Add admin menu
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

        // Register settings
        add_action( 'admin_init', array( $this, 'register_settings' ) );

        // Add AJAX handlers
        add_action( 'wp_ajax_redco_optimizer_save_settings', array( $this, 'save_settings' ) );
        add_action( 'wp_ajax_redco_optimizer_reset_settings', array( $this, 'reset_settings' ) );
        add_action( 'wp_ajax_redco_optimizer_export_settings', array( $this, 'export_settings' ) );
        add_action( 'wp_ajax_redco_optimizer_import_settings', array( $this, 'import_settings' ) );
        add_action( 'wp_ajax_redco_optimizer_get_dashboard_stats', array( $this, 'get_dashboard_stats' ) );
        add_action( 'wp_ajax_redco_optimizer_toggle_module', array( $this, 'toggle_module' ) );
        add_action( 'wp_ajax_redco_optimizer_toggle_all_modules', array( $this, 'toggle_all_modules' ) );
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        $css_dir = plugin_dir_url( dirname( __FILE__ ) ) . 'admin/css/';

        // Main admin CSS
        wp_enqueue_style( $this->plugin_name, $css_dir . 'redco-optimizer-admin.css', array(), $this->version, 'all' );

        // Module CSS
        wp_enqueue_style( $this->plugin_name . '-modules', $css_dir . 'redco-optimizer-modules.css', array($this->plugin_name), $this->version, 'all' );

        // Compact Module Cards CSS (loads after modules CSS to override styles)
        wp_enqueue_style( $this->plugin_name . '-module-cards-compact', $css_dir . 'redco-module-cards-compact.css', array($this->plugin_name . '-modules'), $this->version, 'all' );

        // Additional CSS files
        wp_enqueue_style( $this->plugin_name . '-ajax-settings', $css_dir . 'redco-ajax-settings.css', array($this->plugin_name), $this->version, 'all' );
        wp_enqueue_style( $this->plugin_name . '-checkbox-styles', $css_dir . 'redco-checkbox-styles.css', array($this->plugin_name), $this->version, 'all' );
        wp_enqueue_style( $this->plugin_name . '-modal-system', $css_dir . 'redco-modal-system.css', array($this->plugin_name), $this->version, 'all' );
        wp_enqueue_style( $this->plugin_name . '-notification-system', $css_dir . 'redco-notification-system.css', array($this->plugin_name), $this->version, 'all' );
        wp_enqueue_style( $this->plugin_name . '-tab-enhancements', $css_dir . 'redco-tab-enhancements.css', array($this->plugin_name), $this->version, 'all' );
        // Toggle fixes CSS removed - using standard checkboxes now

        // Help page CSS (only load on help page)
        if (isset($_GET['page']) && $_GET['page'] === 'redco-optimizer-help') {
            wp_enqueue_style( $this->plugin_name . '-help-page', $css_dir . 'redco-help-page.css', array($this->plugin_name), $this->version, 'all' );
            wp_enqueue_style( $this->plugin_name . '-admin-help', $css_dir . 'redco-optimizer-admin-help.css', array($this->plugin_name), $this->version, 'all' );
        }

        // Addons CSS (only load on addons page)
        if (isset($_GET['page']) && $_GET['page'] === 'redco-optimizer-addons') {
            wp_enqueue_style( $this->plugin_name . '-addons', $css_dir . 'redco-optimizer-addons.css', array($this->plugin_name), $this->version, 'all' );
        }
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        $js_dir = plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/';

        // Main admin JS
        wp_enqueue_script( $this->plugin_name, $js_dir . 'redco-optimizer-admin.js', array( 'jquery' ), $this->version, true );

        // Add the AJAX URL and nonce to the script
        wp_localize_script( $this->plugin_name, 'redco_optimizer', array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'redco_optimizer_nonce' ),
            'saving_text' => __( 'Saving...', 'redco-optimizer' ),
            'error_text' => __( 'An error occurred. Please try again.', 'redco-optimizer' ),
            'success_text' => __( 'Settings saved successfully!', 'redco-optimizer' ),
            'refreshing_text' => __( 'Refreshing...', 'redco-optimizer' ),
            'has_premium_access' => $this->has_premium_access(),
            'premiumText' => __( 'Premium', 'redco-optimizer' ),
        ) );

        // Core functionality JS
        wp_enqueue_script( $this->plugin_name . '-modal-system', $js_dir . 'redco-modal-system.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        wp_enqueue_script( $this->plugin_name . '-expandable-sections', $js_dir . 'redco-expandable-sections.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        wp_enqueue_script( $this->plugin_name . '-checkbox-debug', $js_dir . 'redco-checkbox-debug.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        // Toggle fix JavaScript files removed - using standard checkboxes now
        wp_enqueue_script( $this->plugin_name . '-section-expander', $js_dir . 'redco-section-expander.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        wp_enqueue_script( $this->plugin_name . '-section-customizer', $js_dir . 'redco-section-customizer.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        wp_enqueue_script( $this->plugin_name . '-free-features', $js_dir . 'redco-free-features.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        wp_enqueue_script( $this->plugin_name . '-premium-badges', $js_dir . 'redco-premium-badges.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        wp_enqueue_script( $this->plugin_name . '-hide-premium', $js_dir . 'redco-hide-premium.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        wp_enqueue_script( $this->plugin_name . '-safe-mode-notice', $js_dir . 'redco-safe-mode-notice.js', array( 'jquery', $this->plugin_name ), $this->version, true );

        // Conditional scripts
        $current_page = isset($_GET['page']) ? sanitize_text_field($_GET['page']) : '';

        // Help page scripts
        if ($current_page === 'redco-optimizer-help') {
            wp_enqueue_script( $this->plugin_name . '-help-tab-fix', $js_dir . 'redco-help-tab-fix.js', array( 'jquery', $this->plugin_name ), $this->version, true );
            wp_enqueue_script( $this->plugin_name . '-admin-help', $js_dir . 'redco-optimizer-admin-help.js', array( 'jquery', $this->plugin_name ), $this->version, true );
            wp_enqueue_script( $this->plugin_name . '-help-page', $js_dir . 'redco-optimizer-help-page.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        }

        // Addons page scripts
        if ($current_page === 'redco-optimizer-addons') {
            wp_enqueue_script( $this->plugin_name . '-addons', $js_dir . 'redco-optimizer-addons.js', array( 'jquery', $this->plugin_name ), $this->version, true );
            wp_enqueue_script( $this->plugin_name . '-addon-fixes', $js_dir . 'redco-optimizer-addon-fixes.js', array( 'jquery', $this->plugin_name ), $this->version, true );
            wp_enqueue_script( $this->plugin_name . '-addon-modal-fix', $js_dir . 'redco-addon-modal-fix.js', array( 'jquery', $this->plugin_name ), $this->version, true );
            wp_enqueue_script( $this->plugin_name . '-addons-tab-fix', $js_dir . 'redco-addons-tab-fix.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        }

        // Error logs scripts
        if ($current_page === 'redco-optimizer-error-logs') {
            wp_enqueue_script( $this->plugin_name . '-error-logs', $js_dir . 'redco-optimizer-error-logs.js', array( 'jquery', $this->plugin_name ), $this->version, true );
        }

        // Toggle test script removed - using standard checkboxes now
    }

    /**
     * Add admin menu.
     *
     * @since    1.0.0
     */
    public function add_admin_menu() {
        add_menu_page(
            __( 'Redco Optimizer', 'redco-optimizer' ),
            __( 'Redco Optimizer', 'redco-optimizer' ),
            'manage_options',
            'redco-optimizer',
            array( $this, 'display_admin_page' ),
            'dashicons-performance',
            100
        );
    }

    /**
     * Register settings.
     *
     * @since    1.0.0
     */
    public function register_settings() {
        // Register settings for each tab
        register_setting( 'redco_optimizer_caching', 'redco_optimizer_caching_settings' );
        register_setting( 'redco_optimizer_file_optimization', 'redco_optimizer_file_optimization_settings' );
        register_setting( 'redco_optimizer_media', 'redco_optimizer_media_settings' );
        register_setting( 'redco_optimizer_preload', 'redco_optimizer_preload_settings' );
        register_setting( 'redco_optimizer_database', 'redco_optimizer_database_settings' );
        register_setting( 'redco_optimizer_heartbeat', 'redco_optimizer_heartbeat_settings' );
        register_setting( 'redco_optimizer_lazyload', 'redco_optimizer_lazyload_settings' );
        register_setting( 'redco_optimizer_cdn', 'redco_optimizer_cdn_settings' );
        register_setting( 'redco_optimizer_site_health_inspector', 'redco_optimizer_site_health_inspector_settings' );
    }

    /**
     * Display admin page.
     *
     * @since    1.0.0
     */
    public function display_admin_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/redco-optimizer-admin-display.php';
    }

    /**
     * Get settings for a specific tab.
     *
     * @since    1.0.0
     * @param    string    $tab    The tab to get settings for.
     * @return   array     The settings for the tab.
     */
    public function get_settings( $tab ) {
        // Use the Config class to get settings
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-config.php';
        $config = Redco_Optimizer_Config::get_instance();
        return $config->get($tab);
    }

    /**
     * Get default settings for a specific tab.
     *
     * @since    1.0.0
     * @param    string    $tab       The tab to get default settings for.
     * @param    array     $settings  The current settings array (optional).
     * @return   array     The default settings for the tab.
     */
    public function get_default_settings($tab, $settings = array()) {
        // Use the Config class to get default settings
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-config.php';
        $config = Redco_Optimizer_Config::get_instance();
        return $config->get_default_settings($tab);
    }

    /**
     * Sanitize settings.
     *
     * @since    1.0.0
     * @param    array    $settings    The settings to sanitize.
     * @return   array    The sanitized settings.
     */
    public function sanitize_settings( $settings ) {
        $sanitized_settings = array();

        if ( ! is_array( $settings ) ) {
            return $sanitized_settings;
        }

        foreach ( $settings as $key => $value ) {
            if ( is_array( $value ) ) {
                $sanitized_settings[ $key ] = $this->sanitize_settings( $value );
            } else {
                $sanitized_settings[ $key ] = sanitize_text_field( $value );
            }
        }

        return $sanitized_settings;
    }

    /**
     * Format file size.
     *
     * @since    1.0.0
     * @param    int       $bytes    The size in bytes.
     * @param    int       $decimals The number of decimal places.
     * @return   string    The formatted file size.
     */
    public function format_file_size($bytes, $decimals = 2) {
        $size = array('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');
        $factor = floor((strlen($bytes) - 1) / 3);
        return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . ' ' . $size[$factor];
    }

    /**
     * Handle AJAX request to save settings.
     *
     * @since    1.0.0
     */
    public function save_settings() {
        // Check nonce for security
        check_ajax_referer( 'redco_optimizer_nonce', 'nonce' );

        // Check if user has permission
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to perform this action.', 'redco-optimizer' ) ) );
        }

        // Get the tab from the request
        $tab = isset( $_POST['tab'] ) ? sanitize_text_field( wp_unslash( $_POST['tab'] ) ) : '';

        if ( empty( $tab ) ) {
            wp_send_json_error( array( 'message' => __( 'No tab specified.', 'redco-optimizer' ) ) );
        }

        // Extract settings from POST data (excluding system fields)
        $settings = array();
        $system_fields = array('action', 'nonce', 'tab', 'no_redirect');

        foreach ($_POST as $key => $value) {
            if (!in_array($key, $system_fields)) {
                $settings[$key] = sanitize_text_field(wp_unslash($value));
            }
        }

        // Save the settings using the Config class
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-config.php';
        $config = Redco_Optimizer_Config::get_instance();

        // Get the current settings
        $current_settings = $config->get($tab);

        // Special handling for checkboxes - set all checkbox fields to 0 first
        $checkbox_fields = array();

        if ($tab === 'file-optimization') {
            $checkbox_fields = array(
                'minify_html', 'minify_html_comments', 'minify_html_inline_css', 'minify_html_inline_js',
                'minify_css', 'combine_css', 'optimize_css_delivery',
                'minify_js', 'combine_js', 'defer_js',
                'remove_unused_css', 'delay_js', 'delay_js_safe_mode',
                'optimize_google_fonts', 'self_host_google_fonts', 'async_css'
            );

            // Add all delay_js_exclude_* fields
            foreach ($current_settings as $key => $value) {
                if (strpos($key, 'delay_js_exclude_') === 0) {
                    $checkbox_fields[] = $key;
                }
            }
        } elseif ($tab === 'caching') {
            $checkbox_fields = array(
                'enable_page_caching', 'enable_browser_caching', 'enable_mobile_caching',
                'separate_mobile_cache', 'clear_on_post_edit', 'clear_on_comment',
                'cache_logged_in_users', 'cache_ssl', 'cache_404', 'cache_query_strings'
            );
        } elseif ($tab === 'media') {
            $checkbox_fields = array(
                'enable_image_optimization', 'lazy_load_images', 'image_dimensions',
                'webp_conversion', 'disable_emojis', 'disable_embeds'
            );
        } elseif ($tab === 'lazyload') {
            $checkbox_fields = array(
                'lazyload_images', 'lazyload_css_bg', 'lazyload_iframes', 'lazyload_videos'
            );
        } elseif ($tab === 'preload') {
            $checkbox_fields = array(
                'preload_cache', 'preload_sitemap', 'preload_fonts', 'prefetch_dns', 'preload_links'
            );
        } elseif ($tab === 'database') {
            $checkbox_fields = array(
                'clean_post_revisions', 'clean_auto_drafts', 'clean_trashed_posts',
                'clean_spam_comments', 'clean_trashed_comments', 'clean_expired_transients',
                'clean_all_transients', 'clean_optimize_tables', 'schedule_cleanup',
                'cleanup_post_revisions', 'cleanup_auto_drafts', 'cleanup_trashed_posts',
                'cleanup_spam_comments', 'cleanup_trashed_comments', 'cleanup_expired_transients',
                'cleanup_all_transients', 'cleanup_optimize_tables', 'cleanup_postmeta',
                'cleanup_commentmeta', 'cleanup_orphaned_term_relationships', 'cleanup_wp_options'
            );
        } elseif ($tab === 'heartbeat') {
            $checkbox_fields = array(
                'control_heartbeat', 'disable_heartbeat_dashboard', 'disable_heartbeat_frontend',
                'disable_heartbeat_customizer'
            );
        } elseif ($tab === 'cdn') {
            $checkbox_fields = array(
                'cdn_enabled', 'include_images', 'include_js', 'include_css',
                'include_media', 'relative_path', 'cdn_https'
            );
        } elseif ($tab === 'site-health-inspector') {
            $checkbox_fields = array(
                'enable_site_health_inspector', 'auto_fix_issues', 'email_notifications'
            );
        }

        // Set all checkbox fields to 0 first (unchecked state)
        foreach ($checkbox_fields as $field) {
            $current_settings[$field] = '0';
        }

        // Apply the form data (only checked checkboxes will override the 0 values)
        $merged_settings = wp_parse_args($settings, $current_settings);

        // Save the settings
        $result = $config->save_module_settings($tab, $merged_settings);

        // Return success
        wp_send_json_success( array( 'message' => __( 'Settings saved successfully.', 'redco-optimizer' ) ) );
    }

    /**
     * Handle AJAX request to reset settings.
     *
     * @since    1.0.0
     */
    public function reset_settings() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get the current modules
        $modules = $this->update_modules();

        // Reset modules to default state (enabled but not active)
        foreach ($modules as $module_id => &$module) {
            // Keep the module enabled if it was enabled by default
            $module['enabled'] = isset($module['enabled']) ? $module['enabled'] : true;
        }

        // Update modules
        update_option('redco_optimizer_modules', $modules);

        // Reset all module settings using the Config class
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-config.php';
        $config = Redco_Optimizer_Config::get_instance();

        $module_tabs = array(
            'caching',
            'file-optimization',
            'media',
            'preload',
            'database',
            'heartbeat',
            'cdn',
            'site-health-inspector'
        );

        foreach ($module_tabs as $tab) {
            // Get default settings for this tab
            $default_settings = $config->get_default_settings($tab);

            // Save the default settings
            $config->save_module_settings($tab, $default_settings);
        }

        // Clear the config cache
        $config->clear_cache();

        // Return success
        wp_send_json_success(array('message' => __('All settings have been reset to their default values.', 'redco-optimizer')));
    }

    /**
     * Handle AJAX request to export settings.
     *
     * @since    1.0.0
     */
    public function export_settings() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get all settings
        $settings = array();

        // Get modules status
        $settings['modules'] = get_option('redco_optimizer_modules', array());

        // Get settings for each module using the Config class
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-config.php';
        $config = Redco_Optimizer_Config::get_instance();

        $modules = array('caching', 'file-optimization', 'media', 'preload', 'database',
                        'heartbeat', 'cdn', 'site-health-inspector');

        $module_settings = array();
        foreach ($modules as $module) {
            $module_settings[$module] = $config->get($module);
        }

        $settings['module_settings'] = $module_settings;

        // Add plugin version
        $settings['version'] = REDCO_OPTIMIZER_VERSION;

        // Return the settings
        wp_send_json_success(array('settings' => $settings));
    }

    /**
     * Handle AJAX request to import settings.
     *
     * @since    1.0.0
     */
    public function import_settings() {
        // Check nonce for security
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'redco-optimizer')));
        }

        // Get the settings from the request
        $settings_json = isset($_POST['settings']) ? sanitize_text_field(wp_unslash($_POST['settings'])) : '';

        if (empty($settings_json)) {
            wp_send_json_error(array('message' => __('No settings provided.', 'redco-optimizer')));
        }

        // Decode the settings
        $settings = json_decode($settings_json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json_error(array('message' => __('Invalid settings format.', 'redco-optimizer')));
        }

        // Check if the settings are valid
        if (!isset($settings['modules']) || !isset($settings['module_settings'])) {
            wp_send_json_error(array('message' => __('Invalid settings structure.', 'redco-optimizer')));
        }

        // Import modules status
        update_option('redco_optimizer_modules', $settings['modules']);

        // Import settings for each module using the Config class
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-config.php';
        $config = Redco_Optimizer_Config::get_instance();

        foreach ($settings['module_settings'] as $module => $module_settings) {
            $config->save_module_settings($module, $module_settings);
        }

        // Clear the config cache
        $config->clear_cache();

        // Return success
        wp_send_json_success(array('message' => __('Settings imported successfully.', 'redco-optimizer')));
    }

    /**
     * Handle AJAX request to get dashboard stats.
     *
     * @since    1.0.0
     */
    public function get_dashboard_stats() {
        // Check nonce for security
        check_ajax_referer( 'redco_optimizer_nonce', 'nonce' );

        // Check if user has permission
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to perform this action.', 'redco-optimizer' ) ) );
        }

        // Get real optimization stats
        $stats = $this->calculate_real_stats();

        // Return the stats
        wp_send_json_success($stats);
    }

    /**
     * Calculate real optimization statistics.
     *
     * @since    1.0.0
     * @return   array    The optimization statistics.
     */
    private function calculate_real_stats() {
        $stats = array(
            'cache_hits' => 0,
            'images_optimized' => 0,
            'db_size_reduced' => '0 KB',
            'total_saved' => '0 KB',
            'performance_score' => 0,
        );

        // Get cache hits from cache log if available
        $cache_log = get_option('redco_cache_log', array());
        if (!empty($cache_log) && isset($cache_log['hits'])) {
            $stats['cache_hits'] = intval($cache_log['hits']);
        }

        // Get optimized images count
        $optimized_images = get_option('redco_optimized_images', array());
        $stats['images_optimized'] = count($optimized_images);

        // Calculate database size reduction
        $db_cleanup_stats = get_option('redco_db_cleanup_stats', array());
        if (!empty($db_cleanup_stats) && isset($db_cleanup_stats['size_reduced'])) {
            $stats['db_size_reduced'] = $this->format_file_size($db_cleanup_stats['size_reduced']);
        }

        // Calculate total saved (combined savings from all optimizations)
        $total_saved = 0;

        // Add image optimization savings
        $image_savings = get_option('redco_image_optimization_savings', 0);
        $total_saved += intval($image_savings);

        // Add minification savings
        $minification_savings = get_option('redco_minification_savings', 0);
        $total_saved += intval($minification_savings);

        // Add database cleanup savings
        if (!empty($db_cleanup_stats) && isset($db_cleanup_stats['size_reduced'])) {
            $total_saved += intval($db_cleanup_stats['size_reduced']);
        }

        $stats['total_saved'] = $this->format_file_size($total_saved);

        // Calculate performance score based on enabled optimizations
        $score = $this->calculate_performance_score();
        $stats['performance_score'] = $score;

        return $stats;
    }

    /**
     * Calculate performance score based on enabled optimizations.
     *
     * @since    1.0.0
     * @return   int    The performance score.
     */
    private function calculate_performance_score() {
        // Base score
        $score = 50;

        // Get all module settings
        require_once REDCO_OPTIMIZER_PLUGIN_PATH . 'includes/class-redco-optimizer-config.php';
        $config = Redco_Optimizer_Config::get_instance();

        // Check caching settings
        $caching = $config->get('caching');
        if (!empty($caching)) {
            if (isset($caching['enable_page_caching']) && $caching['enable_page_caching']) {
                $score += 10;
            }
            if (isset($caching['enable_browser_caching']) && $caching['enable_browser_caching']) {
                $score += 5;
            }
        }

        // Check file optimization settings
        $file_opt = $config->get('file-optimization');
        if (!empty($file_opt)) {
            if (isset($file_opt['minify_html']) && $file_opt['minify_html']) {
                $score += 3;
            }
            if (isset($file_opt['minify_css']) && $file_opt['minify_css']) {
                $score += 3;
            }
            if (isset($file_opt['minify_js']) && $file_opt['minify_js']) {
                $score += 3;
            }
            if (isset($file_opt['combine_css']) && $file_opt['combine_css']) {
                $score += 5;
            }
            if (isset($file_opt['combine_js']) && $file_opt['combine_js']) {
                $score += 5;
            }
            if (isset($file_opt['defer_js']) && $file_opt['defer_js']) {
                $score += 5;
            }
        }

        // Check media optimization settings
        $media = $config->get('media');
        if (!empty($media)) {
            if (isset($media['lazy_load_images']) && $media['lazy_load_images']) {
                $score += 5;
            }
            if (isset($media['webp_conversion']) && $media['webp_conversion']) {
                $score += 5;
            }
        }

        // Ensure score is between 0 and 100
        $score = max(0, min(100, $score));

        return $score;
    }

    /**
     * Get image optimization statistics.
     *
     * @since    1.0.0
     * @return   array    Array containing image optimization statistics.
     */
    public function get_image_optimization_stats() {
        // Get all image attachments
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => -1,
            'fields' => 'ids',
        );

        $query = new WP_Query($args);
        $total_images = $query->found_posts;

        // Get optimized images
        $optimized_images = get_option('redco_optimized_images', array());
        $optimized_count = count($optimized_images);

        return array(
            'total' => $total_images,
            'optimized' => $optimized_count,
            'unoptimized' => $total_images - $optimized_count,
        );
    }

    /**
     * Get server resource usage.
     *
     * @since    1.0.0
     * @return   array    Array containing CPU, memory, and disk usage percentages.
     */
    public function get_server_resources() {
        $resources = array(
            'cpu' => 0,
            'memory' => 0,
            'disk' => 0,
        );

        // Get memory usage
        if (function_exists('memory_get_usage') && function_exists('memory_get_peak_usage')) {
            $memory_usage = memory_get_usage();
            $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
            if ($memory_limit > 0) {
                $resources['memory'] = round(($memory_usage / $memory_limit) * 100);
            }
        }

        // Get disk usage
        $upload_dir = wp_upload_dir();
        $disk_free_space = function_exists('disk_free_space') ? disk_free_space($upload_dir['basedir']) : 0;
        $disk_total_space = function_exists('disk_total_space') ? disk_total_space($upload_dir['basedir']) : 0;

        if ($disk_total_space > 0) {
            $disk_used_space = $disk_total_space - $disk_free_space;
            $resources['disk'] = round(($disk_used_space / $disk_total_space) * 100);
        }

        // CPU usage is difficult to get reliably in PHP, especially on shared hosting
        // For now, we'll use a placeholder value
        $resources['cpu'] = 0;

        return $resources;
    }

    /**
     * Get active modules.
     *
     * @since    1.0.0
     * @return   array    Array of active module IDs.
     */
    public function get_active_modules() {
        $modules = get_option('redco_optimizer_modules', array());
        $active_modules = array();

        foreach ($modules as $module_id => $module) {
            if (isset($module['enabled']) && $module['enabled']) {
                $active_modules[] = $module_id;
            }
        }

        return $active_modules;
    }

    /**
     * Check if the user has premium access.
     *
     * @since    1.0.0
     * @return   bool    True if the user has premium access, false otherwise.
     */
    public function has_premium_access() {
        // For now, we'll return false since this is a free version
        // In a real implementation, this would check for a valid license key or subscription
        return false;
    }

    /**
     * Refresh site health data.
     *
     * @since    1.0.0
     */
    public function refresh_site_health() {
        // Get site health data
        $site_health = array(
            'performance' => $this->calculate_performance_score(),
            'security' => $this->calculate_security_score(),
            'seo' => $this->calculate_seo_score(),
        );

        // Save site health data
        update_option('redco_site_health', $site_health);

        // Schedule next refresh
        if (!wp_next_scheduled('redco_refresh_site_health')) {
            wp_schedule_event(time() + DAY_IN_SECONDS, 'daily', 'redco_refresh_site_health');
        }
    }

    /**
     * Calculate security score.
     *
     * @since    1.0.0
     * @return   int    The security score.
     */
    private function calculate_security_score() {
        // Base score
        $score = 50;

        // Check if WordPress is up to date
        global $wp_version;
        $response = wp_remote_get('https://api.wordpress.org/core/version-check/1.7/');

        if (!is_wp_error($response) && 200 === wp_remote_retrieve_response_code($response)) {
            $versions = json_decode(wp_remote_retrieve_body($response), true);

            if ($versions && isset($versions['offers']) && is_array($versions['offers'])) {
                $latest_version = $versions['offers'][0]['version'];

                if (version_compare($wp_version, $latest_version, '>=')) {
                    $score += 10;
                }
            }
        }

        // Check if plugins are up to date
        $plugin_updates = get_site_transient('update_plugins');

        if (empty($plugin_updates->response)) {
            $score += 10;
        }

        // Check if themes are up to date
        $theme_updates = get_site_transient('update_themes');

        if (empty($theme_updates->response)) {
            $score += 10;
        }

        // Check if SSL is enabled
        if (is_ssl()) {
            $score += 10;
        }

        // Check if debug mode is disabled
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            $score += 5;
        }

        // Check if file editing is disabled
        if (defined('DISALLOW_FILE_EDIT') && DISALLOW_FILE_EDIT) {
            $score += 5;
        }

        // Ensure score is between 0 and 100
        $score = max(0, min(100, $score));

        return $score;
    }

    /**
     * Calculate SEO score.
     *
     * @since    1.0.0
     * @return   int    The SEO score.
     */
    private function calculate_seo_score() {
        // Base score
        $score = 50;

        // Check if site is public
        if (get_option('blog_public')) {
            $score += 10;
        }

        // Check if permalinks are set
        if (get_option('permalink_structure')) {
            $score += 10;
        }

        // Check if site has a tagline
        if (get_bloginfo('description')) {
            $score += 5;
        }

        // Check if site has a favicon
        if (has_site_icon()) {
            $score += 5;
        }

        // Check if site has a sitemap
        $sitemap_url = home_url('/sitemap.xml');
        $response = wp_remote_head($sitemap_url);

        if (!is_wp_error($response) && 200 === wp_remote_retrieve_response_code($response)) {
            $score += 10;
        }

        // Check if site has a robots.txt file
        $robots_url = home_url('/robots.txt');
        $response = wp_remote_head($robots_url);

        if (!is_wp_error($response) && 200 === wp_remote_retrieve_response_code($response)) {
            $score += 5;
        }

        // Check if site has an SSL certificate
        if (is_ssl()) {
            $score += 5;
        }

        // Ensure score is between 0 and 100
        $score = max(0, min(100, $score));

        return $score;
    }

    /**
     * Update modules.
     *
     * @since    1.0.0
     * @return   array    The updated modules.
     */
    public function update_modules() {
        // Get the current modules
        $modules = get_option('redco_optimizer_modules', array());

        // Define default modules if not set
        if (empty($modules)) {
            $modules = array(
                'caching' => array(
                    'title' => __('Caching', 'redco-optimizer'),
                    'description' => __('Speed up your site with page caching and browser caching.', 'redco-optimizer'),
                    'enabled' => true,
                    'active' => false,
                    'tab' => 'redco-caching-tab',
                ),
                'file-optimization' => array(
                    'title' => __('File Optimization', 'redco-optimizer'),
                    'description' => __('Minify and combine CSS and JavaScript files to reduce HTTP requests.', 'redco-optimizer'),
                    'enabled' => true,
                    'active' => false,
                    'tab' => 'redco-file-optimization-tab',
                ),
                'media' => array(
                    'title' => __('Media Optimization', 'redco-optimizer'),
                    'description' => __('Optimize images and other media files to reduce page load time.', 'redco-optimizer'),
                    'enabled' => true,
                    'active' => false,
                    'tab' => 'redco-media-tab',
                ),
                'preload' => array(
                    'title' => __('Preload', 'redco-optimizer'),
                    'description' => __('Preload resources to improve page load time.', 'redco-optimizer'),
                    'enabled' => true,
                    'active' => false,
                    'tab' => 'redco-preload-tab',
                ),
                'database' => array(
                    'title' => __('Database Optimization', 'redco-optimizer'),
                    'description' => __('Clean up your database to improve performance.', 'redco-optimizer'),
                    'enabled' => true,
                    'active' => false,
                    'tab' => 'redco-database-tab',
                ),
                'heartbeat' => array(
                    'title' => __('Heartbeat Control', 'redco-optimizer'),
                    'description' => __('Control the WordPress Heartbeat API to reduce server load.', 'redco-optimizer'),
                    'enabled' => true,
                    'active' => false,
                    'tab' => 'redco-heartbeat-tab',
                ),

                'cdn' => array(
                    'title' => __('CDN Integration', 'redco-optimizer'),
                    'description' => __('Integrate with a Content Delivery Network to improve page load time.', 'redco-optimizer'),
                    'enabled' => true,
                    'active' => false,
                    'tab' => 'redco-cdn-tab',
                ),
                'site-health-inspector' => array(
                    'title' => __('Site Health Inspector', 'redco-optimizer'),
                    'description' => __('Monitor your site health and get recommendations for improvement.', 'redco-optimizer'),
                    'enabled' => true,
                    'active' => false,
                    'tab' => 'redco-site-health-inspector-tab',
                ),
            );

            // Save the default modules
            update_option('redco_optimizer_modules', $modules);
        } else {
            // Ensure all modules have the required keys
            $updated = false;

            foreach ($modules as $module_id => &$module) {
                // Add tab key if it doesn't exist
                if (!isset($module['tab'])) {
                    $module['tab'] = 'redco-' . $module_id . '-tab';
                    $updated = true;
                }

                // Add title key if it doesn't exist but name does
                if (!isset($module['title']) && isset($module['name'])) {
                    $module['title'] = $module['name'];
                    $updated = true;
                }

                // Add title key if neither title nor name exist
                if (!isset($module['title']) && !isset($module['name'])) {
                    $module['title'] = ucfirst(str_replace('-', ' ', $module_id));
                    $updated = true;
                }

                // Ensure active key exists
                if (!isset($module['active'])) {
                    $module['active'] = isset($module['enabled']) ? $module['enabled'] : false;
                    $updated = true;
                }
            }

            // Save the updated modules if changes were made
            if ($updated) {
                update_option('redco_optimizer_modules', $modules);
            }
        }

        return $modules;
    }

    /**
     * AJAX handler for toggling a single module.
     *
     * @since    1.0.0
     */
    public function toggle_module() {
        // Check nonce
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Get the module ID and status
        $module_id = sanitize_text_field($_POST['module_id']);
        $status = intval($_POST['status']);

        if (empty($module_id)) {
            wp_send_json_error(array(
                'message' => __('Invalid module ID.', 'redco-optimizer')
            ));
        }

        // Get current modules
        $modules = get_option('redco_optimizer_modules', array());

        if (!isset($modules[$module_id])) {
            wp_send_json_error(array(
                'message' => __('Module not found.', 'redco-optimizer')
            ));
        }

        // Check if this is a premium module and user doesn't have access
        if (($module_id === 'cdn' || (isset($modules[$module_id]['premium']) && $modules[$module_id]['premium'])) && !$this->has_premium_access()) {
            wp_send_json_error(array(
                'message' => __('This is a premium feature. Please upgrade to unlock this module.', 'redco-optimizer'),
                'premium' => true
            ));
        }

        // Update the module status
        $modules[$module_id]['enabled'] = (bool)$status;
        $modules[$module_id]['active'] = (bool)$status;

        // Save the updated modules
        update_option('redco_optimizer_modules', $modules);

        // Get module title
        $module_title = isset($modules[$module_id]['title']) ? $modules[$module_id]['title'] : ucfirst(str_replace('-', ' ', $module_id));

        // Return success response
        wp_send_json_success(array(
            'message' => sprintf(
                $status ? __('%s module has been enabled.', 'redco-optimizer') : __('%s module has been disabled.', 'redco-optimizer'),
                $module_title
            ),
            'module_id' => $module_id,
            'status' => $status
        ));
    }

    /**
     * AJAX handler for toggling all modules.
     *
     * @since    1.0.0
     */
    public function toggle_all_modules() {
        // Check nonce
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        // Get the enable status
        $enable = intval($_POST['enable']);

        // Get current modules
        $modules = get_option('redco_optimizer_modules', array());

        if (empty($modules)) {
            wp_send_json_error(array(
                'message' => __('No modules found.', 'redco-optimizer')
            ));
        }

        $updated_count = 0;

        // Update all modules
        foreach ($modules as $module_id => &$module) {
            // Skip premium modules if user doesn't have access
            if (($module_id === 'cdn' || (isset($module['premium']) && $module['premium'])) && !$this->has_premium_access()) {
                continue;
            }

            $module['enabled'] = (bool)$enable;
            $module['active'] = (bool)$enable;
            $updated_count++;
        }

        // Save the updated modules
        update_option('redco_optimizer_modules', $modules);

        // Return success response
        wp_send_json_success(array(
            'message' => sprintf(
                $enable ? __('%d modules have been enabled.', 'redco-optimizer') : __('%d modules have been disabled.', 'redco-optimizer'),
                $updated_count
            ),
            'updated_count' => $updated_count,
            'enable' => $enable
        ));
    }
}
