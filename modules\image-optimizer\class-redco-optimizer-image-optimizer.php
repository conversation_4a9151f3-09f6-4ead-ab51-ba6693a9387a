<?php
/**
 * The image optimizer module functionality of the plugin.
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/image-optimizer
 */

/**
 * The image optimizer module functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for the image optimizer module.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules/image-optimizer
 * <AUTHOR> <<EMAIL>>
 */
class Redco_Optimizer_Image_Optimizer extends Redco_Optimizer_Module {

    /**
     * Register the hooks for this module.
     *
     * @since    1.0.0
     */
    protected function register_hooks() {
        // Add image optimization hooks
        $this->loader->add_filter( 'wp_handle_upload', $this, 'optimize_uploaded_image' );
        $this->loader->add_action( 'wp_ajax_redco_optimizer_optimize_image', $this, 'ajax_optimize_image' );
        $this->loader->add_action( 'wp_ajax_redco_optimizer_bulk_optimize_images', $this, 'ajax_bulk_optimize_images' );

        // Add lazy loading hooks
        $this->loader->add_filter( 'the_content', $this, 'add_lazy_loading' );
        $this->loader->add_filter( 'post_thumbnail_html', $this, 'add_lazy_loading' );
        $this->loader->add_filter( 'widget_text', $this, 'add_lazy_loading' );
    }

    /**
     * Optimize an uploaded image.
     *
     * @since    1.0.0
     * @param    array    $upload    The uploaded file data.
     * @return   array    The uploaded file data.
     */
    public function optimize_uploaded_image( $upload ) {
        // Check if the uploaded file is an image
        if ( ! preg_match( '/(jpg|jpeg|png|gif)$/i', $upload['file'] ) ) {
            return $upload;
        }

        // Get the media settings
        $settings = get_option( 'redco_optimizer_media_settings', array() );
        $quality = isset( $settings['image_quality'] ) ? $settings['image_quality'] : 82;

        // Get the attachment ID for the uploaded file
        $attachment_id = $this->get_attachment_id_from_file( $upload['file'] );

        // Optimize the image
        $this->optimize_image( $upload['file'], $quality, $attachment_id );

        return $upload;
    }

    /**
     * Optimize an image.
     *
     * @since    1.0.0
     * @param    string    $file_path    The path to the image file.
     * @param    int       $quality      The quality to use for optimization (0-100).
     * @param    int       $attachment_id The attachment ID (optional).
     * @return   bool      True on success, false on failure.
     */
    public function optimize_image( $file_path, $quality = 85, $attachment_id = 0 ) {
        if ( ! file_exists( $file_path ) ) {
            return false;
        }

        // Get the image type
        $image_type = exif_imagetype( $file_path );

        // Load the image based on its type
        switch ( $image_type ) {
            case IMAGETYPE_JPEG:
                $image = imagecreatefromjpeg( $file_path );
                break;
            case IMAGETYPE_PNG:
                $image = imagecreatefrompng( $file_path );
                break;
            case IMAGETYPE_GIF:
                $image = imagecreatefromgif( $file_path );
                break;
            default:
                return false;
        }

        if ( ! $image ) {
            return false;
        }

        // Create a backup of the original image
        $backup_path = $file_path . '.bak';
        copy( $file_path, $backup_path );

        // Save the optimized image
        $result = false;

        switch ( $image_type ) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg( $image, $file_path, $quality );
                break;
            case IMAGETYPE_PNG:
                // PNG quality is 0-9, so convert from 0-100
                $png_quality = 9 - round( ( $quality / 100 ) * 9 );
                $result = imagepng( $image, $file_path, $png_quality );
                break;
            case IMAGETYPE_GIF:
                $result = imagegif( $image, $file_path );
                break;
        }

        // Free up memory
        imagedestroy( $image );

        // If optimization failed, restore the backup
        if ( ! $result ) {
            copy( $backup_path, $file_path );
            unlink( $backup_path );
            return false;
        }

        // Check if the optimized image is smaller
        $original_size = filesize( $backup_path );
        $optimized_size = filesize( $file_path );

        if ( $optimized_size >= $original_size ) {
            // If the optimized image is not smaller, restore the backup
            copy( $backup_path, $file_path );
            unlink( $backup_path );
            return false;
        }

        // Remove the backup
        unlink( $backup_path );

        // If we have an attachment ID, mark it as optimized
        if ( $attachment_id > 0 ) {
            // Store optimization metadata
            update_post_meta( $attachment_id, '_redco_optimized', true );
            update_post_meta( $attachment_id, '_redco_original_size', $original_size );
            update_post_meta( $attachment_id, '_redco_optimized_size', $optimized_size );
            update_post_meta( $attachment_id, '_redco_optimization_date', current_time( 'mysql' ) );
        }

        return true;
    }

    /**
     * Handle AJAX request to optimize a single image.
     *
     * @since    1.0.0
     */
    public function ajax_optimize_image() {
        // Check nonce for security
        check_ajax_referer( 'redco_optimizer_nonce', 'nonce' );

        // Check if user has permission
        if ( ! current_user_can( 'upload_files' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to optimize images.', 'redco-optimizer' ) ) );
        }

        // Get the attachment ID
        $attachment_id = isset( $_POST['attachment_id'] ) ? intval( $_POST['attachment_id'] ) : 0;

        if ( ! $attachment_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid attachment ID.', 'redco-optimizer' ) ) );
        }

        // Get the attachment file path
        $file_path = get_attached_file( $attachment_id );

        if ( ! $file_path || ! file_exists( $file_path ) ) {
            wp_send_json_error( array( 'message' => __( 'Attachment file not found.', 'redco-optimizer' ) ) );
        }

        // Get the media settings
        $settings = get_option( 'redco_optimizer_media_settings', array() );
        $quality = isset( $settings['image_quality'] ) ? $settings['image_quality'] : 82;

        // Optimize the image
        $result = $this->optimize_image( $file_path, $quality, $attachment_id );

        if ( $result ) {
            wp_send_json_success( array( 'message' => __( 'Image optimized successfully.', 'redco-optimizer' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to optimize image.', 'redco-optimizer' ) ) );
        }
    }

    /**
     * Handle AJAX request to bulk optimize images.
     *
     * @since    1.0.0
     */
    public function ajax_bulk_optimize_images() {
        // Check nonce for security
        check_ajax_referer( 'redco_optimizer_nonce', 'nonce' );

        // Check if user has permission
        if ( ! current_user_can( 'upload_files' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to optimize images.', 'redco-optimizer' ) ) );
        }

        // Get the attachment IDs
        $attachment_ids = isset( $_POST['attachment_ids'] ) ? array_map( 'intval', $_POST['attachment_ids'] ) : array();

        if ( empty( $attachment_ids ) ) {
            wp_send_json_error( array( 'message' => __( 'No images selected for optimization.', 'redco-optimizer' ) ) );
        }

        // Get the media settings
        $settings = get_option( 'redco_optimizer_media_settings', array() );
        $quality = isset( $settings['image_quality'] ) ? $settings['image_quality'] : 82;

        // Optimize each image
        $success_count = 0;
        $error_count = 0;

        foreach ( $attachment_ids as $attachment_id ) {
            $file_path = get_attached_file( $attachment_id );

            if ( $file_path && file_exists( $file_path ) ) {
                $result = $this->optimize_image( $file_path, $quality, $attachment_id );

                if ( $result ) {
                    $success_count++;
                } else {
                    $error_count++;
                }
            } else {
                $error_count++;
            }
        }

        wp_send_json_success( array(
            'message' => sprintf(
                __( 'Optimization complete. %d images optimized successfully, %d failed.', 'redco-optimizer' ),
                $success_count,
                $error_count
            )
        ) );
    }

    /**
     * Add lazy loading to images in content.
     *
     * @since    1.0.0
     * @param    string    $content    The content to add lazy loading to.
     * @return   string    The content with lazy loading added.
     */
    public function add_lazy_loading( $content ) {
        // Check if lazy loading is enabled
        $settings = get_option( 'redco_optimizer_media_settings', array() );

        if ( ! isset( $settings['lazy_load_images'] ) || ! $settings['lazy_load_images'] ) {
            return $content;
        }

        // Don't lazy load in admin or feeds
        if ( is_admin() || is_feed() ) {
            return $content;
        }

        // Add lazy loading to images
        $content = preg_replace_callback( '/<img([^>]+)>/i', array( $this, 'add_lazy_loading_to_image' ), $content );

        return $content;
    }

    /**
     * Add lazy loading attributes to an image tag.
     *
     * @since    1.0.0
     * @param    array    $matches    The regex matches.
     * @return   string    The image tag with lazy loading attributes.
     */
    private function add_lazy_loading_to_image( $matches ) {
        $image_tag = $matches[0];
        $image_attr = $matches[1];

        // Skip if the image already has lazy loading
        if ( strpos( $image_attr, 'loading=' ) !== false || strpos( $image_attr, 'data-src=' ) !== false ) {
            return $image_tag;
        }

        // Add loading="lazy" attribute
        $image_tag = str_replace( '<img', '<img loading="lazy"', $image_tag );

        return $image_tag;
    }

    /**
     * Get attachment ID from file path.
     *
     * @since    1.0.0
     * @param    string    $file_path    The path to the file.
     * @return   int       The attachment ID or 0 if not found.
     */
    private function get_attachment_id_from_file( $file_path ) {
        // Get the upload directory
        $upload_dir = wp_upload_dir();

        // Make sure the file path is absolute
        if ( strpos( $file_path, $upload_dir['basedir'] ) === false ) {
            $file_path = $upload_dir['basedir'] . '/' . ltrim( $file_path, '/' );
        }

        // Get the file name
        $file_name = basename( $file_path );

        // Query for attachments with this file name
        $args = array(
            'post_type' => 'attachment',
            'post_status' => 'inherit',
            'fields' => 'ids',
            'meta_query' => array(
                array(
                    'key' => '_wp_attached_file',
                    'value' => $file_name,
                    'compare' => 'LIKE',
                ),
            ),
            'posts_per_page' => 1,
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            return $query->posts[0];
        }

        return 0;
    }

    /**
     * Get the module settings HTML.
     *
     * @since    1.0.0
     * @return   string    The module settings HTML.
     */
    public function get_settings_html() {
        $settings = get_option( 'redco_optimizer_media_settings', array() );
        $image_quality = isset( $settings['image_quality'] ) ? $settings['image_quality'] : 82;
        $lazy_load_images = isset( $settings['lazy_load_images'] ) ? $settings['lazy_load_images'] : true;

        ob_start();
        ?>
        <div class="redco-module-settings-section">
            <h3><?php esc_html_e( 'Image Optimization Settings', 'redco-optimizer' ); ?></h3>

            <div class="redco-settings-field">
                <label for="image_quality"><?php esc_html_e( 'Image Quality (1-100)', 'redco-optimizer' ); ?></label>
                <input type="range" id="image_quality" name="image_quality" value="<?php echo esc_attr( $image_quality ); ?>" min="1" max="100" step="1">
                <span class="redco-range-value"><?php echo esc_html( $image_quality ); ?></span>
                <p class="description"><?php esc_html_e( 'Set the quality of optimized images. Higher values mean better quality but larger file sizes.', 'redco-optimizer' ); ?></p>
            </div>

            <div class="redco-settings-field">
                <label>
                    <input type="checkbox" name="lazy_load_images" <?php checked( $lazy_load_images, true ); ?>>
                    <?php esc_html_e( 'Enable Lazy Loading', 'redco-optimizer' ); ?>
                </label>
                <p class="description"><?php esc_html_e( 'Load images only when they are visible in the viewport.', 'redco-optimizer' ); ?></p>
            </div>

            <div class="redco-settings-actions">
                <button type="button" class="redco-button" id="redco-bulk-optimize"><?php esc_html_e( 'Bulk Optimize Images', 'redco-optimizer' ); ?></button>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
