<?php
/**
 * The base module class for all Redco Optimizer modules
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules
 */

/**
 * The base module class.
 *
 * This is used as a base class for all modules in the plugin.
 * It provides common functionality and structure for all modules.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/modules
 * <AUTHOR> <<EMAIL>>
 */
abstract class Redco_Optimizer_Module {

    /**
     * The ID of this module.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $module_id    The ID of this module.
     */
    protected $module_id;

    /**
     * The name of this module.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $name    The name of this module.
     */
    protected $name;

    /**
     * The description of this module.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $description    The description of this module.
     */
    protected $description;

    /**
     * Whether this module is a premium feature.
     *
     * @since    1.0.0
     * @access   protected
     * @var      bool    $is_premium    Whether this module is a premium feature.
     */
    protected $is_premium;

    /**
     * Whether this module is enabled.
     *
     * @since    1.0.0
     * @access   protected
     * @var      bool    $is_enabled    Whether this module is enabled.
     */
    protected $is_enabled;

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Redco_Optimizer_Loader    $loader    Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * Initialize the module.
     *
     * @since    1.0.0
     * @param    string               $module_id       The ID of this module.
     * @param    Redco_Optimizer_Loader $loader          The loader object.
     */
    public function __construct( $module_id, $loader ) {
        $this->module_id = $module_id;
        $this->loader = $loader;

        // Get module settings from options
        $modules = get_option( 'redco_optimizer_modules', array() );

        if ( isset( $modules[$module_id] ) ) {
            // Use 'title' key if available, otherwise fallback to 'name'
            $this->name = isset($modules[$module_id]['title']) ? $modules[$module_id]['title'] :
                         (isset($modules[$module_id]['name']) ? $modules[$module_id]['name'] :
                         ucfirst( str_replace( '-', ' ', $module_id ) ));

            $this->description = isset($modules[$module_id]['description']) ? $modules[$module_id]['description'] : 'A module for Redco Optimizer.';

            // Use 'premium' key if available, otherwise default to false
            $this->is_premium = isset($modules[$module_id]['premium']) ? (bool)$modules[$module_id]['premium'] : false;

            $this->is_enabled = isset($modules[$module_id]['enabled']) ? (bool)$modules[$module_id]['enabled'] : false;
        } else {
            // Default values if not found in options
            $this->name = ucfirst( str_replace( '-', ' ', $module_id ) );
            $this->description = 'A module for Redco Optimizer.';
            $this->is_premium = false;
            $this->is_enabled = false;
        }

        // Only register hooks if the module is enabled
        if ( $this->is_enabled ) {
            $this->register_hooks();
        }
    }

    /**
     * Register the hooks for this module.
     *
     * This method should be implemented by all module classes.
     *
     * @since    1.0.0
     */
    abstract protected function register_hooks();

    /**
     * Get the module ID.
     *
     * @since    1.0.0
     * @return   string    The module ID.
     */
    public function get_module_id() {
        return $this->module_id;
    }

    /**
     * Get the module name.
     *
     * @since    1.0.0
     * @return   string    The module name.
     */
    public function get_name() {
        return $this->name;
    }

    /**
     * Get the module description.
     *
     * @since    1.0.0
     * @return   string    The module description.
     */
    public function get_description() {
        return $this->description;
    }

    /**
     * Check if the module is a premium feature.
     *
     * @since    1.0.0
     * @return   bool    True if the module is a premium feature, false otherwise.
     */
    public function is_premium() {
        return $this->is_premium;
    }

    /**
     * Check if the module is enabled.
     *
     * @since    1.0.0
     * @return   bool    True if the module is enabled, false otherwise.
     */
    public function is_enabled() {
        return $this->is_enabled;
    }

    /**
     * Enable the module.
     *
     * @since    1.0.0
     * @return   bool    True if the module was enabled successfully, false otherwise.
     */
    public function enable() {
        // Check if this is a premium module and user doesn't have premium access
        if ( $this->is_premium && ! $this->has_premium_access() ) {
            // Don't enable premium modules for free users
            return false;
        }

        $this->is_enabled = true;
        $this->update_module_status();
        $this->register_hooks();
        return true;
    }

    /**
     * Disable the module.
     *
     * @since    1.0.0
     * @return   bool    True if the module was disabled successfully, false otherwise.
     */
    public function disable() {
        $this->is_enabled = false;
        $this->update_module_status();
        return true;
    }

    /**
     * Check if the user has premium access.
     *
     * @since    1.0.0
     * @return   bool    True if the user has premium access, false otherwise.
     */
    protected function has_premium_access() {
        // This should be implemented with your licensing system
        // For the free version, always return false to disable premium features

        // Check if license is valid and active
        $license_status = get_option('redco_optimizer_license_status', '');
        $license_key = get_option('redco_optimizer_license_key', '');

        // Only return true if license is valid and active
        if ($license_status === 'valid' && !empty($license_key)) {
            return true;
        }

        // Default to false for free version
        return false;
    }

    /**
     * Update the module status in the options.
     *
     * @since    1.0.0
     */
    protected function update_module_status() {
        $modules = get_option( 'redco_optimizer_modules', array() );

        if ( isset( $modules[$this->module_id] ) ) {
            $modules[$this->module_id]['enabled'] = $this->is_enabled;

            // Also update 'active' status if it exists
            if (isset($modules[$this->module_id]['active'])) {
                $modules[$this->module_id]['active'] = $this->is_enabled;
            }

            update_option( 'redco_optimizer_modules', $modules );
        }
    }

    /**
     * Get the module settings HTML.
     *
     * This method can be overridden by module classes to provide custom settings.
     *
     * @since    1.0.0
     * @return   string    The module settings HTML.
     */
    public function get_settings_html() {
        return '<p>' . esc_html__( 'No settings available for this module.', 'redco-optimizer' ) . '</p>';
    }
}
