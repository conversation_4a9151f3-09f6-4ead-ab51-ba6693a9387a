<?php

/**
 * The Remove Unused CSS functionality of the plugin.
 *
 * @link       https://redcodesolutions.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The Remove Unused CSS functionality of the plugin.
 *
 * Defines the functionality for removing unused CSS from pages.
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Solutions <<EMAIL>>
 */
class Redco_Optimizer_Unused_CSS {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * The settings for unused CSS.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $settings    The settings for unused CSS.
     */
    private $settings;

    /**
     * The cache directory for used CSS.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $used_css_dir    The cache directory for used CSS.
     */
    private $used_css_dir;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $this->get_settings();
        $this->used_css_dir = WP_CONTENT_DIR . '/cache/redco-optimizer/used-css/';
    }

    /**
     * Get unused CSS settings.
     *
     * @since    1.0.0
     * @return   array    The unused CSS settings.
     */
    private function get_settings() {
        $settings = get_option( 'redco_optimizer_file_optimization_settings', array() );
        
        // Default settings
        $defaults = array(
            'remove_unused_css' => 0,
            'css_safelist' => '',
        );
        
        // Merge settings with defaults
        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Initialize unused CSS removal.
     *
     * @since    1.0.0
     */
    public function init() {
        // Check if remove unused CSS is enabled
        if ( ! $this->settings['remove_unused_css'] ) {
            return;
        }

        // Create used CSS directory if it doesn't exist
        if ( ! file_exists( $this->used_css_dir ) ) {
            wp_mkdir_p( $this->used_css_dir );
        }

        // Add hooks for processing pages
        add_filter( 'redco_buffer', array( $this, 'process_page' ), 20 );
        
        // Add hooks for clearing used CSS
        add_action( 'save_post', array( $this, 'clear_post_used_css' ) );
        add_action( 'edit_post', array( $this, 'clear_post_used_css' ) );
        add_action( 'delete_post', array( $this, 'clear_post_used_css' ) );
        add_action( 'wp_trash_post', array( $this, 'clear_post_used_css' ) );
        
        // Add hooks for comments
        add_action( 'comment_post', array( $this, 'clear_post_used_css_on_comment' ) );
        add_action( 'edit_comment', array( $this, 'clear_post_used_css_on_comment' ) );
        add_action( 'delete_comment', array( $this, 'clear_post_used_css_on_comment' ) );
        add_action( 'wp_set_comment_status', array( $this, 'clear_post_used_css_on_comment' ) );
        
        // Add AJAX handler for clearing used CSS
        add_action( 'wp_ajax_redco_clear_used_css', array( $this, 'ajax_clear_used_css' ) );
        
        // Add cron event for cleaning used CSS
        if ( ! wp_next_scheduled( 'redco_clean_used_css' ) ) {
            wp_schedule_event( time(), 'daily', 'redco_clean_used_css' );
        }
        add_action( 'redco_clean_used_css', array( $this, 'clean_used_css' ) );
    }

    /**
     * Process page content to remove unused CSS.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content of the page.
     * @return   string    The processed HTML content.
     */
    public function process_page( $html ) {
        // Don't process if user is logged in
        if ( is_user_logged_in() ) {
            return $html;
        }
        
        // Don't process admin pages
        if ( is_admin() ) {
            return $html;
        }
        
        // Get the current URL
        $url = $this->get_current_url();
        
        // Generate a unique hash for this URL
        $hash = md5( $url );
        
        // Check if we have used CSS for this URL
        $used_css_file = $this->get_used_css_file_path( $hash );
        
        if ( file_exists( $used_css_file ) ) {
            // We have used CSS, apply it to the page
            $used_css = file_get_contents( $used_css_file );
            
            // Remove all CSS files and inline CSS
            $html = $this->remove_css( $html );
            
            // Add used CSS to the page
            $html = $this->add_used_css( $html, $used_css );
        } else {
            // We don't have used CSS yet, generate it
            $this->generate_used_css( $url, $hash, $html );
        }
        
        return $html;
    }

    /**
     * Get the current URL.
     *
     * @since    1.0.0
     * @return   string    The current URL.
     */
    private function get_current_url() {
        $protocol = ( isset( $_SERVER['HTTPS'] ) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http' );
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'];
        
        return $protocol . '://' . $host . $uri;
    }

    /**
     * Get the file path for used CSS.
     *
     * @since    1.0.0
     * @param    string    $hash    The hash of the URL.
     * @return   string    The file path for used CSS.
     */
    private function get_used_css_file_path( $hash ) {
        $dir_path = $this->used_css_dir . substr( $hash, 0, 1 ) . '/' . substr( $hash, 1, 1 ) . '/';
        
        if ( ! file_exists( $dir_path ) ) {
            wp_mkdir_p( $dir_path );
        }
        
        return $dir_path . $hash . '.css';
    }

    /**
     * Remove all CSS from HTML.
     *
     * @since    1.0.0
     * @param    string    $html    The HTML content.
     * @return   string    The HTML content without CSS.
     */
    private function remove_css( $html ) {
        // Remove link tags with rel="stylesheet"
        $html = preg_replace( '/<link[^>]*rel=["\']stylesheet["\'][^>]*>/i', '', $html );
        
        // Remove style tags
        $html = preg_replace( '/<style[^>]*>.*?<\/style>/is', '', $html );
        
        return $html;
    }

    /**
     * Add used CSS to HTML.
     *
     * @since    1.0.0
     * @param    string    $html       The HTML content.
     * @param    string    $used_css   The used CSS content.
     * @return   string    The HTML content with used CSS.
     */
    private function add_used_css( $html, $used_css ) {
        // Add used CSS after </title> tag
        $html = preg_replace( '/<\/title>/i', '</title><style id="redco-used-css">' . $used_css . '</style>', $html, 1 );
        
        return $html;
    }

    /**
     * Generate used CSS for a URL.
     *
     * @since    1.0.0
     * @param    string    $url     The URL to generate used CSS for.
     * @param    string    $hash    The hash of the URL.
     * @param    string    $html    The HTML content of the page.
     */
    private function generate_used_css( $url, $hash, $html ) {
        // In a real implementation, this would use a service to analyze the page and extract used CSS
        // For now, we'll just extract all CSS from the page as a placeholder
        
        $css = '';
        
        // Extract CSS from link tags
        preg_match_all( '/<link[^>]*rel=["\']stylesheet["\'][^>]*href=["\']([^"\']*)["\'][^>]*>/i', $html, $matches );
        
        if ( ! empty( $matches[1] ) ) {
            foreach ( $matches[1] as $css_url ) {
                // Get CSS content
                $response = wp_remote_get( $css_url );
                
                if ( ! is_wp_error( $response ) && 200 === wp_remote_retrieve_response_code( $response ) ) {
                    $css .= wp_remote_retrieve_body( $response );
                }
            }
        }
        
        // Extract CSS from style tags
        preg_match_all( '/<style[^>]*>(.*?)<\/style>/is', $html, $matches );
        
        if ( ! empty( $matches[1] ) ) {
            foreach ( $matches[1] as $style ) {
                $css .= $style;
            }
        }
        
        // Add CSS safelist items
        if ( ! empty( $this->settings['css_safelist'] ) ) {
            $css .= $this->settings['css_safelist'];
        }
        
        // Save used CSS to file
        $used_css_file = $this->get_used_css_file_path( $hash );
        file_put_contents( $used_css_file, $css );
    }

    /**
     * Clear used CSS for a post.
     *
     * @since    1.0.0
     * @param    int    $post_id    The post ID.
     */
    public function clear_post_used_css( $post_id ) {
        // Don't clear for revisions or auto-drafts
        if ( wp_is_post_revision( $post_id ) || wp_is_post_autosave( $post_id ) ) {
            return;
        }
        
        // Clear used CSS for this post
        $post_url = get_permalink( $post_id );
        $this->clear_url_used_css( $post_url );
        
        // Clear home page used CSS
        $this->clear_url_used_css( home_url() );
    }

    /**
     * Clear post used CSS on comment.
     *
     * @since    1.0.0
     * @param    int    $comment_id    The comment ID.
     */
    public function clear_post_used_css_on_comment( $comment_id ) {
        $comment = get_comment( $comment_id );
        if ( $comment && isset( $comment->comment_post_ID ) ) {
            $this->clear_post_used_css( $comment->comment_post_ID );
        }
    }

    /**
     * Clear URL used CSS.
     *
     * @since    1.0.0
     * @param    string    $url    The URL to clear used CSS for.
     */
    public function clear_url_used_css( $url ) {
        $hash = md5( $url );
        $used_css_file = $this->get_used_css_file_path( $hash );
        
        if ( file_exists( $used_css_file ) ) {
            unlink( $used_css_file );
        }
    }

    /**
     * Clear all used CSS.
     *
     * @since    1.0.0
     */
    public function clear_all_used_css() {
        $this->delete_directory_contents( $this->used_css_dir );
        return true;
    }

    /**
     * Delete directory contents.
     *
     * @since    1.0.0
     * @param    string    $dir    The directory to delete contents from.
     */
    private function delete_directory_contents( $dir ) {
        if ( ! is_dir( $dir ) ) {
            return;
        }
        
        $files = scandir( $dir );
        foreach ( $files as $file ) {
            if ( $file === '.' || $file === '..' ) {
                continue;
            }
            
            $path = $dir . '/' . $file;
            if ( is_dir( $path ) ) {
                $this->delete_directory_contents( $path );
                rmdir( $path );
            } else {
                unlink( $path );
            }
        }
    }

    /**
     * AJAX handler for clearing used CSS.
     *
     * @since    1.0.0
     */
    public function ajax_clear_used_css() {
        // Check nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'redco_optimizer_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed.', 'redco-optimizer' ) ) );
        }
        
        // Check user capabilities
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to perform this action.', 'redco-optimizer' ) ) );
        }
        
        // Clear used CSS
        $result = $this->clear_all_used_css();
        
        if ( $result ) {
            wp_send_json_success( array( 'message' => __( 'Used CSS cleared successfully.', 'redco-optimizer' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to clear used CSS.', 'redco-optimizer' ) ) );
        }
    }

    /**
     * Clean used CSS files older than 30 days.
     *
     * @since    1.0.0
     */
    public function clean_used_css() {
        $this->clean_old_used_css_files( $this->used_css_dir, 30 * DAY_IN_SECONDS );
    }

    /**
     * Clean old used CSS files.
     *
     * @since    1.0.0
     * @param    string    $dir       The directory to clean.
     * @param    int       $max_age   The maximum age of files in seconds.
     */
    private function clean_old_used_css_files( $dir, $max_age ) {
        if ( ! is_dir( $dir ) ) {
            return;
        }
        
        $files = scandir( $dir );
        foreach ( $files as $file ) {
            if ( $file === '.' || $file === '..' ) {
                continue;
            }
            
            $path = $dir . '/' . $file;
            if ( is_dir( $path ) ) {
                $this->clean_old_used_css_files( $path, $max_age );
                
                // Remove directory if empty
                $is_empty = ( count( scandir( $path ) ) === 2 ); // Only . and ..
                if ( $is_empty ) {
                    rmdir( $path );
                }
            } else {
                // Check file age
                $file_time = filemtime( $path );
                if ( ( time() - $file_time ) > $max_age ) {
                    unlink( $path );
                }
            }
        }
    }
}
