/**
 * Redco Optimizer Help Page Styles
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 */

/* Help Page Container */
.redco-help-container {
    display: flex !important;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    overflow: hidden;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-help-sidebar {
    width: 280px;
    background: #f8f9fa;
    border-right: 1px solid #e2e4e7;
    padding: 20px 0;
    flex-shrink: 0;
}

.redco-help-search {
    padding: 0 20px 20px;
    border-bottom: 1px solid #e2e4e7;
    margin-bottom: 20px;
}

.redco-help-search input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.redco-help-topics {
    list-style: none;
    margin: 0;
    padding: 0;
}

.redco-help-topic {
    padding: 12px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.redco-help-topic:hover {
    background: #f0f0f1;
    border-left-color: #00A66B;
}

.redco-help-topic.active {
    background: #00A66B;
    color: #fff;
    border-left-color: #008c5a;
}

.redco-help-topic .dashicons {
    margin-right: 10px;
    color: #646970;
}

.redco-help-topic.active .dashicons {
    color: #fff;
}

.redco-help-content-container {
    flex: 1 !important;
    padding: 30px;
    overflow-y: auto;
    max-height: calc(100vh - 200px);
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-help-content {
    display: none;
    animation: fadeIn 0.3s ease;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-help-content.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-help-content h2 {
    font-size: 24px;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e4e7;
    color: #1d2327;
}

.redco-help-section {
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f1;
}

.redco-help-section:last-child {
    border-bottom: none;
}

.redco-help-section h3 {
    font-size: 20px;
    margin-top: 30px;
    margin-bottom: 15px;
    color: #1d2327;
}

.redco-help-section h4 {
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 10px;
    color: #1d2327;
}

.redco-help-intro {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
    color: #50575e;
}

.redco-help-image-container {
    margin: 20px 0;
    text-align: center;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    overflow: hidden;
    background: #f8f9fa;
}

.redco-help-image {
    max-width: 100%;
    height: auto;
    display: block;
}

.redco-help-actual-screenshot {
    display: block;
    width: 100%;
    background: #fff;
    padding: 0;
    margin: 0;
    border: none;
}

.redco-help-actual-screenshot img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

.redco-help-image-caption {
    padding: 10px;
    background: #f8f9fa;
    border-top: 1px solid #e2e4e7;
    margin: 0;
    font-size: 13px;
    color: #646970;
}

.redco-help-tip,
.redco-help-note,
.redco-help-warning {
    padding: 15px 20px;
    border-radius: 4px;
    margin: 20px 0;
}

.redco-help-tip {
    background: #e6f6ef;
    border-left: 4px solid #00A66B;
}

.redco-help-note {
    background: #f0f6fc;
    border-left: 4px solid #72aee6;
}

.redco-help-warning {
    background: #fcf9e8;
    border-left: 4px solid #dba617;
}

.redco-help-feature-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.redco-help-feature-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
}

.redco-help-feature-list li:last-child {
    border-bottom: none;
}

.redco-help-numbered-list {
    padding-left: 20px;
    margin: 15px 0;
}

.redco-help-numbered-list li {
    margin-bottom: 10px;
}

.redco-help-steps {
    margin: 30px 0;
}

.redco-help-step {
    display: flex;
    margin-bottom: 30px;
    position: relative;
}

.redco-help-step-number {
    width: 36px;
    height: 36px;
    background: #00A66B;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.redco-help-step-content {
    flex: 1;
}

.redco-help-step-content h4 {
    margin-top: 5px;
}

/* FAQ Styling */
.redco-help-faq-container {
    margin: 20px 0;
}

.redco-help-faq-item {
    margin-bottom: 25px;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    overflow: hidden;
}

.redco-help-faq-item h3 {
    margin: 0;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e2e4e7;
    font-size: 16px;
    cursor: pointer;
    position: relative;
    transition: background-color 0.2s ease;
}

.redco-help-faq-item h3:hover {
    background-color: #f0f0f1;
}

.redco-help-faq-item.active h3 {
    border-bottom: none;
    background-color: #f0f0f1;
}

.redco-help-faq-item h3:after {
    content: '\f140';
    font-family: dashicons;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #646970;
    transition: transform 0.2s ease;
}

.redco-help-faq-item.active h3:after {
    transform: translateY(-50%) rotate(180deg);
}

.redco-help-faq-answer {
    padding: 20px;
    background: #fff;
    border-top: 1px solid #e2e4e7;
    line-height: 1.6;
}

.redco-help-faq-answer p:first-child {
    margin-top: 0;
}

.redco-help-faq-answer p:last-child {
    margin-bottom: 0;
}

/* Troubleshooting Styling */
.redco-help-issue-container {
    margin: 20px 0;
}

.redco-help-issue-card {
    margin-bottom: 25px;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    overflow: hidden;
}

.redco-help-issue-card h4 {
    margin: 0;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e2e4e7;
    font-size: 16px;
}

.redco-help-issue-content {
    padding: 20px;
    background: #fff;
}

.redco-help-issue-content p {
    margin-top: 0;
}

.redco-help-issue-content ul,
.redco-help-issue-content ol {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 20px;
}

.redco-help-issue-content li {
    margin-bottom: 5px;
}

/* Placeholder styling */
.redco-help-placeholder {
    background-color: #f0f0f1;
    border: 1px dashed #c3c4c7;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    width: 100%;
    color: #646970;
    font-size: 14px;
    text-align: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.redco-help-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #00A66B 0%, #00c77f 100%);
}

.redco-help-placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.redco-help-placeholder .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 15px;
    color: #00A66B;
}

.redco-help-placeholder-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #3c434a;
}

.redco-help-placeholder-subtitle {
    font-size: 13px;
    color: #646970;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 782px) {
    .redco-help-container {
        flex-direction: column;
    }

    .redco-help-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e2e4e7;
    }

    .redco-help-content-container {
        max-height: none;
    }

    .redco-help-troubleshooting {
        grid-template-columns: 1fr;
    }
}

/* Force visibility for all help elements */
.redco-help-container,
.redco-help-sidebar,
.redco-help-content-container,
.redco-help-content.active,
.redco-help-section,
.redco-help-image-container,
.redco-help-faq-container,
.redco-help-faq-item,
.redco-help-faq-item.active .redco-help-faq-answer {
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-help-content.active {
    display: block !important;
}

.redco-help-container {
    display: flex !important;
}

.redco-help-content-container {
    display: block !important;
    flex: 1 !important;
}
