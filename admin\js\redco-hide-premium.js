/**
 * JavaScript to hide premium elements in Redco Optimizer
 *
 * This file contains scripts to hide the "Upgrade to Premium" buttons
 * and related premium elements from the Redco Optimizer interface.
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Hide premium tab in sidebar navigation
        $('.redco-nav-item[data-tab="redco-premium-tab"]').hide();

        // Hide Upgrade to Premium button in page header
        $('.redco-header-action-button[data-tab="redco-premium-tab"]').hide();

        // Hide premium-related buttons in modules
        $('.redco-button-premium.redco-premium-tab-link, .redco-button-premium[data-tab="redco-premium-tab"]').hide();

        // Hide premium badges
        $('.redco-premium-badge, .redco-premium-badge-below, .redco-premium-tag').hide();

        // Hide premium status items in the Optimization Status section
        $('.redco-status-item .redco-status-icon.premium').closest('.redco-status-item').hide();

        // Hide premium-related content in the dashboard
        $('#redco-premium-tab').hide();

        // Hide premium-related CTA sections
        $('.redco-premium-cta, .redco-premium-hero').hide();

        // Hide View My Account button that links to premium page
        $('.redco-view-account').hide();

        // Hide premium notices in add-ons
        $('.redco-premium-notice').hide();

        // Hide premium features in status items by looking for text
        $('.redco-status-item .redco-status-info p').each(function() {
            if ($(this).text().toLowerCase().indexOf('premium') !== -1) {
                $(this).closest('.redco-status-item').hide();
            }
        });

        // Hide sidebar footer
        $('.redco-sidebar-footer').hide();
    });

})(jQuery);
