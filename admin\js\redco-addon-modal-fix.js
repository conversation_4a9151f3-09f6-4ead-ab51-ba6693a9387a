/**
 * Redco Optimizer - Add-on Modal Fix
 * Ensures all sections in add-on modals are visible
 */

(function($) {
    'use strict';

    // Run when document is ready
    $(document).ready(function() {
        console.log('Redco Add-on Modal Fix loaded');

        // Add event listener for all add-on settings buttons
        $(document).on('click', '.redco-addon-settings', function() {
            var addonName = $(this).closest('.redco-addon-card').find('.redco-addon-title').text().trim();
            console.log('Add-on settings button clicked: ' + addonName);

            // Wait for the modal to open
            setTimeout(function() { fixAddonModal(addonName); }, 300);
            setTimeout(function() { fixAddonModal(addonName); }, 600);
            setTimeout(function() { fixAddonModal(addonName); }, 1200);
        });

        // Also listen for AJAX success events
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('admin-ajax.php') !== -1) {
                if (settings.data && settings.data.indexOf('redco_get_addon_settings') !== -1) {
                    console.log('Add-on AJAX detected');

                    // Extract the add-on name from the data
                    var match = settings.data.match(/addon=([^&]+)/);
                    var addonName = match ? decodeURIComponent(match[1]).replace(/-/g, ' ') : '';

                    if (addonName) {
                        console.log('Add-on name from AJAX: ' + addonName);

                        // Wait for the modal to be populated
                        setTimeout(function() { fixAddonModal(addonName); }, 300);
                        setTimeout(function() { fixAddonModal(addonName); }, 600);
                        setTimeout(function() { fixAddonModal(addonName); }, 1200);
                    }
                }
            }
        });
    });

    /**
     * Fix an add-on modal
     */
    function fixAddonModal(addonName) {
        console.log('Fixing modal for add-on: ' + addonName);

        // Check if a modal is open
        if ($('.redco-modal:visible').length === 0) {
            console.log('No modal is open');
            return;
        }

        // Make the modal taller
        $('.redco-modal').css({
            'max-height': '95vh',
            'height': 'auto',
            'width': '800px',
            'max-width': '95%',
            'display': 'flex',
            'flex-direction': 'column',
            'overflow': 'hidden'
        });

        // Make the content area scrollable
        $('.redco-modal-content').css({
            'max-height': 'calc(95vh - 140px)',
            'overflow-y': 'auto',
            'overflow-x': 'hidden',
            'flex': '1 1 auto',
            'padding': '25px',
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });

        // Force all sections to be visible
        $('.redco-modal-section').show().css({
            'display': 'block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '25px !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: block !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 25px !important;';
        });

        // Force all form rows to be visible
        $('.redco-form-row, .redco-setting-row').show().css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'margin-bottom': '15px !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; margin-bottom: 15px !important;';
        });

        // Force all toggle rows to be visible
        $('.redco-toggle-row').show().css({
            'display': 'flex !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'justify-content': 'space-between !important',
            'align-items': 'center !important',
            'margin-bottom': '15px !important'
        }).attr('style', function(i, s) {
            return (s || '') + 'display: flex !important; visibility: visible !important; opacity: 1 !important; justify-content: space-between !important; align-items: center !important; margin-bottom: 15px !important;';
        });

        // Add a scroll indicator at the bottom if not already present
        if ($('.redco-scroll-indicator-bottom').length === 0) {
            $('<div>')
                .addClass('redco-scroll-indicator-bottom')
                .html('<div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-top: 20px; text-align: center;">' +
                      '<p style="font-weight: bold; margin: 0;">End of settings</p>' +
                      '<div style="margin-top: 5px;">Don\'t forget to save your changes!</div>' +
                      '</div>')
                .appendTo('.redco-modal-content');
        }

        // Apply specific fixes based on the add-on name
        switch (addonName.toLowerCase()) {
            case 'font optimizer':
                fixFontOptimizerModal();
                break;
            case 'schema markup generator':
                fixSchemaMarkupGeneratorModal();
                break;
            case 'critical css generator':
                fixCriticalCssGeneratorModal();
                break;
        }

        // Log the number of visible sections
        console.log('Visible sections: ' + $('.redco-modal-section:visible').length);
        console.log('Form rows visible: ' + $('.redco-form-row:visible, .redco-setting-row:visible').length);
        console.log('Toggle rows visible: ' + $('.redco-toggle-row:visible').length);
    }

    /**
     * Fix Font Optimizer modal
     */
    function fixFontOptimizerModal() {
        console.log('Applying specific fixes for Font Optimizer modal');

        // Check for missing sections
        var expectedSections = [
            'Font Optimization Settings',
            'Google Fonts Settings',
            'Local Fonts Settings',
            'Font Display Settings'
        ];

        checkAndFixMissingSections(expectedSections);
    }


    /**
     * Fix Schema Markup Generator modal
     */
    function fixSchemaMarkupGeneratorModal() {
        console.log('Applying specific fixes for Schema Markup Generator modal');

        // Check for missing sections
        var expectedSections = [
            'Schema Settings',
            'Organization Schema',
            'Website Schema',
            'Article Schema'
        ];

        checkAndFixMissingSections(expectedSections);
    }


    /**
     * Fix Critical CSS Generator modal
     */
    function fixCriticalCssGeneratorModal() {
        console.log('Applying specific fixes for Critical CSS Generator modal');

        // Check for missing sections
        var expectedSections = [
            'Critical CSS Settings',
            'Generation Settings',
            'Advanced Settings'
        ];

        checkAndFixMissingSections(expectedSections);
    }

    /**
     * Check for missing sections and fix them
     */
    function checkAndFixMissingSections(expectedSections) {
        var missingSections = [];

        expectedSections.forEach(function(section) {
            if ($('.redco-modal-content h3:contains("' + section + '")').length === 0) {
                missingSections.push(section);
            }
        });

        if (missingSections.length > 0) {
            console.log('Missing sections detected: ' + missingSections.join(', '));

            // Add a debug message to the modal
            if ($('.redco-modal-debug-message').length === 0) {
                $('<div>')
                    .addClass('redco-modal-debug-message')
                    .html('<div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; text-align: center;">' +
                          '<p style="font-weight: bold; margin: 0;">Debug: Some sections may be hidden. Try scrolling down to see all options.</p>' +
                          '<div style="margin-top: 10px; font-size: 24px; animation: bounce 1s infinite;">↓</div>' +
                          '</div>' +
                          '<style>@keyframes bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }</style>')
                    .prependTo('.redco-modal-content');
            }
        } else {
            console.log('All expected sections found');
        }
    }

})(jQuery);
