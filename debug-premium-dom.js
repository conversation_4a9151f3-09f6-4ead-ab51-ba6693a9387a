// Debug Premium Tab DOM
console.log('=== PREMIUM TAB DOM DEBUG ===');

// 1. Check if premium tab exists in DOM
const premiumTab = document.getElementById('redco-premium-tab');
console.log('1. Premium tab element exists:', !!premiumTab);
if (premiumTab) {
    console.log('   - Display style:', window.getComputedStyle(premiumTab).display);
    console.log('   - Visibility style:', window.getComputedStyle(premiumTab).visibility);
    console.log('   - Opacity style:', window.getComputedStyle(premiumTab).opacity);
    console.log('   - Inline style:', premiumTab.style.cssText);
    console.log('   - Class list:', premiumTab.classList.toString());
    console.log('   - Content length:', premiumTab.innerHTML.length);
    console.log('   - Content preview:', premiumTab.innerHTML.substring(0, 200) + '...');
}

// 2. Check if premium nav item exists
const premiumNavItem = document.querySelector('.redco-nav-item[data-tab="redco-premium-tab"]');
console.log('2. Premium nav item exists:', !!premiumNavItem);
if (premiumNavItem) {
    console.log('   - Display style:', window.getComputedStyle(premiumNavItem).display);
    console.log('   - Class list:', premiumNavItem.classList.toString());
    console.log('   - Data tab:', premiumNavItem.getAttribute('data-tab'));
}

// 3. Check all tab content elements
const allTabs = document.querySelectorAll('.redco-tab-content');
console.log('3. All tab content elements:', allTabs.length);
allTabs.forEach((tab, index) => {
    console.log(`   Tab ${index + 1}: ${tab.id}`);
    console.log(`     - Display: ${window.getComputedStyle(tab).display}`);
    console.log(`     - Visibility: ${window.getComputedStyle(tab).visibility}`);
    console.log(`     - Opacity: ${window.getComputedStyle(tab).opacity}`);
});

// 4. Check if switchTab function exists
console.log('4. switchTab function exists:', typeof window.switchTab);

// 5. Test switching to premium tab
console.log('5. Testing premium tab switch...');
if (typeof window.switchTab === 'function') {
    try {
        window.switchTab('redco-premium-tab');
        console.log('   - Switch function called successfully');
        
        // Check state after switch
        setTimeout(() => {
            const premiumTabAfter = document.getElementById('redco-premium-tab');
            if (premiumTabAfter) {
                console.log('   - After switch display:', window.getComputedStyle(premiumTabAfter).display);
                console.log('   - After switch visibility:', window.getComputedStyle(premiumTabAfter).visibility);
                console.log('   - After switch opacity:', window.getComputedStyle(premiumTabAfter).opacity);
            }
        }, 500);
    } catch (error) {
        console.error('   - Error calling switchTab:', error);
    }
} else {
    console.log('   - switchTab function not available');
}

console.log('=== DEBUG COMPLETE ===');
