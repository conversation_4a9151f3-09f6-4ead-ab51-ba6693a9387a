<?php
/**
 * The file that defines the addon functionality
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 */

/**
 * The addon functionality class.
 *
 * This class handles all addon-related functionality including
 * loading, activating, deactivating, and managing addons.
 *
 * @since      1.0.0
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/includes
 * <AUTHOR> Team <<EMAIL>>
 */
class Redco_Optimizer_Addons {

    /**
     * The addons directory path.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $addons_dir    The addons directory path.
     */
    private $addons_dir;

    /**
     * The addons URL.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $addons_url    The addons URL.
     */
    private $addons_url;

    /**
     * The installed addons.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $installed_addons    The installed addons.
     */
    private $installed_addons = array();

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->addons_dir = plugin_dir_path(dirname(__FILE__)) . 'addons/';
        $this->addons_url = plugin_dir_url(dirname(__FILE__)) . 'addons/';
        $this->load_installed_addons();
    }

    /**
     * Load installed addons.
     *
     * @since    1.0.0
     */
    private function load_installed_addons() {
        // Create addons directory if it doesn't exist
        if (!file_exists($this->addons_dir)) {
            wp_mkdir_p($this->addons_dir);
        }

        // Get active addons from options
        $active_addons = get_option('redco_active_addons', array());

        // Get installed addons from options
        $saved_addons = get_option('redco_optimizer_installed_addons', array());
        if (!empty($saved_addons)) {
            $this->installed_addons = $saved_addons;
        }

        // Scan addons directory
        $addon_dirs = glob($this->addons_dir . '*', GLOB_ONLYDIR);

        if (!empty($addon_dirs)) {
            foreach ($addon_dirs as $addon_dir) {
                $addon_slug = basename($addon_dir);
                $addon_file = $addon_dir . '/' . $addon_slug . '.php';

                if (file_exists($addon_file)) {
                    // Get addon data from file
                    $addon_data = $this->get_addon_data($addon_file);

                    if (!empty($addon_data)) {
                        $addon_data['slug'] = $addon_slug;
                        $addon_data['active'] = isset($active_addons[$addon_slug]) ? true : false;
                        $addon_data['file'] = $addon_file;
                        $addon_data['dir'] = $addon_dir;
                        $addon_data['url'] = $this->addons_url . $addon_slug;

                        $this->installed_addons[$addon_slug] = $addon_data;

                        // Load active addons
                        if ($addon_data['active']) {
                            include_once $addon_file;
                        }
                    }
                }
            }
        }
    }

    /**
     * Get addon data from file.
     *
     * @since    1.0.0
     * @param    string    $file    The addon file path.
     * @return   array     The addon data.
     */
    private function get_addon_data($file) {
        $default_headers = array(
            'Name'        => 'Addon Name',
            'Description' => 'Description',
            'Version'     => 'Version',
            'Author'      => 'Author',
            'AuthorURI'   => 'Author URI',
            'Premium'     => 'Premium',
            'HasSettings' => 'Has Settings',
            'Icon'        => 'Icon',
            'Dashicon'    => 'Dashicon',
            'ComingSoon'  => 'Coming Soon',
        );

        $addon_data = get_file_data($file, $default_headers);

        // Convert Premium, HasSettings, and ComingSoon to boolean
        $addon_data['Premium'] = filter_var($addon_data['Premium'], FILTER_VALIDATE_BOOLEAN);
        $addon_data['HasSettings'] = filter_var($addon_data['HasSettings'], FILTER_VALIDATE_BOOLEAN);
        $addon_data['ComingSoon'] = filter_var($addon_data['ComingSoon'], FILTER_VALIDATE_BOOLEAN);

        // Format addon data
        return array(
            'name'         => $addon_data['Name'],
            'description'  => $addon_data['Description'],
            'version'      => $addon_data['Version'],
            'author'       => $addon_data['Author'],
            'author_uri'   => $addon_data['AuthorURI'],
            'premium'      => $addon_data['Premium'],
            'has_settings' => $addon_data['HasSettings'],
            'icon'         => $addon_data['Icon'],
            'dashicon'     => $addon_data['Dashicon'],
            'coming_soon'  => $addon_data['ComingSoon'],
        );
    }

    /**
     * Get installed addons.
     *
     * @since    1.0.0
     * @return   array    The installed addons.
     */
    public function get_installed_addons() {
        return $this->installed_addons;
    }

    /**
     * Get available add-ons.
     *
     * @since    1.0.0
     * @return   array    The available add-ons.
     */
    public function get_available_addons() {
        // Check if we have a cached version of the available add-ons
        $cached_addons = get_transient('redco_optimizer_addons_list');
        if (false !== $cached_addons) {
            return $cached_addons;
        }

        // In a real implementation, this would fetch available add-ons from a remote API
        // For now, we'll return a static list of available add-ons

        $available_addons = array(
            // Removed CDN Manager add-on
            // 'cdn-manager' => array(
            //     'name' => 'CDN Manager',
            //     'description' => 'Advanced CDN integration with multiple providers including Cloudflare, BunnyCDN, KeyCDN, and more.',
            //     'version' => '1.0.0',
            //     'author' => 'Redco',
            //     'author_url' => 'https://redco-optimizer.com',
            //     'premium' => true,
            //     'icon' => 'dashicons-networking',
            //     'has_settings' => true,
            //     'coming_soon' => true,
            // ),
            'advanced-cache-preloader' => array(
                'name' => 'Advanced Cache Preloader',
                'description' => 'Intelligent cache preloading with sitemap integration, scheduled preloading, and priority-based crawling.',
                'version' => '1.0.0',
                'author' => 'Redco',
                'author_url' => 'https://redco-optimizer.com',
                'premium' => false,
                'icon' => 'dashicons-performance',
                'has_settings' => true,
            ),
            'schema-markup-generator' => array(
                'name' => 'Schema Markup Generator',
                'description' => 'Generate structured data for better SEO with support for multiple schema types including Article, Product, FAQ, and more.',
                'version' => '1.0.0',
                'author' => 'Redco',
                'author_url' => 'https://redco-optimizer.com',
                'premium' => true,
                'icon' => 'dashicons-editor-code',
                'has_settings' => true,
                'coming_soon' => true,
            ),
            'font-optimizer' => array(
                'name' => 'Font Optimizer',
                'description' => 'Optimize web fonts with local hosting, subsetting, preloading, and font-display optimization for better performance.',
                'version' => '1.0.0',
                'author' => 'Redco',
                'author_url' => 'https://redco-optimizer.com',
                'premium' => false,
                'icon' => 'dashicons-editor-textcolor',
                'has_settings' => true,
            ),
            // Add the add-ons that are displayed in the admin interface
            // Removed Advanced Lazy Load add-on
            // 'advanced-lazy-load' => array(
            //     'name' => 'Advanced Lazy Load',
            //     'description' => 'Enhanced lazy loading for images, iframes, and videos with advanced options and exclusions.',
            //     'version' => '1.0.0',
            //     'author' => 'Redco',
            //     'author_url' => 'https://redco-optimizer.com',
            //     'premium' => false,
            //     'icon' => 'dashicons-images-alt2',
            //     'has_settings' => true,
            //     'coming_soon' => true,
            // ),
            // Removed WebP Converter add-on
            // 'webp-converter' => array(
            //     'name' => 'WebP Converter',
            //     'description' => 'Automatically convert your images to WebP format for faster loading and better performance.',
            //     'version' => '1.0.0',
            //     'author' => 'Redco',
            //     'author_url' => 'https://redco-optimizer.com',
            //     'premium' => true,
            //     'icon' => 'dashicons-format-image',
            //     'has_settings' => true,
            //     'coming_soon' => true,
            // ),
            'critical-css-generator' => array(
                'name' => 'Critical CSS Generator',
                'description' => 'Generate and inline critical CSS to eliminate render-blocking CSS and improve page load times.',
                'version' => '1.0.0',
                'author' => 'Redco',
                'author_url' => 'https://redco-optimizer.com',
                'premium' => false,
                'icon' => 'dashicons-admin-appearance',
                'has_settings' => true,
            ),
            // Add more available add-ons that are not coming soon
            // Removed Image Optimizer add-on because it conflicts with the Image Optimizer module
            // 'image-optimizer' => array(
            //     'name' => 'Image Optimizer',
            //     'description' => 'Optimize your images with advanced compression techniques to reduce file size without losing quality.',
            //     'version' => '1.0.0',
            //     'author' => 'Redco',
            //     'author_url' => 'https://redco-optimizer.com',
            //     'premium' => false,
            //     'icon' => 'dashicons-format-image',
            //     'has_settings' => true,
            //     'coming_soon' => false,
            // ),
            'security-enhancer' => array(
                'name' => 'Security Enhancer',
                'description' => 'Enhance your website security with advanced features like malware scanning, firewall protection, and login security.',
                'version' => '1.0.0',
                'author' => 'Redco',
                'author_url' => 'https://redco-optimizer.com',
                'premium' => false,
                'icon' => 'dashicons-shield',
                'has_settings' => true,
                'coming_soon' => true, // Changed to true to move to Coming Soon section
            ),
        );

        // Cache the available add-ons for 1 hour
        set_transient('redco_optimizer_addons_list', $available_addons, HOUR_IN_SECONDS);

        return $available_addons;
    }

    /**
     * Activate an addon.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The addon slug.
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    public function activate_addon($addon_slug) {
        if (!isset($this->installed_addons[$addon_slug])) {
            return new WP_Error('addon_not_found', __('Addon not found.', 'redco-optimizer'));
        }

        $addon = $this->installed_addons[$addon_slug];

        // Check if addon is premium and user doesn't have premium
        if ($addon['premium'] && !redco_is_premium()) {
            return new WP_Error('premium_required', __('This addon requires a premium license.', 'redco-optimizer'));
        }

        // Get active addons
        $active_addons = get_option('redco_active_addons', array());
        $active_addons[$addon_slug] = true;

        // Update active addons
        update_option('redco_active_addons', $active_addons);

        // Load addon
        include_once $addon['file'];

        // Update installed addons
        $this->installed_addons[$addon_slug]['active'] = true;

        return true;
    }

    /**
     * Deactivate an addon.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The addon slug.
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    public function deactivate_addon($addon_slug) {
        if (!isset($this->installed_addons[$addon_slug])) {
            return new WP_Error('addon_not_found', __('Addon not found.', 'redco-optimizer'));
        }

        // Get active addons
        $active_addons = get_option('redco_active_addons', array());

        // Remove addon from active addons
        if (isset($active_addons[$addon_slug])) {
            unset($active_addons[$addon_slug]);
        }

        // Update active addons
        update_option('redco_active_addons', $active_addons);

        // Update installed addons
        $this->installed_addons[$addon_slug]['active'] = false;

        return true;
    }

    /**
     * Install an add-on.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The add-on slug.
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    public function install_addon($addon_slug) {
        // Prevent installing the image-optimizer add-on as it conflicts with the module
        if ($addon_slug === 'image-optimizer') {
            return new WP_Error('addon_conflict', __('The Image Optimizer add-on cannot be installed because it conflicts with the Image Optimizer module.', 'redco-optimizer'));
        }

        // Prevent installing the security-enhancer add-on as it's marked as coming soon
        if ($addon_slug === 'security-enhancer') {
            return new WP_Error('addon_coming_soon', __('The Security Enhancer add-on is coming soon and cannot be installed yet.', 'redco-optimizer'));
        }

        // Prevent installing removed add-ons
        $removed_addons = array('advanced-lazy-load', 'webp-converter', 'cdn-manager');
        if (in_array($addon_slug, $removed_addons)) {
            return new WP_Error('addon_removed', __('This add-on has been removed and is no longer available.', 'redco-optimizer'));
        }

        // Get available add-ons
        $available_addons = $this->get_available_addons();

        // Check if the add-on exists
        if (!isset($available_addons[$addon_slug])) {
            return new WP_Error('addon_not_found', __('Add-on not found.', 'redco-optimizer'));
        }

        // Get the add-on data
        $addon = $available_addons[$addon_slug];

        // Check if this is a premium add-on
        if (isset($addon['premium']) && $addon['premium'] && !redco_is_premium()) {
            return new WP_Error('premium_required', __('This add-on requires a premium license.', 'redco-optimizer'));
        }

        // Create the add-on directory if it doesn't exist
        $addon_dir = $this->addons_dir . $addon_slug;
        if (!file_exists($addon_dir)) {
            wp_mkdir_p($addon_dir);
        }

        // Create a basic add-on file
        $addon_file = $addon_dir . '/' . $addon_slug . '.php';
        if (!file_exists($addon_file)) {
            $addon_file_content = "<?php
/**
 * {$addon['name']}
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/{$addon_slug}
 *
 * Addon Name: {$addon['name']}
 * Description: {$addon['description']}
 * Version: {$addon['version']}
 * Author: {$addon['author']}
 * Author URI: {$addon['author_url']}
 * Premium: " . (isset($addon['premium']) && $addon['premium'] ? 'true' : 'false') . "
 * Has Settings: " . (isset($addon['has_settings']) && $addon['has_settings'] ? 'true' : 'false') . "
 * Coming Soon: " . (isset($addon['coming_soon']) && $addon['coming_soon'] ? 'true' : 'false') . "
 * Icon: " . (isset($addon['icon']) ? $addon['icon'] : 'dashicons-admin-plugins') . "
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Currently plugin version.
 */
define('REDCO_" . strtoupper(str_replace('-', '_', $addon_slug)) . "_VERSION', '{$addon['version']}');

/**
 * The class responsible for defining all actions specific to this add-on.
 */
class Redco_Optimizer_" . str_replace('-', '_', $addon_slug) . " {

    /**
     * The settings for this add-on.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    \$settings    The settings for this add-on.
     */
    private \$settings;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Load settings
        \$this->settings = \$this->get_settings();

        // Initialize the add-on
        add_action('init', array(\$this, 'init'));
    }

    /**
     * Initialize the add-on.
     *
     * @since    1.0.0
     */
    public function init() {
        // Add your initialization code here
    }

    /**
     * Get add-on settings.
     *
     * @since    1.0.0
     * @return   array    The add-on settings.
     */
    public function get_settings() {
        return get_option('redco_" . str_replace('-', '_', $addon_slug) . "_settings', array());
    }

    /**
     * Save add-on settings.
     *
     * @since    1.0.0
     * @param    array    \$settings    The add-on settings.
     * @return   bool     True on success, false on failure.
     */
    public function save_settings(\$settings) {
        return update_option('redco_" . str_replace('-', '_', $addon_slug) . "_settings', \$settings);
    }
}

// Initialize the add-on
\$redco_" . str_replace('-', '_', $addon_slug) . " = new Redco_Optimizer_" . str_replace('-', '_', $addon_slug) . "();
";
            file_put_contents($addon_file, $addon_file_content);
        }

        // Create templates directory if it doesn't exist
        $templates_dir = $addon_dir . '/templates';
        if (!file_exists($templates_dir)) {
            wp_mkdir_p($templates_dir);
        }

        // Create settings template file if it doesn't exist
        $settings_file = $templates_dir . '/settings.php';
        if (!file_exists($settings_file)) {
            $settings_file_content = "<?php
/**
 * {$addon['name']} Settings Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/{$addon_slug}/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings from the appropriate source
// This handles both direct class access and AJAX context
\$settings = isset(\$settings) ? \$settings : (isset(\$this->settings) ? \$this->settings : array());
?>

<form method=\"post\" action=\"\" class=\"redco-addon-settings-form\" data-addon=\"{$addon_slug}\">
    <?php wp_nonce_field('redco_{$addon_slug}_save_settings', 'redco_{$addon_slug}_nonce'); ?>

    <div class=\"redco-card\">
        <div class=\"redco-card-header\">
            <h3><?php esc_html_e('{$addon['name']} Settings', 'redco-optimizer'); ?></h3>
        </div>
        <div class=\"redco-card-content\">
            <div class=\"redco-toggle-row\">
                <div class=\"redco-toggle-info\">
                    <h4><?php esc_html_e('Enable {$addon['name']}', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('{$addon['description']}', 'redco-optimizer'); ?></p>
                </div>
                <div class=\"redco-toggle-control\">
                    <label class=\"redco-switch\">
                        <input type=\"checkbox\" name=\"enabled\" <?php checked(isset(\$settings['enabled']) ? \$settings['enabled'] : 0, 1); ?> value=\"1\">
                        <span class=\"redco-slider\"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class=\"redco-form-actions\">
        <button type=\"submit\" class=\"redco-button redco-button-primary\"><?php esc_html_e('Save Settings', 'redco-optimizer'); ?></button>
    </div>
</form>
";
            file_put_contents($settings_file, $settings_file_content);
        }

        // Create settings modal template file if it doesn't exist
        $settings_modal_file = $templates_dir . '/settings-modal.php';
        if (!file_exists($settings_modal_file)) {
            $settings_modal_file_content = "<?php
/**
 * {$addon['name']} Settings Modal Template
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/addons/{$addon_slug}/templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get settings from the appropriate source
// This handles both direct class access and AJAX context
\$settings = isset(\$settings) ? \$settings : (isset(\$this->settings) ? \$this->settings : array());
?>

<form method=\"post\" action=\"\" class=\"redco-addon-settings-form\" data-addon=\"{$addon_slug}\">
    <?php wp_nonce_field('redco_{$addon_slug}_save_settings', 'redco_{$addon_slug}_nonce'); ?>

    <div class=\"redco-card\">
        <div class=\"redco-card-header\">
            <h3><?php esc_html_e('{$addon['name']} Settings', 'redco-optimizer'); ?></h3>
        </div>
        <div class=\"redco-card-content\">
            <div class=\"redco-toggle-row\">
                <div class=\"redco-toggle-info\">
                    <h4><?php esc_html_e('Enable {$addon['name']}', 'redco-optimizer'); ?></h4>
                    <p><?php esc_html_e('{$addon['description']}', 'redco-optimizer'); ?></p>
                </div>
                <div class=\"redco-toggle-control\">
                    <label class=\"redco-switch\">
                        <input type=\"checkbox\" name=\"enabled\" <?php checked(isset(\$settings['enabled']) ? \$settings['enabled'] : 0, 1); ?> value=\"1\">
                        <span class=\"redco-slider\"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class=\"redco-form-actions\">
        <button type=\"submit\" class=\"redco-button redco-button-primary\"><?php esc_html_e('Save Settings', 'redco-optimizer'); ?></button>
    </div>
</form>
";
            file_put_contents($settings_modal_file, $settings_modal_file_content);
        }

        // Add the add-on to the installed add-ons
        $this->installed_addons[$addon_slug] = array(
            'name' => $addon['name'],
            'description' => $addon['description'],
            'version' => $addon['version'],
            'author' => $addon['author'],
            'author_uri' => $addon['author_url'],
            'active' => false,
            'premium' => isset($addon['premium']) ? $addon['premium'] : false,
            'has_settings' => isset($addon['has_settings']) ? $addon['has_settings'] : false,
            'coming_soon' => isset($addon['coming_soon']) ? $addon['coming_soon'] : false,
            'icon' => isset($addon['icon']) ? $addon['icon'] : 'dashicons-admin-plugins',
            'file' => $addon_slug . '/' . $addon_slug . '.php',
            'slug' => $addon_slug,
        );

        // Save the updated add-ons
        update_option('redco_optimizer_installed_addons', $this->installed_addons);

        return true;
    }

    /**
     * Uninstall an add-on.
     *
     * @since    1.0.0
     * @param    string    $addon_slug    The add-on slug.
     * @return   bool|WP_Error    True on success, WP_Error on failure.
     */
    public function uninstall_addon($addon_slug) {
        // Check if the add-on exists
        if (!isset($this->installed_addons[$addon_slug])) {
            return new WP_Error('addon_not_found', __('Add-on not found.', 'redco-optimizer'));
        }

        // Check if the add-on is active
        if (isset($this->installed_addons[$addon_slug]['active']) && $this->installed_addons[$addon_slug]['active']) {
            return new WP_Error('addon_active', __('Please deactivate the add-on before uninstalling.', 'redco-optimizer'));
        }

        // Store the add-on data before removing it
        $addon_data = $this->installed_addons[$addon_slug];

        // Get the add-on directory path
        $addon_dir = $this->addons_dir . $addon_slug;

        // Remove the add-on from installed add-ons
        unset($this->installed_addons[$addon_slug]);

        // Save the updated add-ons
        update_option('redco_optimizer_installed_addons', $this->installed_addons);

        // Try to physically delete the add-on files
        // This is optional and will only work if the server has permission to delete files
        if (file_exists($addon_dir) && is_dir($addon_dir)) {
            $this->delete_directory($addon_dir);
        }

        return true;
    }

    /**
     * Delete a directory and all its contents recursively.
     *
     * @since    1.0.0
     * @param    string    $dir    The directory path.
     * @return   bool      True on success, false on failure.
     */
    private function delete_directory($dir) {
        if (!file_exists($dir)) {
            return true;
        }

        if (!is_dir($dir)) {
            return unlink($dir);
        }

        foreach (scandir($dir) as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }

            if (!$this->delete_directory($dir . DIRECTORY_SEPARATOR . $item)) {
                return false;
            }
        }

        return rmdir($dir);
    }

    /**
     * Refresh add-ons.
     *
     * @since    1.0.0
     * @return   bool    True on success, false on failure.
     */
    public function refresh_addons() {
        // In a real implementation, this would check for updates and new add-ons from a remote API
        // For now, we'll just refresh the local add-ons

        // Clear the add-ons cache
        delete_transient('redco_optimizer_addons_list');

        // Remove the image-optimizer add-on if it exists (it conflicts with the module)
        if (isset($this->installed_addons['image-optimizer'])) {
            // If it's active, deactivate it first
            if (isset($this->installed_addons['image-optimizer']['active']) && $this->installed_addons['image-optimizer']['active']) {
                $this->deactivate_addon('image-optimizer');
            }

            // Remove it from installed add-ons
            unset($this->installed_addons['image-optimizer']);

            // Save the updated add-ons
            update_option('redco_optimizer_installed_addons', $this->installed_addons);

            error_log('Redco Optimizer - Removed image-optimizer add-on because it conflicts with the module');
        }

        // Update the security-enhancer add-on to mark it as coming soon if it exists
        if (isset($this->installed_addons['security-enhancer'])) {
            // If it's active, deactivate it first
            if (isset($this->installed_addons['security-enhancer']['active']) && $this->installed_addons['security-enhancer']['active']) {
                $this->deactivate_addon('security-enhancer');
            }

            // Update it to mark as coming soon
            $this->installed_addons['security-enhancer']['coming_soon'] = true;

            // Save the updated add-ons
            update_option('redco_optimizer_installed_addons', $this->installed_addons);

            error_log('Redco Optimizer - Updated security-enhancer add-on to mark it as coming soon');
        }

        // Remove the deleted add-ons if they exist
        $removed_addons = array('advanced-lazy-load', 'webp-converter', 'cdn-manager');
        $removed_any = false;

        foreach ($removed_addons as $addon_slug) {
            if (isset($this->installed_addons[$addon_slug])) {
                // If it's active, deactivate it first
                if (isset($this->installed_addons[$addon_slug]['active']) && $this->installed_addons[$addon_slug]['active']) {
                    $this->deactivate_addon($addon_slug);
                }

                // Remove it from installed add-ons
                unset($this->installed_addons[$addon_slug]);

                $removed_any = true;
                error_log('Redco Optimizer - Removed ' . $addon_slug . ' add-on because it has been deleted');
            }
        }

        // Save the updated add-ons if any were removed
        if ($removed_any) {
            update_option('redco_optimizer_installed_addons', $this->installed_addons);
        }

        // Get available add-ons (this will rebuild the cache)
        $available_addons = $this->get_available_addons();

        // Update installed add-ons with any changes from available add-ons
        foreach ($this->installed_addons as $addon_slug => $addon) {
            if (isset($available_addons[$addon_slug])) {
                // Update add-on data
                $this->installed_addons[$addon_slug]['name'] = $available_addons[$addon_slug]['name'];
                $this->installed_addons[$addon_slug]['description'] = $available_addons[$addon_slug]['description'];
                $this->installed_addons[$addon_slug]['version'] = $available_addons[$addon_slug]['version'];
                $this->installed_addons[$addon_slug]['author'] = $available_addons[$addon_slug]['author'];
                $this->installed_addons[$addon_slug]['author_uri'] = $available_addons[$addon_slug]['author_url'];
                $this->installed_addons[$addon_slug]['premium'] = isset($available_addons[$addon_slug]['premium']) ? $available_addons[$addon_slug]['premium'] : false;
                $this->installed_addons[$addon_slug]['coming_soon'] = isset($available_addons[$addon_slug]['coming_soon']) ? $available_addons[$addon_slug]['coming_soon'] : false;
                $this->installed_addons[$addon_slug]['icon'] = isset($available_addons[$addon_slug]['icon']) ? $available_addons[$addon_slug]['icon'] : 'dashicons-admin-plugins';
            }
        }

        // Save the updated add-ons
        update_option('redco_optimizer_installed_addons', $this->installed_addons);

        return true;
    }
}

/**
 * Get installed addons.
 *
 * @since    1.0.0
 * @return   array    The installed addons.
 */
function redco_get_installed_addons() {
    global $redco_optimizer_addons;

    if (!isset($redco_optimizer_addons)) {
        $redco_optimizer_addons = new Redco_Optimizer_Addons();
    }

    return $redco_optimizer_addons->get_installed_addons();
}

/**
 * Check if user has premium license.
 *
 * @since    1.0.0
 * @return   bool    True if user has premium license, false otherwise.
 */
function redco_is_premium() {
    // This is a placeholder function. In a real implementation, this would check the license status.
    return false;
}
