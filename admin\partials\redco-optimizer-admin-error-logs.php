<?php
/**
 * Error Logs section for the Tools tab
 *
 * @link       https://redco-optimizer.com
 * @since      1.0.0
 *
 * @package    Redco_Optimizer
 * @subpackage Redco_Optimizer/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Initialize the error logger
$error_logger = new Redco_Optimizer_Error_Logger();
$log_content = $error_logger->get_log_content();
$log_file_path = $error_logger->get_log_file_path();
?>

<div class="redco-card">
    <div class="redco-card-header">
        <h3><?php esc_html_e('Error Logs', 'redco-optimizer'); ?></h3>
    </div>
    <div class="redco-card-content">
        <p class="redco-settings-description">
            <?php esc_html_e('View and manage error logs generated by Redco Optimizer. These logs can help diagnose issues with the plugin.', 'redco-optimizer'); ?>
        </p>

        <div class="redco-error-log-actions">
            <button type="button" class="redco-button redco-button-secondary redco-refresh-logs">
                <span class="dashicons dashicons-update"></span>
                <?php esc_html_e('Refresh Logs', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="redco-button redco-button-secondary redco-clear-logs">
                <span class="dashicons dashicons-trash"></span>
                <?php esc_html_e('Clear Logs', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="redco-button redco-button-secondary redco-export-logs">
                <span class="dashicons dashicons-download"></span>
                <?php esc_html_e('Export Logs', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="redco-button redco-button-secondary redco-email-logs">
                <span class="dashicons dashicons-email"></span>
                <?php esc_html_e('Email Logs', 'redco-optimizer'); ?>
            </button>
        </div>

        <div class="redco-error-log-container">
            <?php if (empty($log_content)) : ?>
                <div class="redco-empty-logs-message">
                    <p><?php esc_html_e('No error logs found. This is good news! It means Redco Optimizer is running smoothly.', 'redco-optimizer'); ?></p>
                </div>
            <?php else : ?>
                <div class="redco-log-filters">
                    <div class="redco-log-filter">
                        <label for="redco-log-level-filter"><?php esc_html_e('Filter by Level:', 'redco-optimizer'); ?></label>
                        <select id="redco-log-level-filter" class="redco-select">
                            <option value="all"><?php esc_html_e('All Levels', 'redco-optimizer'); ?></option>
                            <option value="ERROR"><?php esc_html_e('Errors', 'redco-optimizer'); ?></option>
                            <option value="WARNING"><?php esc_html_e('Warnings', 'redco-optimizer'); ?></option>
                            <option value="INFO"><?php esc_html_e('Info', 'redco-optimizer'); ?></option>
                        </select>
                    </div>

                    <div class="redco-log-filter">
                        <label for="redco-log-search"><?php esc_html_e('Search:', 'redco-optimizer'); ?></label>
                        <input type="text" id="redco-log-search" class="redco-input" placeholder="<?php esc_attr_e('Search logs...', 'redco-optimizer'); ?>">
                    </div>

                    <div class="redco-log-filter">
                        <button type="button" id="redco-reset-filters" class="redco-button redco-button-secondary">
                            <span class="dashicons dashicons-dismiss"></span>
                            <?php esc_html_e('Reset Filters', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </div>

                <div class="redco-log-content-wrapper">
                    <pre id="redco-log-content" class="redco-log-content"><?php echo esc_html($log_content); ?></pre>
                </div>

                <div class="redco-log-info">
                    <p>
                        <?php
                        if (file_exists($log_file_path)) {
                            $file_size = size_format(filesize($log_file_path), 2);
                            $file_modified = date_i18n(get_option('date_format') . ' ' . get_option('time_format'), filemtime($log_file_path));

                            printf(
                                esc_html__('Log file size: %1$s | Last modified: %2$s | Path: %3$s', 'redco-optimizer'),
                                $file_size,
                                $file_modified,
                                $log_file_path
                            );
                        }
                        ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Email Logs Modal -->
<div id="redco-email-logs-modal" class="redco-modal-overlay" style="display: none; visibility: hidden; opacity: 0;">
    <div class="redco-modal">
        <div class="redco-modal-header">
            <h2><?php esc_html_e('Email Error Logs', 'redco-optimizer'); ?></h2>
            <button type="button" class="redco-modal-close">&times;</button>
        </div>
        <div class="redco-modal-content">
            <p><?php esc_html_e('Send the error logs to an email address for further analysis.', 'redco-optimizer'); ?></p>

            <div class="redco-form-row">
                <div class="redco-form-label">
                    <label for="redco-email-recipient"><?php esc_html_e('Recipient Email:', 'redco-optimizer'); ?></label>
                </div>
                <div class="redco-form-field">
                    <input type="email" id="redco-email-recipient" class="redco-input" value="<?php echo esc_attr(get_option('admin_email')); ?>" placeholder="<?php echo esc_attr(get_option('admin_email')); ?>">
                </div>
            </div>

            <div class="redco-form-row">
                <div class="redco-form-label">
                    <label for="redco-email-subject"><?php esc_html_e('Subject:', 'redco-optimizer'); ?></label>
                </div>
                <div class="redco-form-field">
                    <input type="text" id="redco-email-subject" class="redco-input" value="<?php echo esc_attr(sprintf(__('Redco Optimizer Error Logs - %s', 'redco-optimizer'), get_bloginfo('name'))); ?>">
                </div>
            </div>

            <div class="redco-form-row">
                <div class="redco-form-label">
                    <label for="redco-email-message"><?php esc_html_e('Message:', 'redco-optimizer'); ?></label>
                </div>
                <div class="redco-form-field">
                    <textarea id="redco-email-message" class="redco-textarea" rows="4"><?php echo esc_textarea(__("Here are the error logs from Redco Optimizer.\n\nSite URL: ", 'redco-optimizer') . get_site_url()); ?></textarea>
                </div>
            </div>
        </div>
        <div class="redco-modal-footer">
            <button type="button" class="redco-button redco-button-secondary redco-modal-cancel"><?php esc_html_e('Cancel', 'redco-optimizer'); ?></button>
            <button type="button" class="redco-button redco-button-primary redco-send-logs-email"><?php esc_html_e('Send Email', 'redco-optimizer'); ?></button>
        </div>
    </div>
</div>
